<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bd.mapper.TyphoonMapper">

    <select id="GetTyphoonInfo" parameterType="String" resultType="com.bd.entity.TyphoonInfo">
        select b.ID as id, a.TFBH as tfbh, a.LOADTIME as loadTime,
               a.TFLAND as tfland, a.TFM as tfm, a.TFME as tfme,
               a.TFYEAR as tfyear, b.<PERSON><PERSON><PERSON><PERSON> as radiusten, b.RADIUS7 as radiusseven,
               b.JD as lon, b.WD as lat, b.ZXFS as zxfs, b.ZXQY as zxqy, b.GQSJ as gqsj,
               b.YDSD as ydsd, b.YDFX as ydfx
        from TYPHOONINFO a left join TYPHOONLSLJ b on a.TFBH = b.TFBH
        where b.GQSJ > #{time} ORDER BY a.TFBH DESC,b.GQSJ ASC;
    </select>

    <select id="GetTyphoonCountInfo" parameterType="String" resultType="java.lang.Integer">
        select distinct a.TFBH as tfbh
        from TYPHOONINFO a left join TYPHOONLSLJ b on a.TFBH = b.TFBH
        where b.GQSJ > #{time} ORDER BY a.TFBH DESC;
    </select>

    <select id="GetTyphoonInfoByYear" resultType="com.bd.entity.TyphoonInfo">
        select a.ID as id, a.TFBH as tfbh, a.TFM as tfm, a.TFME as tfme
        from TYPHOONINFO a
        where a.TFYEAR = #{year} ORDER BY a.TFBH DESC;
    </select>

    <select id="GetTyphoonInfoByYearCount" resultType="java.lang.Integer">
        select distinct a.TFYEAR
        from TYPHOONINFO a order by a.TFYEAR desc;
    </select>

    <select id="GetTyphoonInfoById" resultType="com.bd.entity.TyphoonInfo">
        select b.ID as id, a.TFBH as tfbh, a.LOADTIME as loadTime,
               a.TFLAND as tfland, a.TFM as tfm, a.TFME as tfme,
               a.TFYEAR as tfyear, b.RADIUS10 as radiusten, b.RADIUS7 as radiusseven,
               b.JD as lon, b.WD as lat, b.ZXFS as zxfs, b.ZXQY as zxqy, b.GQSJ as gqsj,
               b.YDSD as ydsd, b.YDFX as ydfx
        from TYPHOONINFO a left join TYPHOONLSLJ b on a.TFBH = b.TFBH
        where b.TFBH = #{id} ORDER BY a.TFBH DESC,b.GQSJ ASC;
    </select>

    <select id="GetYBTyphoonInfoById" resultType="com.bd.entity.TyphoonInfo">
        select *
        from (select t1.ID,
                     t1.TFBH,
                     t1.YBTM,
                     t1.YBSJ,
                     t1.GQSJ,
                     t1.JD as lon,
                     t1.WD as lat,
                     t1.RADIUS7 as radiusseven,
                     t1.RADIUS10 as radiusten,
                     t1.ZXFS,
                     t1.ZXQY,
                     t1.YDSD,
                     t1.YDFX,
                     t1.TFLAND,
                     t1.TFM,
                     t1.TFME,
                     t1.TFYEAR,
                     max(t1.GQSJ) over(partition by t1.YBTM) temp1
              from (select t.ID,
                           t.TFBH,
                           t.YBTM,
                           t.YBSJ,
                           t.GQSJ,
                           t.JD,
                           t.WD,
                           t.RADIUS7,
                           t.RADIUS10,
                           t.ZXFS,
                           t.ZXQY,
                           t.YDSD,
                           t.YDFX,
                           ti.TFLAND,
                           ti.TFM,
                           ti.TFME,
                           ti.TFYEAR
                    from TYPHOONINFO ti
                             left join (select *
                                        from (select ty.ID,
                                                     ty.TFBH,
                                                     ty.YBTM,
                                                     ty.YBSJ,
                                                     ty.GQSJ,
                                                     max(ty.GQSJ) over(partition by ty.YBTM,ty.YBSJ) temp, ty.JD,
                                                     ty.WD,
                                                     ty.RADIUS7,
                                                     ty.RADIUS10,
                                                     ty.ZXFS,
                                                     ty.ZXQY,
                                                     ty.YDSD,
                                                     ty.YDFX
                                              from TYPHOONYBLJ ty
                                              where ty.TFBH = #{id}
                                              order by ty.YBTM, ty.YBSJ)
                                        where GQSJ = temp) t on ti.TFBH = t.TFBH
                    ) t1)
        where GQSJ = temp1 and sysdate - GQSJ <![CDATA[<]]> 2
        order by YBTM, YBSJ
    </select>

    <select id="GetTodayTyphoonInfo" resultType="com.bd.entity.TyphoonInfo">
        select b.ID as id, a.TFBH as tfbh, a.LOADTIME as loadTime,
               a.TFLAND as tfland, a.TFM as tfm, a.TFME as tfme,
               a.TFYEAR as tfyear, b.RADIUS10 as radiusten, b.RADIUS7 as radiusseven,
               b.JD as lon, b.WD as lat, b.ZXFS as zxfs, b.ZXQY as zxqy, b.GQSJ as gqsj,
               b.YDSD as ydsd, b.YDFX as ydfx
        from TYPHOONINFO a left join TYPHOONLSLJ b on a.TFBH = b.TFBH
        where b.GQSJ > #{startTime} and b.GQSJ &lt; #{endTime} ORDER BY a.TFBH DESC,b.GQSJ ASC;
    </select>

    <select id="GetTodayTyphoonInfoCount" resultType="java.lang.Integer">
        select count (a.TFBH)
        from TYPHOONINFO a left join TYPHOONLSLJ b on a.TFBH = b.TFBH
        where b.GQSJ > #{startTime} and b.GQSJ &lt; #{endTime} ORDER BY a.TFBH DESC,b.GQSJ ASC;
    </select>
    <select id="GetYBQDById" resultType="com.bd.entity.TyphoonInfo">
        select ID       as id,
               RADIUS10 as radiusten,
               RADIUS7  as radiusseven,
               JD       as lon,
               WD       as lat,
               ZXFS     as zxfs,
               ZXQY     as zxqy,
               GQSJ     as gqsj,
               YDSD     as ydsd,
               YDFX     as ydfx
        from TYPHOONLSLJ
        where GQSJ in (select GQSJ
                       from (select t1.ID,
                                    t1.YBTM,
                                    t1.YBSJ,
                                    t1.GQSJ,
                                    max(t1.GQSJ) over(partition by t1.YBTM) temp1,
                                    t1.JD,
                                    t1.WD
                             from (select t.ID,
                                          t.TFBH,
                                          t.YBTM,
                                          t.YBSJ,
                                          t.GQSJ,
                                          t.JD,
                                          t.WD,
                                          t.RADIUS7,
                                          t.RADIUS10,
                                          t.ZXFS,
                                          t.ZXQY,
                                          t.YDSD,
                                          t.YDFX,
                                          ti.TFLAND,
                                          ti.TFM,
                                          ti.TFME,
                                          ti.TFYEAR
                                   from TYPHOONINFO ti
                                            left join (select *
                                                       from (select ty.ID,
                                                                    ty.TFBH,
                                                                    ty.YBTM,
                                                                    ty.YBSJ,
                                                                    ty.GQSJ,
                                                                    max(ty.GQSJ) over(partition by ty.YBTM,ty.YBSJ) temp,
                                                                    ty.JD,
                                                                    ty.WD,
                                                                    ty.RADIUS7,
                                                                    ty.RADIUS10,
                                                                    ty.ZXFS,
                                                                    ty.ZXQY,
                                                                    ty.YDSD,
                                                                    ty.YDFX
                                                             from TYPHOONYBLJ ty
                                                             where ty.TFBH = #{id}
                                                             order by ty.YBTM, ty.YBSJ)
                                                       where GQSJ = temp) t on ti.TFBH = t.TFBH) t1)
                       where GQSJ = temp1
                       order by YBTM, YBSJ)
    </select>

</mapper>