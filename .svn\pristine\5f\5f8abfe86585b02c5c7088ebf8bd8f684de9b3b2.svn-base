package com.bd.entity.BusinessManagement;

import lombok.Data;

@Data
public class AlarmRecord {
    private int ID;
    private String SHIPNAME;
    private int STATICSHIPID;
    private int PEOPLEID;
    private String BDID;
    private int PORTID;
    private int LON;
    private int LAT;
    private int TYPE;
    private int STATE;
    private String LOADTIME;
    private String CONTENT;
    private String UPDATETIME;

    private String LastPosTermNo;
    private String LastPosBDId;
    private String SSDW;
    private String owner;
    private String LXDH;
    private String areaName;
}
