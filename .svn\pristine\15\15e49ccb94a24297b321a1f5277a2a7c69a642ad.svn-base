2025-01-02 14:57:21.257 INFO  [restartedMain]com.bd.Application.logStarting:50 -Starting Application on THUNDEROBOT with PID 14880 (G:\yimaWorking\bdxt_shipMonitor_web\Server\springbootmybatisdmdb\target\classes started by 1p in G:\yimaWorking\bdxt_shipMonitor_web\Server\springbootmybatisdmdb)
2025-01-02 14:57:21.284 INFO  [restartedMain]com.bd.Application.logStartupProfileInfo:663 -The following profiles are active: dev
2025-01-02 14:57:21.358 INFO  [restartedMain]o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext.prepareRefresh:590 -Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7e301474: startup date [Thu Jan 02 14:57:21 CST 2025]; root of context hierarchy
2025-01-02 14:57:24.155 INFO  [restartedMain]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:328 -Bean 'asyncTaskConfig' of type [com.bd.config.AsyncTaskConfig$$EnhancerBySpringCGLIB$$f16e56d8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-02 14:57:24.169 INFO  [restartedMain]o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:171 -Initializing ExecutorService 
2025-01-02 14:57:24.187 INFO  [restartedMain]o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:171 -Initializing ExecutorService  'getAsyncExecutor'
2025-01-02 14:57:24.188 INFO  [restartedMain]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:328 -Bean 'getAsyncExecutor' of type [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-02 14:57:24.702 INFO  [restartedMain]o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:91 -Tomcat initialized with port(s): 7101 (http)
2025-01-02 14:57:24.714 INFO  [restartedMain]org.apache.coyote.http11.Http11NioProtocol.log:180 -Initializing ProtocolHandler ["http-nio-7101"]
2025-01-02 14:57:24.725 INFO  [restartedMain]org.apache.catalina.core.StandardService.log:180 -Starting service [Tomcat]
2025-01-02 14:57:24.725 INFO  [restartedMain]org.apache.catalina.core.StandardEngine.log:180 -Starting Servlet Engine: Apache Tomcat/8.5.31
2025-01-02 14:57:24.731 INFO  [localhost-startStop-1]org.apache.catalina.core.AprLifecycleListener.log:180 -The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Program Files\Java\jdk1.8.0_91\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\PlasticSCM5\server;C:\Program Files\PlasticSCM5\client;F:\vmware\bin\;C:\ProgramData\Oracle\Java\javapath;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\TortiiseSVN\bin;G:\sqlite\sqlite-tools-win32-x86-3320300;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Java\jdk-16.0.1\bin;C:\Program Files\Java\jdk-16.0.1\jre\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;G:\Node.js\;G:\Node.js\node_global;C:\Program Files\Git\cmd;C:\Program Files\Microsoft SQL Server\120\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\100\Tools\Binn\;C:\Program Files\Microsoft SQL Server\100\Tools\Binn\;C:\Program Files\Microsoft SQL Server\100\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\90\Tools\binn\;G:\Windows Kits\10\Windows Performance Toolkit\;F:\apache-maven-3.8.5\bin;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;G:\CLionSpace\untitled2\cmake-build-debug\;C:\Program Files\dotnet\;G:\skia_win\third_party\externals\emsdk;G:\skia_win\third_party\externals\emsdk\upstream\emscripten;G:\skia\third_party\externals\emsdk;G:\skia\third_party\externals\emsdk\upstream\emscripten;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\;C:\Users\<USER>\depot_tools;D:\VC++\Tools\WinNT;D:\VC++\MSDev98\Bin;D:\VC++\Tools;D:\Microsoft Visual Studio\VC98\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps2;G:\IntelliJ IDEA 2020.3\bin;;G:\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;G:\PyCharm 2020.2.3\bin;;C:\Users\<USER>\ninja;C:\Python27;G:\HUAWEI DevEco Studio 4.0\bin;;C:\Users\<USER>\.dotnet\tools;.]
2025-01-02 14:57:24.835 INFO  [localhost-startStop-1]o.a.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log:180 -Initializing Spring embedded WebApplicationContext
2025-01-02 14:57:24.835 INFO  [localhost-startStop-1]org.springframework.web.context.ContextLoader.prepareWebApplicationContext:285 -Root WebApplicationContext: initialization completed in 3477 ms
2025-01-02 14:57:24.960 INFO  [localhost-startStop-1]o.s.boot.web.servlet.ServletRegistrationBean.addRegistration:186 -Servlet dispatcherServlet mapped to [/]
2025-01-02 14:57:24.966 INFO  [localhost-startStop-1]o.s.boot.web.servlet.FilterRegistrationBean.configure:245 -Mapping filter: 'characterEncodingFilter' to: [/*]
2025-01-02 14:57:24.966 INFO  [localhost-startStop-1]o.s.boot.web.servlet.FilterRegistrationBean.configure:245 -Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-01-02 14:57:24.967 INFO  [localhost-startStop-1]o.s.boot.web.servlet.FilterRegistrationBean.configure:245 -Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-01-02 14:57:24.967 INFO  [localhost-startStop-1]o.s.boot.web.servlet.FilterRegistrationBean.configure:245 -Mapping filter: 'requestContextFilter' to: [/*]
2025-01-02 14:57:26.964 INFO  [restartedMain]com.zaxxer.hikari.HikariDataSource.getConnection:110 -HikariPool-1 - Starting...
2025-01-02 14:57:27.394 INFO  [restartedMain]com.zaxxer.hikari.pool.PoolBase.getAndSetNetworkTimeout:516 -HikariPool-1 - Driver does not support get/set network timeout for connections. (不支持的接口或功能)
2025-01-02 14:57:27.434 INFO  [restartedMain]com.zaxxer.hikari.HikariDataSource.getConnection:123 -HikariPool-1 - Start completed.
2025-01-02 14:57:27.611 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 14:57:27.836 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerAdapter.initControllerAdviceCache:574 -Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7e301474: startup date [Thu Jan 02 14:57:21 CST 2025]; root of context hierarchy
2025-01-02 14:57:27.936 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/token],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.token() throws java.io.IOException
2025-01-02 14:57:27.937 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/test],methods=[GET]}" onto public int com.bd.controller.HttpController.pp()
2025-01-02 14:57:27.939 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/Postppp],methods=[POST]}" onto public java.lang.String com.bd.controller.HttpController.post(java.lang.String,java.lang.String)
2025-01-02 14:57:27.939 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllBDShipPosition],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllBDShipPosition(int)
2025-01-02 14:57:27.940 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetBDShipPosition],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipDynamicInfo> com.bd.controller.HttpController.GetBDShipPosition(int)
2025-01-02 14:57:27.940 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/getShipInOutReport],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.getShipInOutReport(java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 14:57:27.940 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.getShipInOutByShipName()
2025-01-02 14:57:27.941 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipInfoByShipName]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_all> com.bd.controller.HttpController.GetOneShipInfoByShipName(java.lang.String)
2025-01-02 14:57:27.941 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipDistribute]}" onto public com.bd.entity.Ship_DistributeCount com.bd.controller.HttpController.GetShipDistribute(int)
2025-01-02 14:57:27.941 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetImportanceShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.SetImportanceShip(int,int)
2025-01-02 14:57:27.942 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutInPortRecordByShipId],methods=[GET]}" onto public java.util.List<com.bd.entity.OutInPortRecord> com.bd.controller.HttpController.GetOutInPortRecordByShipId(java.lang.String)
2025-01-02 14:57:27.942 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShip_inOrOutPortInfoDetail],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShip_inOrOutPortInfoDetail(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-01-02 14:57:27.943 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetFuxiuWhiteShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.SetFuxiuWhiteShip(int)
2025-01-02 14:57:27.944 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAreaInfoByUserIdPageNum],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAreaInfoByUserIdPageNum(int,int)
2025-01-02 14:57:27.944 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutInPortRecord],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetOutInPortRecord()
2025-01-02 14:57:27.944 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetImportanceShip],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipDynamicInfo> com.bd.controller.HttpController.GetImportanceShip(int)
2025-01-02 14:57:27.946 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTodayWarningAllStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTodayWarningAllStatistics(int,java.lang.String,int)
2025-01-02 14:57:27.946 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTodayWarningStatistics],methods=[GET]}" onto public com.bd.entity.WarningStatistics com.bd.controller.HttpController.GetTodayWarningStatistics(int,int)
2025-01-02 14:57:27.946 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipInfoById],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_all> com.bd.controller.HttpController.GetOneShipInfoById(int)
2025-01-02 14:57:27.947 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetBDShipPosition_ronghe],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipDynamicInfo> com.bd.controller.HttpController.GetBDShipPosition_ronghe(int)
2025-01-02 14:57:27.947 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipCardById],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_card> com.bd.controller.HttpController.GetOneShipCardById(int)
2025-01-02 14:57:27.947 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAreaInfoByUserId],methods=[GET]}" onto public java.util.List<com.bd.entity.AreaInfo> com.bd.controller.HttpController.GetAreaInfoByUserId(java.lang.String)
2025-01-02 14:57:27.948 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWarningStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetWarningStatistics(int,int)
2025-01-02 14:57:27.948 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteFuxiuWhiteShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.DeleteFuxiuWhiteShip(int)
2025-01-02 14:57:27.948 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipDynamicInfoById]}" onto public com.bd.entity.ShipDynamicInfo com.bd.controller.HttpController.GetOneShipDynamicInfoById(int)
2025-01-02 14:57:27.949 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipCardTexuById],methods=[GET]}" onto public com.bd.entity.FisheryPermitInfo com.bd.controller.HttpController.GetOneShipCardTexuById(int)
2025-01-02 14:57:27.949 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateOutLineReason],methods=[POST]}" onto public java.lang.String com.bd.controller.HttpController.updateOutlineReason(com.bd.entity.ShipEncryption)
2025-01-02 14:57:27.949 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPlayShipInfoById],methods=[GET]}" onto public com.bd.entity.PlayShipInfo com.bd.controller.HttpController.GetPlayShipInfoById(int,int,int)
2025-01-02 14:57:27.950 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPlayAreaHistoryInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipTrack> com.bd.controller.HttpController.GetPlayAreaHistoryInfo(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 14:57:27.950 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTodayWarningInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.WarningInfo> com.bd.controller.HttpController.GetTodayWarningInfo(int,java.lang.String,int)
2025-01-02 14:57:27.951 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipHistoryTrackById],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipTrack> com.bd.controller.HttpController.GetShipHistoryTrackById(int,long,long,int,int,int)
2025-01-02 14:57:27.951 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteImportanceShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.DeleteImportanceShip(int,int)
2025-01-02 14:57:27.951 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShip_inOrOutPortInfoDetail],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShip_inOrOutPortInfoDetail(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 14:57:27.952 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllPeople_Export]}" onto public java.lang.String com.bd.controller.HttpController.GetAllPeople_Export(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.bd.entity.dto.PeopleQueryDto)
2025-01-02 14:57:27.952 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetApplinameByShipId],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetApplinameByShipId(int)
2025-01-02 14:57:27.952 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPortStatisticsInOutShipCount],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetPortStatisticsInOutShipCount()
2025-01-02 14:57:27.953 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllLawRecordInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllLawRecordInfo(java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 14:57:27.953 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAlarmCountByTime],methods=[GET]}" onto public com.bd.entity.TongjiModel com.bd.controller.HttpController.GetAlarmCountByTime(int,int,int)
2025-01-02 14:57:27.953 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCrewPeopleByShipId]}" onto public java.lang.String com.bd.controller.HttpController.GetCrewPeopleByShipId(int)
2025-01-02 14:57:27.953 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPeopleByShipId],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetPeopleByShipId(int)
2025-01-02 14:57:27.954 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipOnlineStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShipOnlineStatistics(java.lang.String,java.lang.String,java.lang.String)
2025-01-02 14:57:27.954 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetYuzhengShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.Ship_Yuzheng> com.bd.controller.HttpController.GetYuzhengShipInfo()
2025-01-02 14:57:27.954 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeletedLawRecordInfo]}" onto public java.lang.String com.bd.controller.HttpController.DeletedLawRecordInfo(java.util.List<java.lang.Integer>)
2025-01-02 14:57:27.955 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipOnlineStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipOnlineStatistics(java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 14:57:27.955 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetZhifaPeopleInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.LawPeople> com.bd.controller.HttpController.GetZhifaPeopleInfo()
2025-01-02 14:57:27.955 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPeopleByIdcards],methods=[POST]}" onto public java.lang.String com.bd.controller.HttpController.GetPeopleByIdcards(java.util.Map<java.lang.String, java.util.List<java.lang.String>>) throws java.lang.Exception
2025-01-02 14:57:27.955 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipStatistics()
2025-01-02 14:57:27.956 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPortNodeByShipId]}" onto public java.lang.String com.bd.controller.HttpController.GetPortNodeByShipId(int,int)
2025-01-02 14:57:27.956 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllAlarmRecordInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllAlarmRecordInfo(java.lang.String,java.lang.String,java.lang.String,int,int,int)
2025-01-02 14:57:27.956 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAlarmShipIdByAlarmId],methods=[GET]}" onto public int com.bd.controller.HttpController.GetAlarmShipIdByAlarmId()
2025-01-02 14:57:27.957 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllPeople],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllPeople(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int,com.bd.entity.dto.PeopleQueryDto)
2025-01-02 14:57:27.957 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllFishShip]}" onto public java.lang.String com.bd.controller.HttpController.GetAllFishShip(java.lang.String,java.lang.String,java.lang.String,int,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,com.bd.entity.dto.ShipQueryDto)
2025-01-02 14:57:27.957 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllAreaInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllAreaInfo(int)
2025-01-02 14:57:27.957 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllVoyageInfo]}" onto public java.lang.String com.bd.controller.HttpController.GetAllVoyageInfo(java.lang.String,int)
2025-01-02 14:57:27.958 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllFocusShip]}" onto public java.lang.String com.bd.controller.HttpController.GetAllFocusShip(java.lang.String,java.lang.String,java.lang.String,int,int,int)
2025-01-02 14:57:27.958 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/api/shipCount]}" onto public java.lang.String com.bd.controller.HttpController.shipCount()
2025-01-02 14:57:27.958 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/getCheckRecordByShipName],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.getCheckRecord(java.lang.String)
2025-01-02 14:57:27.958 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetFocusShip]}" onto public java.lang.String com.bd.controller.HttpController.GetFocusShip(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 14:57:27.959 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetServerState],methods=[GET]}" onto public com.bd.entity.TongjiModel com.bd.controller.HttpController.GetServerState(int)
2025-01-02 14:57:27.959 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetLawRecordInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetLawRecordInfo(java.lang.String,java.lang.String,java.lang.String)
2025-01-02 14:57:27.959 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllInPortShip]}" onto public java.lang.String com.bd.controller.HttpController.GetAllInPortShip(java.lang.String,int)
2025-01-02 14:57:27.959 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipManyRegis],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipManyRegis(int)
2025-01-02 14:57:27.960 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetSpecialShip],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipDynamicInfo> com.bd.controller.HttpController.GetSpecialShip()
2025-01-02 14:57:27.960 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAreaInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.AreaInfo> com.bd.controller.HttpController.GetAreaInfo()
2025-01-02 14:57:27.960 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/EditPort]}" onto public java.lang.String com.bd.controller.HttpController.EditPort(int,java.lang.String,java.lang.String,int,int,java.lang.String,java.util.List<com.bd.entity.Coordinate>)
2025-01-02 14:57:27.961 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/AddPort]}" onto public int com.bd.controller.HttpController.AddPort(java.lang.String,java.lang.String,int,int,java.lang.String,java.util.List<com.bd.entity.Coordinate>)
2025-01-02 14:57:27.961 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeletePort]}" onto public java.lang.String com.bd.controller.HttpController.DeletePort(java.util.List<java.lang.Integer>)
2025-01-02 14:57:27.961 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetInPortShipCount]}" onto public java.lang.String com.bd.controller.HttpController.GetShipCount()
2025-01-02 14:57:27.961 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPortShipCount],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipCount> com.bd.controller.HttpController.GetPortShipCount()
2025-01-02 14:57:27.962 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOnePortInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortInfo> com.bd.controller.HttpController.GetOnePortInfo(int)
2025-01-02 14:57:27.962 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/Login]}" onto public com.bd.entity.UserInfo com.bd.controller.HttpController.Login(com.bd.entity.UserInfo,javax.servlet.http.HttpServletRequest)
2025-01-02 14:57:27.962 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllPortInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortInfo> com.bd.controller.HttpController.GetAllPortInfo()
2025-01-02 14:57:27.962 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/resetErrorTimes]}" onto public void com.bd.controller.HttpController.resetErrorTimes(java.lang.String)
2025-01-02 14:57:27.962 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateUser]}" onto public java.lang.String com.bd.controller.HttpController.UpdateUser(int,com.bd.entity.UserInfo)
2025-01-02 14:57:27.962 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteUser]}" onto public java.lang.String com.bd.controller.HttpController.DeleteUser(java.util.List<java.lang.Integer>)
2025-01-02 14:57:27.962 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPortInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetPortInfo(int)
2025-01-02 14:57:27.962 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/QueryShip],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipSerch> com.bd.controller.HttpController.GetShipInfoByNameOrTerminalNumber(java.lang.String)
2025-01-02 14:57:27.963 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetInPortShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetInPortShipInfo(int)
2025-01-02 14:57:27.963 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetWarningToOtherAccountIsRead]}" onto public void com.bd.controller.HttpController.SetWarningToOtherAccountIsRead(int)
2025-01-02 14:57:27.963 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOnLineShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetOnLineShipInfo()
2025-01-02 14:57:27.963 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWSIn168169ShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetWSIn168169ShipInfo()
2025-01-02 14:57:27.963 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetlawCaseByShipId]}" onto public java.util.List<com.bd.entity.LawCase> com.bd.controller.HttpController.GetlawCaseByShipId(int)
2025-01-02 14:57:27.964 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetFishAreaShipInfo]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetFishAreaShipInfo(int)
2025-01-02 14:57:27.964 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateUserSetting]}" onto public void com.bd.controller.HttpController.UpdateUserSetting(java.util.List<com.bd.entity.UserSettingInfo>)
2025-01-02 14:57:27.964 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutPortShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetOutPortShipInfo()
2025-01-02 14:57:27.964 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCrewCertificateStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetCrewCertificateStatistics(com.bd.entity.dto.CrewCertificationQueryDto)
2025-01-02 14:57:27.965 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWarningToOtherAccount]}" onto public java.util.List<com.bd.entity.PushInformation> com.bd.controller.HttpController.GetWarningToOtherAccount(int,int)
2025-01-02 14:57:27.965 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipInfoByPort],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetShipInfoByPort(int)
2025-01-02 14:57:27.965 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteUserSetting],methods=[GET]}" onto public void com.bd.controller.HttpController.DeleteUserSetting(int,int)
2025-01-02 14:57:27.965 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWSInShangHaiShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetWSInShangHaiShipInfo()
2025-01-02 14:57:27.966 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWSInPortShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetWSInPortShipInfo(int)
2025-01-02 14:57:27.966 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SendWarningToOtherAccount]}" onto public void com.bd.controller.HttpController.SendWarningToOtherAccount(int,int,int)
2025-01-02 14:57:27.966 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWSInJinBuShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetWSInJinBuShipInfo()
2025-01-02 14:57:27.966 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetLawRecordInfoById]}" onto public com.bd.entity.LawRecordInfo com.bd.controller.HttpController.GetLawRecordInfoById(int,int)
2025-01-02 14:57:27.966 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipWorkInfo]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShipWorkInfo(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 14:57:27.967 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllBdMsgByUserId]}" onto public java.util.List<com.bd.entity.BdMsg> com.bd.controller.HttpController.GetAllBdMsgByUserId(int,int)
2025-01-02 14:57:27.967 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetUserOperationInfo]}" onto public java.lang.String com.bd.controller.HttpController.SetUserOperationInfo(com.bd.entity.UserOperation,javax.servlet.http.HttpServletRequest)
2025-01-02 14:57:27.967 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/deleteMarkInfoById]}" onto public java.lang.String com.bd.controller.HttpController.deleteMarkInfoById(int,int)
2025-01-02 14:57:27.968 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipWorkInfo_Export]}" onto public java.util.List<com.bd.entity.Ship_WorkInfo> com.bd.controller.HttpController.GetAllShipWorkInfo_Export(java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 14:57:27.968 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutsideInPortShip_Export]}" onto public java.lang.String com.bd.controller.HttpController.GetOutsideInPortShip()
2025-01-02 14:57:27.968 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteSpecialShip]}" onto public java.lang.String com.bd.controller.HttpController.DeleteSpecialShip(java.util.List<com.bd.entity.SpecialShipType>)
2025-01-02 14:57:27.968 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetJianCeShipInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetJianCeShipInfo(java.lang.String,int)
2025-01-02 14:57:27.968 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllFishShip_Export]}" onto public java.lang.String com.bd.controller.HttpController.GetAllFishShip_Export(java.lang.String,java.lang.String,java.lang.String,int,int,int,int,int,int)
2025-01-02 14:57:27.969 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertLawRecordInfo]}" onto public int com.bd.controller.HttpController.InsertLawRecordInfo(com.bd.entity.LawRecordInfo)
2025-01-02 14:57:27.969 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteAlarmRecordInfo]}" onto public java.lang.String com.bd.controller.HttpController.DeleteAlarmRecordInfo(java.util.List<java.lang.Integer>)
2025-01-02 14:57:27.969 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCurrentInPortShips_Export]}" onto public java.lang.String com.bd.controller.HttpController.GetCurrentInPortShips()
2025-01-02 14:57:27.969 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllVoyageInfo_Export]}" onto public java.util.List<com.bd.entity.Ship_Voyage> com.bd.controller.HttpController.GetAllVoyageInfo_Export(java.lang.String)
2025-01-02 14:57:27.970 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAlarmDetialById]}" onto public com.bd.entity.BusinessManagement.AlarmRecord com.bd.controller.HttpController.GetAlarmDetialById(int,int)
2025-01-02 14:57:27.970 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetLawRecordTodayWarning]}" onto public java.util.List<com.bd.entity.LawRecordInfo> com.bd.controller.HttpController.GetLawRecordTodayWarning(int)
2025-01-02 14:57:27.970 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/api/specialShip]}" onto public java.util.List<com.bd.entity.other.SpecialShip> com.bd.controller.HttpController.GetSpecialShipToZhongtai()
2025-01-02 14:57:27.970 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShutDownInfo]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShutDownInfo(java.lang.String,int,int)
2025-01-02 14:57:27.970 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllInPortShip_Export]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_all> com.bd.controller.HttpController.GetAllInPortShip_Export(java.lang.String)
2025-01-02 14:57:27.971 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOnlineShipCount]}" onto public java.lang.String com.bd.controller.HttpController.GetOnlineShipCount()
2025-01-02 14:57:27.971 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShutDownInfo_Export]}" onto public java.util.List<com.bd.entity.BusinessManagement.AlarmRecord> com.bd.controller.HttpController.GetAllShutDownInfo_Export(java.lang.String,int)
2025-01-02 14:57:27.971 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAlarmRecordInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAlarmRecordInfo(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 14:57:27.971 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWhiteAndBlackShip]}" onto public java.lang.String com.bd.controller.HttpController.GetWhiteAndBlackShip(java.lang.String,java.lang.String,java.lang.String,int,int,int)
2025-01-02 14:57:27.972 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetWhiteOrBlackList]}" onto public java.lang.String com.bd.controller.HttpController.SetWhiteOrBlackList(com.bd.entity.BlackAndWhiteList)
2025-01-02 14:57:27.972 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetJianCeShipInfo_Export],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetJianCeShipInfo_Export(java.lang.String)
2025-01-02 14:57:27.972 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertBdMsgExample]}" onto public void com.bd.controller.HttpController.InsertBdMsgExample(java.lang.String,int)
2025-01-02 14:57:27.972 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTyphoonInfoByYear],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTyphoonInfoByYear(int)
2025-01-02 14:57:27.972 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertShipForTerm]}" onto public void com.bd.controller.HttpController.InsertShipForTerm(int,int)
2025-01-02 14:57:27.973 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutLineShipInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetOutLineShipInfo(java.lang.String)
2025-01-02 14:57:27.973 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTodayTyphoonInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTodayTyphoonInfo(java.lang.String,java.lang.String)
2025-01-02 14:57:27.973 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateWhiteOrBlackList]}" onto public java.lang.String com.bd.controller.HttpController.UpdateWhiteOrBlackList(com.bd.entity.BlackAndWhiteList)
2025-01-02 14:57:27.973 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHistoryWarningInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.BusinessManagement.AlarmRecord> com.bd.controller.HttpController.GetHistoryWarningInfo(int,java.lang.String,java.lang.String,int)
2025-01-02 14:57:27.973 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCameraInfoBySearch],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetCameraInfoBySearch(java.lang.String)
2025-01-02 14:57:27.974 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteShipFromTerm]}" onto public void com.bd.controller.HttpController.DeleteShipFromTerm(int)
2025-01-02 14:57:27.974 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWhiteAndBlackListInfoById],methods=[GET]}" onto public com.bd.entity.BlackAndWhiteList com.bd.controller.HttpController.GetWhiteAndBlackListInfoById(int)
2025-01-02 14:57:27.974 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetJianCeShipInfoByShipId],methods=[GET]}" onto public com.bd.entity.JianCeShipInfo com.bd.controller.HttpController.GetJianCeShipInfoByShipId(int)
2025-01-02 14:57:27.974 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTyphoonInfoById],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTyphoonInfoById(int)
2025-01-02 14:57:27.975 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWhiteAndBlackListInfoByType],methods=[GET]}" onto public java.util.List<com.bd.entity.BlackAndWhiteList> com.bd.controller.HttpController.GetWhiteAndBlackListInfoByType(int)
2025-01-02 14:57:27.975 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateBdMsgExample]}" onto public void com.bd.controller.HttpController.DeleteBdMsgExample(int,int,java.lang.String)
2025-01-02 14:57:27.975 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteBdMsgExample]}" onto public void com.bd.controller.HttpController.DeleteBdMsgExample(int)
2025-01-02 14:57:27.976 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllWhiteAndBlackShip]}" onto public java.lang.String com.bd.controller.HttpController.GetAllWhiteAndBlackShip(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 14:57:27.976 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteWhiteOrBlackList]}" onto public java.lang.String com.bd.controller.HttpController.DeleteWhiteOrBlackList(java.util.List<java.lang.Integer>)
2025-01-02 14:57:27.977 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetJianCeShipInfoById],methods=[GET]}" onto public com.bd.entity.JianCeShipInfo com.bd.controller.HttpController.GetJianCeShipInfoById(int)
2025-01-02 14:57:27.977 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllBdMsg]}" onto public java.util.List<com.bd.entity.BdMsg> com.bd.controller.HttpController.GetAllBdMsg(java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-01-02 14:57:27.977 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetSpecialShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.SetSpecialShip(int,int)
2025-01-02 14:57:27.977 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/EditAreaInfo]}" onto public java.lang.String com.bd.controller.HttpController.EditAreaInfo(com.bd.entity.AreaInfo)
2025-01-02 14:57:27.977 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertUser]}" onto public java.lang.String com.bd.controller.HttpController.InsertUser(com.bd.entity.UserInfo)
2025-01-02 14:57:27.978 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeletedAreaInfo]}" onto public java.lang.String com.bd.controller.HttpController.DeletedAreaInfo(java.util.List<java.lang.Integer>)
2025-01-02 14:57:27.978 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllUser]}" onto public java.lang.String com.bd.controller.HttpController.GetAllUser(java.lang.String,int,int)
2025-01-02 14:57:27.978 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetAreaInfo]}" onto public java.lang.String com.bd.controller.HttpController.SetAreaInfo(com.bd.entity.AreaInfo)
2025-01-02 14:57:27.978 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetBdMsg]}" onto public java.lang.String com.bd.controller.HttpController.GetBdMsg(java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 14:57:27.978 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertBdMsg]}" onto public int com.bd.controller.HttpController.InsertBdMsg(com.bd.entity.BdMsg)
2025-01-02 14:57:27.979 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteBdMsg]}" onto public java.lang.String com.bd.controller.HttpController.DeleteBdMsg(java.util.List<java.lang.Integer>)
2025-01-02 14:57:27.979 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTyphoonInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTyphoonInfo()
2025-01-02 14:57:27.979 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetShipWithArea],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.SetShipWithArea(int,int)
2025-01-02 14:57:27.979 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAreaInfoById],methods=[GET]}" onto public com.bd.entity.AreaInfo com.bd.controller.HttpController.GetAreaInfoById(int)
2025-01-02 14:57:27.979 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHlsImg],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetHlsImg(int,int)
2025-01-02 14:57:27.980 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/LoginFromIPMS]}" onto public void com.bd.controller.HttpController.LoginFromIPMS(java.lang.String,javax.servlet.http.HttpServletResponse) throws java.lang.Exception
2025-01-02 14:57:27.980 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DLogin]}" onto public void com.bd.controller.HttpController.LoginFromIPMS(javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-01-02 14:57:27.980 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/LoginFromIPMS_XC]}" onto public void com.bd.controller.HttpController.LoginFromIPMS_XC(java.lang.String,javax.servlet.http.HttpServletResponse) throws java.lang.Exception
2025-01-02 14:57:27.980 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DLogin_XC]}" onto public void com.bd.controller.HttpController.LoginFromIPMS_XC(javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-01-02 14:57:27.980 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetUserInfoById]}" onto public com.bd.entity.UserInfo com.bd.controller.HttpController.GetUserInfoById(int)
2025-01-02 14:57:27.981 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipTerm]}" onto public java.util.List<com.bd.entity.Ship_Term> com.bd.controller.HttpController.GetShipTerm(int)
2025-01-02 14:57:27.981 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipFromTerm]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_all> com.bd.controller.HttpController.GetShipFromTerm(int)
2025-01-02 14:57:27.981 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllMarkInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllMarkInfo(int)
2025-01-02 14:57:27.981 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetMarkInfoById],methods=[GET]}" onto public com.bd.entity.MarkInfo com.bd.controller.HttpController.GetMarkInfoById(int)
2025-01-02 14:57:27.981 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/AddShipTerm]}" onto public void com.bd.controller.HttpController.AddShipTerm(java.lang.String,int)
2025-01-02 14:57:27.982 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetUserOperation],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetUserOperation(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 14:57:27.982 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/EditMarkInfo]}" onto public java.lang.String com.bd.controller.HttpController.EditMarkInfo(com.bd.entity.MarkInfo)
2025-01-02 14:57:27.982 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetMapToken]}" onto public java.lang.String com.bd.controller.HttpController.GetMapToken()
2025-01-02 14:57:27.982 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWeather]}" onto public java.lang.String com.bd.controller.HttpController.GetWeather() throws java.lang.Exception
2025-01-02 14:57:27.982 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCameraInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.CameraInfo> com.bd.controller.HttpController.GetCameraInfo()
2025-01-02 14:57:27.983 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/AddJianCeShip]}" onto public java.lang.String com.bd.controller.HttpController.AddJianCeShip(com.bd.entity.JianCeShipInfo)
2025-01-02 14:57:27.983 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetBdMsgExample]}" onto public java.util.List<com.bd.entity.BDMsgExample> com.bd.controller.HttpController.GetBdMsgExample(int)
2025-01-02 14:57:27.983 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHlsById],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetHlsById(int)
2025-01-02 14:57:27.983 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/CheckShipInTerm]}" onto public void com.bd.controller.HttpController.CheckShipInTerm(int,int)
2025-01-02 14:57:27.983 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllBlackList],methods=[GET]}" onto public java.util.List<com.bd.entity.BlackAndWhiteList> com.bd.controller.HttpController.GetAllBlackList()
2025-01-02 14:57:27.984 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/CheckShipBout]}" onto public java.util.List<java.lang.Integer> com.bd.controller.HttpController.CheckShipBout(java.util.List<java.lang.Integer>)
2025-01-02 14:57:27.984 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetUserCount],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetUserCount()
2025-01-02 14:57:27.984 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHlsUrl]}" onto public java.lang.String com.bd.controller.HttpController.GetHlsUrl(int,java.lang.String)
2025-01-02 14:57:27.984 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHlsUrlByIndex]}" onto public java.lang.String com.bd.controller.HttpController.GetHlsUrlByIndex(java.lang.String,java.lang.String)
2025-01-02 14:57:27.984 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteJianCeShip]}" onto public java.lang.String com.bd.controller.HttpController.DeleteJianCeShip(java.util.List<java.lang.Integer>)
2025-01-02 14:57:27.985 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllWhiteList],methods=[GET]}" onto public java.util.List<com.bd.entity.BlackAndWhiteList> com.bd.controller.HttpController.GetAllWhiteList()
2025-01-02 14:57:27.985 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateCrewInfo],methods=[GET]}" onto private void com.bd.controller.HttpController.UpdateCrewInfo(int) throws java.lang.Exception
2025-01-02 14:57:27.985 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteShipTerm]}" onto public void com.bd.controller.HttpController.DeleteShipTerm(int)
2025-01-02 14:57:27.986 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertMarkInfo]}" onto public java.lang.String com.bd.controller.HttpController.InsertMarkInfo(com.bd.entity.MarkInfo)
2025-01-02 14:57:27.986 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetNearestHls],methods=[GET]}" onto public java.util.List<java.lang.String> com.bd.controller.HttpController.GetNearestHls(java.lang.String)
2025-01-02 14:57:27.986 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipSetting],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipSetting(int)
2025-01-02 14:57:27.987 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetAllShipInfo()
2025-01-02 14:57:27.987 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/isCheckAccount],methods=[GET]}" onto public com.bd.entity.UserInfo com.bd.controller.HttpController.isCheckAccount(java.lang.String)
2025-01-02 14:57:27.987 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetManagerCount]}" onto public int com.bd.controller.HttpController.GetManagerCount(int)
2025-01-02 14:57:27.987 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetShipSetting],methods=[GET]}" onto public void com.bd.controller.HttpController.SetShipSetting(int,java.lang.String)
2025-01-02 14:57:27.987 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/excel/upload]}" onto public void com.bd.controller.HttpController.excelUpload(java.util.List<com.bd.entity.other.ExcelEntity>)
2025-01-02 14:57:27.988 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipType],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShipType(int,int)
2025-01-02 14:57:27.988 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetUserSetting]}" onto public void com.bd.controller.HttpController.SetUserSetting(java.util.List<com.bd.entity.UserSettingInfo>)
2025-01-02 14:57:27.988 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetUserSetting]}" onto public java.util.List<com.bd.entity.UserSettingInfo> com.bd.controller.HttpController.GetUserSetting(int)
2025-01-02 14:57:27.988 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipIdByName],methods=[GET]}" onto public int com.bd.controller.HttpController.GetShipIdByName(java.lang.String)
2025-01-02 14:57:27.989 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/series/add],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.addseries()
2025-01-02 14:57:27.989 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipImgUrl],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipImgUrl(int)
2025-01-02 14:57:27.989 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetIp]}" onto public java.lang.String com.bd.controller.HttpController.GetIp(javax.servlet.http.HttpServletRequest)
2025-01-02 14:57:27.994 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-01-02 14:57:27.994 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-01-02 14:57:28.053 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 14:57:28.053 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 14:57:28.053 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/map/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 14:57:28.053 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/wxMap/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 14:57:28.053 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/YimaMap1/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 14:57:28.053 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/YimaMap2/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 14:57:28.053 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/shipImg/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 14:57:28.411 INFO  [restartedMain]o.s.boot.devtools.autoconfigure.OptionalLiveReloadServer.startServer:57 -LiveReload server is running on port 35729
2025-01-02 14:57:28.445 INFO  [restartedMain]o.s.jmx.export.annotation.AnnotationMBeanExporter.afterSingletonsInstantiated:433 -Registering beans for JMX exposure on startup
2025-01-02 14:57:28.447 INFO  [restartedMain]o.s.jmx.export.annotation.AnnotationMBeanExporter.autodetect:895 -Bean with name 'dataSource' has been autodetected for JMX exposure
2025-01-02 14:57:28.453 INFO  [restartedMain]o.s.jmx.export.annotation.AnnotationMBeanExporter.registerBeanInstance:668 -Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-01-02 14:57:28.482 INFO  [restartedMain]o.s.s.annotation.ScheduledAnnotationBeanPostProcessor.finishRegistration:275 -No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-01-02 14:57:28.493 INFO  [restartedMain]org.apache.coyote.http11.Http11NioProtocol.log:180 -Starting ProtocolHandler ["http-nio-7101"]
2025-01-02 14:57:28.508 INFO  [restartedMain]org.apache.tomcat.util.net.NioSelectorPool.log:180 -Using a shared selector for servlet write/read
2025-01-02 14:57:28.529 INFO  [restartedMain]o.s.boot.web.embedded.tomcat.TomcatWebServer.start:206 -Tomcat started on port(s): 7101 (http) with context path ''
2025-01-02 14:57:28.534 INFO  [restartedMain]com.bd.Application.logStarted:59 -Started Application in 8.4 seconds (JVM running for 10.562)
2025-01-02 14:58:41.134 INFO  [pool-2-thread-1]o.s.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions:316 -Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2025-01-02 14:58:41.158 INFO  [pool-2-thread-1]org.springframework.jdbc.support.SQLErrorCodesFactory.<init>:128 -SQLErrorCodes loaded: [DB2, Derby, H2, HDB, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase]
2025-01-02 15:00:25.039 INFO  [restartedMain]com.bd.Application.logStarting:50 -Starting Application on THUNDEROBOT with PID 15632 (G:\yimaWorking\bdxt_shipMonitor_web\Server\springbootmybatisdmdb\target\classes started by 1p in G:\yimaWorking\bdxt_shipMonitor_web\Server\springbootmybatisdmdb)
2025-01-02 15:00:25.040 INFO  [restartedMain]com.bd.Application.logStartupProfileInfo:663 -The following profiles are active: dev
2025-01-02 15:00:25.113 INFO  [restartedMain]o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext.prepareRefresh:590 -Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@66add6d1: startup date [Thu Jan 02 15:00:25 CST 2025]; root of context hierarchy
2025-01-02 15:00:26.452 INFO  [restartedMain]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:328 -Bean 'asyncTaskConfig' of type [com.bd.config.AsyncTaskConfig$$EnhancerBySpringCGLIB$$4fc57592] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-02 15:00:26.461 INFO  [restartedMain]o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:171 -Initializing ExecutorService 
2025-01-02 15:00:26.467 INFO  [restartedMain]o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:171 -Initializing ExecutorService  'getAsyncExecutor'
2025-01-02 15:00:26.467 INFO  [restartedMain]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:328 -Bean 'getAsyncExecutor' of type [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-02 15:00:26.885 INFO  [restartedMain]o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:91 -Tomcat initialized with port(s): 7101 (http)
2025-01-02 15:00:26.895 INFO  [restartedMain]org.apache.coyote.http11.Http11NioProtocol.log:180 -Initializing ProtocolHandler ["http-nio-7101"]
2025-01-02 15:00:26.901 INFO  [restartedMain]org.apache.catalina.core.StandardService.log:180 -Starting service [Tomcat]
2025-01-02 15:00:26.901 INFO  [restartedMain]org.apache.catalina.core.StandardEngine.log:180 -Starting Servlet Engine: Apache Tomcat/8.5.31
2025-01-02 15:00:26.904 INFO  [localhost-startStop-1]org.apache.catalina.core.AprLifecycleListener.log:180 -The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Program Files\Java\jdk1.8.0_91\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\PlasticSCM5\server;C:\Program Files\PlasticSCM5\client;F:\vmware\bin\;C:\ProgramData\Oracle\Java\javapath;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\TortiiseSVN\bin;G:\sqlite\sqlite-tools-win32-x86-3320300;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Java\jdk-16.0.1\bin;C:\Program Files\Java\jdk-16.0.1\jre\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;G:\Node.js\;G:\Node.js\node_global;C:\Program Files\Git\cmd;C:\Program Files\Microsoft SQL Server\120\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\100\Tools\Binn\;C:\Program Files\Microsoft SQL Server\100\Tools\Binn\;C:\Program Files\Microsoft SQL Server\100\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\90\Tools\binn\;G:\Windows Kits\10\Windows Performance Toolkit\;F:\apache-maven-3.8.5\bin;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;G:\CLionSpace\untitled2\cmake-build-debug\;C:\Program Files\dotnet\;G:\skia_win\third_party\externals\emsdk;G:\skia_win\third_party\externals\emsdk\upstream\emscripten;G:\skia\third_party\externals\emsdk;G:\skia\third_party\externals\emsdk\upstream\emscripten;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\;C:\Users\<USER>\depot_tools;D:\VC++\Tools\WinNT;D:\VC++\MSDev98\Bin;D:\VC++\Tools;D:\Microsoft Visual Studio\VC98\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps2;G:\IntelliJ IDEA 2020.3\bin;;G:\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;G:\PyCharm 2020.2.3\bin;;C:\Users\<USER>\ninja;C:\Python27;G:\HUAWEI DevEco Studio 4.0\bin;;C:\Users\<USER>\.dotnet\tools;.]
2025-01-02 15:00:27.014 INFO  [localhost-startStop-1]o.a.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log:180 -Initializing Spring embedded WebApplicationContext
2025-01-02 15:00:27.014 INFO  [localhost-startStop-1]org.springframework.web.context.ContextLoader.prepareWebApplicationContext:285 -Root WebApplicationContext: initialization completed in 1901 ms
2025-01-02 15:00:27.142 INFO  [localhost-startStop-1]o.s.boot.web.servlet.ServletRegistrationBean.addRegistration:186 -Servlet dispatcherServlet mapped to [/]
2025-01-02 15:00:27.147 INFO  [localhost-startStop-1]o.s.boot.web.servlet.FilterRegistrationBean.configure:245 -Mapping filter: 'characterEncodingFilter' to: [/*]
2025-01-02 15:00:27.148 INFO  [localhost-startStop-1]o.s.boot.web.servlet.FilterRegistrationBean.configure:245 -Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-01-02 15:00:27.148 INFO  [localhost-startStop-1]o.s.boot.web.servlet.FilterRegistrationBean.configure:245 -Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-01-02 15:00:27.148 INFO  [localhost-startStop-1]o.s.boot.web.servlet.FilterRegistrationBean.configure:245 -Mapping filter: 'requestContextFilter' to: [/*]
2025-01-02 15:00:27.891 INFO  [restartedMain]com.zaxxer.hikari.HikariDataSource.getConnection:110 -HikariPool-1 - Starting...
2025-01-02 15:00:28.099 INFO  [restartedMain]com.zaxxer.hikari.pool.PoolBase.getAndSetNetworkTimeout:516 -HikariPool-1 - Driver does not support get/set network timeout for connections. (不支持的接口或功能)
2025-01-02 15:00:28.148 INFO  [restartedMain]com.zaxxer.hikari.HikariDataSource.getConnection:123 -HikariPool-1 - Start completed.
2025-01-02 15:00:28.330 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:00:28.552 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerAdapter.initControllerAdviceCache:574 -Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@66add6d1: startup date [Thu Jan 02 15:00:25 CST 2025]; root of context hierarchy
2025-01-02 15:00:28.636 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/token],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.token() throws java.io.IOException
2025-01-02 15:00:28.637 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/test],methods=[GET]}" onto public int com.bd.controller.HttpController.pp()
2025-01-02 15:00:28.639 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/QueryShip],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipSerch> com.bd.controller.HttpController.GetShipInfoByNameOrTerminalNumber(java.lang.String)
2025-01-02 15:00:28.639 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/Postppp],methods=[POST]}" onto public java.lang.String com.bd.controller.HttpController.post(java.lang.String,java.lang.String)
2025-01-02 15:00:28.639 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPlayShipInfoById],methods=[GET]}" onto public com.bd.entity.PlayShipInfo com.bd.controller.HttpController.GetPlayShipInfoById(int,int,int)
2025-01-02 15:00:28.640 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateOutLineReason],methods=[POST]}" onto public java.lang.String com.bd.controller.HttpController.updateOutlineReason(com.bd.entity.ShipEncryption)
2025-01-02 15:00:28.641 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipCardById],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_card> com.bd.controller.HttpController.GetOneShipCardById(int)
2025-01-02 15:00:28.641 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetBDShipPosition],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipDynamicInfo> com.bd.controller.HttpController.GetBDShipPosition(int)
2025-01-02 15:00:28.642 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipCardTexuById],methods=[GET]}" onto public com.bd.entity.FisheryPermitInfo com.bd.controller.HttpController.GetOneShipCardTexuById(int)
2025-01-02 15:00:28.642 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipDynamicInfoById]}" onto public com.bd.entity.ShipDynamicInfo com.bd.controller.HttpController.GetOneShipDynamicInfoById(int)
2025-01-02 15:00:28.642 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/getShipInOutReport],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.getShipInOutReport(java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:00:28.643 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipInfoByShipName]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_all> com.bd.controller.HttpController.GetOneShipInfoByShipName(java.lang.String)
2025-01-02 15:00:28.643 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipInfoById],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_all> com.bd.controller.HttpController.GetOneShipInfoById(int)
2025-01-02 15:00:28.643 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutInPortRecord],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetOutInPortRecord()
2025-01-02 15:00:28.643 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllBDShipPosition],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllBDShipPosition(int)
2025-01-02 15:00:28.644 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutInPortRecordByShipId],methods=[GET]}" onto public java.util.List<com.bd.entity.OutInPortRecord> com.bd.controller.HttpController.GetOutInPortRecordByShipId(java.lang.String)
2025-01-02 15:00:28.645 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipDistribute]}" onto public com.bd.entity.Ship_DistributeCount com.bd.controller.HttpController.GetShipDistribute(int)
2025-01-02 15:00:28.645 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipHistoryTrackById],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipTrack> com.bd.controller.HttpController.GetShipHistoryTrackById(int,long,long,int,int,int)
2025-01-02 15:00:28.646 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPlayAreaHistoryInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipTrack> com.bd.controller.HttpController.GetPlayAreaHistoryInfo(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:00:28.646 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.getShipInOutByShipName()
2025-01-02 15:00:28.646 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPeopleByShipId],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetPeopleByShipId(int)
2025-01-02 15:00:28.647 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAreaInfoByUserId],methods=[GET]}" onto public java.util.List<com.bd.entity.AreaInfo> com.bd.controller.HttpController.GetAreaInfoByUserId(java.lang.String)
2025-01-02 15:00:28.647 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTodayWarningStatistics],methods=[GET]}" onto public com.bd.entity.WarningStatistics com.bd.controller.HttpController.GetTodayWarningStatistics(int,int)
2025-01-02 15:00:28.647 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTodayWarningInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.WarningInfo> com.bd.controller.HttpController.GetTodayWarningInfo(int,java.lang.String,int)
2025-01-02 15:00:28.648 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetImportanceShip],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipDynamicInfo> com.bd.controller.HttpController.GetImportanceShip(int)
2025-01-02 15:00:28.648 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAreaInfoByUserIdPageNum],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAreaInfoByUserIdPageNum(int,int)
2025-01-02 15:00:28.648 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipStatistics()
2025-01-02 15:00:28.648 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWarningStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetWarningStatistics(int,int)
2025-01-02 15:00:28.648 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipOnlineStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipOnlineStatistics(java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:00:28.648 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetYuzhengShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.Ship_Yuzheng> com.bd.controller.HttpController.GetYuzhengShipInfo()
2025-01-02 15:00:28.648 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllPeople_Export]}" onto public java.lang.String com.bd.controller.HttpController.GetAllPeople_Export(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.bd.entity.dto.PeopleQueryDto)
2025-01-02 15:00:28.648 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetApplinameByShipId],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetApplinameByShipId(int)
2025-01-02 15:00:28.648 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShip_inOrOutPortInfoDetail],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShip_inOrOutPortInfoDetail(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-01-02 15:00:28.649 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPeopleByIdcards],methods=[POST]}" onto public java.lang.String com.bd.controller.HttpController.GetPeopleByIdcards(java.util.Map<java.lang.String, java.util.List<java.lang.String>>) throws java.lang.Exception
2025-01-02 15:00:28.649 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPortNodeByShipId]}" onto public java.lang.String com.bd.controller.HttpController.GetPortNodeByShipId(int,int)
2025-01-02 15:00:28.649 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipOnlineStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShipOnlineStatistics(java.lang.String,java.lang.String,java.lang.String)
2025-01-02 15:00:28.649 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetImportanceShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.SetImportanceShip(int,int)
2025-01-02 15:00:28.650 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShip_inOrOutPortInfoDetail],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShip_inOrOutPortInfoDetail(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:00:28.650 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetFuxiuWhiteShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.SetFuxiuWhiteShip(int)
2025-01-02 15:00:28.650 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetZhifaPeopleInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.LawPeople> com.bd.controller.HttpController.GetZhifaPeopleInfo()
2025-01-02 15:00:28.651 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetBDShipPosition_ronghe],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipDynamicInfo> com.bd.controller.HttpController.GetBDShipPosition_ronghe(int)
2025-01-02 15:00:28.651 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteImportanceShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.DeleteImportanceShip(int,int)
2025-01-02 15:00:28.651 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTodayWarningAllStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTodayWarningAllStatistics(int,java.lang.String,int)
2025-01-02 15:00:28.651 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAlarmShipIdByAlarmId],methods=[GET]}" onto public int com.bd.controller.HttpController.GetAlarmShipIdByAlarmId()
2025-01-02 15:00:28.651 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCrewPeopleByShipId]}" onto public java.lang.String com.bd.controller.HttpController.GetCrewPeopleByShipId(int)
2025-01-02 15:00:28.652 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPortStatisticsInOutShipCount],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetPortStatisticsInOutShipCount()
2025-01-02 15:00:28.652 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAlarmCountByTime],methods=[GET]}" onto public com.bd.entity.TongjiModel com.bd.controller.HttpController.GetAlarmCountByTime(int,int,int)
2025-01-02 15:00:28.652 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteFuxiuWhiteShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.DeleteFuxiuWhiteShip(int)
2025-01-02 15:00:28.652 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeletedLawRecordInfo]}" onto public java.lang.String com.bd.controller.HttpController.DeletedLawRecordInfo(java.util.List<java.lang.Integer>)
2025-01-02 15:00:28.653 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipWorkInfo_Export]}" onto public java.util.List<com.bd.entity.Ship_WorkInfo> com.bd.controller.HttpController.GetAllShipWorkInfo_Export(java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:00:28.653 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutsideInPortShip_Export]}" onto public java.lang.String com.bd.controller.HttpController.GetOutsideInPortShip()
2025-01-02 15:00:28.653 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllLawRecordInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllLawRecordInfo(java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:00:28.653 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAlarmRecordInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAlarmRecordInfo(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 15:00:28.654 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShutDownInfo]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShutDownInfo(java.lang.String,int,int)
2025-01-02 15:00:28.654 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetLawRecordTodayWarning]}" onto public java.util.List<com.bd.entity.LawRecordInfo> com.bd.controller.HttpController.GetLawRecordTodayWarning(int)
2025-01-02 15:00:28.654 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOnlineShipCount]}" onto public java.lang.String com.bd.controller.HttpController.GetOnlineShipCount()
2025-01-02 15:00:28.654 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertLawRecordInfo]}" onto public int com.bd.controller.HttpController.InsertLawRecordInfo(com.bd.entity.LawRecordInfo)
2025-01-02 15:00:28.655 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShutDownInfo_Export]}" onto public java.util.List<com.bd.entity.BusinessManagement.AlarmRecord> com.bd.controller.HttpController.GetAllShutDownInfo_Export(java.lang.String,int)
2025-01-02 15:00:28.655 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAlarmDetialById]}" onto public com.bd.entity.BusinessManagement.AlarmRecord com.bd.controller.HttpController.GetAlarmDetialById(int,int)
2025-01-02 15:00:28.655 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllInPortShip_Export]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_all> com.bd.controller.HttpController.GetAllInPortShip_Export(java.lang.String)
2025-01-02 15:00:28.655 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCurrentInPortShips_Export]}" onto public java.lang.String com.bd.controller.HttpController.GetCurrentInPortShips()
2025-01-02 15:00:28.656 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllVoyageInfo_Export]}" onto public java.util.List<com.bd.entity.Ship_Voyage> com.bd.controller.HttpController.GetAllVoyageInfo_Export(java.lang.String)
2025-01-02 15:00:28.656 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllBdMsgByUserId]}" onto public java.util.List<com.bd.entity.BdMsg> com.bd.controller.HttpController.GetAllBdMsgByUserId(int,int)
2025-01-02 15:00:28.656 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetLawRecordInfoById]}" onto public com.bd.entity.LawRecordInfo com.bd.controller.HttpController.GetLawRecordInfoById(int,int)
2025-01-02 15:00:28.656 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteSpecialShip]}" onto public java.lang.String com.bd.controller.HttpController.DeleteSpecialShip(java.util.List<com.bd.entity.SpecialShipType>)
2025-01-02 15:00:28.657 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/api/specialShip]}" onto public java.util.List<com.bd.entity.other.SpecialShip> com.bd.controller.HttpController.GetSpecialShipToZhongtai()
2025-01-02 15:00:28.657 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllFishShip_Export]}" onto public java.lang.String com.bd.controller.HttpController.GetAllFishShip_Export(java.lang.String,java.lang.String,java.lang.String,int,int,int,int,int,int)
2025-01-02 15:00:28.657 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipWorkInfo]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShipWorkInfo(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 15:00:28.657 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllAlarmRecordInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllAlarmRecordInfo(java.lang.String,java.lang.String,java.lang.String,int,int,int)
2025-01-02 15:00:28.658 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteAlarmRecordInfo]}" onto public java.lang.String com.bd.controller.HttpController.DeleteAlarmRecordInfo(java.util.List<java.lang.Integer>)
2025-01-02 15:00:28.658 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetUserOperationInfo]}" onto public java.lang.String com.bd.controller.HttpController.SetUserOperationInfo(com.bd.entity.UserOperation,javax.servlet.http.HttpServletRequest)
2025-01-02 15:00:28.658 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteBdMsgExample]}" onto public void com.bd.controller.HttpController.DeleteBdMsgExample(int)
2025-01-02 15:00:28.658 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateBdMsgExample]}" onto public void com.bd.controller.HttpController.DeleteBdMsgExample(int,int,java.lang.String)
2025-01-02 15:00:28.659 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetJianCeShipInfoByShipId],methods=[GET]}" onto public com.bd.entity.JianCeShipInfo com.bd.controller.HttpController.GetJianCeShipInfoByShipId(int)
2025-01-02 15:00:28.659 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/deleteMarkInfoById]}" onto public java.lang.String com.bd.controller.HttpController.deleteMarkInfoById(int,int)
2025-01-02 15:00:28.659 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetJianCeShipInfoById],methods=[GET]}" onto public com.bd.entity.JianCeShipInfo com.bd.controller.HttpController.GetJianCeShipInfoById(int)
2025-01-02 15:00:28.659 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWSInJinBuShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetWSInJinBuShipInfo()
2025-01-02 15:00:28.659 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWSIn168169ShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetWSIn168169ShipInfo()
2025-01-02 15:00:28.660 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWhiteAndBlackListInfoByType],methods=[GET]}" onto public java.util.List<com.bd.entity.BlackAndWhiteList> com.bd.controller.HttpController.GetWhiteAndBlackListInfoByType(int)
2025-01-02 15:00:28.660 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWSInPortShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetWSInPortShipInfo(int)
2025-01-02 15:00:28.660 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteShipFromTerm]}" onto public void com.bd.controller.HttpController.DeleteShipFromTerm(int)
2025-01-02 15:00:28.660 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTyphoonInfoByYear],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTyphoonInfoByYear(int)
2025-01-02 15:00:28.660 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTodayTyphoonInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTodayTyphoonInfo(java.lang.String,java.lang.String)
2025-01-02 15:00:28.661 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCameraInfoBySearch],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetCameraInfoBySearch(java.lang.String)
2025-01-02 15:00:28.661 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateWhiteOrBlackList]}" onto public java.lang.String com.bd.controller.HttpController.UpdateWhiteOrBlackList(com.bd.entity.BlackAndWhiteList)
2025-01-02 15:00:28.661 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteWhiteOrBlackList]}" onto public java.lang.String com.bd.controller.HttpController.DeleteWhiteOrBlackList(java.util.List<java.lang.Integer>)
2025-01-02 15:00:28.661 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetJianCeShipInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetJianCeShipInfo(java.lang.String,int)
2025-01-02 15:00:28.662 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertShipForTerm]}" onto public void com.bd.controller.HttpController.InsertShipForTerm(int,int)
2025-01-02 15:00:28.662 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetWhiteOrBlackList]}" onto public java.lang.String com.bd.controller.HttpController.SetWhiteOrBlackList(com.bd.entity.BlackAndWhiteList)
2025-01-02 15:00:28.662 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetInPortShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetInPortShipInfo(int)
2025-01-02 15:00:28.662 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOnLineShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetOnLineShipInfo()
2025-01-02 15:00:28.662 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHistoryWarningInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.BusinessManagement.AlarmRecord> com.bd.controller.HttpController.GetHistoryWarningInfo(int,java.lang.String,java.lang.String,int)
2025-01-02 15:00:28.663 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWhiteAndBlackListInfoById],methods=[GET]}" onto public com.bd.entity.BlackAndWhiteList com.bd.controller.HttpController.GetWhiteAndBlackListInfoById(int)
2025-01-02 15:00:28.663 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllWhiteAndBlackShip]}" onto public java.lang.String com.bd.controller.HttpController.GetAllWhiteAndBlackShip(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 15:00:28.663 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetJianCeShipInfo_Export],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetJianCeShipInfo_Export(java.lang.String)
2025-01-02 15:00:28.663 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWhiteAndBlackShip]}" onto public java.lang.String com.bd.controller.HttpController.GetWhiteAndBlackShip(java.lang.String,java.lang.String,java.lang.String,int,int,int)
2025-01-02 15:00:28.664 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutLineShipInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetOutLineShipInfo(java.lang.String)
2025-01-02 15:00:28.664 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutPortShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetOutPortShipInfo()
2025-01-02 15:00:28.664 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTyphoonInfoById],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTyphoonInfoById(int)
2025-01-02 15:00:28.664 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWSInShangHaiShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetWSInShangHaiShipInfo()
2025-01-02 15:00:28.665 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertBdMsgExample]}" onto public void com.bd.controller.HttpController.InsertBdMsgExample(java.lang.String,int)
2025-01-02 15:00:28.665 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetFishAreaShipInfo]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetFishAreaShipInfo(int)
2025-01-02 15:00:28.665 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateUserSetting]}" onto public void com.bd.controller.HttpController.UpdateUserSetting(java.util.List<com.bd.entity.UserSettingInfo>)
2025-01-02 15:00:28.665 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteUserSetting],methods=[GET]}" onto public void com.bd.controller.HttpController.DeleteUserSetting(int,int)
2025-01-02 15:00:28.666 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipInfoByPort],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetShipInfoByPort(int)
2025-01-02 15:00:28.666 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCrewCertificateStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetCrewCertificateStatistics(com.bd.entity.dto.CrewCertificationQueryDto)
2025-01-02 15:00:28.666 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWarningToOtherAccount]}" onto public java.util.List<com.bd.entity.PushInformation> com.bd.controller.HttpController.GetWarningToOtherAccount(int,int)
2025-01-02 15:00:28.666 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetWarningToOtherAccountIsRead]}" onto public void com.bd.controller.HttpController.SetWarningToOtherAccountIsRead(int)
2025-01-02 15:00:28.667 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SendWarningToOtherAccount]}" onto public void com.bd.controller.HttpController.SendWarningToOtherAccount(int,int,int)
2025-01-02 15:00:28.667 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetlawCaseByShipId]}" onto public java.util.List<com.bd.entity.LawCase> com.bd.controller.HttpController.GetlawCaseByShipId(int)
2025-01-02 15:00:28.667 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/Login]}" onto public com.bd.entity.UserInfo com.bd.controller.HttpController.Login(com.bd.entity.UserInfo,javax.servlet.http.HttpServletRequest)
2025-01-02 15:00:28.667 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/resetErrorTimes]}" onto public void com.bd.controller.HttpController.resetErrorTimes(java.lang.String)
2025-01-02 15:00:28.667 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPortShipCount],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipCount> com.bd.controller.HttpController.GetPortShipCount()
2025-01-02 15:00:28.668 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetSpecialShip],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipDynamicInfo> com.bd.controller.HttpController.GetSpecialShip()
2025-01-02 15:00:28.668 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteUser]}" onto public java.lang.String com.bd.controller.HttpController.DeleteUser(java.util.List<java.lang.Integer>)
2025-01-02 15:00:28.669 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPortInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetPortInfo(int)
2025-01-02 15:00:28.669 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOnePortInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortInfo> com.bd.controller.HttpController.GetOnePortInfo(int)
2025-01-02 15:00:28.669 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllPortInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortInfo> com.bd.controller.HttpController.GetAllPortInfo()
2025-01-02 15:00:28.669 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/EditPort]}" onto public java.lang.String com.bd.controller.HttpController.EditPort(int,java.lang.String,java.lang.String,int,int,java.lang.String,java.util.List<com.bd.entity.Coordinate>)
2025-01-02 15:00:28.669 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateUser]}" onto public java.lang.String com.bd.controller.HttpController.UpdateUser(int,com.bd.entity.UserInfo)
2025-01-02 15:00:28.669 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetInPortShipCount]}" onto public java.lang.String com.bd.controller.HttpController.GetShipCount()
2025-01-02 15:00:28.669 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/AddPort]}" onto public int com.bd.controller.HttpController.AddPort(java.lang.String,java.lang.String,int,int,java.lang.String,java.util.List<com.bd.entity.Coordinate>)
2025-01-02 15:00:28.669 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipManyRegis],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipManyRegis(int)
2025-01-02 15:00:28.670 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeletePort]}" onto public java.lang.String com.bd.controller.HttpController.DeletePort(java.util.List<java.lang.Integer>)
2025-01-02 15:00:28.670 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAreaInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.AreaInfo> com.bd.controller.HttpController.GetAreaInfo()
2025-01-02 15:00:28.670 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllInPortShip]}" onto public java.lang.String com.bd.controller.HttpController.GetAllInPortShip(java.lang.String,int)
2025-01-02 15:00:28.670 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllPeople],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllPeople(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int,com.bd.entity.dto.PeopleQueryDto)
2025-01-02 15:00:28.671 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/api/shipCount]}" onto public java.lang.String com.bd.controller.HttpController.shipCount()
2025-01-02 15:00:28.671 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllAreaInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllAreaInfo(int)
2025-01-02 15:00:28.671 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetLawRecordInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetLawRecordInfo(java.lang.String,java.lang.String,java.lang.String)
2025-01-02 15:00:28.671 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllFishShip]}" onto public java.lang.String com.bd.controller.HttpController.GetAllFishShip(java.lang.String,java.lang.String,java.lang.String,int,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,com.bd.entity.dto.ShipQueryDto)
2025-01-02 15:00:28.671 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetServerState],methods=[GET]}" onto public com.bd.entity.TongjiModel com.bd.controller.HttpController.GetServerState(int)
2025-01-02 15:00:28.672 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/getCheckRecordByShipName],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.getCheckRecord(java.lang.String)
2025-01-02 15:00:28.672 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetFocusShip]}" onto public java.lang.String com.bd.controller.HttpController.GetFocusShip(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 15:00:28.672 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/LoginFromIPMS_XC]}" onto public void com.bd.controller.HttpController.LoginFromIPMS_XC(java.lang.String,javax.servlet.http.HttpServletResponse) throws java.lang.Exception
2025-01-02 15:00:28.672 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DLogin_XC]}" onto public void com.bd.controller.HttpController.LoginFromIPMS_XC(javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-01-02 15:00:28.672 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetUserInfoById]}" onto public com.bd.entity.UserInfo com.bd.controller.HttpController.GetUserInfoById(int)
2025-01-02 15:00:28.673 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetSpecialShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.SetSpecialShip(int,int)
2025-01-02 15:00:28.673 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllBdMsg]}" onto public java.util.List<com.bd.entity.BdMsg> com.bd.controller.HttpController.GetAllBdMsg(java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-01-02 15:00:28.673 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteBdMsg]}" onto public java.lang.String com.bd.controller.HttpController.DeleteBdMsg(java.util.List<java.lang.Integer>)
2025-01-02 15:00:28.673 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetAreaInfo]}" onto public java.lang.String com.bd.controller.HttpController.SetAreaInfo(com.bd.entity.AreaInfo)
2025-01-02 15:00:28.674 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertUser]}" onto public java.lang.String com.bd.controller.HttpController.InsertUser(com.bd.entity.UserInfo)
2025-01-02 15:00:28.674 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertBdMsg]}" onto public int com.bd.controller.HttpController.InsertBdMsg(com.bd.entity.BdMsg)
2025-01-02 15:00:28.674 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeletedAreaInfo]}" onto public java.lang.String com.bd.controller.HttpController.DeletedAreaInfo(java.util.List<java.lang.Integer>)
2025-01-02 15:00:28.674 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllFocusShip]}" onto public java.lang.String com.bd.controller.HttpController.GetAllFocusShip(java.lang.String,java.lang.String,java.lang.String,int,int,int)
2025-01-02 15:00:28.674 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/LoginFromIPMS]}" onto public void com.bd.controller.HttpController.LoginFromIPMS(java.lang.String,javax.servlet.http.HttpServletResponse) throws java.lang.Exception
2025-01-02 15:00:28.675 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DLogin]}" onto public void com.bd.controller.HttpController.LoginFromIPMS(javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-01-02 15:00:28.675 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllVoyageInfo]}" onto public java.lang.String com.bd.controller.HttpController.GetAllVoyageInfo(java.lang.String,int)
2025-01-02 15:00:28.675 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetBdMsg]}" onto public java.lang.String com.bd.controller.HttpController.GetBdMsg(java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:00:28.675 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAreaInfoById],methods=[GET]}" onto public com.bd.entity.AreaInfo com.bd.controller.HttpController.GetAreaInfoById(int)
2025-01-02 15:00:28.675 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/EditAreaInfo]}" onto public java.lang.String com.bd.controller.HttpController.EditAreaInfo(com.bd.entity.AreaInfo)
2025-01-02 15:00:28.676 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllUser]}" onto public java.lang.String com.bd.controller.HttpController.GetAllUser(java.lang.String,int,int)
2025-01-02 15:00:28.676 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipTerm]}" onto public java.util.List<com.bd.entity.Ship_Term> com.bd.controller.HttpController.GetShipTerm(int)
2025-01-02 15:00:28.676 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHlsUrl]}" onto public java.lang.String com.bd.controller.HttpController.GetHlsUrl(int,java.lang.String)
2025-01-02 15:00:28.677 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWeather]}" onto public java.lang.String com.bd.controller.HttpController.GetWeather() throws java.lang.Exception
2025-01-02 15:00:28.677 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateCrewInfo],methods=[GET]}" onto private void com.bd.controller.HttpController.UpdateCrewInfo(int) throws java.lang.Exception
2025-01-02 15:00:28.677 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteShipTerm]}" onto public void com.bd.controller.HttpController.DeleteShipTerm(int)
2025-01-02 15:00:28.677 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetUserOperation],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetUserOperation(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 15:00:28.678 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllMarkInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllMarkInfo(int)
2025-01-02 15:00:28.678 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetShipWithArea],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.SetShipWithArea(int,int)
2025-01-02 15:00:28.678 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllWhiteList],methods=[GET]}" onto public java.util.List<com.bd.entity.BlackAndWhiteList> com.bd.controller.HttpController.GetAllWhiteList()
2025-01-02 15:00:28.679 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetBdMsgExample]}" onto public java.util.List<com.bd.entity.BDMsgExample> com.bd.controller.HttpController.GetBdMsgExample(int)
2025-01-02 15:00:28.679 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHlsById],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetHlsById(int)
2025-01-02 15:00:28.679 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetUserCount],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetUserCount()
2025-01-02 15:00:28.680 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteJianCeShip]}" onto public java.lang.String com.bd.controller.HttpController.DeleteJianCeShip(java.util.List<java.lang.Integer>)
2025-01-02 15:00:28.680 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTyphoonInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTyphoonInfo()
2025-01-02 15:00:28.680 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCameraInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.CameraInfo> com.bd.controller.HttpController.GetCameraInfo()
2025-01-02 15:00:28.680 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/EditMarkInfo]}" onto public java.lang.String com.bd.controller.HttpController.EditMarkInfo(com.bd.entity.MarkInfo)
2025-01-02 15:00:28.681 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetMarkInfoById],methods=[GET]}" onto public com.bd.entity.MarkInfo com.bd.controller.HttpController.GetMarkInfoById(int)
2025-01-02 15:00:28.681 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHlsUrlByIndex]}" onto public java.lang.String com.bd.controller.HttpController.GetHlsUrlByIndex(java.lang.String,java.lang.String)
2025-01-02 15:00:28.681 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertMarkInfo]}" onto public java.lang.String com.bd.controller.HttpController.InsertMarkInfo(com.bd.entity.MarkInfo)
2025-01-02 15:00:28.681 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/AddJianCeShip]}" onto public java.lang.String com.bd.controller.HttpController.AddJianCeShip(com.bd.entity.JianCeShipInfo)
2025-01-02 15:00:28.682 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/AddShipTerm]}" onto public void com.bd.controller.HttpController.AddShipTerm(java.lang.String,int)
2025-01-02 15:00:28.682 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHlsImg],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetHlsImg(int,int)
2025-01-02 15:00:28.682 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllBlackList],methods=[GET]}" onto public java.util.List<com.bd.entity.BlackAndWhiteList> com.bd.controller.HttpController.GetAllBlackList()
2025-01-02 15:00:28.682 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipSetting],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipSetting(int)
2025-01-02 15:00:28.682 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetManagerCount]}" onto public int com.bd.controller.HttpController.GetManagerCount(int)
2025-01-02 15:00:28.683 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetMapToken]}" onto public java.lang.String com.bd.controller.HttpController.GetMapToken()
2025-01-02 15:00:28.683 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetShipSetting],methods=[GET]}" onto public void com.bd.controller.HttpController.SetShipSetting(int,java.lang.String)
2025-01-02 15:00:28.683 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetIp]}" onto public java.lang.String com.bd.controller.HttpController.GetIp(javax.servlet.http.HttpServletRequest)
2025-01-02 15:00:28.683 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetUserSetting]}" onto public void com.bd.controller.HttpController.SetUserSetting(java.util.List<com.bd.entity.UserSettingInfo>)
2025-01-02 15:00:28.683 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipImgUrl],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipImgUrl(int)
2025-01-02 15:00:28.684 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetAllShipInfo()
2025-01-02 15:00:28.684 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetNearestHls],methods=[GET]}" onto public java.util.List<java.lang.String> com.bd.controller.HttpController.GetNearestHls(java.lang.String)
2025-01-02 15:00:28.684 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetUserSetting]}" onto public java.util.List<com.bd.entity.UserSettingInfo> com.bd.controller.HttpController.GetUserSetting(int)
2025-01-02 15:00:28.684 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/isCheckAccount],methods=[GET]}" onto public com.bd.entity.UserInfo com.bd.controller.HttpController.isCheckAccount(java.lang.String)
2025-01-02 15:00:28.685 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/CheckShipInTerm]}" onto public void com.bd.controller.HttpController.CheckShipInTerm(int,int)
2025-01-02 15:00:28.685 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipFromTerm]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_all> com.bd.controller.HttpController.GetShipFromTerm(int)
2025-01-02 15:00:28.685 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipIdByName],methods=[GET]}" onto public int com.bd.controller.HttpController.GetShipIdByName(java.lang.String)
2025-01-02 15:00:28.685 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/series/add],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.addseries()
2025-01-02 15:00:28.685 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipType],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShipType(int,int)
2025-01-02 15:00:28.686 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/CheckShipBout]}" onto public java.util.List<java.lang.Integer> com.bd.controller.HttpController.CheckShipBout(java.util.List<java.lang.Integer>)
2025-01-02 15:00:28.686 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/excel/upload]}" onto public void com.bd.controller.HttpController.excelUpload(java.util.List<com.bd.entity.other.ExcelEntity>)
2025-01-02 15:00:28.690 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-01-02 15:00:28.690 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-01-02 15:00:28.767 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:00:28.767 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:00:28.767 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/map/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:00:28.768 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/wxMap/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:00:28.768 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/YimaMap1/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:00:28.768 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/YimaMap2/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:00:28.768 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/shipImg/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:00:29.129 INFO  [restartedMain]o.s.boot.devtools.autoconfigure.OptionalLiveReloadServer.startServer:57 -LiveReload server is running on port 35729
2025-01-02 15:00:29.166 INFO  [restartedMain]o.s.jmx.export.annotation.AnnotationMBeanExporter.afterSingletonsInstantiated:433 -Registering beans for JMX exposure on startup
2025-01-02 15:00:29.168 INFO  [restartedMain]o.s.jmx.export.annotation.AnnotationMBeanExporter.autodetect:895 -Bean with name 'dataSource' has been autodetected for JMX exposure
2025-01-02 15:00:29.178 INFO  [restartedMain]o.s.jmx.export.annotation.AnnotationMBeanExporter.registerBeanInstance:668 -Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-01-02 15:00:29.217 INFO  [restartedMain]o.s.s.annotation.ScheduledAnnotationBeanPostProcessor.finishRegistration:275 -No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-01-02 15:00:29.243 INFO  [restartedMain]org.apache.coyote.http11.Http11NioProtocol.log:180 -Starting ProtocolHandler ["http-nio-7101"]
2025-01-02 15:00:29.255 INFO  [restartedMain]org.apache.tomcat.util.net.NioSelectorPool.log:180 -Using a shared selector for servlet write/read
2025-01-02 15:00:29.270 INFO  [restartedMain]o.s.boot.web.embedded.tomcat.TomcatWebServer.start:206 -Tomcat started on port(s): 7101 (http) with context path ''
2025-01-02 15:00:29.275 INFO  [restartedMain]com.bd.Application.logStarted:59 -Started Application in 4.667 seconds (JVM running for 6.205)
2025-01-02 15:01:44.053 INFO  [pool-2-thread-1]o.s.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions:316 -Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2025-01-02 15:01:44.076 INFO  [pool-2-thread-1]org.springframework.jdbc.support.SQLErrorCodesFactory.<init>:128 -SQLErrorCodes loaded: [DB2, Derby, H2, HDB, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase]
2025-01-02 15:02:12.702 INFO  [restartedMain]com.bd.Application.logStarting:50 -Starting Application on THUNDEROBOT with PID 12748 (G:\yimaWorking\bdxt_shipMonitor_web\Server\springbootmybatisdmdb\target\classes started by 1p in G:\yimaWorking\bdxt_shipMonitor_web\Server\springbootmybatisdmdb)
2025-01-02 15:02:12.703 INFO  [restartedMain]com.bd.Application.logStartupProfileInfo:663 -The following profiles are active: dev
2025-01-02 15:02:12.769 INFO  [restartedMain]o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext.prepareRefresh:590 -Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7f4c58b3: startup date [Thu Jan 02 15:02:12 CST 2025]; root of context hierarchy
2025-01-02 15:02:14.381 INFO  [restartedMain]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:328 -Bean 'asyncTaskConfig' of type [com.bd.config.AsyncTaskConfig$$EnhancerBySpringCGLIB$$8097146f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-02 15:02:14.396 INFO  [restartedMain]o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:171 -Initializing ExecutorService 
2025-01-02 15:02:14.405 INFO  [restartedMain]o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:171 -Initializing ExecutorService  'getAsyncExecutor'
2025-01-02 15:02:14.406 INFO  [restartedMain]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:328 -Bean 'getAsyncExecutor' of type [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-02 15:02:14.960 INFO  [restartedMain]o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:91 -Tomcat initialized with port(s): 7101 (http)
2025-01-02 15:02:14.970 INFO  [restartedMain]org.apache.coyote.http11.Http11NioProtocol.log:180 -Initializing ProtocolHandler ["http-nio-7101"]
2025-01-02 15:02:14.977 INFO  [restartedMain]org.apache.catalina.core.StandardService.log:180 -Starting service [Tomcat]
2025-01-02 15:02:14.978 INFO  [restartedMain]org.apache.catalina.core.StandardEngine.log:180 -Starting Servlet Engine: Apache Tomcat/8.5.31
2025-01-02 15:02:14.981 INFO  [localhost-startStop-1]org.apache.catalina.core.AprLifecycleListener.log:180 -The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Program Files\Java\jdk1.8.0_91\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\PlasticSCM5\server;C:\Program Files\PlasticSCM5\client;F:\vmware\bin\;C:\ProgramData\Oracle\Java\javapath;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\TortiiseSVN\bin;G:\sqlite\sqlite-tools-win32-x86-3320300;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Java\jdk-16.0.1\bin;C:\Program Files\Java\jdk-16.0.1\jre\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;G:\Node.js\;G:\Node.js\node_global;C:\Program Files\Git\cmd;C:\Program Files\Microsoft SQL Server\120\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\100\Tools\Binn\;C:\Program Files\Microsoft SQL Server\100\Tools\Binn\;C:\Program Files\Microsoft SQL Server\100\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\90\Tools\binn\;G:\Windows Kits\10\Windows Performance Toolkit\;F:\apache-maven-3.8.5\bin;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;G:\CLionSpace\untitled2\cmake-build-debug\;C:\Program Files\dotnet\;G:\skia_win\third_party\externals\emsdk;G:\skia_win\third_party\externals\emsdk\upstream\emscripten;G:\skia\third_party\externals\emsdk;G:\skia\third_party\externals\emsdk\upstream\emscripten;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\;C:\Users\<USER>\depot_tools;D:\VC++\Tools\WinNT;D:\VC++\MSDev98\Bin;D:\VC++\Tools;D:\Microsoft Visual Studio\VC98\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps2;G:\IntelliJ IDEA 2020.3\bin;;G:\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;G:\PyCharm 2020.2.3\bin;;C:\Users\<USER>\ninja;C:\Python27;G:\HUAWEI DevEco Studio 4.0\bin;;C:\Users\<USER>\.dotnet\tools;.]
2025-01-02 15:02:15.094 INFO  [localhost-startStop-1]o.a.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log:180 -Initializing Spring embedded WebApplicationContext
2025-01-02 15:02:15.094 INFO  [localhost-startStop-1]org.springframework.web.context.ContextLoader.prepareWebApplicationContext:285 -Root WebApplicationContext: initialization completed in 2325 ms
2025-01-02 15:02:15.308 INFO  [localhost-startStop-1]o.s.boot.web.servlet.ServletRegistrationBean.addRegistration:186 -Servlet dispatcherServlet mapped to [/]
2025-01-02 15:02:15.314 INFO  [localhost-startStop-1]o.s.boot.web.servlet.FilterRegistrationBean.configure:245 -Mapping filter: 'characterEncodingFilter' to: [/*]
2025-01-02 15:02:15.314 INFO  [localhost-startStop-1]o.s.boot.web.servlet.FilterRegistrationBean.configure:245 -Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-01-02 15:02:15.315 INFO  [localhost-startStop-1]o.s.boot.web.servlet.FilterRegistrationBean.configure:245 -Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-01-02 15:02:15.315 INFO  [localhost-startStop-1]o.s.boot.web.servlet.FilterRegistrationBean.configure:245 -Mapping filter: 'requestContextFilter' to: [/*]
2025-01-02 15:02:16.567 INFO  [restartedMain]com.zaxxer.hikari.HikariDataSource.getConnection:110 -HikariPool-1 - Starting...
2025-01-02 15:02:16.790 INFO  [restartedMain]com.zaxxer.hikari.pool.PoolBase.getAndSetNetworkTimeout:516 -HikariPool-1 - Driver does not support get/set network timeout for connections. (不支持的接口或功能)
2025-01-02 15:02:16.842 INFO  [restartedMain]com.zaxxer.hikari.HikariDataSource.getConnection:123 -HikariPool-1 - Start completed.
2025-01-02 15:02:17.116 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:02:17.412 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerAdapter.initControllerAdviceCache:574 -Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7f4c58b3: startup date [Thu Jan 02 15:02:12 CST 2025]; root of context hierarchy
2025-01-02 15:02:17.523 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/token],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.token() throws java.io.IOException
2025-01-02 15:02:17.525 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/test],methods=[GET]}" onto public int com.bd.controller.HttpController.pp()
2025-01-02 15:02:17.527 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/Postppp],methods=[POST]}" onto public java.lang.String com.bd.controller.HttpController.post(java.lang.String,java.lang.String)
2025-01-02 15:02:17.527 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateUser]}" onto public java.lang.String com.bd.controller.HttpController.UpdateUser(int,com.bd.entity.UserInfo)
2025-01-02 15:02:17.528 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/Login]}" onto public com.bd.entity.UserInfo com.bd.controller.HttpController.Login(com.bd.entity.UserInfo,javax.servlet.http.HttpServletRequest)
2025-01-02 15:02:17.528 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/resetErrorTimes]}" onto public void com.bd.controller.HttpController.resetErrorTimes(java.lang.String)
2025-01-02 15:02:17.528 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/AddPort]}" onto public int com.bd.controller.HttpController.AddPort(java.lang.String,java.lang.String,int,int,java.lang.String,java.util.List<com.bd.entity.Coordinate>)
2025-01-02 15:02:17.529 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPortShipCount],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipCount> com.bd.controller.HttpController.GetPortShipCount()
2025-01-02 15:02:17.529 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteUser]}" onto public java.lang.String com.bd.controller.HttpController.DeleteUser(java.util.List<java.lang.Integer>)
2025-01-02 15:02:17.530 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPortInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetPortInfo(int)
2025-01-02 15:02:17.530 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/EditPort]}" onto public java.lang.String com.bd.controller.HttpController.EditPort(int,java.lang.String,java.lang.String,int,int,java.lang.String,java.util.List<com.bd.entity.Coordinate>)
2025-01-02 15:02:17.531 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeletePort]}" onto public java.lang.String com.bd.controller.HttpController.DeletePort(java.util.List<java.lang.Integer>)
2025-01-02 15:02:17.531 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetInPortShipCount]}" onto public java.lang.String com.bd.controller.HttpController.GetShipCount()
2025-01-02 15:02:17.531 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetSpecialShip],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipDynamicInfo> com.bd.controller.HttpController.GetSpecialShip()
2025-01-02 15:02:17.532 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAreaInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.AreaInfo> com.bd.controller.HttpController.GetAreaInfo()
2025-01-02 15:02:17.532 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOnePortInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortInfo> com.bd.controller.HttpController.GetOnePortInfo(int)
2025-01-02 15:02:17.534 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipManyRegis],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipManyRegis(int)
2025-01-02 15:02:17.535 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllPortInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortInfo> com.bd.controller.HttpController.GetAllPortInfo()
2025-01-02 15:02:17.535 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/getCheckRecordByShipName],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.getCheckRecord(java.lang.String)
2025-01-02 15:02:17.535 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllFishShip]}" onto public java.lang.String com.bd.controller.HttpController.GetAllFishShip(java.lang.String,java.lang.String,java.lang.String,int,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,com.bd.entity.dto.ShipQueryDto)
2025-01-02 15:02:17.535 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllInPortShip]}" onto public java.lang.String com.bd.controller.HttpController.GetAllInPortShip(java.lang.String,int)
2025-01-02 15:02:17.536 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllAreaInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllAreaInfo(int)
2025-01-02 15:02:17.536 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetLawRecordInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetLawRecordInfo(java.lang.String,java.lang.String,java.lang.String)
2025-01-02 15:02:17.537 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/api/shipCount]}" onto public java.lang.String com.bd.controller.HttpController.shipCount()
2025-01-02 15:02:17.537 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllPeople],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllPeople(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int,com.bd.entity.dto.PeopleQueryDto)
2025-01-02 15:02:17.538 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetServerState],methods=[GET]}" onto public com.bd.entity.TongjiModel com.bd.controller.HttpController.GetServerState(int)
2025-01-02 15:02:17.538 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllUser]}" onto public java.lang.String com.bd.controller.HttpController.GetAllUser(java.lang.String,int,int)
2025-01-02 15:02:17.539 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/EditAreaInfo]}" onto public java.lang.String com.bd.controller.HttpController.EditAreaInfo(com.bd.entity.AreaInfo)
2025-01-02 15:02:17.539 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetUserInfoById]}" onto public com.bd.entity.UserInfo com.bd.controller.HttpController.GetUserInfoById(int)
2025-01-02 15:02:17.540 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllVoyageInfo]}" onto public java.lang.String com.bd.controller.HttpController.GetAllVoyageInfo(java.lang.String,int)
2025-01-02 15:02:17.540 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllBdMsg]}" onto public java.util.List<com.bd.entity.BdMsg> com.bd.controller.HttpController.GetAllBdMsg(java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-01-02 15:02:17.540 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetAreaInfo]}" onto public java.lang.String com.bd.controller.HttpController.SetAreaInfo(com.bd.entity.AreaInfo)
2025-01-02 15:02:17.541 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteBdMsg]}" onto public java.lang.String com.bd.controller.HttpController.DeleteBdMsg(java.util.List<java.lang.Integer>)
2025-01-02 15:02:17.541 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAreaInfoById],methods=[GET]}" onto public com.bd.entity.AreaInfo com.bd.controller.HttpController.GetAreaInfoById(int)
2025-01-02 15:02:17.541 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetSpecialShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.SetSpecialShip(int,int)
2025-01-02 15:02:17.541 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetBdMsg]}" onto public java.lang.String com.bd.controller.HttpController.GetBdMsg(java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:02:17.542 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertBdMsg]}" onto public int com.bd.controller.HttpController.InsertBdMsg(com.bd.entity.BdMsg)
2025-01-02 15:02:17.542 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeletedAreaInfo]}" onto public java.lang.String com.bd.controller.HttpController.DeletedAreaInfo(java.util.List<java.lang.Integer>)
2025-01-02 15:02:17.542 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertUser]}" onto public java.lang.String com.bd.controller.HttpController.InsertUser(com.bd.entity.UserInfo)
2025-01-02 15:02:17.543 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DLogin]}" onto public void com.bd.controller.HttpController.LoginFromIPMS(javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-01-02 15:02:17.543 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/LoginFromIPMS]}" onto public void com.bd.controller.HttpController.LoginFromIPMS(java.lang.String,javax.servlet.http.HttpServletResponse) throws java.lang.Exception
2025-01-02 15:02:17.543 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DLogin_XC]}" onto public void com.bd.controller.HttpController.LoginFromIPMS_XC(javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-01-02 15:02:17.543 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/LoginFromIPMS_XC]}" onto public void com.bd.controller.HttpController.LoginFromIPMS_XC(java.lang.String,javax.servlet.http.HttpServletResponse) throws java.lang.Exception
2025-01-02 15:02:17.544 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllFocusShip]}" onto public java.lang.String com.bd.controller.HttpController.GetAllFocusShip(java.lang.String,java.lang.String,java.lang.String,int,int,int)
2025-01-02 15:02:17.544 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetFocusShip]}" onto public java.lang.String com.bd.controller.HttpController.GetFocusShip(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 15:02:17.544 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetUserCount],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetUserCount()
2025-01-02 15:02:17.544 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHlsById],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetHlsById(int)
2025-01-02 15:02:17.544 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWeather]}" onto public java.lang.String com.bd.controller.HttpController.GetWeather() throws java.lang.Exception
2025-01-02 15:02:17.545 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/EditMarkInfo]}" onto public java.lang.String com.bd.controller.HttpController.EditMarkInfo(com.bd.entity.MarkInfo)
2025-01-02 15:02:17.545 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteJianCeShip]}" onto public java.lang.String com.bd.controller.HttpController.DeleteJianCeShip(java.util.List<java.lang.Integer>)
2025-01-02 15:02:17.545 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/AddJianCeShip]}" onto public java.lang.String com.bd.controller.HttpController.AddJianCeShip(com.bd.entity.JianCeShipInfo)
2025-01-02 15:02:17.546 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateCrewInfo],methods=[GET]}" onto private void com.bd.controller.HttpController.UpdateCrewInfo(int) throws java.lang.Exception
2025-01-02 15:02:17.546 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetBdMsgExample]}" onto public java.util.List<com.bd.entity.BDMsgExample> com.bd.controller.HttpController.GetBdMsgExample(int)
2025-01-02 15:02:17.546 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/AddShipTerm]}" onto public void com.bd.controller.HttpController.AddShipTerm(java.lang.String,int)
2025-01-02 15:02:17.546 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllMarkInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllMarkInfo(int)
2025-01-02 15:02:17.547 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllWhiteList],methods=[GET]}" onto public java.util.List<com.bd.entity.BlackAndWhiteList> com.bd.controller.HttpController.GetAllWhiteList()
2025-01-02 15:02:17.547 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteShipTerm]}" onto public void com.bd.controller.HttpController.DeleteShipTerm(int)
2025-01-02 15:02:17.547 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipTerm]}" onto public java.util.List<com.bd.entity.Ship_Term> com.bd.controller.HttpController.GetShipTerm(int)
2025-01-02 15:02:17.548 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllBlackList],methods=[GET]}" onto public java.util.List<com.bd.entity.BlackAndWhiteList> com.bd.controller.HttpController.GetAllBlackList()
2025-01-02 15:02:17.548 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetMarkInfoById],methods=[GET]}" onto public com.bd.entity.MarkInfo com.bd.controller.HttpController.GetMarkInfoById(int)
2025-01-02 15:02:17.548 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCameraInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.CameraInfo> com.bd.controller.HttpController.GetCameraInfo()
2025-01-02 15:02:17.548 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTyphoonInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTyphoonInfo()
2025-01-02 15:02:17.548 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipFromTerm]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_all> com.bd.controller.HttpController.GetShipFromTerm(int)
2025-01-02 15:02:17.549 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetUserOperation],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetUserOperation(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 15:02:17.549 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHlsUrl]}" onto public java.lang.String com.bd.controller.HttpController.GetHlsUrl(int,java.lang.String)
2025-01-02 15:02:17.549 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetShipWithArea],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.SetShipWithArea(int,int)
2025-01-02 15:02:17.550 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHlsUrlByIndex]}" onto public java.lang.String com.bd.controller.HttpController.GetHlsUrlByIndex(java.lang.String,java.lang.String)
2025-01-02 15:02:17.550 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHlsImg],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetHlsImg(int,int)
2025-01-02 15:02:17.550 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertMarkInfo]}" onto public java.lang.String com.bd.controller.HttpController.InsertMarkInfo(com.bd.entity.MarkInfo)
2025-01-02 15:02:17.550 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetManagerCount]}" onto public int com.bd.controller.HttpController.GetManagerCount(int)
2025-01-02 15:02:17.551 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/series/add],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.addseries()
2025-01-02 15:02:17.551 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/CheckShipBout]}" onto public java.util.List<java.lang.Integer> com.bd.controller.HttpController.CheckShipBout(java.util.List<java.lang.Integer>)
2025-01-02 15:02:17.551 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetIp]}" onto public java.lang.String com.bd.controller.HttpController.GetIp(javax.servlet.http.HttpServletRequest)
2025-01-02 15:02:17.551 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetShipSetting],methods=[GET]}" onto public void com.bd.controller.HttpController.SetShipSetting(int,java.lang.String)
2025-01-02 15:02:17.552 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/isCheckAccount],methods=[GET]}" onto public com.bd.entity.UserInfo com.bd.controller.HttpController.isCheckAccount(java.lang.String)
2025-01-02 15:02:17.552 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/excel/upload]}" onto public void com.bd.controller.HttpController.excelUpload(java.util.List<com.bd.entity.other.ExcelEntity>)
2025-01-02 15:02:17.552 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetNearestHls],methods=[GET]}" onto public java.util.List<java.lang.String> com.bd.controller.HttpController.GetNearestHls(java.lang.String)
2025-01-02 15:02:17.553 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipType],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShipType(int,int)
2025-01-02 15:02:17.553 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetUserSetting]}" onto public java.util.List<com.bd.entity.UserSettingInfo> com.bd.controller.HttpController.GetUserSetting(int)
2025-01-02 15:02:17.553 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetMapToken]}" onto public java.lang.String com.bd.controller.HttpController.GetMapToken()
2025-01-02 15:02:17.554 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/CheckShipInTerm]}" onto public void com.bd.controller.HttpController.CheckShipInTerm(int,int)
2025-01-02 15:02:17.554 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipImgUrl],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipImgUrl(int)
2025-01-02 15:02:17.554 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetAllShipInfo()
2025-01-02 15:02:17.554 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipIdByName],methods=[GET]}" onto public int com.bd.controller.HttpController.GetShipIdByName(java.lang.String)
2025-01-02 15:02:17.555 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipSetting],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipSetting(int)
2025-01-02 15:02:17.555 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetUserSetting]}" onto public void com.bd.controller.HttpController.SetUserSetting(java.util.List<com.bd.entity.UserSettingInfo>)
2025-01-02 15:02:17.555 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllBDShipPosition],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllBDShipPosition(int)
2025-01-02 15:02:17.555 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.getShipInOutByShipName()
2025-01-02 15:02:17.555 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipInfoById],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_all> com.bd.controller.HttpController.GetOneShipInfoById(int)
2025-01-02 15:02:17.555 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipCardById],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_card> com.bd.controller.HttpController.GetOneShipCardById(int)
2025-01-02 15:02:17.556 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipCardTexuById],methods=[GET]}" onto public com.bd.entity.FisheryPermitInfo com.bd.controller.HttpController.GetOneShipCardTexuById(int)
2025-01-02 15:02:17.556 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetBDShipPosition],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipDynamicInfo> com.bd.controller.HttpController.GetBDShipPosition(int)
2025-01-02 15:02:17.556 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/getShipInOutReport],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.getShipInOutReport(java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:02:17.557 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipInfoByShipName]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_all> com.bd.controller.HttpController.GetOneShipInfoByShipName(java.lang.String)
2025-01-02 15:02:17.557 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipHistoryTrackById],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipTrack> com.bd.controller.HttpController.GetShipHistoryTrackById(int,long,long,int,int,int)
2025-01-02 15:02:17.557 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShip_inOrOutPortInfoDetail],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShip_inOrOutPortInfoDetail(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-01-02 15:02:17.558 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipDynamicInfoById]}" onto public com.bd.entity.ShipDynamicInfo com.bd.controller.HttpController.GetOneShipDynamicInfoById(int)
2025-01-02 15:02:17.558 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteImportanceShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.DeleteImportanceShip(int,int)
2025-01-02 15:02:17.558 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPlayShipInfoById],methods=[GET]}" onto public com.bd.entity.PlayShipInfo com.bd.controller.HttpController.GetPlayShipInfoById(int,int,int)
2025-01-02 15:02:17.559 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetYuzhengShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.Ship_Yuzheng> com.bd.controller.HttpController.GetYuzhengShipInfo()
2025-01-02 15:02:17.559 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTodayWarningStatistics],methods=[GET]}" onto public com.bd.entity.WarningStatistics com.bd.controller.HttpController.GetTodayWarningStatistics(int,int)
2025-01-02 15:02:17.559 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetFuxiuWhiteShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.SetFuxiuWhiteShip(int)
2025-01-02 15:02:17.560 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWarningStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetWarningStatistics(int,int)
2025-01-02 15:02:17.560 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetZhifaPeopleInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.LawPeople> com.bd.controller.HttpController.GetZhifaPeopleInfo()
2025-01-02 15:02:17.560 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteFuxiuWhiteShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.DeleteFuxiuWhiteShip(int)
2025-01-02 15:02:17.561 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAreaInfoByUserId],methods=[GET]}" onto public java.util.List<com.bd.entity.AreaInfo> com.bd.controller.HttpController.GetAreaInfoByUserId(java.lang.String)
2025-01-02 15:02:17.561 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutInPortRecordByShipId],methods=[GET]}" onto public java.util.List<com.bd.entity.OutInPortRecord> com.bd.controller.HttpController.GetOutInPortRecordByShipId(java.lang.String)
2025-01-02 15:02:17.561 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutInPortRecord],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetOutInPortRecord()
2025-01-02 15:02:17.562 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAreaInfoByUserIdPageNum],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAreaInfoByUserIdPageNum(int,int)
2025-01-02 15:02:17.562 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTodayWarningInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.WarningInfo> com.bd.controller.HttpController.GetTodayWarningInfo(int,java.lang.String,int)
2025-01-02 15:02:17.562 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShip_inOrOutPortInfoDetail],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShip_inOrOutPortInfoDetail(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:02:17.562 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetBDShipPosition_ronghe],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipDynamicInfo> com.bd.controller.HttpController.GetBDShipPosition_ronghe(int)
2025-01-02 15:02:17.563 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetImportanceShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.SetImportanceShip(int,int)
2025-01-02 15:02:17.563 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipDistribute]}" onto public com.bd.entity.Ship_DistributeCount com.bd.controller.HttpController.GetShipDistribute(int)
2025-01-02 15:02:17.563 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetImportanceShip],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipDynamicInfo> com.bd.controller.HttpController.GetImportanceShip(int)
2025-01-02 15:02:17.564 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPlayAreaHistoryInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipTrack> com.bd.controller.HttpController.GetPlayAreaHistoryInfo(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:02:17.564 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateOutLineReason],methods=[POST]}" onto public java.lang.String com.bd.controller.HttpController.updateOutlineReason(com.bd.entity.ShipEncryption)
2025-01-02 15:02:17.564 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTodayWarningAllStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTodayWarningAllStatistics(int,java.lang.String,int)
2025-01-02 15:02:17.564 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPeopleByShipId],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetPeopleByShipId(int)
2025-01-02 15:02:17.565 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPeopleByIdcards],methods=[POST]}" onto public java.lang.String com.bd.controller.HttpController.GetPeopleByIdcards(java.util.Map<java.lang.String, java.util.List<java.lang.String>>) throws java.lang.Exception
2025-01-02 15:02:17.565 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCurrentInPortShips_Export]}" onto public java.lang.String com.bd.controller.HttpController.GetCurrentInPortShips()
2025-01-02 15:02:17.565 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllInPortShip_Export]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_all> com.bd.controller.HttpController.GetAllInPortShip_Export(java.lang.String)
2025-01-02 15:02:17.565 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShutDownInfo_Export]}" onto public java.util.List<com.bd.entity.BusinessManagement.AlarmRecord> com.bd.controller.HttpController.GetAllShutDownInfo_Export(java.lang.String,int)
2025-01-02 15:02:17.566 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllVoyageInfo_Export]}" onto public java.util.List<com.bd.entity.Ship_Voyage> com.bd.controller.HttpController.GetAllVoyageInfo_Export(java.lang.String)
2025-01-02 15:02:17.566 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipOnlineStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShipOnlineStatistics(java.lang.String,java.lang.String,java.lang.String)
2025-01-02 15:02:17.566 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAlarmShipIdByAlarmId],methods=[GET]}" onto public int com.bd.controller.HttpController.GetAlarmShipIdByAlarmId()
2025-01-02 15:02:17.566 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipStatistics()
2025-01-02 15:02:17.567 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipOnlineStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipOnlineStatistics(java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:02:17.567 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllFishShip_Export]}" onto public java.lang.String com.bd.controller.HttpController.GetAllFishShip_Export(java.lang.String,java.lang.String,java.lang.String,int,int,int,int,int,int)
2025-01-02 15:02:17.568 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetApplinameByShipId],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetApplinameByShipId(int)
2025-01-02 15:02:17.568 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipWorkInfo]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShipWorkInfo(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 15:02:17.568 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOnlineShipCount]}" onto public java.lang.String com.bd.controller.HttpController.GetOnlineShipCount()
2025-01-02 15:02:17.569 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllLawRecordInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllLawRecordInfo(java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:02:17.569 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShutDownInfo]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShutDownInfo(java.lang.String,int,int)
2025-01-02 15:02:17.569 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCrewPeopleByShipId]}" onto public java.lang.String com.bd.controller.HttpController.GetCrewPeopleByShipId(int)
2025-01-02 15:02:17.569 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutsideInPortShip_Export]}" onto public java.lang.String com.bd.controller.HttpController.GetOutsideInPortShip()
2025-01-02 15:02:17.570 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipWorkInfo_Export]}" onto public java.util.List<com.bd.entity.Ship_WorkInfo> com.bd.controller.HttpController.GetAllShipWorkInfo_Export(java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:02:17.570 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPortNodeByShipId]}" onto public java.lang.String com.bd.controller.HttpController.GetPortNodeByShipId(int,int)
2025-01-02 15:02:17.570 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllPeople_Export]}" onto public java.lang.String com.bd.controller.HttpController.GetAllPeople_Export(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.bd.entity.dto.PeopleQueryDto)
2025-01-02 15:02:17.570 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPortStatisticsInOutShipCount],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetPortStatisticsInOutShipCount()
2025-01-02 15:02:17.570 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeletedLawRecordInfo]}" onto public java.lang.String com.bd.controller.HttpController.DeletedLawRecordInfo(java.util.List<java.lang.Integer>)
2025-01-02 15:02:17.571 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAlarmCountByTime],methods=[GET]}" onto public com.bd.entity.TongjiModel com.bd.controller.HttpController.GetAlarmCountByTime(int,int,int)
2025-01-02 15:02:17.571 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllAlarmRecordInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllAlarmRecordInfo(java.lang.String,java.lang.String,java.lang.String,int,int,int)
2025-01-02 15:02:17.571 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAlarmRecordInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAlarmRecordInfo(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 15:02:17.572 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteAlarmRecordInfo]}" onto public java.lang.String com.bd.controller.HttpController.DeleteAlarmRecordInfo(java.util.List<java.lang.Integer>)
2025-01-02 15:02:17.572 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllWhiteAndBlackShip]}" onto public java.lang.String com.bd.controller.HttpController.GetAllWhiteAndBlackShip(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 15:02:17.573 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTyphoonInfoByYear],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTyphoonInfoByYear(int)
2025-01-02 15:02:17.573 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWhiteAndBlackShip]}" onto public java.lang.String com.bd.controller.HttpController.GetWhiteAndBlackShip(java.lang.String,java.lang.String,java.lang.String,int,int,int)
2025-01-02 15:02:17.573 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertBdMsgExample]}" onto public void com.bd.controller.HttpController.InsertBdMsgExample(java.lang.String,int)
2025-01-02 15:02:17.573 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteBdMsgExample]}" onto public void com.bd.controller.HttpController.DeleteBdMsgExample(int)
2025-01-02 15:02:17.574 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateBdMsgExample]}" onto public void com.bd.controller.HttpController.DeleteBdMsgExample(int,int,java.lang.String)
2025-01-02 15:02:17.574 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllBdMsgByUserId]}" onto public java.util.List<com.bd.entity.BdMsg> com.bd.controller.HttpController.GetAllBdMsgByUserId(int,int)
2025-01-02 15:02:17.575 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetLawRecordInfoById]}" onto public com.bd.entity.LawRecordInfo com.bd.controller.HttpController.GetLawRecordInfoById(int,int)
2025-01-02 15:02:17.575 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTyphoonInfoById],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTyphoonInfoById(int)
2025-01-02 15:02:17.575 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetWhiteOrBlackList]}" onto public java.lang.String com.bd.controller.HttpController.SetWhiteOrBlackList(com.bd.entity.BlackAndWhiteList)
2025-01-02 15:02:17.576 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/deleteMarkInfoById]}" onto public java.lang.String com.bd.controller.HttpController.deleteMarkInfoById(int,int)
2025-01-02 15:02:17.576 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/api/specialShip]}" onto public java.util.List<com.bd.entity.other.SpecialShip> com.bd.controller.HttpController.GetSpecialShipToZhongtai()
2025-01-02 15:02:17.576 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetLawRecordTodayWarning]}" onto public java.util.List<com.bd.entity.LawRecordInfo> com.bd.controller.HttpController.GetLawRecordTodayWarning(int)
2025-01-02 15:02:17.576 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetJianCeShipInfo_Export],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetJianCeShipInfo_Export(java.lang.String)
2025-01-02 15:02:17.577 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAlarmDetialById]}" onto public com.bd.entity.BusinessManagement.AlarmRecord com.bd.controller.HttpController.GetAlarmDetialById(int,int)
2025-01-02 15:02:17.577 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTodayTyphoonInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTodayTyphoonInfo(java.lang.String,java.lang.String)
2025-01-02 15:02:17.577 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCameraInfoBySearch],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetCameraInfoBySearch(java.lang.String)
2025-01-02 15:02:17.577 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteSpecialShip]}" onto public java.lang.String com.bd.controller.HttpController.DeleteSpecialShip(java.util.List<com.bd.entity.SpecialShipType>)
2025-01-02 15:02:17.578 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHistoryWarningInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.BusinessManagement.AlarmRecord> com.bd.controller.HttpController.GetHistoryWarningInfo(int,java.lang.String,java.lang.String,int)
2025-01-02 15:02:17.578 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertLawRecordInfo]}" onto public int com.bd.controller.HttpController.InsertLawRecordInfo(com.bd.entity.LawRecordInfo)
2025-01-02 15:02:17.578 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetJianCeShipInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetJianCeShipInfo(java.lang.String,int)
2025-01-02 15:02:17.578 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWhiteAndBlackListInfoByType],methods=[GET]}" onto public java.util.List<com.bd.entity.BlackAndWhiteList> com.bd.controller.HttpController.GetWhiteAndBlackListInfoByType(int)
2025-01-02 15:02:17.579 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWhiteAndBlackListInfoById],methods=[GET]}" onto public com.bd.entity.BlackAndWhiteList com.bd.controller.HttpController.GetWhiteAndBlackListInfoById(int)
2025-01-02 15:02:17.579 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetUserOperationInfo]}" onto public java.lang.String com.bd.controller.HttpController.SetUserOperationInfo(com.bd.entity.UserOperation,javax.servlet.http.HttpServletRequest)
2025-01-02 15:02:17.579 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteWhiteOrBlackList]}" onto public java.lang.String com.bd.controller.HttpController.DeleteWhiteOrBlackList(java.util.List<java.lang.Integer>)
2025-01-02 15:02:17.579 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateWhiteOrBlackList]}" onto public java.lang.String com.bd.controller.HttpController.UpdateWhiteOrBlackList(com.bd.entity.BlackAndWhiteList)
2025-01-02 15:02:17.580 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertShipForTerm]}" onto public void com.bd.controller.HttpController.InsertShipForTerm(int,int)
2025-01-02 15:02:17.580 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateUserSetting]}" onto public void com.bd.controller.HttpController.UpdateUserSetting(java.util.List<com.bd.entity.UserSettingInfo>)
2025-01-02 15:02:17.580 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteShipFromTerm]}" onto public void com.bd.controller.HttpController.DeleteShipFromTerm(int)
2025-01-02 15:02:17.580 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWSIn168169ShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetWSIn168169ShipInfo()
2025-01-02 15:02:17.581 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCrewCertificateStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetCrewCertificateStatistics(com.bd.entity.dto.CrewCertificationQueryDto)
2025-01-02 15:02:17.581 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetFishAreaShipInfo]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetFishAreaShipInfo(int)
2025-01-02 15:02:17.581 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWSInShangHaiShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetWSInShangHaiShipInfo()
2025-01-02 15:02:17.581 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetJianCeShipInfoByShipId],methods=[GET]}" onto public com.bd.entity.JianCeShipInfo com.bd.controller.HttpController.GetJianCeShipInfoByShipId(int)
2025-01-02 15:02:17.582 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetInPortShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetInPortShipInfo(int)
2025-01-02 15:02:17.582 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SendWarningToOtherAccount]}" onto public void com.bd.controller.HttpController.SendWarningToOtherAccount(int,int,int)
2025-01-02 15:02:17.582 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutPortShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetOutPortShipInfo()
2025-01-02 15:02:17.582 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetJianCeShipInfoById],methods=[GET]}" onto public com.bd.entity.JianCeShipInfo com.bd.controller.HttpController.GetJianCeShipInfoById(int)
2025-01-02 15:02:17.582 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWSInJinBuShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetWSInJinBuShipInfo()
2025-01-02 15:02:17.583 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetWarningToOtherAccountIsRead]}" onto public void com.bd.controller.HttpController.SetWarningToOtherAccountIsRead(int)
2025-01-02 15:02:17.583 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetlawCaseByShipId]}" onto public java.util.List<com.bd.entity.LawCase> com.bd.controller.HttpController.GetlawCaseByShipId(int)
2025-01-02 15:02:17.583 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWSInPortShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetWSInPortShipInfo(int)
2025-01-02 15:02:17.583 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutLineShipInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetOutLineShipInfo(java.lang.String)
2025-01-02 15:02:17.584 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOnLineShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetOnLineShipInfo()
2025-01-02 15:02:17.584 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWarningToOtherAccount]}" onto public java.util.List<com.bd.entity.PushInformation> com.bd.controller.HttpController.GetWarningToOtherAccount(int,int)
2025-01-02 15:02:17.584 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipInfoByPort],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetShipInfoByPort(int)
2025-01-02 15:02:17.584 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteUserSetting],methods=[GET]}" onto public void com.bd.controller.HttpController.DeleteUserSetting(int,int)
2025-01-02 15:02:17.585 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/QueryShip],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipSerch> com.bd.controller.HttpController.GetShipInfoByNameOrTerminalNumber(java.lang.String)
2025-01-02 15:02:17.590 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-01-02 15:02:17.590 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-01-02 15:02:17.658 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:02:17.658 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:02:17.659 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/map/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:02:17.659 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/wxMap/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:02:17.659 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/YimaMap1/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:02:17.659 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/YimaMap2/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:02:17.659 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/shipImg/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:02:18.085 INFO  [restartedMain]o.s.boot.devtools.autoconfigure.OptionalLiveReloadServer.startServer:57 -LiveReload server is running on port 35729
2025-01-02 15:02:18.126 INFO  [restartedMain]o.s.jmx.export.annotation.AnnotationMBeanExporter.afterSingletonsInstantiated:433 -Registering beans for JMX exposure on startup
2025-01-02 15:02:18.129 INFO  [restartedMain]o.s.jmx.export.annotation.AnnotationMBeanExporter.autodetect:895 -Bean with name 'dataSource' has been autodetected for JMX exposure
2025-01-02 15:02:18.134 INFO  [restartedMain]o.s.jmx.export.annotation.AnnotationMBeanExporter.registerBeanInstance:668 -Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-01-02 15:02:18.160 INFO  [restartedMain]o.s.s.annotation.ScheduledAnnotationBeanPostProcessor.finishRegistration:275 -No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-01-02 15:02:29.122 INFO  [restartedMain]org.apache.coyote.http11.Http11NioProtocol.log:180 -Starting ProtocolHandler ["http-nio-7101"]
2025-01-02 15:02:29.137 INFO  [restartedMain]org.apache.tomcat.util.net.NioSelectorPool.log:180 -Using a shared selector for servlet write/read
2025-01-02 15:02:29.156 INFO  [restartedMain]o.s.boot.web.embedded.tomcat.TomcatWebServer.start:206 -Tomcat started on port(s): 7101 (http) with context path ''
2025-01-02 15:02:29.161 INFO  [restartedMain]com.bd.Application.logStarted:59 -Started Application in 16.94 seconds (JVM running for 19.751)
2025-01-02 15:02:29.278 INFO  [pool-2-thread-1]o.s.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions:316 -Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2025-01-02 15:02:29.309 INFO  [Thread-24]o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext.doClose:993 -Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7f4c58b3: startup date [Thu Jan 02 15:02:12 CST 2025]; root of context hierarchy
2025-01-02 15:02:29.316 INFO  [Thread-24]o.s.jmx.export.annotation.AnnotationMBeanExporter.destroy:451 -Unregistering JMX-exposed beans on shutdown
2025-01-02 15:02:29.316 INFO  [pool-2-thread-1]org.springframework.jdbc.support.SQLErrorCodesFactory.<init>:128 -SQLErrorCodes loaded: [DB2, Derby, H2, HDB, HSQL, Informix, MS-SQL, MySQL, Oracle, PostgreSQL, Sybase]
2025-01-02 15:02:29.317 INFO  [Thread-24]o.s.jmx.export.annotation.AnnotationMBeanExporter.unregisterBeans:183 -Unregistering JMX-exposed beans
2025-01-02 15:02:29.320 INFO  [Thread-24]com.zaxxer.hikari.HikariDataSource.close:381 -HikariPool-1 - Shutdown initiated...
2025-01-02 15:02:29.325 INFO  [Thread-24]com.zaxxer.hikari.HikariDataSource.close:383 -HikariPool-1 - Shutdown completed.
2025-01-02 15:02:29.326 INFO  [Thread-24]o.s.scheduling.concurrent.ThreadPoolTaskExecutor.shutdown:208 -Shutting down ExecutorService 'getAsyncExecutor'
2025-01-02 15:16:33.063 INFO  [restartedMain]com.bd.Application.logStarting:50 -Starting Application on THUNDEROBOT with PID 12664 (G:\yimaWorking\bdxt_shipMonitor_web\Server\springbootmybatisdmdb\target\classes started by 1p in G:\yimaWorking\bdxt_shipMonitor_web\Server\springbootmybatisdmdb)
2025-01-02 15:16:33.065 INFO  [restartedMain]com.bd.Application.logStartupProfileInfo:663 -The following profiles are active: dev
2025-01-02 15:16:33.129 INFO  [restartedMain]o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext.prepareRefresh:590 -Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@70179de4: startup date [Thu Jan 02 15:16:33 CST 2025]; root of context hierarchy
2025-01-02 15:16:35.319 INFO  [restartedMain]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:328 -Bean 'asyncTaskConfig' of type [com.bd.config.AsyncTaskConfig$$EnhancerBySpringCGLIB$$e5cf1914] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-02 15:16:35.330 INFO  [restartedMain]o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:171 -Initializing ExecutorService 
2025-01-02 15:16:35.339 INFO  [restartedMain]o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:171 -Initializing ExecutorService  'getAsyncExecutor'
2025-01-02 15:16:35.340 INFO  [restartedMain]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:328 -Bean 'getAsyncExecutor' of type [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-02 15:16:35.813 INFO  [restartedMain]o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:91 -Tomcat initialized with port(s): 7101 (http)
2025-01-02 15:16:35.821 INFO  [restartedMain]org.apache.coyote.http11.Http11NioProtocol.log:180 -Initializing ProtocolHandler ["http-nio-7101"]
2025-01-02 15:16:35.828 INFO  [restartedMain]org.apache.catalina.core.StandardService.log:180 -Starting service [Tomcat]
2025-01-02 15:16:35.828 INFO  [restartedMain]org.apache.catalina.core.StandardEngine.log:180 -Starting Servlet Engine: Apache Tomcat/8.5.31
2025-01-02 15:16:35.830 INFO  [localhost-startStop-1]org.apache.catalina.core.AprLifecycleListener.log:180 -The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Program Files\Java\jdk1.8.0_91\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\PlasticSCM5\server;C:\Program Files\PlasticSCM5\client;F:\vmware\bin\;C:\ProgramData\Oracle\Java\javapath;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\TortiiseSVN\bin;G:\sqlite\sqlite-tools-win32-x86-3320300;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Java\jdk-16.0.1\bin;C:\Program Files\Java\jdk-16.0.1\jre\bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;G:\Node.js\;G:\Node.js\node_global;C:\Program Files\Git\cmd;C:\Program Files\Microsoft SQL Server\120\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\100\Tools\Binn\;C:\Program Files\Microsoft SQL Server\100\Tools\Binn\;C:\Program Files\Microsoft SQL Server\100\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\90\Tools\binn\;G:\Windows Kits\10\Windows Performance Toolkit\;F:\apache-maven-3.8.5\bin;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;G:\CLionSpace\untitled2\cmake-build-debug\;C:\Program Files\dotnet\;G:\skia_win\third_party\externals\emsdk;G:\skia_win\third_party\externals\emsdk\upstream\emscripten;G:\skia\third_party\externals\emsdk;G:\skia\third_party\externals\emsdk\upstream\emscripten;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\;C:\Users\<USER>\depot_tools;D:\VC++\Tools\WinNT;D:\VC++\MSDev98\Bin;D:\VC++\Tools;D:\Microsoft Visual Studio\VC98\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps2;G:\IntelliJ IDEA 2020.3\bin;;G:\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;G:\PyCharm 2020.2.3\bin;;C:\Users\<USER>\ninja;C:\Python27;G:\HUAWEI DevEco Studio 4.0\bin;;C:\Users\<USER>\.dotnet\tools;.]
2025-01-02 15:16:35.928 INFO  [localhost-startStop-1]o.a.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log:180 -Initializing Spring embedded WebApplicationContext
2025-01-02 15:16:35.928 INFO  [localhost-startStop-1]org.springframework.web.context.ContextLoader.prepareWebApplicationContext:285 -Root WebApplicationContext: initialization completed in 2799 ms
2025-01-02 15:16:36.060 INFO  [localhost-startStop-1]o.s.boot.web.servlet.ServletRegistrationBean.addRegistration:186 -Servlet dispatcherServlet mapped to [/]
2025-01-02 15:16:36.063 INFO  [localhost-startStop-1]o.s.boot.web.servlet.FilterRegistrationBean.configure:245 -Mapping filter: 'characterEncodingFilter' to: [/*]
2025-01-02 15:16:36.063 INFO  [localhost-startStop-1]o.s.boot.web.servlet.FilterRegistrationBean.configure:245 -Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-01-02 15:16:36.063 INFO  [localhost-startStop-1]o.s.boot.web.servlet.FilterRegistrationBean.configure:245 -Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-01-02 15:16:36.063 INFO  [localhost-startStop-1]o.s.boot.web.servlet.FilterRegistrationBean.configure:245 -Mapping filter: 'requestContextFilter' to: [/*]
2025-01-02 15:16:36.785 INFO  [restartedMain]com.zaxxer.hikari.HikariDataSource.getConnection:110 -HikariPool-1 - Starting...
2025-01-02 15:16:37.012 INFO  [restartedMain]com.zaxxer.hikari.pool.PoolBase.getAndSetNetworkTimeout:516 -HikariPool-1 - Driver does not support get/set network timeout for connections. (不支持的接口或功能)
2025-01-02 15:16:37.062 INFO  [restartedMain]com.zaxxer.hikari.HikariDataSource.getConnection:123 -HikariPool-1 - Start completed.
2025-01-02 15:16:37.232 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:16:37.506 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerAdapter.initControllerAdviceCache:574 -Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@70179de4: startup date [Thu Jan 02 15:16:33 CST 2025]; root of context hierarchy
2025-01-02 15:16:37.578 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/token],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.token() throws java.io.IOException
2025-01-02 15:16:37.581 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipCardById],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_card> com.bd.controller.HttpController.GetOneShipCardById(int)
2025-01-02 15:16:37.581 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllBDShipPosition],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllBDShipPosition(int)
2025-01-02 15:16:37.581 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetBDShipPosition],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipDynamicInfo> com.bd.controller.HttpController.GetBDShipPosition(int)
2025-01-02 15:16:37.581 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/getShipInOutReport],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.getShipInOutReport(java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:16:37.582 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.getShipInOutByShipName()
2025-01-02 15:16:37.582 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipInfoByShipName]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_all> com.bd.controller.HttpController.GetOneShipInfoByShipName(java.lang.String)
2025-01-02 15:16:37.582 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipInfoById],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_all> com.bd.controller.HttpController.GetOneShipInfoById(int)
2025-01-02 15:16:37.583 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShip_inOrOutPortInfoDetail],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShip_inOrOutPortInfoDetail(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:16:37.583 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipHistoryTrackById],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipTrack> com.bd.controller.HttpController.GetShipHistoryTrackById(int,long,long,int,int,int)
2025-01-02 15:16:37.583 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutInPortRecord],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetOutInPortRecord()
2025-01-02 15:16:37.583 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetImportanceShip],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipDynamicInfo> com.bd.controller.HttpController.GetImportanceShip(int)
2025-01-02 15:16:37.584 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetYuzhengShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.Ship_Yuzheng> com.bd.controller.HttpController.GetYuzhengShipInfo()
2025-01-02 15:16:37.584 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTodayWarningAllStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTodayWarningAllStatistics(int,java.lang.String,int)
2025-01-02 15:16:37.584 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWarningStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetWarningStatistics(int,int)
2025-01-02 15:16:37.584 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteImportanceShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.DeleteImportanceShip(int,int)
2025-01-02 15:16:37.586 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAreaInfoByUserIdPageNum],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAreaInfoByUserIdPageNum(int,int)
2025-01-02 15:16:37.586 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetImportanceShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.SetImportanceShip(int,int)
2025-01-02 15:16:37.586 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetBDShipPosition_ronghe],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipDynamicInfo> com.bd.controller.HttpController.GetBDShipPosition_ronghe(int)
2025-01-02 15:16:37.586 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTodayWarningStatistics],methods=[GET]}" onto public com.bd.entity.WarningStatistics com.bd.controller.HttpController.GetTodayWarningStatistics(int,int)
2025-01-02 15:16:37.587 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteFuxiuWhiteShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.DeleteFuxiuWhiteShip(int)
2025-01-02 15:16:37.587 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetFuxiuWhiteShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.SetFuxiuWhiteShip(int)
2025-01-02 15:16:37.587 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutInPortRecordByShipId],methods=[GET]}" onto public java.util.List<com.bd.entity.OutInPortRecord> com.bd.controller.HttpController.GetOutInPortRecordByShipId(java.lang.String)
2025-01-02 15:16:37.587 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateOutLineReason],methods=[POST]}" onto public java.lang.String com.bd.controller.HttpController.updateOutlineReason(com.bd.entity.ShipEncryption)
2025-01-02 15:16:37.588 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipCardTexuById],methods=[GET]}" onto public com.bd.entity.FisheryPermitInfo com.bd.controller.HttpController.GetOneShipCardTexuById(int)
2025-01-02 15:16:37.588 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPlayShipInfoById],methods=[GET]}" onto public com.bd.entity.PlayShipInfo com.bd.controller.HttpController.GetPlayShipInfoById(int,int,int)
2025-01-02 15:16:37.588 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOneShipDynamicInfoById]}" onto public com.bd.entity.ShipDynamicInfo com.bd.controller.HttpController.GetOneShipDynamicInfoById(int)
2025-01-02 15:16:37.588 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAreaInfoByUserId],methods=[GET]}" onto public java.util.List<com.bd.entity.AreaInfo> com.bd.controller.HttpController.GetAreaInfoByUserId(java.lang.String)
2025-01-02 15:16:37.589 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPlayAreaHistoryInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipTrack> com.bd.controller.HttpController.GetPlayAreaHistoryInfo(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:16:37.589 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTodayWarningInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.WarningInfo> com.bd.controller.HttpController.GetTodayWarningInfo(int,java.lang.String,int)
2025-01-02 15:16:37.589 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipDistribute]}" onto public com.bd.entity.Ship_DistributeCount com.bd.controller.HttpController.GetShipDistribute(int)
2025-01-02 15:16:37.589 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShip_inOrOutPortInfoDetail],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShip_inOrOutPortInfoDetail(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-01-02 15:16:37.590 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetApplinameByShipId],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetApplinameByShipId(int)
2025-01-02 15:16:37.590 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAlarmRecordInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAlarmRecordInfo(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 15:16:37.590 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllPeople_Export]}" onto public java.lang.String com.bd.controller.HttpController.GetAllPeople_Export(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.bd.entity.dto.PeopleQueryDto)
2025-01-02 15:16:37.590 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetZhifaPeopleInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.LawPeople> com.bd.controller.HttpController.GetZhifaPeopleInfo()
2025-01-02 15:16:37.591 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllInPortShip_Export]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_all> com.bd.controller.HttpController.GetAllInPortShip_Export(java.lang.String)
2025-01-02 15:16:37.591 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShutDownInfo]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShutDownInfo(java.lang.String,int,int)
2025-01-02 15:16:37.591 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShutDownInfo_Export]}" onto public java.util.List<com.bd.entity.BusinessManagement.AlarmRecord> com.bd.controller.HttpController.GetAllShutDownInfo_Export(java.lang.String,int)
2025-01-02 15:16:37.591 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPeopleByShipId],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetPeopleByShipId(int)
2025-01-02 15:16:37.592 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCurrentInPortShips_Export]}" onto public java.lang.String com.bd.controller.HttpController.GetCurrentInPortShips()
2025-01-02 15:16:37.592 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipWorkInfo]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShipWorkInfo(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 15:16:37.592 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipOnlineStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShipOnlineStatistics(java.lang.String,java.lang.String,java.lang.String)
2025-01-02 15:16:37.592 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeletedLawRecordInfo]}" onto public java.lang.String com.bd.controller.HttpController.DeletedLawRecordInfo(java.util.List<java.lang.Integer>)
2025-01-02 15:16:37.592 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllAlarmRecordInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllAlarmRecordInfo(java.lang.String,java.lang.String,java.lang.String,int,int,int)
2025-01-02 15:16:37.593 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPeopleByIdcards],methods=[POST]}" onto public java.lang.String com.bd.controller.HttpController.GetPeopleByIdcards(java.util.Map<java.lang.String, java.util.List<java.lang.String>>) throws java.lang.Exception
2025-01-02 15:16:37.593 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutsideInPortShip_Export]}" onto public java.lang.String com.bd.controller.HttpController.GetOutsideInPortShip()
2025-01-02 15:16:37.593 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllVoyageInfo_Export]}" onto public java.util.List<com.bd.entity.Ship_Voyage> com.bd.controller.HttpController.GetAllVoyageInfo_Export(java.lang.String)
2025-01-02 15:16:37.593 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPortStatisticsInOutShipCount],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetPortStatisticsInOutShipCount()
2025-01-02 15:16:37.594 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCrewPeopleByShipId]}" onto public java.lang.String com.bd.controller.HttpController.GetCrewPeopleByShipId(int)
2025-01-02 15:16:37.594 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllFishShip_Export]}" onto public java.lang.String com.bd.controller.HttpController.GetAllFishShip_Export(java.lang.String,java.lang.String,java.lang.String,int,int,int,int,int,int)
2025-01-02 15:16:37.594 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPortNodeByShipId]}" onto public java.lang.String com.bd.controller.HttpController.GetPortNodeByShipId(int,int)
2025-01-02 15:16:37.594 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteAlarmRecordInfo]}" onto public java.lang.String com.bd.controller.HttpController.DeleteAlarmRecordInfo(java.util.List<java.lang.Integer>)
2025-01-02 15:16:37.595 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipOnlineStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipOnlineStatistics(java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:16:37.595 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAlarmCountByTime],methods=[GET]}" onto public com.bd.entity.TongjiModel com.bd.controller.HttpController.GetAlarmCountByTime(int,int,int)
2025-01-02 15:16:37.595 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOnlineShipCount]}" onto public java.lang.String com.bd.controller.HttpController.GetOnlineShipCount()
2025-01-02 15:16:37.595 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllLawRecordInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllLawRecordInfo(java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:16:37.595 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipStatistics()
2025-01-02 15:16:37.596 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAlarmShipIdByAlarmId],methods=[GET]}" onto public int com.bd.controller.HttpController.GetAlarmShipIdByAlarmId()
2025-01-02 15:16:37.596 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAlarmDetialById]}" onto public com.bd.entity.BusinessManagement.AlarmRecord com.bd.controller.HttpController.GetAlarmDetialById(int,int)
2025-01-02 15:16:37.596 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateWhiteOrBlackList]}" onto public java.lang.String com.bd.controller.HttpController.UpdateWhiteOrBlackList(com.bd.entity.BlackAndWhiteList)
2025-01-02 15:16:37.596 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/deleteMarkInfoById]}" onto public java.lang.String com.bd.controller.HttpController.deleteMarkInfoById(int,int)
2025-01-02 15:16:37.597 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteWhiteOrBlackList]}" onto public java.lang.String com.bd.controller.HttpController.DeleteWhiteOrBlackList(java.util.List<java.lang.Integer>)
2025-01-02 15:16:37.597 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTyphoonInfoByYear],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTyphoonInfoByYear(int)
2025-01-02 15:16:37.597 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllWhiteAndBlackShip]}" onto public java.lang.String com.bd.controller.HttpController.GetAllWhiteAndBlackShip(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 15:16:37.597 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTodayTyphoonInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTodayTyphoonInfo(java.lang.String,java.lang.String)
2025-01-02 15:16:37.598 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetLawRecordTodayWarning]}" onto public java.util.List<com.bd.entity.LawRecordInfo> com.bd.controller.HttpController.GetLawRecordTodayWarning(int)
2025-01-02 15:16:37.598 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipWorkInfo_Export]}" onto public java.util.List<com.bd.entity.Ship_WorkInfo> com.bd.controller.HttpController.GetAllShipWorkInfo_Export(java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:16:37.598 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWhiteAndBlackShip]}" onto public java.lang.String com.bd.controller.HttpController.GetWhiteAndBlackShip(java.lang.String,java.lang.String,java.lang.String,int,int,int)
2025-01-02 15:16:37.598 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertBdMsgExample]}" onto public void com.bd.controller.HttpController.InsertBdMsgExample(java.lang.String,int)
2025-01-02 15:16:37.598 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetLawRecordInfoById]}" onto public com.bd.entity.LawRecordInfo com.bd.controller.HttpController.GetLawRecordInfoById(int,int)
2025-01-02 15:16:37.599 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateBdMsgExample]}" onto public void com.bd.controller.HttpController.DeleteBdMsgExample(int,int,java.lang.String)
2025-01-02 15:16:37.599 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteBdMsgExample]}" onto public void com.bd.controller.HttpController.DeleteBdMsgExample(int)
2025-01-02 15:16:37.599 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertLawRecordInfo]}" onto public int com.bd.controller.HttpController.InsertLawRecordInfo(com.bd.entity.LawRecordInfo)
2025-01-02 15:16:37.599 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetUserOperationInfo]}" onto public java.lang.String com.bd.controller.HttpController.SetUserOperationInfo(com.bd.entity.UserOperation,javax.servlet.http.HttpServletRequest)
2025-01-02 15:16:37.599 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetJianCeShipInfo_Export],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetJianCeShipInfo_Export(java.lang.String)
2025-01-02 15:16:37.600 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHistoryWarningInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.BusinessManagement.AlarmRecord> com.bd.controller.HttpController.GetHistoryWarningInfo(int,java.lang.String,java.lang.String,int)
2025-01-02 15:16:37.600 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCameraInfoBySearch],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetCameraInfoBySearch(java.lang.String)
2025-01-02 15:16:37.600 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWhiteAndBlackListInfoByType],methods=[GET]}" onto public java.util.List<com.bd.entity.BlackAndWhiteList> com.bd.controller.HttpController.GetWhiteAndBlackListInfoByType(int)
2025-01-02 15:16:37.600 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTyphoonInfoById],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTyphoonInfoById(int)
2025-01-02 15:16:37.601 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWhiteAndBlackListInfoById],methods=[GET]}" onto public com.bd.entity.BlackAndWhiteList com.bd.controller.HttpController.GetWhiteAndBlackListInfoById(int)
2025-01-02 15:16:37.601 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/api/specialShip]}" onto public java.util.List<com.bd.entity.other.SpecialShip> com.bd.controller.HttpController.GetSpecialShipToZhongtai()
2025-01-02 15:16:37.601 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetWhiteOrBlackList]}" onto public java.lang.String com.bd.controller.HttpController.SetWhiteOrBlackList(com.bd.entity.BlackAndWhiteList)
2025-01-02 15:16:37.601 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteSpecialShip]}" onto public java.lang.String com.bd.controller.HttpController.DeleteSpecialShip(java.util.List<com.bd.entity.SpecialShipType>)
2025-01-02 15:16:37.602 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllBdMsgByUserId]}" onto public java.util.List<com.bd.entity.BdMsg> com.bd.controller.HttpController.GetAllBdMsgByUserId(int,int)
2025-01-02 15:16:37.602 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetJianCeShipInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetJianCeShipInfo(java.lang.String,int)
2025-01-02 15:16:37.602 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteShipFromTerm]}" onto public void com.bd.controller.HttpController.DeleteShipFromTerm(int)
2025-01-02 15:16:37.602 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCrewCertificateStatistics],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetCrewCertificateStatistics(com.bd.entity.dto.CrewCertificationQueryDto)
2025-01-02 15:16:37.603 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertShipForTerm]}" onto public void com.bd.controller.HttpController.InsertShipForTerm(int,int)
2025-01-02 15:16:37.603 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWSInShangHaiShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetWSInShangHaiShipInfo()
2025-01-02 15:16:37.603 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWSInPortShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetWSInPortShipInfo(int)
2025-01-02 15:16:37.603 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutPortShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetOutPortShipInfo()
2025-01-02 15:16:37.603 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteUserSetting],methods=[GET]}" onto public void com.bd.controller.HttpController.DeleteUserSetting(int,int)
2025-01-02 15:16:37.604 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetJianCeShipInfoById],methods=[GET]}" onto public com.bd.entity.JianCeShipInfo com.bd.controller.HttpController.GetJianCeShipInfoById(int)
2025-01-02 15:16:37.604 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipInfoByPort],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetShipInfoByPort(int)
2025-01-02 15:16:37.604 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetJianCeShipInfoByShipId],methods=[GET]}" onto public com.bd.entity.JianCeShipInfo com.bd.controller.HttpController.GetJianCeShipInfoByShipId(int)
2025-01-02 15:16:37.604 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWSIn168169ShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetWSIn168169ShipInfo()
2025-01-02 15:16:37.605 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetWarningToOtherAccountIsRead]}" onto public void com.bd.controller.HttpController.SetWarningToOtherAccountIsRead(int)
2025-01-02 15:16:37.605 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SendWarningToOtherAccount]}" onto public void com.bd.controller.HttpController.SendWarningToOtherAccount(int,int,int)
2025-01-02 15:16:37.605 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetlawCaseByShipId]}" onto public java.util.List<com.bd.entity.LawCase> com.bd.controller.HttpController.GetlawCaseByShipId(int)
2025-01-02 15:16:37.605 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateUserSetting]}" onto public void com.bd.controller.HttpController.UpdateUserSetting(java.util.List<com.bd.entity.UserSettingInfo>)
2025-01-02 15:16:37.606 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOutLineShipInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetOutLineShipInfo(java.lang.String)
2025-01-02 15:16:37.606 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOnLineShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetOnLineShipInfo()
2025-01-02 15:16:37.606 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetFishAreaShipInfo]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetFishAreaShipInfo(int)
2025-01-02 15:16:37.606 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWarningToOtherAccount]}" onto public java.util.List<com.bd.entity.PushInformation> com.bd.controller.HttpController.GetWarningToOtherAccount(int,int)
2025-01-02 15:16:37.607 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetInPortShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetInPortShipInfo(int)
2025-01-02 15:16:37.607 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWSInJinBuShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetWSInJinBuShipInfo()
2025-01-02 15:16:37.607 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/test],methods=[GET]}" onto public int com.bd.controller.HttpController.pp()
2025-01-02 15:16:37.607 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/QueryShip],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipSerch> com.bd.controller.HttpController.GetShipInfoByNameOrTerminalNumber(java.lang.String)
2025-01-02 15:16:37.607 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/Postppp],methods=[POST]}" onto public java.lang.String com.bd.controller.HttpController.post(java.lang.String,java.lang.String)
2025-01-02 15:16:37.608 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetOnePortInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortInfo> com.bd.controller.HttpController.GetOnePortInfo(int)
2025-01-02 15:16:37.608 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/Login]}" onto public com.bd.entity.UserInfo com.bd.controller.HttpController.Login(com.bd.entity.UserInfo,javax.servlet.http.HttpServletRequest)
2025-01-02 15:16:37.608 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/AddPort]}" onto public int com.bd.controller.HttpController.AddPort(java.lang.String,java.lang.String,int,int,java.lang.String,java.util.List<com.bd.entity.Coordinate>)
2025-01-02 15:16:37.608 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPortInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetPortInfo(int)
2025-01-02 15:16:37.609 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteUser]}" onto public java.lang.String com.bd.controller.HttpController.DeleteUser(java.util.List<java.lang.Integer>)
2025-01-02 15:16:37.609 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateUser]}" onto public java.lang.String com.bd.controller.HttpController.UpdateUser(int,com.bd.entity.UserInfo)
2025-01-02 15:16:37.609 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllPortInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortInfo> com.bd.controller.HttpController.GetAllPortInfo()
2025-01-02 15:16:37.609 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/resetErrorTimes]}" onto public void com.bd.controller.HttpController.resetErrorTimes(java.lang.String)
2025-01-02 15:16:37.609 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllPeople],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllPeople(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int,com.bd.entity.dto.PeopleQueryDto)
2025-01-02 15:16:37.609 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetPortShipCount],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipCount> com.bd.controller.HttpController.GetPortShipCount()
2025-01-02 15:16:37.609 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAreaInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.AreaInfo> com.bd.controller.HttpController.GetAreaInfo()
2025-01-02 15:16:37.609 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetSpecialShip],methods=[GET]}" onto public java.util.List<com.bd.entity.ShipDynamicInfo> com.bd.controller.HttpController.GetSpecialShip()
2025-01-02 15:16:37.609 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/EditPort]}" onto public java.lang.String com.bd.controller.HttpController.EditPort(int,java.lang.String,java.lang.String,int,int,java.lang.String,java.util.List<com.bd.entity.Coordinate>)
2025-01-02 15:16:37.610 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeletePort]}" onto public java.lang.String com.bd.controller.HttpController.DeletePort(java.util.List<java.lang.Integer>)
2025-01-02 15:16:37.610 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipManyRegis],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipManyRegis(int)
2025-01-02 15:16:37.610 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetInPortShipCount]}" onto public java.lang.String com.bd.controller.HttpController.GetShipCount()
2025-01-02 15:16:37.610 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllAreaInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllAreaInfo(int)
2025-01-02 15:16:37.611 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetLawRecordInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetLawRecordInfo(java.lang.String,java.lang.String,java.lang.String)
2025-01-02 15:16:37.611 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/api/shipCount]}" onto public java.lang.String com.bd.controller.HttpController.shipCount()
2025-01-02 15:16:37.611 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetServerState],methods=[GET]}" onto public com.bd.entity.TongjiModel com.bd.controller.HttpController.GetServerState(int)
2025-01-02 15:16:37.611 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllVoyageInfo]}" onto public java.lang.String com.bd.controller.HttpController.GetAllVoyageInfo(java.lang.String,int)
2025-01-02 15:16:37.612 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DLogin]}" onto public void com.bd.controller.HttpController.LoginFromIPMS(javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-01-02 15:16:37.612 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/LoginFromIPMS]}" onto public void com.bd.controller.HttpController.LoginFromIPMS(java.lang.String,javax.servlet.http.HttpServletResponse) throws java.lang.Exception
2025-01-02 15:16:37.612 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DLogin_XC]}" onto public void com.bd.controller.HttpController.LoginFromIPMS_XC(javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-01-02 15:16:37.612 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/LoginFromIPMS_XC]}" onto public void com.bd.controller.HttpController.LoginFromIPMS_XC(java.lang.String,javax.servlet.http.HttpServletResponse) throws java.lang.Exception
2025-01-02 15:16:37.612 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllInPortShip]}" onto public java.lang.String com.bd.controller.HttpController.GetAllInPortShip(java.lang.String,int)
2025-01-02 15:16:37.613 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllFocusShip]}" onto public java.lang.String com.bd.controller.HttpController.GetAllFocusShip(java.lang.String,java.lang.String,java.lang.String,int,int,int)
2025-01-02 15:16:37.613 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetFocusShip]}" onto public java.lang.String com.bd.controller.HttpController.GetFocusShip(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 15:16:37.613 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllFishShip]}" onto public java.lang.String com.bd.controller.HttpController.GetAllFishShip(java.lang.String,java.lang.String,java.lang.String,int,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,com.bd.entity.dto.ShipQueryDto)
2025-01-02 15:16:37.613 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/getCheckRecordByShipName],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.getCheckRecord(java.lang.String)
2025-01-02 15:16:37.613 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetSpecialShip],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.SetSpecialShip(int,int)
2025-01-02 15:16:37.614 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetUserOperation],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetUserOperation(java.lang.String,java.lang.String,java.lang.String,int,int)
2025-01-02 15:16:37.614 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHlsUrl]}" onto public java.lang.String com.bd.controller.HttpController.GetHlsUrl(int,java.lang.String)
2025-01-02 15:16:37.614 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetBdMsg]}" onto public java.lang.String com.bd.controller.HttpController.GetBdMsg(java.lang.String,java.lang.String,java.lang.String,java.lang.String,int)
2025-01-02 15:16:37.614 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetUserInfoById]}" onto public com.bd.entity.UserInfo com.bd.controller.HttpController.GetUserInfoById(int)
2025-01-02 15:16:37.614 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllBdMsg]}" onto public java.util.List<com.bd.entity.BdMsg> com.bd.controller.HttpController.GetAllBdMsg(java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-01-02 15:16:37.615 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllUser]}" onto public java.lang.String com.bd.controller.HttpController.GetAllUser(java.lang.String,int,int)
2025-01-02 15:16:37.615 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertBdMsg]}" onto public int com.bd.controller.HttpController.InsertBdMsg(com.bd.entity.BdMsg)
2025-01-02 15:16:37.615 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteBdMsg]}" onto public java.lang.String com.bd.controller.HttpController.DeleteBdMsg(java.util.List<java.lang.Integer>)
2025-01-02 15:16:37.615 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/EditAreaInfo]}" onto public java.lang.String com.bd.controller.HttpController.EditAreaInfo(com.bd.entity.AreaInfo)
2025-01-02 15:16:37.616 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeletedAreaInfo]}" onto public java.lang.String com.bd.controller.HttpController.DeletedAreaInfo(java.util.List<java.lang.Integer>)
2025-01-02 15:16:37.616 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetAreaInfo]}" onto public java.lang.String com.bd.controller.HttpController.SetAreaInfo(com.bd.entity.AreaInfo)
2025-01-02 15:16:37.616 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetTyphoonInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetTyphoonInfo()
2025-01-02 15:16:37.616 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetShipWithArea],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.SetShipWithArea(int,int)
2025-01-02 15:16:37.616 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHlsImg],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetHlsImg(int,int)
2025-01-02 15:16:37.617 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertUser]}" onto public java.lang.String com.bd.controller.HttpController.InsertUser(com.bd.entity.UserInfo)
2025-01-02 15:16:37.617 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetCameraInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.CameraInfo> com.bd.controller.HttpController.GetCameraInfo()
2025-01-02 15:16:37.617 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAreaInfoById],methods=[GET]}" onto public com.bd.entity.AreaInfo com.bd.controller.HttpController.GetAreaInfoById(int)
2025-01-02 15:16:37.617 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetWeather]}" onto public java.lang.String com.bd.controller.HttpController.GetWeather() throws java.lang.Exception
2025-01-02 15:16:37.617 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/InsertMarkInfo]}" onto public java.lang.String com.bd.controller.HttpController.InsertMarkInfo(com.bd.entity.MarkInfo)
2025-01-02 15:16:37.618 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/EditMarkInfo]}" onto public java.lang.String com.bd.controller.HttpController.EditMarkInfo(com.bd.entity.MarkInfo)
2025-01-02 15:16:37.618 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHlsUrlByIndex]}" onto public java.lang.String com.bd.controller.HttpController.GetHlsUrlByIndex(java.lang.String,java.lang.String)
2025-01-02 15:16:37.618 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/AddJianCeShip]}" onto public java.lang.String com.bd.controller.HttpController.AddJianCeShip(com.bd.entity.JianCeShipInfo)
2025-01-02 15:16:37.618 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipType],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllShipType(int,int)
2025-01-02 15:16:37.618 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllMarkInfo],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetAllMarkInfo(int)
2025-01-02 15:16:37.619 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipIdByName],methods=[GET]}" onto public int com.bd.controller.HttpController.GetShipIdByName(java.lang.String)
2025-01-02 15:16:37.619 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/CheckShipInTerm]}" onto public void com.bd.controller.HttpController.CheckShipInTerm(int,int)
2025-01-02 15:16:37.619 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetMapToken]}" onto public java.lang.String com.bd.controller.HttpController.GetMapToken()
2025-01-02 15:16:37.619 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetMarkInfoById],methods=[GET]}" onto public com.bd.entity.MarkInfo com.bd.controller.HttpController.GetMarkInfoById(int)
2025-01-02 15:16:37.619 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllWhiteList],methods=[GET]}" onto public java.util.List<com.bd.entity.BlackAndWhiteList> com.bd.controller.HttpController.GetAllWhiteList()
2025-01-02 15:16:37.620 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/UpdateCrewInfo],methods=[GET]}" onto private void com.bd.controller.HttpController.UpdateCrewInfo(int) throws java.lang.Exception
2025-01-02 15:16:37.620 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/AddShipTerm]}" onto public void com.bd.controller.HttpController.AddShipTerm(java.lang.String,int)
2025-01-02 15:16:37.620 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteJianCeShip]}" onto public java.lang.String com.bd.controller.HttpController.DeleteJianCeShip(java.util.List<java.lang.Integer>)
2025-01-02 15:16:37.620 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/DeleteShipTerm]}" onto public void com.bd.controller.HttpController.DeleteShipTerm(int)
2025-01-02 15:16:37.620 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipFromTerm]}" onto public java.util.List<com.bd.entity.ShipStaticInfo_all> com.bd.controller.HttpController.GetShipFromTerm(int)
2025-01-02 15:16:37.621 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipTerm]}" onto public java.util.List<com.bd.entity.Ship_Term> com.bd.controller.HttpController.GetShipTerm(int)
2025-01-02 15:16:37.621 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetHlsById],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetHlsById(int)
2025-01-02 15:16:37.621 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetBdMsgExample]}" onto public java.util.List<com.bd.entity.BDMsgExample> com.bd.controller.HttpController.GetBdMsgExample(int)
2025-01-02 15:16:37.621 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllBlackList],methods=[GET]}" onto public java.util.List<com.bd.entity.BlackAndWhiteList> com.bd.controller.HttpController.GetAllBlackList()
2025-01-02 15:16:37.621 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/CheckShipBout]}" onto public java.util.List<java.lang.Integer> com.bd.controller.HttpController.CheckShipBout(java.util.List<java.lang.Integer>)
2025-01-02 15:16:37.622 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetAllShipInfo],methods=[GET]}" onto public java.util.List<com.bd.entity.PortShipInfo> com.bd.controller.HttpController.GetAllShipInfo()
2025-01-02 15:16:37.622 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetUserCount],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetUserCount()
2025-01-02 15:16:37.622 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/series/add],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.addseries()
2025-01-02 15:16:37.622 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetIp]}" onto public java.lang.String com.bd.controller.HttpController.GetIp(javax.servlet.http.HttpServletRequest)
2025-01-02 15:16:37.622 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetManagerCount]}" onto public int com.bd.controller.HttpController.GetManagerCount(int)
2025-01-02 15:16:37.623 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetShipSetting],methods=[GET]}" onto public void com.bd.controller.HttpController.SetShipSetting(int,java.lang.String)
2025-01-02 15:16:37.623 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/excel/upload]}" onto public void com.bd.controller.HttpController.excelUpload(java.util.List<com.bd.entity.other.ExcelEntity>)
2025-01-02 15:16:37.623 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetNearestHls],methods=[GET]}" onto public java.util.List<java.lang.String> com.bd.controller.HttpController.GetNearestHls(java.lang.String)
2025-01-02 15:16:37.623 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/SetUserSetting]}" onto public void com.bd.controller.HttpController.SetUserSetting(java.util.List<com.bd.entity.UserSettingInfo>)
2025-01-02 15:16:37.624 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipSetting],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipSetting(int)
2025-01-02 15:16:37.624 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/isCheckAccount],methods=[GET]}" onto public com.bd.entity.UserInfo com.bd.controller.HttpController.isCheckAccount(java.lang.String)
2025-01-02 15:16:37.624 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetUserSetting]}" onto public java.util.List<com.bd.entity.UserSettingInfo> com.bd.controller.HttpController.GetUserSetting(int)
2025-01-02 15:16:37.624 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/web/GetShipImgUrl],methods=[GET]}" onto public java.lang.String com.bd.controller.HttpController.GetShipImgUrl(int)
2025-01-02 15:16:37.629 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-01-02 15:16:37.629 INFO  [restartedMain]o.s.w.s.m.method.annotation.RequestMappingHandlerMapping.register:547 -Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-01-02 15:16:37.680 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:16:37.680 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:16:37.680 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/map/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:16:37.680 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/wxMap/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:16:37.680 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/YimaMap1/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:16:37.680 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/YimaMap2/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:16:37.680 INFO  [restartedMain]o.s.web.servlet.handler.SimpleUrlHandlerMapping.registerHandler:373 -Mapped URL path [/shipImg/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-01-02 15:16:37.993 INFO  [restartedMain]o.s.boot.devtools.autoconfigure.OptionalLiveReloadServer.startServer:57 -LiveReload server is running on port 35729
2025-01-02 15:16:38.019 INFO  [restartedMain]o.s.jmx.export.annotation.AnnotationMBeanExporter.afterSingletonsInstantiated:433 -Registering beans for JMX exposure on startup
2025-01-02 15:16:38.021 INFO  [restartedMain]o.s.jmx.export.annotation.AnnotationMBeanExporter.autodetect:895 -Bean with name 'dataSource' has been autodetected for JMX exposure
2025-01-02 15:16:38.025 INFO  [restartedMain]o.s.jmx.export.annotation.AnnotationMBeanExporter.registerBeanInstance:668 -Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-01-02 15:16:38.044 INFO  [restartedMain]o.s.s.annotation.ScheduledAnnotationBeanPostProcessor.finishRegistration:275 -No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-01-02 15:16:38.053 INFO  [restartedMain]org.apache.coyote.http11.Http11NioProtocol.log:180 -Starting ProtocolHandler ["http-nio-7101"]
2025-01-02 15:16:38.060 INFO  [restartedMain]org.apache.tomcat.util.net.NioSelectorPool.log:180 -Using a shared selector for servlet write/read
2025-01-02 15:16:38.073 INFO  [restartedMain]o.s.boot.web.embedded.tomcat.TomcatWebServer.start:206 -Tomcat started on port(s): 7101 (http) with context path ''
2025-01-02 15:16:38.077 INFO  [restartedMain]com.bd.Application.logStarted:59 -Started Application in 5.506 seconds (JVM running for 7.115)
