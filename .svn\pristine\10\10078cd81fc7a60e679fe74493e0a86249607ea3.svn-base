package com.bd.thread;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bd.entity.FisheryPermitInfo;
import com.bd.entity.FishingBoatInfo;
import com.bd.mapper.CrewExamMapper;
import com.bd.service.UserService;
import com.bd.util.HttpTool;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Controller
@EnableScheduling
public class UpdateFishingBoatInfo {
    @Resource
    private CrewExamMapper crewExamMapper;

    @Resource
    private UserService userService;

    public List<String> GetFishingBoatInfoList(int pageNum) {
        String token = userService.GetOpenCenterToken();
        System.out.println("-.-" + token);
        String header = "Bearer " + token;
        String url = "http://10.90.7.5:18080/api/service/share/P431503333480468480?conditions=null&pageNum=" + pageNum + "&pageSize=" + 500;
        String lawData = HttpTool.doGet(url, header);
        JSONArray jsonArray = JSONArray.parseArray(lawData);

        List<String> list = new ArrayList<>();
        for (Object obj : jsonArray) {
            list.add(obj.toString());
        }
        return list;
    }

    public void updateLawCase() throws Exception{
        int pageNum = 1;
        List<String> infoList = new ArrayList<>();
        while (true){
            try{
                infoList = GetFishingBoatInfoList(pageNum);
            }catch (Exception e){
                continue;
            }
            List<FishingBoatInfo> boatInfos = new ArrayList<>();
            if(infoList.size() < 2) break;
            for (String jsonString : infoList) {
                JSONObject fishingBoatJson = JSONObject.parseObject(jsonString);

                FishingBoatInfo fishingBoatInfo = new FishingBoatInfo();
                fishingBoatInfo.setYcxxwybs(fishingBoatJson.getString("ycxxwybs"));
                fishingBoatInfo.setGxsj(fishingBoatJson.getString("gxsj"));
                fishingBoatInfo.setGxcz(fishingBoatJson.getString("gxcz"));
                fishingBoatInfo.setYcssdqdm(fishingBoatJson.getString("ycssdqdm"));
                fishingBoatInfo.setYcssdqmc(fishingBoatJson.getString("ycssdqmc"));
                fishingBoatInfo.setYcbm(fishingBoatJson.getString("ycbm"));
                fishingBoatInfo.setCm(fishingBoatJson.getString("cm"));
                fishingBoatInfo.setCmyw(fishingBoatJson.getString("cmyw"));
                fishingBoatInfo.setCbzl(fishingBoatJson.getString("cbzl"));
                fishingBoatInfo.setCz(fishingBoatJson.getString("cz"));
                fishingBoatInfo.setXk(fishingBoatJson.getString("xk"));
                fishingBoatInfo.setXs(fishingBoatJson.getString("xs"));
                fishingBoatInfo.setZdw(fishingBoatJson.getString("zdw"));
                fishingBoatInfo.setJdw(fishingBoatJson.getString("jdw"));
                fishingBoatInfo.setZjzgl(fishingBoatJson.getString("zjzgl"));
                fishingBoatInfo.setZjxhy(fishingBoatJson.getString("zjxhy"));
                fishingBoatInfo.setZjxhe(fishingBoatJson.getString("zjxhe"));
                fishingBoatInfo.setZjxhs(fishingBoatJson.getString("zjxhs"));
                fishingBoatInfo.setZjgly(fishingBoatJson.getString("zjgly"));
                fishingBoatInfo.setZjgle(fishingBoatJson.getString("zjgle"));
                fishingBoatInfo.setZjgls(fishingBoatJson.getString("zjgls"));
                fishingBoatInfo.setCtcz(fishingBoatJson.getString("ctcz"));
                fishingBoatInfo.setCtczyw(fishingBoatJson.getString("ctczyw"));
                fishingBoatInfo.setJzwgrq(fishingBoatJson.getString("jzwgrq"));
                fishingBoatInfo.setZjsl(fishingBoatJson.getString("zjsl"));
                fishingBoatInfo.setCbhhsbm(fishingBoatJson.getString("cbhhsbm"));
                fishingBoatInfo.setZczjsl(fishingBoatJson.getString("zczjsl"));
                fishingBoatInfo.setZczjxh(fishingBoatJson.getString("zczjxh"));
                fishingBoatInfo.setZccm(fishingBoatJson.getString("zccm"));
                fishingBoatInfo.setZcsl(fishingBoatJson.getString("zcsl"));
                fishingBoatInfo.setZczgl(fishingBoatJson.getString("zczgl"));
                fishingBoatInfo.setHyyyblxkzlb(fishingBoatJson.getString("hyyyblxkzlb"));
                fishingBoatInfo.setCbsyrmc(fishingBoatJson.getString("cbsyrmc"));
                fishingBoatInfo.setCbsyrmcyw(fishingBoatJson.getString("cbsyrmcyw"));
                fishingBoatInfo.setCbsyrdz(fishingBoatJson.getString("cbsyrdz"));
                fishingBoatInfo.setCbsyrdzyw(fishingBoatJson.getString("cbsyrdzyw"));
                fishingBoatInfo.setCbsyrdh(fishingBoatJson.getString("cbsyrdh"));
                fishingBoatInfo.setCblx(fishingBoatJson.getString("cblx"));
                fishingBoatInfo.setCblxyw(fishingBoatJson.getString("cblxyw"));
                fishingBoatInfo.setZjjhy(fishingBoatJson.getString("zjjhy"));
                fishingBoatInfo.setZjjhe(fishingBoatJson.getString("zjjhe"));
                fishingBoatInfo.setZjjhs(fishingBoatJson.getString("zjjhs"));
                fishingBoatInfo.setCwgjzbpzsbh(fishingBoatJson.getString("cwgjzbpzsbh"));
                fishingBoatInfo.setSkgl(fishingBoatJson.getString("skgl"));
                fishingBoatInfo.setHzcz(fishingBoatJson.getString("hzcz"));
                fishingBoatInfo.setHzzd(fishingBoatJson.getString("hzzd"));
                fishingBoatInfo.setEtl_time(fishingBoatJson.getString("etl_time"));

                boatInfos.add(fishingBoatInfo);
            }

            System.out.println("updateFisheryPermitInfo-pageNum:" + pageNum);
            //Thread.sleep(5000);
            pageNum ++;

            crewExamMapper.insertFishingBoatInfo(boatInfos);

        }
    }

    //@Scheduled(cron = "0 0 1 * * ?")
    public void run() throws Exception {
        crewExamMapper.deleteLawCase();
        updateLawCase();
    }
}
