package com.bd.service.impl;
import com.bd.entity.*;
import com.bd.entity.dto.ShipQueryDto;
import com.bd.entity.other.ExcelEntity;
import com.bd.entity.other.SpecialShip;
import com.bd.mapper.PortMapper;
import com.bd.mapper.ShipMapper;
import com.bd.service.ShipService;
import com.bd.thread.UdateCrewBx;
import com.bd.util.M_POINT;
import com.bd.util.Utils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
@Service
public class ShipImpl implements ShipService {
    @Resource
    private ShipMapper shipMapper;
    @Resource
    private UdateCrewBx crewBx;
    @Resource
    private PortMapper portMapper;

    @Override
    public List<Ship> GetBDShipPosition(int sec) {
        return shipMapper.GetBDShipPosition(sec);
    }
    @Override
    public List<ShipDynamicInfo> GetBDShipPosition_ronghe(int sec) {
        return shipMapper.GetBDShipPosition_ronghe(sec);
    }
    @Override
    public List<ShipDynamicInfo> GetBDShipPosition_ronghe_strTime(String time) {
        return shipMapper.GetBDShipPosition_ronghe_strTime(time);
    }
    @Override
    public List<ShipDynamicInfo> GetAllBDShipPosition_ronghe(int pageNum) {
        return shipMapper.GetAllBDShipPosition_ronghe(pageNum);
    }
    @Override
    public int GetBDShipPosition_rongheCount() {
        return shipMapper.GetBDShipPosition_rongheCount();
    }
    @Override
    public ShipDynamicInfo GetOneShipDynamicInfoById(int id) {
        return shipMapper.GetOneShipDynamicInfoById(id);
    }
    @Override
    public List<Ship> GetBDShipPositionOtherProvinces(int sec) {
        return shipMapper.GetBDShipPositionOtherProvinces(sec);
    }
    @Override
    public void UpdateShipInPortState(int portId, int state, int ID) {
        shipMapper.UpdateShipInPortState(portId, state, ID);
    }
    @Override
    public void InsertOutInPortRecord(ShipDynamicInfo ship) {
        shipMapper.InsertOutInPortRecord(ship);
        OutInPortRecord outInPortRecord = portMapper.GetOutInPortRecordByShipId(String.valueOf(ship.getStaticShipId()),-1).get(0);

        crewBx.insert401AlarmInfo(outInPortRecord);
    }
    @Override
    public List<ShipTrack> GetShipHistoryTrackById(int id, long startTime, long endTime, int addAis, int addBd, int bOutSide) {
        return shipMapper.GetShipHistoryTrackById(id, startTime, endTime, addAis, addBd, bOutSide);
    }
    @Override
    public int selectshipidfromterm(String BDID) {
        return shipMapper.selectshipidfromterm(BDID);
    }
    @Override
    public List<ShipDynamicInfo> GetImportanceShip() {
        return shipMapper.GetImportanceShip();
    }
    @Override
    public void SetImportanceShip(int id) {
        shipMapper.SetImportanceShip(id);
    }
    @Override
    public void DeleteImportanceShip(int id) {
        shipMapper.DeleteImportanceShip(id);
    }
    @Override
    public void SetFuxiuWhiteShip(int id) {
        shipMapper.SetFuxiuWhiteShip(id);
    }
    @Override
    public void DeleteFuxiuWhiteShip(int id) {
        shipMapper.DeleteFuxiuWhiteShip(id);
    }
    @Override
    public List<ShipDynamicInfo> GetSpecialShip() {
        return shipMapper.GetSpecialShip();
    }
    @Override
    public List<Ship_Yuzheng> GetYuzhengShipInfo() {
        return shipMapper.GetYuzhengShipInfo();
    }
    @Override
    public int GetRegisteredFish() {
        return shipMapper.GetRegisteredFish();
    }
    @Override
    public int GetInPortFish() {
        return shipMapper.GetInPortFish(Utils.GetNowTimeString(24*3600));
    }
    @Override
    public int GetOutPortFish() {
        return shipMapper.GetOutPortFish(Utils.GetNowTimeString(24*3600));
    }
    @Override
    public int GetWsInPortFish() {
        return shipMapper.GetWsInPortFish(Utils.GetNowTimeString(24*3600));
    }
    @Override
    public int getAllShipCount() {
        return shipMapper.getAllShipCount();
    }
    @Override
    public int getOnlineShipCount() {
        return shipMapper.getOnlineShipCount(Utils.GetNowTimeString(24*3600));
    }
    @Override
    public int GetInPortFish2(int portId) {
        return shipMapper.GetInPortFish2(portId, Utils.GetNowTimeString(24*3600));
    }
    @Override
    public int GetWSInShanghai() {
        return shipMapper.GetWSInShanghai(Utils.GetNowTimeString(24*3600));
    }
    @Override
    public int GetWSInJinbu() {
        return shipMapper.GetWSInJinbu(Utils.GetNowTimeString(24*3600));
    }
    @Override
    public int GetWSInPort(int portId) {
        return shipMapper.GetWSInPort(portId, Utils.GetNowTimeString(24*3600));
    }
    @Override
    public List<SpecialShip> GetSpecialShipToZhongtai() {
        return shipMapper.GetSpecialShipToZhongtai();
    }
    @Override
    public List<ShipOnlineCount> GetShipOnlineStatistics(String shipName, String startTime, String endTime, int pageNum) {
        return shipMapper.GetShipOnlineStatistics(shipName, startTime, endTime, pageNum);
    }
    @Override
    public List<ShipOnlineCount> GetAllShipOnlineStatistics(String shipName, String startTime, String endTime) {
        return shipMapper.GetAllShipOnlineStatistics(shipName, startTime, endTime);
    }
    @Override
    public int GetShipOnlineStatisticsCount(String shipName, String startTime, String endTime) {
        return shipMapper.GetShipOnlineStatisticsCount(shipName, startTime, endTime);
    }
    @Override
    public void SetShipWithArea(int shipId, int areaId) {
        shipMapper.SetShipWithArea(shipId, areaId);
    }
    @Override
    public List<ShipTrack> GetPlayAreaHistoryInfo(String startTime, String endTime, String minLon, String maxLon, String minLat, String maxLat, int addAis) {
        return shipMapper.GetPlayAreaHistoryInfo(startTime, endTime, minLon, maxLon, minLat, maxLat, addAis);
    }
    @Override
    public List<JianCeShipInfo> GetJianCeShipInfo(String shipName, int pageNum) {
        return shipMapper.GetJianCeShipInfo(shipName, pageNum);
    }
    @Override
    public int GetJianCeShipInfoCount(String shipName) {
        return shipMapper.GetJianCeShipInfoCount(shipName);
    }
    @Override
    public List<JianCeShipInfo> GetJianCeShipInfo_Export(String shipName) {
        return shipMapper.GetJianCeShipInfo_Export(shipName);
    }
    @Override
    public void DeleteJianCeShip(int id) {
        shipMapper.DeleteJianCeShip(id);
    }
    @Override
    public void AddJianCeShip(JianCeShipInfo jianCeShipInfo) {
        shipMapper.AddJianCeShip(jianCeShipInfo);
    }
    @Override
    public List<BlackAndWhiteList> GetWhiteAndBlackListInfoByType(int type) {
        return shipMapper.GetWhiteAndBlackListInfoByType(type);
    }
    @Override
    public BlackAndWhiteList GetWhiteAndBlackListInfoById(int typeId) {
        return shipMapper.GetWhiteAndBlackListInfoById(typeId);
    }
    @Override
    public void SetWhiteOrBlackList(BlackAndWhiteList blackAndWhiteList) {
        shipMapper.SetWhiteOrBlackList(blackAndWhiteList);
    };
    @Override
    public void DeleteWhiteOrBlackList(int id) {
        shipMapper.DeleteWhiteOrBlackList(id);
    };
    @Override
    public void UpdateWhiteOrBlackList(BlackAndWhiteList blackAndWhiteList) {
        shipMapper.UpdateWhiteOrBlackList(blackAndWhiteList);
    };
    @Override
    public ShipCount GetShipCount() {
        return shipMapper.GetShipCount();
    }
    @Override
    public List<BlackAndWhiteList> GetAllWhiteList() {
        return shipMapper.GetAllWhiteList();
    };
    @Override
    public List<BlackAndWhiteList> GetAllBlackList() {
        return shipMapper.GetAllBlackList();
    };
    @Override
    public List<BlackAndWhiteList> GetAllWhiteAndBlackShip(String shipName, String bdId, String mmsi, int shipType, int specialType) {
        return shipMapper.GetAllWhiteAndBlackShip(shipName, bdId, mmsi, shipType, specialType);
    };
    @Override
    public List<BlackAndWhiteList> GetWhiteAndBlackShip(String shipName, String bdId, String mmsi, int shipType, int specialType, int pageNum) {
        return shipMapper.GetWhiteAndBlackShip(shipName, bdId, mmsi, shipType, specialType, pageNum);
    };
    @Override
    public int GetWhiteAndBlackShipCount(String shipName, String bdId, String mmsi, int shipType, int specialType) {
        return shipMapper.GetWhiteAndBlackShipCount(shipName, bdId, mmsi, shipType, specialType);
    }
    @Override
    public void AddShipTerm(String name, int userId) {
        shipMapper.AddShipTerm(name, userId);
    }
    @Override
    public void DeleteShipTerm(int id) {
        shipMapper.DeleteShipTerm(id);
    }
    @Override
    public List<Ship_Term> GetShipTerm(int userId) {
        return shipMapper.GetShipTerm(userId);
    }
    @Override
    public void InsertShipForTerm(int termId, int staticShipId) {
        if(shipMapper.CheckShipInTerm(termId, staticShipId) > 0) return;
        shipMapper.InsertShipForTerm(termId, staticShipId);
    }
    @Override
    public void DeleteShipFromTerm(int id) {
        shipMapper.DeleteShipFromTerm(id);
    }
    @Override
    public List<ShipStaticInfo_all> GetShipFromTerm(int termId) {
        return shipMapper.GetShipFromTerm(termId);
    }
    @Override
    public int CheckShipInTerm(int termId, int staticShipId) {
        return shipMapper.CheckShipInTerm(termId, staticShipId);
    }
    @Override
    public void InsertShipVoyage(Ship_Voyage voyage) {
        shipMapper.InsertShipVoyage(voyage);
    }
    @Override
    public List<ShipDynamicInfo> GetBDShipPosition_ronghe_strTime_thread(String getNowTimeString) {
        return shipMapper.GetBDShipPosition_ronghe_strTime_thread(getNowTimeString);
    }
    @Override
    public int CheckShipBout(int shipId) {
        return shipMapper.CheckShipBout(shipId);
    }
    @Override
    public Ship_DistributeCount GetShipdistribute(int portId) {
        return shipMapper.GetShipdistribute(portId, Utils.GetNowTimeString(3600 * 24));
    }
    @Override
    public List<ShipDynamicInfo> GetBDShipPosition_ronghe_strTime_thread_ws(String getNowTimeString) {
        return shipMapper.GetBDShipPosition_ronghe_strTime_thread_ws(getNowTimeString);
    }
    @Override
    public void updateShipInShanghaiState(ShipDynamicInfo ship, int state) {
        shipMapper.updateShipInShanghaiState(ship, state);
    }
    @Override
    public JianCeShipInfo GetJianCeShipInfoByShipId(int shipId) {
        return shipMapper.GetJianCeShipInfoByShipId(shipId);
    }
    @Override
    public JianCeShipInfo GetJianCeShipInfoById (int id) {
        return shipMapper.GetJianCeShipInfoById(id);
    }
    @Override
    public String GetshipNameById(int shipId) {
        return shipMapper.GetshipNameById(shipId);
    }
    @Override
    public String GetShipImgUrl(int shipId) {
        return shipMapper.GetShipImgUrl(shipId);
    }
    @Override
    public String GetShipSetting(int userId) {
        return shipMapper.GetShipSetting(userId);
    }
    @Override
    public void SetShipSetting(int userId, String content) {
        shipMapper.SetShipSetting(userId, content);
    }
    @Override
    public void updateFishAreaId(ShipDynamicInfo ship, int name) {
        shipMapper.updateFishAreaId(ship, name);
    }
	@Override
    public int addExcelEntity(List<ExcelEntity> entities) {
        return shipMapper.updateExcelEntity(entities);
    }
    @Override
    public List<String> GetTrackBdidList() {
        return shipMapper.GetTrackBdidList();
    }
    @Override
    public int GetStaticshipidByBdid(String bdid) {
        return shipMapper.GetStaticshipidByBdid(bdid);
    }
    @Override
    public void InsertTrack(int staticshipid, String bdid) {
        shipMapper.InsertTrack(staticshipid, bdid);
    }
    @Override
    public int GetstaticshipidCount(String bdid) {
        return shipMapper.GetstaticshipidCount(bdid);
    }

    @Override
    public CheckRecord getCheckRecordByShipName(String shipName) {
        return shipMapper.getCheckRecordByShipName(shipName);
    }

    @Override
    public void UpdateShipSetting(int userId, String content) {
        shipMapper.UpdateShipSetting(userId, content);
    }

    @Override
    public List<String> GetshipNamesByIds(List<Integer> shipIds) {
        return shipMapper.GetshipNamesByIds(shipIds);
    }

    /*@Override
    public List<M_POINT> GetAllShipsPositionByIds(List<Integer> shipIds) {
        return shipMapper.GetAllShipsPositionByIds(shipIds);
    }*/
}
