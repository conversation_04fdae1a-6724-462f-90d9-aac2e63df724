package com.bd.entity;

import java.io.Serializable;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 
 * @TableName SHIP_ENCRYPTION
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShipEncryption implements Serializable {
    /**
     * 
     */
    private Integer shipId;

    /**
     * 离线原因
     */
    private String outLineReason;

    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ShipEncryption that = (ShipEncryption) o;
        return Objects.equals(shipId, that.shipId) && Objects.equals(outLineReason, that.outLineReason);
    }

    @Override
    public int hashCode() {
        return Objects.hash(shipId, outLineReason);
    }

    @Override
    public String toString() {
        return "ShipEncryption{" +
                "shipId=" + shipId +
                ", outlineReason='" + outLineReason + '\'' +
                '}';
    }
}