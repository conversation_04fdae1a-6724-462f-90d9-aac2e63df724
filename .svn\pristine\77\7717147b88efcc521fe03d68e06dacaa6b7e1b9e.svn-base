export default function getCurTime(timeType){
    var myDate = new Date();
    var year = myDate.getFullYear();    //获取完整的年份(4位,1970-????)
    var month = myDate.getMonth() + 1;       //获取当前月份(0-11,0代表1月)
    var date = myDate.getDate();        //获取当前日(1-31)
    var hour = myDate.getHours();       //获取当前小时数(0-23)
    var minute = myDate.getMinutes();     //获取当前分钟数(0-59)
    var second = myDate.getSeconds();     //获取当前秒数(0-59)
    month = month <= 9 ? "0" + month : month;
    date = date <= 9 ? "0" + date : date;
    hour = hour <= 9 ? "0" + hour : hour;
    minute = minute <= 9 ? "0" + minute : minute;
    second = second <= 9 ? "0" + second : second;
    var timeElement = {year,month,date,hour,minute,second};
    return timeElement;
    
}
