<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="User Data Source" uuid="b6e75e25-0964-4c89-b66f-09ced77c8ea0">
      <synchronize>true</synchronize>
      <configured-by-url>true</configured-by-url>
      <jdbc-driver>dm.jdbc.driver.DmDriver</jdbc-driver>
      <jdbc-url>jdbc:dm://10.1.1.17:5236</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>