package com.bd.service;

import com.bd.entity.*;
import com.bd.entity.BusinessManagement.AlarmRecord;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface UserService {

    UserInfo GetUser(UserInfo userInfo);
    int GetUserName(String username);
    UserInfo GetUserInfoByUsername(String username);
    UserInfo GetUserInfoById(int id);
    List<UserInfo> GetAllUser(String userName, int userType, int pageNum);
    int GetAllUserCount(String userName, int userType);

    void InsertUser(UserInfo user);

    List<UserInfo> GetUserInfo();
    void UpdateUser(int userId, UserInfo userInfo);
    void DeleteUser(int id);

    TongjiModel GetCPUState();

    TongjiModel GetNeiCunState();

    TongjiModel GetYingPanState();

    TongjiModel GetNetState();

    void addUserOperationInfo(UserOperation userOperation);

    List<UserOperation> GetUserOperation(String userName, String startTime, String endTime, int type, int pageNum);

    int GetUserOperationCount(String userName, String startTime, String endTime, int type);

    int GetUserCount(int type);

    void UpdateToken(String access_token);

    String GetMapToken();

    int GetErrorCount(String name);

    void SetErrorCount(String name, int count);

    void SetUserSetting(UserSettingInfo userSettingInfo);

    void UpdateUserSetting(UserSettingInfo userSettingInfo);

    void DeleteUserSetting(int userId, int setting);

    List<UserSettingInfo> GetUserSetting(int userId);

    UserInfo isCheckAccount(String sendName);

    void SendWarningToOtherAccount(int sendId, int receiveId, int warningId);

    String GetOpenCenterToken();

    String GetOpenCenterToken_HIK();

    void UpdateOpenCenterToken(String token);

    void UpdateOpenCenterToken_HIK(String token);

    List<PushInformation> GetWarningToOtherAccount(int userId, int model);

    void SetWarningToOtherAccountIsRead(int userId);

    void deletePortNode();

    String getUserPermissionAreas(int id);
}
