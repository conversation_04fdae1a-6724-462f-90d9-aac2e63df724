package com.bd.service;
import com.bd.entity.BusinessManagement.*;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface LawEnforcementService {
    List<LawEnforcementRecords> GetPlatformCommunicationAndLawEnforcementRecords(String name, String startTime, String endTime, int pageNum);
    int GetPlatformCommunicationAndLawEnforcementRecordsCount(String name, String startTime, String endTime);
}
