/**
 * Created by promoter on 2020-10-13.
 */

//绘制类型结构体
var DynamicSymbolType =
{
    none: 0, //无状态
    drawPoint: 1, //绘制点
    drawLine: 2, //绘制线
    drawFace: 3, //绘制面
};

function init() {
    var objMapInfo = [];
    var mapObj = document.getElementById("map");
    API_SetMapImgMode(1);
    API_SetMapMinMaxLevel(3, 17);

    objMapInfo.div = "map"; //海图容器div的id
    objMapInfo.model = "pc"; //用于pc环境

    API_InitYimaMap(objMapInfo);
    //API_SetMapViewCenter(125.5, 31.5, null);

    API_SetShowToolBarOrNot(true, 80, 50); //显示工具条

    API_SetMapImagesUrl("MSAChartImages/");
    //API_SetMapImagesUrl("../");
    //API_SetMapImagesUrl("http://localhost:8090/");
    //API_SetMapImagesUrl("http://localhost:8090/map/");
    // API_SetMapImagesUrl("http://www.yimasoftdemo.cc:800/mapimg/");

    API_SetDrawCenterPoToFixedLen(6);
    API_SetMousePoInfoDivPosition(true, 20, 20); //显示鼠标位置
    API_SetScaleInfoDivPosition(true, 20, 20); //显示比例尺位置
    API_SetScaleLenInfoPosition(true, 20, 50); //显示比例尺长度

    API_SetMapLevel(10, { x: 121.7, y: 31.3 });

    API_SetSelectAllObjectByMouseMove(50000000, true);

    AddLayer();
    AddAdministrativeAreaLayer();
    AddAdministrativeArea();

    setTimeout(API_ReDrawLayer, 500);
}

//读取行政区省份地理信息jason文件
function ReadAdministrativeAreaJasonFile(file, callback) {
    var rawFile = new XMLHttpRequest();
    rawFile.overrideMimeType("application/json");
    rawFile.open("GET", file, true);
    rawFile.onreadystatechange = function() {
        if (rawFile.readyState === 4 && rawFile.status == "200") {
            callback(rawFile.responseText);
        }
    }
    rawFile.send(null);
}

//添加行政区域 操作
function AddAdministrativeArea() {
    ReadAdministrativeAreaJasonFile("provinceJason/china.json",  AdministrativeAreaJasonStr);
}

//解析省份jason信息
function AdministrativeAreaJasonStr(text){
    var data = JSON.parse(text);
    // var provinceCount = data["features"].length;
    // //输出省份名称
    // for (var i = 0; i < provinceCount; i++) {
    //     console.info(i + ":" + data["features"][i]["properties"]["name"]);
    // }

    var selectProvince = [8, 9, 10, 11, 12, 13]; //上海、江苏、浙江、安徽、福建、江西、

    for (var i = 0; i < selectProvince.length; i++) {
        var privicePos = selectProvince[i];
        var proviceName = data["features"][privicePos]["properties"]["name"]; //省份名称
        var coorsArr = data["features"][privicePos]["geometry"]["coordinates"][0][0];//省份坐标数组
        AddAdministrativeAreaObjectToMap(proviceName, coorsArr);
    }

    API_ReDrawLayer();
}

//添加省份行政区域物标
function AddAdministrativeAreaObjectToMap(proviceName, coorsArr){
    var layerPos = API_GetLayerPosById(g_iAdministrativeAreaLayerId); //获取图层pos
    if(layerPos == -1) return;

    g_iAddObjId++;
    var objInfo = [];
    var arrExpAttrValue = []; //扩展字段，假如没有可以传入null

    objInfo.layerPos = layerPos; //图层索引
    objInfo.objId = g_iAddObjId; //物标id
    objInfo.name = proviceName;//物标名称
    objInfo.showText = proviceName;//显示内容
    objInfo.layerStylePos = g_iAdministrativeAreaStylePos; //使用样式索引
    objInfo.objType = 3;
    arrExpAttrValue.push(proviceName + "_aa");// 设置物标扩展属性值信息。

    //坐标转换
    var arrCurGeoPo = [];
    var iCount = coorsArr.length;
    for (var i = 0; i < iCount; i++) {
        arrCurGeoPo.push({ x: parseFloat(coorsArr[i][0]) * 10000000, y: parseFloat(coorsArr[i][1]) * 10000000 } );
    }

    var objPos = API_AddNewObject(objInfo, arrCurGeoPo, arrExpAttrValue); //添加物标
    if(objPos == -1) return;
}

//添加标记
function AddMarkPoint(){
    //设置标记颜色样式：
    var style = { pointSize: 5, pointColor: "#00ff00", lineColor: "#f0f0f0",  lineWidth:2,
    textFont:"24px 黑体", textColor: "#00ffff", selectFillColor: "#02E4FC", textBorderColor: "#02E4FC",
        textBorderWidth: 1 };

    API_SetMarkPointStyle(style);

    //标记1
    var lineGeoPoints = [];
    lineGeoPoints.push({x:1230000000, y:320000000});
    lineGeoPoints.push({x:1240000000, y:325000000});

    API_AddMarkPointInfo({x:1230000000, y:320000000}, lineGeoPoints, "@111", "11111111111111");

    //标记2
    var lineGeoPoints2 = [];
    lineGeoPoints2.push({x:1230000000, y:420000000});
    lineGeoPoints2.push({x:1240000000, y:425000000});

    API_AddMarkPointInfo({x:1230000000, y:420000000}, lineGeoPoints2, "@22", "22222222222222");

}

function ClearMarkPoint() {
    API_ClearMarkPointInfo();
}

//演示跳转动画
//地图支持跳转动画，比如2秒内从中国地图放大到东南五省，有一定的动画效果;
function JumpAnimation(endGeo){
    var startGeo = API_GetCurMapCenterLonLatPo();//获取当前屏幕中心点
    //返回值：当前屏幕中心点经纬度(度)，格式{x:121.21,y:31.45}
    var stratLon = startGeo.x;//屏幕中心的经度
    var startLat = startGeo.y//屏幕中心的纬度

    var endLon   = endGeo.x;//需求的经度
    var endLat   = endGeo.y//需求的纬度

    API_SetMapLevel(7 , startGeo); //7
    var scale = API_GetCurMapScale();//获取当前比例尺

    var startPo = API_GetScrnPoByLonLatPo(stratLon, startLat, scale);//屏幕中心的屏幕坐标
    var endPo   = API_GetScrnPoByLonLatPo(endLon, endLat, scale);    //需求的屏幕坐标

    var startX = startPo.x;
    var startY = startPo.y;

    var endX = endPo.x;
    var endY = endPo.y;

    var x = endX-startX;
    var y = endY-startY;

    var initial = 400;//每次移动距离
    var times = 0;

    var absX = Math.abs(x);
    var absY = Math.abs(y);

    var timesRun = Math.ceil((absX + absY) / initial);
    timesRun += 1;//移动次数

    var interval = setInterval(function(){
        times += 1;
        if(times === timesRun){
            clearInterval(interval);
        }
        API_MoveMapViewByMoveSize(x/timesRun, y/timesRun, false);
    }, 200);

    //一次性放大
    setTimeout(function (){
        API_SetMapLevel(8, endGeo);
    },timesRun * 200 + 300);
}


//绘制图形
function DrawDynamicSymbol(type) {

    document.getElementById("objContentSpan").innerHTML = "物标备注：";
    switch (type) {
        case DynamicSymbolType.drawPoint: //绘制点
            API_SetCurDrawDynamicUseType(DynamicSymbolType.drawPoint);
            ShowDivBoxOrNot("drawObjBox", true);
            document.getElementById("drawObjType").value = DynamicSymbolType.drawPoint;
            break;

        case DynamicSymbolType.drawLine: //绘制线
            API_SetCurDrawDynamicUseType(DynamicSymbolType.drawLine);
            ShowDivBoxOrNot("drawObjBox", true);
            document.getElementById("drawObjType").value = DynamicSymbolType.drawLine;
            break;

        case DynamicSymbolType.drawFace: //绘制面
            API_SetCurDrawDynamicUseType(DynamicSymbolType.drawFace);
            ShowDivBoxOrNot("drawObjBox", true);
            document.getElementById("drawObjType").value = DynamicSymbolType.drawFace;
            break;
        default:
            break;
    }
};

var g_showSimpleInfoDiv = null;//显示信息div
var g_iPointLayerId = 100; //点图层的id
var g_iPointStylePos = 0;//样式pos
var g_iLineLayerId = 200; //线图层的id
var g_iLineStylePos = 0;
var g_iFaceLayerId = 300; //面图层的id
var g_iFaceStylePos = 0;
var g_iAdministrativeAreaLayerId= 301; //行政区面图层id
var g_iAdministrativeAreaStylePos = 0; //行政区面图层样式Pos
var g_iAddObjId = 0;

//--------------------------添加图层，添加物标前，必须先创建图层，这样才可以把物标添加到图层里面，一般初始化就添加好
//添加行政区面图层
function AddAdministrativeAreaLayer() {
    var faceLayerInfo = [];
    faceLayerInfo.id = g_iAdministrativeAreaLayerId;
    faceLayerInfo.type = 3; //类型：1=点图层，2=线图层，3=面图层
    faceLayerInfo.name = "行政区"; //图层名称
    faceLayerInfo.bShow = true; //显示
    var faceLayerPos = API_AddNewLayer(faceLayerInfo,null); //添加图层，得到图层的pos
    if (faceLayerPos > -1) {
        var faceStyle = [];
        faceStyle.borderWith = 1; //线的粗细
        faceStyle.borderColor = "#092ee8"; //线的颜色
        faceStyle.bFilled = true; //是否填充颜色
        faceStyle.fillColor = "#02E4FC"; //填充的颜色
        faceStyle.iOpacity = 50; //透明度
        faceStyle.bShowText = true; //是否显示名称
        faceStyle.textColor = "#000000"; //名称颜色
        faceStyle.fontSize = "12px"; //名称字体大小
        faceStyle.iTextOpacity = 80; //透明度
        faceStyle.iLineOpacity = 100;

        g_iAdministrativeAreaStylePos = API_AddFaceLayerStyleByPos(faceLayerPos, faceStyle);
    }
}

//添加基本的点、线、面三个图层
function AddLayer() {
    //这里演示添加3个图层，分别是点物标图层、线物标图层、面物标图层

    //--------------------------添加点物标图层-----------------------------------
    var pointLayerInfo = [];
    pointLayerInfo.id = g_iPointLayerId;
    pointLayerInfo.type = 1;//类型：1=点图层，2=线图层，3=面图层
    pointLayerInfo.name = "点图层";//图层名称
    pointLayerInfo.bShow = true; //显示

    pointLayerInfo.minShowScale = 1;//最大比例尺
    pointLayerInfo.maxShowScale = 2000000000;//最小比例尺
    pointLayerInfo.bShowTextOrNot = true;//是否显示名称
    pointLayerInfo.iStartShowTextScale = 5000000;//开始显示名称的最小比例尺

    var pointLayerPos = API_AddNewLayer(pointLayerInfo,null); //添加图层，得到图层的pos
    if (pointLayerPos > -1) {
        var pointStyle = [];
        
        //点图片样式
        pointStyle.strImgSrc = "img/light.png"; //图片地址
        pointStyle.iImgWidth = 20; //图片的宽度
        pointStyle.iImgHeight = 30; //图片的高度
        pointStyle.offsetScrnPo = {x:0,y:-15};//显示的偏移量，(0,0)为图片中心


        /*
         //点矢量符号样式
         var arrSymbolPo = [];//矢量符号坐标
         arrSymbolPo.push({ x: 0, y: 15 });
         arrSymbolPo.push({ x: 15, y: -15 });
         arrSymbolPo.push({ x: -15, y: -15 });
         pointStyle.arrSymbolPo = arrSymbolPo;
         */


        //小圆符号样式
        //pointStyle.iCircleR = 5;//圆半径

        pointStyle.bShowImg = true;
        pointStyle.bShowText = true; //是否显示名称
        pointStyle.textColor = "#FF0000"; //名称颜色
        pointStyle.fontSize = "12px"; //名称字体大小
        pointStyle.iOpacity = 100;
        pointStyle.iTextOpacity = 10; //透明度
        pointStyle.bFilled = true; //是否填充颜色
        pointStyle.fillColor = "#ee5d72"; //填充的颜色
        g_iPointStylePos = API_AddPointLayerStyleByPos(pointLayerPos, pointStyle);

        API_SetLayerTextBackGroundColorByPos(pointLayerPos,true,"#FF0000",50);//设置文字背景颜色
    }

    //---------------------------------添加线物标图层----------------------------

    var lintLayerInfo = [];
    lintLayerInfo.id = g_iLineLayerId;
    lintLayerInfo.type = 2; //类型：1=点图层，2=线图层，3=面图层
    lintLayerInfo.name = "线图层"; //图层名称
    lintLayerInfo.bShow = true; //显示
    var lineLayerPos = API_AddNewLayer(lintLayerInfo, null); //添加图层，得到图层的pos

    if (lineLayerPos > -1) {
        var lineStyle = [];
        lineStyle.borderWith = 1; //线的粗细
        lineStyle.borderColor = "#0000FF"; //线的颜色
        lineStyle.iOpacity = 80; //透明度
        lineStyle.bShowText = true; //是否显示名称
        lineStyle.textColor = "#000000"; //名称颜色
        lineStyle.fontSize = "12px"; //名称字体大小
        lineStyle.iTextOpacity = 80; //透明度

        g_iLineStylePos = API_AddLineLayerStyleByPos(lineLayerPos, lineStyle);
        API_SetLayerTextBackGroundColorByPos(lineLayerPos,true, "#00FF00", 50); //设置文字背景颜色
    }

    //-------------------------------------添加面物标图层---------------------
    var faceLayerInfo = [];
    faceLayerInfo.id = g_iFaceLayerId;
    faceLayerInfo.type = 3; //类型：1=点图层，2=线图层，3=面图层
    faceLayerInfo.name = "面图层"; //图层名称
    faceLayerInfo.bShow = true; //显示
    var faceLayerPos = API_AddNewLayer(faceLayerInfo,null); //添加图层，得到图层的pos
    if (faceLayerPos > -1) {
        var faceStyle = [];
        faceStyle.borderWith = 1; //线的粗细
        faceStyle.borderColor = "#092ee8"; //线的颜色
        faceStyle.bFilled = true; //是否填充颜色
        faceStyle.fillColor = "#FFFFFF"; //填充的颜色
        faceStyle.iOpacity = 50; //透明度
        faceStyle.bShowText = true; //是否显示名称
        faceStyle.textColor = "#000000"; //名称颜色
        faceStyle.fontSize = "12px"; //名称字体大小
        faceStyle.iTextOpacity = 80; //透明度
        faceStyle.iLineOpacity = 100;

        g_iFaceStylePos = API_AddFaceLayerStyleByPos(faceLayerPos, faceStyle);
        API_SetLayerTextBackGroundColorByPos(faceLayerPos,true, "#0000FF", 50); //设置文字背景颜色
    }

}

//保存物标信息
function AddCurDrawObject() {
    var objType = document.getElementById("drawObjType").value;
    var objName = document.getElementById("drawObjName").value;
    var objContent = document.getElementById("drawObjContent").value;
    var arrObjPo = API_GetCurDrawDynamicObjGeoPo(); //这里坐标从组件中获取，假如要修改的话，就不能这样获取了，只能从物标信息面板中获取
    var drawObjPoNum = arrObjPo.length;

    var strGeoValue = "";
    for (var i = 0; i < arrObjPo.length; i++) {
        strGeoValue += "_" + parseInt(arrObjPo[i].x) + "," + parseInt(arrObjPo[i].y);
    }

    if (objName == "") {
        alert("请输入标注名称！");
        return;
    }

    if (objType == "3" && drawObjPoNum < parseInt(3)) {
        alert("绘制的点数量不够组成一个面物标，请再添加绘制点。");
        return;
    }
    else if (objType == "2" && drawObjPoNum < parseInt(2)) {
        alert("绘制的点数量不够组成一个线物标，请再添加绘制点。");
        return;
    }

    if (objType == 9 && objContent == "" && isNaN(objContent)) {
        alert("请输入线区域半径。");
        return;
    }

    var layerStylePos = 0;
    var layerPos = -1;
    if (objType == 1) {
        //添加点
        layerPos = API_GetLayerPosById(g_iPointLayerId); //获取点图层的pos
        layerStylePos = g_iPointStylePos;
    }
    else if (objType == 2 || objType == 9) {//添加线
        layerPos = API_GetLayerPosById(g_iLineLayerId); //获取线图层的pos
        layerStylePos = g_iLineStylePos;
    }
    else if (objType == 3) {
        //添加面
        layerPos = API_GetLayerPosById(g_iFaceLayerId); //获取面图层的pos
        layerStylePos = g_iFaceStylePos;
    }
    var bAddResult = false;
    if (layerPos > -1) {
        g_iAddObjId++;
        var objInfo = [];
        var arrExpAttrValue = []; //扩展字段，假如没有可以传入null

        objInfo.layerPos = layerPos; //图层索引
        objInfo.objId = g_iAddObjId; //物标id
        objInfo.name = objName;//物标名称
        objInfo.showText = objName;//显示内容
        objInfo.layerStylePos = layerStylePos; //使用样式索引
        objInfo.objType = objType;
        arrExpAttrValue.push("来一个扩展字段");//扩展字段信息

        var objPos = API_AddNewObject(objInfo, arrObjPo, arrExpAttrValue);
        if (objPos > -1) {
            bAddResult = true;

            if (objType == 9) {
                var fLineAreaSizeM = parseFloat(objContent);//线区域大小
                API_SetLineObjectAreaSize(layerPos, objPos, fLineAreaSizeM);
            }
            if (objType == 3)//面
            {
//                var myColor = "#0000FF";
//                var objStyleInfo = [];
//                objStyleInfo.bFilled = true;       //是否填充(布尔)，true=填充，false=不填充
//                objStyleInfo.fillColor = myColor;     //填充颜色(字符串)，例如"#000000"
//                objStyleInfo.iOpacity = 50;      //填充透明度(数字)，0~100，100为不透明
//                objStyleInfo.borderWith = 1;    //线粗细(数字)
//                objStyleInfo.iLineOpacity = 50; //线的透明度(数字)，0~100，100为不透明
//                objStyleInfo.borderColor = "#FF0000";   //线颜色(字符串)，例如"#000000"
//                objStyleInfo.bShowText = true;     //是否显示信息(布尔)，true=显示，false=不显示
//                objStyleInfo.textColor = "#00FF00";     //物标名称字体颜色(字符串)，例如"#000000"
//                objStyleInfo.fontSize = "20px 宋体";      //物标名称字体(字符串)，格式例如"12px 宋体"
//                objStyleInfo.iTextOpacity = 40;  //文本透明度(数字)，0~100，100为不透明

//                API_SetFaceObjStyleByPos(layerPos, objPos, true, objStyleInfo)
            }

            if (objType == 2)//添加线时候，设置线使用个性样式
            {
                var objStyleInfo = [];

                objStyleInfo.iOpacity = 50;      //填充透明度(数字)，0~100，100为不透明
                objStyleInfo.borderWith = 5;    //线粗细(数字)，大于0
                objStyleInfo.borderColor = "#0000FF";   //线颜色(字符串)，例如"#000000"
                objStyleInfo.textColor = "#FF0000";     //物标名称字体颜色(字符串)，例如"#000000"
                objStyleInfo.fontSize = "12px 宋体";      //物标名称字体(字符串)，格式例如"12px 宋体"
                objStyleInfo.bShowText = true;     //是否显示信息(布尔)，true=显示，false=不显示
                objStyleInfo.iTextOpacity = 50;  //文本透明度(数字)，0~100，100为不透明
                API_SetLineObjStyleByPos(layerPos, objPos, true, objStyleInfo);
            }

            if (objType == 1)//添加点时候，设置点使用个性样式
            {

                var objStyleInfo = [];
                objStyleInfo.bShowImg = true;      //是否用图片显示

                objStyleInfo.strImgSrc = "img/point.png";     //图片地址，例如"img/point.png"
                objStyleInfo.iImgWidth = 20;     //图片宽度(数字)
                objStyleInfo.iImgHeight = 20;    //图片高度(数字)
                objStyleInfo.iOpacity = 50;     //填充颜色的透明度(数字)，0~100，100为不透明
                /*
                 var arrSymbolPo = []; //矢量符号坐标
                 arrSymbolPo.push({ x: 0, y: 15 });
                 arrSymbolPo.push({ x: 15, y: -15 });
                 arrSymbolPo.push({ x: -15, y: -15 });
                 objStyleInfo.arrSymbolPo = arrSymbolPo; //矢量符号坐标
                 */
                /*
                 objStyleInfo.iCircleScrnR = 8;  //使用圆表示时候，圆的半径

                 objStyleInfo.bFilled = true;      //是否填充颜色(矢量符号有效)，true=填充，false=不填充。
                 objStyleInfo.fillColor = "#FF0000";    //填充颜色
                 objStyleInfo.iOpacity = 80;     //填充颜色的透明度(数字)，0~100，100为不透明
                 objStyleInfo.borderWith = 0; //线粗细，0=不显示边
                 objStyleInfo.borderColor = "#000000"; //线颜色，例如"#000000"
                 objStyleInfo.iLineOpacity = 90; //线透明度(数字)，0~100，100为不透明
                 */
                //文字样式
                objStyleInfo.textColor = "#FFFFFF";     //物标名称字体颜色(字符串)，例如"#000000"
                objStyleInfo.fontSize = "12px 宋体";      //物标名称字体(字符串)，格式例如"12px 宋体"
                objStyleInfo.bShowText = false;     //是否显示信息(布尔)，true=显示，false=不显示
                objStyleInfo.iTextOpacity = 100;  //文本透明度(数字)，0~100，100为不透明

                API_SetPointObjStyleByPos(layerPos, objPos, true, objStyleInfo);
            }
        }
    }



    if (bAddResult == true) {
        API_ReDrawLayer();
        EndDrawObj();
        ShowDivBoxOrNot("drawObjBox", false);
    }
    else {
        alert("添加失败");
    }
}

//结束添加物标
function EndDrawObj() {
    ShowDivBoxOrNot("drawObjBox", false);
    API_SetCurDrawDynamicUseType(DynamicSymbolType.none);
}

//设置div显示状态
function ShowDivBoxOrNot(objName, bShow) {
    if (bShow == true) {
        document.getElementById(objName).style.display = "block";
    }
    else {
        document.getElementById(objName).style.display = "none";
    }
}

//显示信息
function ShowInfoDivBox(objDiv,scrnPo) {
    if (objDiv) {
        //获取海图界面大小
        var offsetLen = 10;
        var divLeft = scrnPo.x + offsetLen;
        var divTop = scrnPo.y + offsetLen;
        var divSize = { w: objDiv.clientWidth, h: objDiv.clientHeight };
        var mapDiv = document.getElementById("map");
        if (mapDiv) {
            var mapWidth = mapDiv.clientWidth;
            var mapHeight = mapDiv.clientHeight;

            if (divLeft + divSize.w > mapWidth) { //越界判断
                divLeft = scrnPo.x - divSize.w - offsetLen;
            }
            if (divTop + divSize.h > mapHeight) {
                divTop = scrnPo.y - divSize.h - offsetLen;
            }
        }

        objDiv.style.left = divLeft + "px";
        objDiv.style.top = divTop + "px";
        objDiv.style.display = "block";
    }
}

//判断是否选中行政区物标
function CheackIsSelectAdministrativeArea(objInfo){

    if (objInfo) {
        switch (objInfo.objType) {//{objType,id,po}
            case 2: //选中了物标，得到物标所属的图层layerId以及物标objId
                var layerId = objInfo.layerId;//图层的id
                var objId = objInfo.objId;//物标的id
                //如果是行政区物标，则高亮显示。
                return layerId == g_iAdministrativeAreaLayerId;
        }
    }

    return false;
}

//-----------------------------------------------------------------------以下方法是sdk内部调用的-------------------------
//鼠标移动时，选中对象的信息（手机版本这个方法不会被调用）
function ReturnSelectObjByMouseMove(objInfo) {

}

//鼠标左击时查询到的对象信息
function ReturnSelectObjByMouseLeftDown(objInfo) {

    API_SetCurHighLightObjectById(-1, -1);//先取消高亮

    //设置高亮物标样式
    var highLightStyle = {borderWith:3, borderColor:"#092ee8", iOpacity:70, fillColor:"#009CAD"};
    API_SetCurHighLightObjectStyle(highLightStyle);

    if(CheackIsSelectAdministrativeArea(objInfo)) {//如果是行政区物标，则高亮显示。
        API_SetCurHighLightObjectById(objInfo.layerId, objInfo.objId);
    }
    return;


    //以下废弃不用
    //设置显示样式以及信息等
    var iMsgBoxHeight = 20;
    var iMsgBoxWidth = 200;
    var strInnerHTML = "";
    g_showSimpleInfoDiv = document.getElementById("ShowSimpleInfoByMouseMoveDiv");

    if(CheackIsSelectAdministrativeArea(objInfo)) {//如果是行政区物标，弹窗显示行政区信息

        var iLayerPos = API_GetLayerPosById(objInfo.layerId);//得到图层的pos
        var objPos = API_GetObjectPosById(iLayerPos, objInfo.objId);
        //var attrInfo = API_GetObjectOneExpAttrByPos(objPos.iLayerPos, objPos.iObjPos, 0);//查询对象的额外信息
        strInnerHTML += "点击行政区要显示的信息";
        g_showSimpleInfoDiv.innerHTML = strInnerHTML;
        g_showSimpleInfoDiv.style.height = iMsgBoxHeight + "px";
        g_showSimpleInfoDiv.style.width = iMsgBoxWidth + "px";

        ShowInfoDivBox(g_showSimpleInfoDiv, objInfo.po);
    }
    else if (objInfo.objType == 4) {//选中标记物标

        //显示的标记详细信息
        strInnerHTML += "显示的标记详细信息";
        g_showSimpleInfoDiv.innerHTML = strInnerHTML;
        g_showSimpleInfoDiv.style.height = iMsgBoxHeight + "px";
        g_showSimpleInfoDiv.style.width = iMsgBoxWidth + "px";

        ShowInfoDivBox(g_showSimpleInfoDiv, objInfo.po);
    }

}

//动态绘制物标时，选中点之后返回的坐标
function ReturnDrawDynamicObjNewInfo(objDynamicInfo) {

}

//返回测距时的距离
//CurDis：当前段距离（km)
//allMeasureDist：累加距离（km）
//CurDegrees：当前方位（度）
function ReturnCurMeasureDist(CurDis, allMeasureDist, CurDegrees) {

}
//---------------------------------------------------------------------------------------------------------------------


