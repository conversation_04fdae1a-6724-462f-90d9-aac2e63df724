package com.bd.entity.other;

import lombok.Data;

@Data
public class AlarmEvent {
    private int alarmCode; //报警类型
    private long alarmEventId; //报警事件ID
    private String alarmId; //报警模板id
    private int alarmLevel; //报警等级
    private String alarmName; //报警名称
    private String areaId; //区域id
    private String areaName; //区域名称
    private String caseSourceNumber; //案源编号
    private ControlDutyArea controlDutyArea; //管控责任区
    private String dataSource; //数据来源
    private String dataType; //数据类型
    private double latitude; //报警发生点经度
    private double longitude; //报警发生点纬度
    private double speed; //速度
    private int status; //目标状态 1位正常，2位预测
    private String targetId; //报警目标id
    private String targetType; //目标类型
    private long updateTime; //报警更新时间

    //定义内部类ControlDutyArea
    @Data
    public static class ControlDutyArea {
        private String alarmId; //报警事件id
        private String controlAreaName; //管控责任区名称
        private String id; //管控责任区唯一id
        private String personLiable; //负责人名称
        private String phone; //责任人电话
    }}
