// id-父级id
// isShow-显示或隐藏，
// 因为调用函数关闭div都是在导出结束的时候进行，所以id为Fid + "Child"的节点一定存在

function syncRemind(Fid, isShow) {
    var child = document.getElementById(Fid + "Child");
    if(!isShow){
        child.style.display = "none";
        return;
    }
    var parent = document.getElementById(Fid);
    // var div = document.getElementById(Fid + "Child");
    if(!child){
        var div = document.createElement("div");
        div.style.position = 'absolute';
        div.style.width = '200px';
        div.style.top = '50%';
        div.style.left = '45%';
        div.style.height = 'auto';
        // 孩子节点的id是确定的，由父级的id加上Child构成
        div.id = Fid + "Child";
        div.style.color = 'white';//添加背景色
        div.style.zIndex = '13';
        div.style.border = '0';
        div.style.borderRadius = '5px';
        div.style.backgroundColor = '#00143F';
        var span = document.createElement("span");
        span.style.width = '160px';
        span.style.height = '50px';
        span.style.position = 'absolute';
        span.style.top = '20px';
        span.style.left = '40px';
        span.style.marginTop = '10px';
        span.style.borderRadius = '5px';
        span.style.lineHeight = '50px';
        span.style.backgroundColor = '#00143F';
    
        span.style.color = 'white';//添加背景色
        span.style.textAlign = 'center'
        span.style.fontSize = '12px';
        span.innerHTML = "导出中，请等待……";
    
        div.appendChild(span);
    
        parent.appendChild(div);
    }else{
        child.style.display = "block";
    }
    
    return 0;
}