package com.bd.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
public class FisheryBoatInOutReport {

    private String portId;
    private String portName;
    private String shipName;
    private String time;
    private String crewStr;
    private int inOrOut; /* 0 进港  1 出港 */
    private String zw;
    private String zsdj;
    private String zshm;
    private String endTime;
}

