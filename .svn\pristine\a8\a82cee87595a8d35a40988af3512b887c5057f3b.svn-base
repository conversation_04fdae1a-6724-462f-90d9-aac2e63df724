<template>
  <div class="container">
    <!-- 顶部栏 -->
    <div class="top-bar">
      <div id="search-container" class="search-container">
        <input type="text" id="searchInput" v-model="searchQuery" placeholder="请输入船名、呼号、IMO或MMSI"
          @keyup.enter="handleSearch" class="search-input">
        <button @click="handleSearch" class="search-button">
          <i class="icon-search">搜索</i>
        </button>
        <div v-if="showSearchResults" class="search-results-dropdown">
          <div class="search-results-header">
            <span>搜索结果</span>
            <button @click="closeSearchResults" class="close-btn">×</button>
          </div>
          <div class="search-results-content">
            <table>
              <thead>
              </thead>
              <tbody>
                <tr v-for="(ship, index) in searchResults" :key="index" @click="handleShipClick(ship)">
                  <td>{{ ship.shipName }}&nbsp;&nbsp;&nbsp;&nbsp;终端ID{{ ship.bdid }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

      </div>
    </div>
    <!-- 主地图区域 -->
    <div class="map-bar">
      <div id="map" ref="myDiv" @contextmenu.prevent="handleContextMenu" class="map-container">
        <!-- 地图将由API渲染在这里 -->
        <!-- 船舶信息浮窗 -->
        <div
          v-if="showShipInfoWindow"
          :style="{
            position: 'absolute',
            left: shipInfoWindowData.x + 'px',
            top: shipInfoWindowData.y + 'px',
            background: '#FFA726',
            color: '#333',
            border: '3px solid #222',
            borderRadius: '6px',
            padding: '12px 18px',
            zIndex: 2000,
            minWidth: '220px'
          }"
        >
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span>
              <b>船名：</b>{{ shipInfoWindowData.shipName }}
            </span>
            <button @click="showShipInfoWindow = false" style="background:none;border:none;font-size:18px;cursor:pointer;">×</button>
          </div>
          <div><b>终端号：</b>{{ shipInfoWindowData.terminalId }}</div>
          <div><b>终端类型：</b>{{ shipInfoWindowData.terminalType }}</div>
          <div><b>时间：</b>{{ shipInfoWindowData.time }}</div>
          <div style="margin-top:8px;font-size:13px;color:#222;">
            <b>经度：</b>{{ shipInfoWindowData.lonText }}　
            <b>纬度：</b>{{ shipInfoWindowData.latText }}
          </div>
        </div>
      </div>
      <div class="sidebar-container">
        <div class="sidebar-up-content">
          <button class="sidebar-btn">
            <i class="icon">📍</i>
          </button>
          <button class="sidebar-btn">
            <i class="icon">🛰️</i>
          </button>
          <button class="sidebar-btn">
            <i class="icon">📊</i>
          </button>
          <button class="sidebar-btn">
            <i class="icon">⚙️</i>
          </button>
          <button class="sidebar-btn">
            <i class="icon">📍</i>
          </button>
          <button class="sidebar-btn">
            <i class="icon">🛰️</i>
          </button>
          <button class="sidebar-btn">
            <i class="icon">📊</i>
          </button>
          <button class="sidebar-btn">
            <i class="icon">⚙️</i>
          </button>
        </div>
        <div class="sidebar-down-content">
          <button class="sidebar-btn">
            <i class="icon">📌</i>
          </button>
          <button class="sidebar-btn">
            <i class="icon">🔍</i>
          </button>
          <button class="sidebar-btn">
            <i class="icon">🗑️</i>
          </button>
        </div>
      </div>
    </div>

    <!-- 底边栏 -->
    <div class="bottom-bar">
      <div class="time-filters">
        <button v-for="filter in timeFilters" :key="filter.value" @click="setActiveFilter(filter.value)"
          :class="{ active: activeFilter === filter.value }" class="filter-btn">
          {{ filter.label }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import global from './Global.vue';
import setAlertWindowShow from '../../static/js/windowAlert.js'
//import ShipSimpleInformation from '@/components/ComprehensiveSituation/ShipSimpleInformation.vue'
import ShipSimpleInformation from '@/components/ShipSimpleInformation.vue'

var _this;
export default {
  name: "Index",
  data() {
    return {
      searchQuery: '', // 查询
      activeFilter: '1h', // 选中的时间
      timeFilters: [
        { label: '1h内在线船', value: '1h' },
        { label: '2h内在线船', value: '2h' },
        { label: '6h内在线船', value: '6h' }
      ], // 时间在线渔船
      showSearchResults: false, // 是否查到搜索结果
      searchResults: [], // 存储搜索结果
      showShipInfoWindow: false, // 新增：控制浮窗显示
      shipInfoWindowData: {     // 新增：浮窗数据
        shipName: '',
        terminalId: '',
        terminalType: '',
        time: '',
        x: 0,
        y: 0,
        lonText: '', // 新增
        latText: ''  // 新增
      },
      shipInfoImg: '',
      shipInfoList: [],
      shipInfoList1: [],
      fouInfo: {},
      shipSimpleInformationData: {},
      wakeIsShowOrNot: false,
      shipInfoList: [],
      shipInfoList1: [],
      fouInfo: {},
      shipSimpleInformationData: {},
      wakeIsShowOrNot: false,
    };
  },
  beforeCreate() {
    _this = this;
  },
  mounted() {

        //---------------------------sdkreturn
        window.ReturnSelectObjByMouseMoveTest = this.ReturnSelectObjByMouseMoveTest.bind(this);
        window.ReturnSelectObjByMouseLeftDownTest = this.ReturnSelectObjByMouseLeftDownTest.bind(this);
        window.ReturnOnMouseRightDownTest = this.ReturnOnMouseRightDownTest.bind(this);
        window.ReturnOnMouseLeftDownTest = this.ReturnOnMouseLeftDownTest.bind(this);
        window.ReturnOnMouseUpTest = this.ReturnOnMouseUpTest.bind(this);
        window.ReturnDrawDynamicObjNewInfoTest = this.ReturnDrawDynamicObjNewInfoTest.bind(this);
        window.GetCurMeasureDist = this.GetCurMeasureDist.bind(this);
        window.ReturnCurMeasurePoInfoByMouseDownTest = this.ReturnCurMeasurePoInfoByMouseDownTest.bind(this);
        window.ReturnDragMapTest = this.ReturnDragMapTest.bind(this);
        window.ReturnZoomMapForPcTest = this.ReturnZoomMapForPcTest.bind(this);
        window.ReturnOnMouseMoveTest = this.ReturnOnMouseMoveTest.bind(this);
        window.ReturnCurMeasureAreaSizeTest = this.ReturnCurMeasureAreaSizeTest.bind(this);
        window.ReturnTouchmoveByDragTest = this.ReturnTouchmoveByDragTest.bind(this);
        window.ReturnMapViewAfterDragOrZoomTest = this.ReturnMapViewAfterDragOrZoomTest.bind(this);
        window.ReturnCurPlayTrackTimeInfoTest = this.ReturnCurPlayTrackTimeInfoTest.bind(this);

        window.ReturnEditObjByMouseRightDownCallBack = this.ReturnEditObjByMouseRightDownCallBack.bind(this);

        window.ReturnCancelObjByMouseLeftDownCallBack = this.ReturnCancelObjByMouseLeftDownCallBack.bind(this);

        window.showLocation = this.showLocation.bind(this);

    _this.timer2 = setInterval(this.getShipInfo, 50000);

    setTimeout(function () {
            //_this.GetShipRule();
            //_this.getAllBDShipPosition();
            getMapToken();
            _this.getShipInfo(50000);
        }, 10)

        setTimeout(() => {
            //_this.getShipInfoByTime(2);
        }, 10);

    // 注册船舶点击事件

    function regist() {
    }
    function getMapToken() {
    }
    function Test_AddLayer() {
    }
    function Test_AddShipStyle() {
    }
    function init() {
      var objMapInfo = [];
      var mapObj = document.getElementById("map");
      API_SetMapImgMode(1);
      API_SetShipInObj(true);
      API_SetMapMinMaxLevel(1, 18);

      objMapInfo.div = "map"; //海图容器div的id

      // 自动判断设备类型
      if (/Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)) {
        objMapInfo.model = "android";
      } else {
        objMapInfo.model = "pc";
      }
      API_InitYimaMap(objMapInfo);
      regist();
      API_HiddenYimaEncText();
      getMapToken();
      // // 注册船舶点击事件，兼容不同亿海图SDK版本
      // if (typeof API_OnShipClick === 'function') {
      //   API_OnShipClick(function(shipInfo) {
      //     if (shipInfo && shipInfo.staticShipId && shipInfo.shipName) {
      //       alert('staticShipId: ' + shipInfo.staticShipId + '\nshipname: ' + shipInfo.shipName);
      //     } else {
      //       alert('未获取到船舶信息');
      //     }
      //   });
      // }
      // if (typeof API_SetOnShipClick === 'function') {
      //   API_SetOnShipClick(function(shipInfo) {
      //     if (shipInfo && shipInfo.staticShipId && shipInfo.shipName) {
      //       alert('staticShipId: ' + shipInfo.staticShipId + '\nshipname: ' + shipInfo.shipName);
      //     } else {
      //       alert('未获取到船舶信息');
      //     }
      //   });
      // }
      // window.OnShipClick = function(shipInfo) {
      //   if (shipInfo && shipInfo.staticShipId && shipInfo.shipName) {
      //     alert('staticShipId: ' + shipInfo.staticShipId + '\nshipname: ' + shipInfo.shipName);
      //   } else {
      //     alert('未获取到船舶信息');
      //   }
      // };
      // API_SetUserYima(false);
      API_SetMapImagesUrl("http://**************:8089/dianxin_chart/");
      // API_SetMapImagesUrl("http://***********:11080/api/map/cuttingMap/");
      // API_SetMapImagesUrl("http://***********:11080/api/map/cuttingMap/");
      // $.get(global.IP + "/web/GetMapToken", function (data, status) {
      //   var wmsToken = '?graphNode=' + global.mapModel + '&token=' + data;
      //   API_RefreshMapImg();
      // }).fail(function (msg) {
      //   // 服务器地址
      //   // API_SetMapImagesUrl("http://**************:8089/YimaMap1/");
      //   API_SetMapImagesUrl("http://***********:7001/map/");
      //   API_RefreshMapImg();
      // });


      API_SetSaveTrackPoCount(1000);
      API_SetDrawCenterPoToFixedLen(6);
      API_SetMousePoInfoDivPosition(false, 500, 50); //显示鼠标位置
      API_SetScaleInfoDivPosition(false, 20, 50); //显示比例尺位置
      API_SetScaleLenInfoPosition(false, 20, 60); //显示比例尺长度
      API_SetShowToolBarOrNot(false);

      global.g_showSimpleInfoDiv = document.getElementById("ShowSimpleInfoByMouseMoveDiv");
      global.g_showDrawObjInfoByMouseMoveDiv = document.getElementById("ShowDrawObjInfoByMouseMoveDiv");
      global.g_showDetailShipInfoDiv = document.getElementById("ShowDetailShipInfoDiv");

      Test_AddLayer(); //添加图层
      Test_AddShipStyle(); //添加船舶样式
      API_SetFocusShipShowStyleByMinScale(10240000); //设置选中船舶至少显示的最小比例尺样式

      API_SetMapLevel(10, { x: 121.83219484335935, y: 31.417383188620214 });
      // API_SetMapLevel(7, { x: 131.83219484335935, y: 39.417383188620214});

      API_SetSelectAllObjectByMouseMove(50000000, true);
      API_SetShowShipHistroyTrackScale(5000000);
      API_SetIsShowShipHistroyTrackInfoBox(false);

      // setTimeout(API_ReDrawLayer, 500);

      API_SetIsShowShipHistroyTrackInfoBox(true);

      API_SteIsShowHistoryTrackDirectionSign(true);

      API_SetSelectShipByScrnPoStartScale(500000000);
      API_SetShowShipHistroyTrackScale(500000000);
      API_SetStartShowShipScale(500000000);

      API_SetWarnShipCircleStyle(2, "#FFFF00", 100);

      var optionClustererStyle = [];
      optionClustererStyle.push({ src: "../../static/img/clusterers1.png", w: 53, h: 52 });//个位
      optionClustererStyle.push({ src: "../../static/img/clusterers2.png", w: 56, h: 55 });//十位
      optionClustererStyle.push({ src: "../../static/img/clusterers3.png", w: 66, h: 65 });//百位
      optionClustererStyle.push({ src: "../../static/img/clusterers4.png", w: 78, h: 77 });//千位
      optionClustererStyle.push({ src: "../../static/img/clusterers5.png", w: 90, h: 89 });//万位
      var bInitClustererStyle = API_InitClustererStyle(optionClustererStyle);//设置聚合底图
      API_SetClustererShowOption(true, 1, 7, true);//显示聚合0～13级
      API_SetSelectRectByMouseMoveNotShow(true);
      API_DBClickByRigth(true);
      var CancelButtonStyle = [];
      CancelButtonStyle.lineWidth = 1;
      CancelButtonStyle.lineColor = "#FF0000";
      CancelButtonStyle.lineOpacity = 90;
      CancelButtonStyle.setFill = true;
      CancelButtonStyle.fillColor = "#FFFFFF";
      CancelButtonStyle.fillOpacity = 100;
      CancelButtonStyle.width = 10;
      CancelButtonStyle.height = 10;
      API_SetCancelButtonStyle(CancelButtonStyle);

      // 为兼容安卓端，使用Arial字体，避免黑体在部分安卓设备不显示
      API_SetShowShipInfoStyle("20px Arial", "#0000FF", 90);
      API_SetFishAreaName(true);
      //船舶选中
      API_SetShowSelectShipInfoStyle(true, "12px Arial", "#FF00FF", 80);
      API_ShipTrackTimeMarkShowOrNot(false);
      API_SetShipShowNameFrameStyle(1, 10, 20, 1, "#FF0000", 100);
      API_SetShipSimpleDetailsShowOrNot(true);
      // 强制显示船舶名称（如有此API，部分亿海图版本支持）
      if(typeof API_SetShowShipNameOrNot === 'function') {
        API_SetShowShipNameOrNot(true);
      }
      // 地图初始化后再次调用，确保生效
      setTimeout(function(){
        API_SetShowShipInfoStyle("20px Arial", "#0000FF", 90);
        API_SetShowSelectShipInfoStyle(true, "12px Arial", "#FF00FF", 80);
        API_SetShipSimpleDetailsShowOrNot(true);
        if(typeof API_SetShowShipNameOrNot === 'function') {
          API_SetShowShipNameOrNot(true);
        }
      }, 1000);

      API_SetShipImgRotate(true);
      API_SteIsShowHistoryTrackDirectionSign(false);
      API_SetShowAllTyphoonPredictTrack(true);
      global.isCheckInit = true;

      Test_AddLayer(); //添加图层
      Test_AddShipStyle(); //添加船舶样式
      //alert("init end!");

      // 注释掉API_OnShipClick和API_SetOnShipClick注册
      /*
      if (typeof API_OnShipClick === 'function') {
        API_OnShipClick(function(shipInfo) {
          alert('点击了船舶！');
        });
      }
      if (typeof API_SetOnShipClick === 'function') {
        API_SetOnShipClick(function(shipInfo) {
          alert('点击了船舶！');
        });
      }
      */
      // 只保留window.OnShipClick注册，延迟2秒
      setTimeout(() => {
        window.OnShipClick = function(shipInfo) {
          // 调用locationShip方法
          let id = shipInfo.staticShipId || shipInfo.shipId || shipInfo.bdid || shipInfo.bdId;
          if (!id) return;
          _this.$options.methods.locationShip.bind(_this)(id);
          // 弹出简单信息弹窗
          global.curSelectShipId = id;
          document.getElementById('ShipSimpleInformationView').style.display = 'block';
          _this.$options.methods.changeCurIndex.bind(_this)("ShipSimpleInformationView");
        };
      }, 2000);
    }

    function Test_AddLayer () {
            //这里演示添加3个图层，分别是点标注图层、线标注图层、面标注图层、气象图层

            //--------------------------添加点标注图层-----------------------------------
            var pointLayerInfo = [];
            pointLayerInfo.id = global.g_iPointLayerId;
            pointLayerInfo.type = 1;//类型：1=点图层，2=线图层，3=面图层
            pointLayerInfo.name = "点图层";//图层名称
            pointLayerInfo.bShow = true; //显示

            pointLayerInfo.minShowScale = 1;//最大比例尺
            pointLayerInfo.maxShowScale = 2000000000;//最小比例尺
            pointLayerInfo.bShowTextOrNot = true;//是否显示名称
            pointLayerInfo.iStartShowTextScale = 5000000;//开始显示名称的最小比例尺

            var pointLayerPos = API_AddNewLayer(pointLayerInfo,null); //添加图层，得到图层的pos
            if (pointLayerPos > -1) {
                var pointStyle = [];

                //点图片样式
                pointStyle.strImgSrc = "/static/yimaencsdk/YimaEncSDK/img/light.png"; //图片地址
                pointStyle.iImgWidth = 20; //图片的宽度
                pointStyle.iImgHeight = 30; //图片的高度
                pointStyle.offsetScrnPo = {x:0,y:-15};//显示的偏移量，(0,0)为图片中心

                pointStyle.bShowImg = true;
                pointStyle.bShowText = true; //是否显示名称
                pointStyle.textColor = "#FF0000"; //名称颜色
                pointStyle.fontSize = "12px"; //名称字体大小
                pointStyle.iOpacity = 100;
                pointStyle.iTextOpacity = 10; //透明度
                pointStyle.bFilled = true; //是否填充颜色
                pointStyle.fillColor = "#ee5d72"; //填充的颜色
                global.g_iPointStylePos = API_AddPointLayerStyleByPos(pointLayerPos, pointStyle);

                API_SetLayerTextBackGroundColorByPos(pointLayerPos,true,"#FF0000",50);//设置文字背景颜色
            }

            //---------------------------------添加线标注图层----------------------------

            var lintLayerInfo = [];
            lintLayerInfo.id = global.g_iLineLayerId;
            lintLayerInfo.type = 2; //类型：1=点图层，2=线图层，3=面图层
            lintLayerInfo.name = "线图层"; //图层名称
            lintLayerInfo.bShow = true; //显示
            var lineLayerPos = API_AddNewLayer(lintLayerInfo, null); //添加图层，得到图层的pos

            if (lineLayerPos > -1) {
                var lineStyle = [];
                lineStyle.borderWith = 3; //线的粗细
                lineStyle.borderColor = "#FFB90F"; //线的颜色
                lineStyle.iOpacity = 80; //透明度
                lineStyle.bShowText = true; //是否显示名称
                lineStyle.textColor = "#000000"; //名称颜色
                lineStyle.fontSize = "12px"; //名称字体大小
                lineStyle.iTextOpacity = 80; //透明度
                lineStyle.lineType = 1;//绘制实线、1=虚线
                lineStyle.lineLen = 5;
                lineStyle.dashLen = 2;
                global.g_iLineStylePos = API_AddLineLayerStyleByPos(lineLayerPos, lineStyle);
                API_SetLayerTextBackGroundColorByPos(lineLayerPos,true, "#00FF00", 50); //设置文字背景颜色
            }

            //-------------------------------------添加面标注图层---------------------
            var faceLayerInfo = [];
            faceLayerInfo.id = global.g_iFaceLayerId;
            faceLayerInfo.type = 3; //类型：1=点图层，2=线图层，3=面图层
            faceLayerInfo.name = "面图层"; //图层名称
            faceLayerInfo.bShow = true; //显示
            var faceLayerPos = API_AddNewLayer(faceLayerInfo,null); //添加图层，得到图层的pos
            if (faceLayerPos > -1) {
                var faceStyle = [];
                faceStyle.borderWith = 1; //线的粗细
                faceStyle.borderColor = "#092ee8"; //线的颜色
                faceStyle.bFilled = true; //是否填充颜色
                faceStyle.fillColor = "#FFFFFF"; //填充的颜色
                faceStyle.iOpacity = 50; //透明度
                faceStyle.bShowText = true; //是否显示名称
                faceStyle.textColor = "#000000"; //名称颜色
                faceStyle.fontSize = "12px"; //名称字体大小
                faceStyle.iTextOpacity = 80; //透明度
                faceStyle.iLineOpacity = 100;

                global.g_iFaceStylePos = API_AddFaceLayerStyleByPos(faceLayerPos, faceStyle);
                API_SetLayerTextBackGroundColorByPos(faceLayerPos,true, "#0000FF", 50); //设置文字背景颜色
            }

            //--------------------------------------添加气象图层(也是点标注一种)--------------
            var weatherLayerInfo = [];
            weatherLayerInfo.id = global.g_iWeatherLayerId;
            weatherLayerInfo.type = 1; //类型：1=点图层，2=线图层，3=面图层
            weatherLayerInfo.name = "气象图层"; //图层名称
            weatherLayerInfo.bShow = true; //显示
            weatherLayerInfo.bShowImg = true;//显示图片
            weatherLayerInfo.minShowScale = 20000;//最小显示比例尺
            weatherLayerInfo.maxShowScale = 1000000; //最大显示比例尺
            var weatherLayerPos = API_AddNewLayer(weatherLayerInfo,null); //添加图层，得到图层的pos
            if (weatherLayerPos > -1) {
                //这里添加两种气象样式
                var weatherStyle1 = [];
                weatherStyle1.bShowImg = true;
                weatherStyle1.strImgSrc = "/static/yimaencsdk/YimaEncSDK/img/sunshine1.png"; //图片地址（晴天图片）
                weatherStyle1.iImgWidth = 30; //图片的宽度
                weatherStyle1.iImgHeight = 26; //图片的高度
                weatherStyle1.bShowText = false; //是否显示名称

                var pos1 = API_AddPointLayerStyleByPos(weatherLayerPos, weatherStyle1);//添加第一种气象样式,这里的pos1应该是0

                var weatherStyle2 = [];
                weatherStyle2.bShowImg = true;//显示图片
                weatherStyle2.strImgSrc = "/static/yimaencsdk/YimaEncSDK/img/raining1.png"; //图片地址（阴天图片）
                weatherStyle2.iImgWidth = 30; //图片的宽度
                weatherStyle2.iImgHeight = 26; //图片的高度
                weatherStyle2.bShowText = false; //是否显示名称
                var pos2 = API_AddPointLayerStyleByPos(weatherLayerPos, weatherStyle2); //添加第一种气象样式,这里的pos1应该是1
            }

            //--------------------------------------添加港口图层(也是点标注一种)--------------
            var portLayerInfo = [];
            portLayerInfo.id = global.g_iPortLayerId;
            portLayerInfo.type = 1; //类型：1=点图层，2=线图层，3=面图层
            portLayerInfo.name = "港口图层"; //图层名称
            portLayerInfo.bShow = true; //显示
            portLayerInfo.bShowImg = true;
            var portLayerPos = API_AddNewLayer(portLayerInfo,null); //添加图层，得到图层的pos
            if (portLayerPos > -1) {
                //这里一种样式
                var portStyle = [];
                portStyle.bShowImg = true; //显示图片
                portStyle.strImgSrc = "../..//static/img/port.png"; //图片地址（晴天图片）
                portStyle.iImgWidth = 10; //图片的宽度
                portStyle.iImgHeight = 10; //图片的高度
                portStyle.bShowText = false; //是否显示名称
                portStyle.iOpacity = 50; //透明度
                var pos = API_AddPointLayerStyleByPos(portLayerPos, portStyle); //添加第一种港口样式,这里的pos应该是0
            }

            //---------------------------------------添加洋流图层(也是点标注，只是使用矢量符号来显示(箭头))-----------------------------------
            var ocLayerInfo = [];
            ocLayerInfo.id = global.g_iOceanCirculationLayerId; //洋流图层Id
            ocLayerInfo.type = 1; //类型：1=点图层，2=线图层，3=面图层
            ocLayerInfo.name = "洋流图层"; //图层名称
            ocLayerInfo.bShow = true; //显示
            var ocLayerPos = API_AddNewLayer(ocLayerInfo, null); //添加图层，得到图层的pos
            if (ocLayerPos > -1) {
                //这里一种样式
                var arrSymbolPo = [];//箭头符号
                arrSymbolPo.push({ x: -1, y: 10 });
                arrSymbolPo.push({ x: 1, y: 10 });
                arrSymbolPo.push({ x: 1, y: -3 });
                arrSymbolPo.push({ x: 3, y: -3 });
                arrSymbolPo.push({ x: 0, y: -10 });
                arrSymbolPo.push({ x: -3, y: -3 });
                arrSymbolPo.push({ x: -1, y: -3 });

                var ocStyle = [];
                ocStyle.bShowImg = false; //不使用图片，使用矢量符号
                ocStyle.arrSymbolPo = arrSymbolPo; //矢量符号顶点
                ocStyle.iImgWidth = 20; //符号的宽度
                ocStyle.iImgHeight = 50; //符号的高度
                ocStyle.bShowText = false; //是否显示名称
                ocStyle.borderWith = 1; //线的粗细
                ocStyle.borderColor = "#FF0000"; //线的颜色
                ocStyle.bFilled = false; //是否填充颜色
                ocStyle.fillColor = "#FF0000"; //填充的颜色
                ocStyle.iOpacity = 50; //透明度
                ocStyle.iCheckDrawMinNearOtherLen = 30;//该图层直接的标注间隙

                var pos = API_AddPointLayerStyleByPos(ocLayerPos, ocStyle); //添加第一种港口样式,这里的pos应该是0
            }


            // ---------------------------------------添加渔港图层-----------------------------------------
            var compositeLayerInfo = [];
            compositeLayerInfo.id = global.g_iFishingPortLayerId;
            compositeLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层 composite = 100 = 综合图层
            compositeLayerInfo.name = "渔港图层"; //图层名称
            compositeLayerInfo.bShow = true; //显示
            compositeLayerInfo.minShowScale = 1;//最大比例尺
            compositeLayerInfo.maxShowScale = 500000;//最小比例尺
            compositeLayerInfo.bShowTextOrNot = true;//是否显示名称
            compositeLayerInfo.iStartShowTextScale = 50000;//开始显示名称的最小比例尺
            var compositeLayerPos = API_AddNewLayer(compositeLayerInfo,null); //添加图层，得到图层的pos
            if (compositeLayerPos > -1) {
                var compositeStyle = [];
                compositeStyle.borderWith = 2; //线的粗细
                compositeStyle.borderColor = "#00143F"; //线的颜色
                compositeStyle.bFilled = true; //是否填充颜色
                compositeStyle.fillColor = "#246fa8"; //填充的颜色
                compositeStyle.iOpacity = 20; //透明度
                compositeStyle.bShowImg = false;
                compositeStyle.strImgSrc = '../../static/img/point.png'; //图片地址
                compositeStyle.iImgWidth = 10; //图片的宽度
                compositeStyle.iImgHeight = 10; //图片的高度
                compositeStyle.bShowText = true; //是否显示名称
                compositeStyle.textColor = "#000000"; //名称颜色
                compositeStyle.fontSize = "12px"; //名称字体大小
                compositeStyle.iTextOpacity = 100; //透明度
                //compositeStyle.iCheckDrawMinNearOtherLen = null;
                compositeStyle.iLineOpacity = 50;
                compositeStyle.offsetScrnPo = {x:-10,y:15};
                // compositeStyle.bDrawPointCircle = null;//是否绘制小圆点
                compositeStyle.lineType = 1;//绘制实线、1=虚线
                compositeStyle.lineLen = 5;
                compositeStyle.dashLen = 2;

                global.g_iFishingPortStylePos = API_AddCompositeLayerStyleByPos(compositeLayerPos, compositeStyle);
            }

             // ---------------------------------------添加伏休图层-----------------------------------------
            var FuxiuLayerInfo = [];
            FuxiuLayerInfo.id = global.g_iAreaFuxiuLayerId;
            FuxiuLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层 composite = 100 = 综合图层
            FuxiuLayerInfo.name = "伏休图层"; //图层名称
            FuxiuLayerInfo.bShow = true; //显示
            FuxiuLayerInfo.minShowScale = 1;//最大比例尺
            FuxiuLayerInfo.maxShowScale = 50000000;//最小比例尺
            FuxiuLayerInfo.bShowTextOrNot = true;//是否显示名称
            FuxiuLayerInfo.iStartShowTextScale = 500000;//开始显示名称的最小比例尺
            var FuxiuLayerPos = API_AddNewLayer(FuxiuLayerInfo,null); //添加图层，得到图层的pos
            if (FuxiuLayerPos > -1) {
                var FuxiuStyle = [];
                FuxiuStyle.borderWith = 3; //线的粗细
                FuxiuStyle.borderColor = "#FFFF00"; //线的颜色
                FuxiuStyle.bFilled = true; //是否填充颜色
                FuxiuStyle.fillColor = "#FFB90F"; //填充的颜色
                FuxiuStyle.iOpacity = 5; //透明度
                FuxiuStyle.bShowImg = false;
                FuxiuStyle.bShowText = false; //是否显示名称
                FuxiuStyle.textColor = "#000000"; //名称颜色
                FuxiuStyle.fontSize = "12px"; //名称字体大小
                FuxiuStyle.iTextOpacity = 100; //透明度
                FuxiuStyle.iLineOpacity = 50;
                FuxiuStyle.offsetScrnPo = {x:-10,y:15};
                FuxiuStyle.lineType = 1;//绘制实线、1=虚线
                FuxiuStyle.lineLen = 10;
                FuxiuStyle.dashLen = 2;

                global.g_iAreaFuxiuStylePos = API_AddCompositeLayerStyleByPos(FuxiuLayerPos, FuxiuStyle);
            }

            // ---------------------------------------添加高危图层-----------------------------------------
            var GaoweiLayerInfo = [];
            GaoweiLayerInfo.id = global.g_iAreaGaoweiLayerId;
            GaoweiLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层 composite = 100 = 综合图层
            GaoweiLayerInfo.name = "高危图层"; //图层名称
            GaoweiLayerInfo.bShow = true; //显示
            GaoweiLayerInfo.minShowScale = 1;//最大比例尺
            GaoweiLayerInfo.maxShowScale = 5000000;//最小比例尺
            GaoweiLayerInfo.bShowTextOrNot = true;//是否显示名称
            GaoweiLayerInfo.iStartShowTextScale = 500000;//开始显示名称的最小比例尺
            var GaoweiLayerPos = API_AddNewLayer(GaoweiLayerInfo,null); //添加图层，得到图层的pos
            if (GaoweiLayerPos > -1) {
                var GaoweiStyle = [];
                GaoweiStyle.borderWith = 2; //线的粗细
                GaoweiStyle.borderColor = "#FF0000"; //线的颜色
                GaoweiStyle.bFilled = true; //是否填充颜色
                GaoweiStyle.fillColor = "#FF0000"; //填充的颜色
                GaoweiStyle.iOpacity = 10; //透明度
                GaoweiStyle.bShowImg = false;
                GaoweiStyle.bShowText = true; //是否显示名称
                GaoweiStyle.textColor = "#000000"; //名称颜色
                GaoweiStyle.fontSize = "12px"; //名称字体大小
                GaoweiStyle.iTextOpacity = 100; //透明度
                GaoweiStyle.iLineOpacity = 50;
                GaoweiStyle.offsetScrnPo = {x:-10,y:15};
                GaoweiStyle.lineType = 1;//绘制实线、1=虚线
                GaoweiStyle.lineLen = 5;
                GaoweiStyle.dashLen = 2;

                global.g_iAreaGaoweiStylePos = API_AddCompositeLayerStyleByPos(GaoweiLayerPos, GaoweiStyle);
            }

            // ---------------------------------------添加特殊图层-----------------------------------------
            var TeshuLayerInfo = [];
            TeshuLayerInfo.id = global.g_iAreaTeshuLayerId;
            TeshuLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层 composite = 100 = 综合图层
            TeshuLayerInfo.name = "特殊图层"; //图层名称
            TeshuLayerInfo.bShow = true; //显示
            TeshuLayerInfo.minShowScale = 1;//最大比例尺
            TeshuLayerInfo.maxShowScale = 50000000;//最小比例尺
            TeshuLayerInfo.bShowTextOrNot = true;//是否显示名称
            TeshuLayerInfo.iStartShowTextScale = 500000;//开始显示名称的最小比例尺
            var TeshuLayerPos = API_AddNewLayer(TeshuLayerInfo,null); //添加图层，得到图层的pos
            if (TeshuLayerPos > -1) {
                var TeshuStyle = [];
                TeshuStyle.borderWith = 2; //线的粗细
                TeshuStyle.borderColor = "#000000"; //线的颜色
                TeshuStyle.bFilled = true; //是否填充颜色
                TeshuStyle.fillColor = "#00FA9A"; //填充的颜色
                TeshuStyle.iOpacity = 10; //透明度
                TeshuStyle.bShowImg = false;
                TeshuStyle.bShowText = true; //是否显示名称
                TeshuStyle.textColor = "#000000"; //名称颜色
                TeshuStyle.fontSize = "12px"; //名称字体大小
                TeshuStyle.iTextOpacity = 100; //透明度
                TeshuStyle.iLineOpacity = 50;
                TeshuStyle.offsetScrnPo = {x:-10,y:15};
                TeshuStyle.lineType = 1;//绘制实线、1=虚线
                TeshuStyle.lineLen = 10;
                TeshuStyle.dashLen = 2;

                global.g_iAreaTeshuStylePos = API_AddCompositeLayerStyleByPos(TeshuLayerPos, TeshuStyle);
            }

            // ---------------------------------------添加自定义图层-----------------------------------------
            var SelfLayerInfo = [];
            SelfLayerInfo.id = global.g_iAreaSelfLayerId;
            SelfLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层 composite = 100 = 综合图层
            SelfLayerInfo.name = "自定义图层"; //图层名称
            SelfLayerInfo.bShow = true; //显示
            SelfLayerInfo.minShowScale = 1;//最大比例尺
            SelfLayerInfo.maxShowScale = 5000000;//最小比例尺
            SelfLayerInfo.bShowTextOrNot = true;//是否显示名称
            SelfLayerInfo.iStartShowTextScale = 500000;//开始显示名称的最小比例尺
            var SelfLayerPos = API_AddNewLayer(SelfLayerInfo,null); //添加图层，得到图层的pos
            if (SelfLayerPos > -1) {
                var SelfStyle = [];
                SelfStyle.borderWith = 1; //线的粗细
                SelfStyle.borderColor = "#00143F"; //线的颜色
                SelfStyle.bFilled = true; //是否填充颜色
                SelfStyle.fillColor = "#246fa8"; //填充的颜色
                SelfStyle.iOpacity = 10; //透明度
                SelfStyle.bShowImg = false;
                SelfStyle.bShowText = true; //是否显示名称
                SelfStyle.textColor = "#000000"; //名称颜色
                SelfStyle.fontSize = "12px"; //名称字体大小
                SelfStyle.iTextOpacity = 100; //透明度
                SelfStyle.iLineOpacity = 50;
                SelfStyle.offsetScrnPo = {x:-10,y:15};
                SelfStyle.lineType = 1;//绘制实线、1=虚线
                SelfStyle.lineLen = 5;
                SelfStyle.dashLen = 2;

                global.g_iAreaSelfStylePos = API_AddCompositeLayerStyleByPos(SelfLayerPos, SelfStyle);
            }

            // ---------------------------------------添加综合图层-线标注-----------------------------------------
            var ComprehensiveLayerInfo = [];
            ComprehensiveLayerInfo.id = global.g_iComprehensiveLayerId;
            ComprehensiveLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层 composite = 100 = 综合图层
            ComprehensiveLayerInfo.name = "综合图层"; //图层名称
            ComprehensiveLayerInfo.bShow = true; //显示
            ComprehensiveLayerInfo.minShowScale = 1;//最大比例尺
            ComprehensiveLayerInfo.maxShowScale = 50000000;//最小比例尺
            ComprehensiveLayerInfo.bShowTextOrNot = true;//是否显示名称
            ComprehensiveLayerInfo.iStartShowTextScale = 500000;//开始显示名称的最小比例尺
            var ComprehensiveLayerPos = API_AddNewLayer(ComprehensiveLayerInfo,null); //添加图层，得到图层的pos
            if (ComprehensiveLayerPos > -1) {
                var ComprehensiveStyle = [];
                ComprehensiveStyle.borderWith = 2; //线的粗细
                ComprehensiveStyle.borderColor = "#FFB90F"; //线的颜色
                ComprehensiveStyle.bFilled = true; //是否填充颜色
                ComprehensiveStyle.fillColor = "#246fa8"; //填充的颜色
                ComprehensiveStyle.iOpacity = 100; //透明度
                ComprehensiveStyle.bShowImg = false;
                ComprehensiveStyle.bShowText = true; //是否显示名称
                ComprehensiveStyle.textColor = "#000000"; //名称颜色
                ComprehensiveStyle.fontSize = "12px"; //名称字体大小
                ComprehensiveStyle.iTextOpacity = 100; //透明度
                ComprehensiveStyle.iLineOpacity = 50;
                ComprehensiveStyle.offsetScrnPo = {x:-10,y:15};
                ComprehensiveStyle.lineType = 1;//绘制实线、1=虚线
                ComprehensiveStyle.lineLen = 10;
                ComprehensiveStyle.dashLen = 2;

                global.g_iComprehensiveStylePos = API_AddCompositeLayerStyleByPos(ComprehensiveLayerPos, ComprehensiveStyle);
            }

            //--------------------------------------添加摄像头图层(也是点标注一种)--------------
            var cameraLayerInfo = [];
            cameraLayerInfo.id = global.g_iCameraLayerId;
            cameraLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层
            cameraLayerInfo.name = "气象图层"; //图层名称
            cameraLayerInfo.bShow = true; //显示
            cameraLayerInfo.bShowImg = true;//显示图片
            cameraLayerInfo.minShowScale = 1;//最小显示比例尺
            cameraLayerInfo.maxShowScale = 500000; //最大显示比例尺
            var cameraLayerPos = API_AddNewLayer(cameraLayerInfo,null); //添加图层，得到图层的pos
            if (cameraLayerPos > -1) {
                var cameraStyle = [];
                cameraStyle.borderWith = 3; //线的粗细
                cameraStyle.borderColor = "#FF0000"; //线的颜色
                cameraStyle.bFilled = true; //是否填充颜色
                cameraStyle.fillColor = "#FFFF00"; //填充的颜色
                cameraStyle.iOpacity = 90; //透明度
                cameraStyle.bShowImg = true;
                cameraStyle.strImgSrc = '/static/img/camera-urgent.png'; //图片地址
                cameraStyle.iImgWidth = 16; //图片的宽度
                cameraStyle.iImgHeight = 16; //图片的高度
                cameraStyle.bShowText = false; //是否显示名称
                cameraStyle.textColor = "#000000"; //名称颜色
                cameraStyle.fontSize = "12px"; //名称字体大小
                cameraStyle.iTextOpacity = 60; //透明度
                cameraStyle.iLineOpacity = 50;
                cameraStyle.offsetScrnPo = {x: 0, y: 0};
                cameraStyle.lineType = 0;//绘制实线、1=虚线
                cameraStyle.lineLen = 6;
                cameraStyle.dashLen = 4;

                global.g_iCameraStylePos = API_AddCompositeLayerStyleByPos(cameraLayerPos, cameraStyle);
            }

            //--------------------------------------添加标注图层--------------
            var markLayerInfo = [];
            markLayerInfo.id = global.g_iMarkLayerId;
            markLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层
            markLayerInfo.name = "标注图层"; //图层名称
            markLayerInfo.bShow = true; //显示
            markLayerInfo.bShowImg = true;//显示图片
            markLayerInfo.minShowScale = 1;//最小显示比例尺
            markLayerInfo.maxShowScale = 5000000; //最大显示比例尺
            var markLayerPos = API_AddNewLayer(markLayerInfo,null); //添加图层，得到图层的pos
            if (markLayerPos > -1) {
                var markStyle = [];
                markStyle.borderWith = 3; //线的粗细
                markStyle.borderColor = "#FF0000"; //线的颜色
                markStyle.bFilled = true; //是否填充颜色
                markStyle.fillColor = "#FFFF00"; //填充的颜色
                markStyle.iOpacity = 10; //透明度
                markStyle.bShowImg = true;
                markStyle.strImgSrc = '/static/img/point.png'; //图片地址
                markStyle.iImgWidth = 16; //图片的宽度
                markStyle.iImgHeight = 16; //图片的高度
                markStyle.bShowText = false; //是否显示名称
                markStyle.textColor = "#000000"; //名称颜色
                markStyle.fontSize = "12px"; //名称字体大小
                markStyle.iTextOpacity = 60; //透明度
                markStyle.iLineOpacity = 50;
                markStyle.offsetScrnPo = {x: 0, y: 0};
                markStyle.lineType = 0;//绘制实线、1=虚线
                markStyle.lineLen = 6;
                markStyle.dashLen = 4;

                global.g_iMarkStylePos = API_AddCompositeLayerStyleByPos(markLayerPos, markStyle);
            }

            //--------------------------------------添加定位点物标图层--------------
            var locationLayerInfo = [];
            locationLayerInfo.id = global.g_iLocationLayerId;
            locationLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层
            locationLayerInfo.name = "定位图层"; //图层名称
            locationLayerInfo.bShow = true; //显示
            locationLayerInfo.bShowImg = true;//显示图片
            locationLayerInfo.minShowScale = 1;//最小显示比例尺
            locationLayerInfo.maxShowScale = 5000000; //最大显示比例尺
            var locationLayerPos = API_AddNewLayer(locationLayerInfo, null); //添加图层，得到图层的pos
            if (locationLayerPos > -1) {
                var locationStyle = [];
                locationStyle.borderWith = 3; //线的粗细
                locationStyle.borderColor = "#FF0000"; //线的颜色
                locationStyle.bFilled = true; //是否填充颜色
                locationStyle.fillColor = "#FFFF00"; //填充的颜色
                locationStyle.iOpacity = 10; //透明度
                locationStyle.bShowImg = true;
                locationStyle.strImgSrc = '../../static/ico/location.png'; //图片地址
                locationStyle.iImgWidth = 32; //图片的宽度
                locationStyle.iImgHeight = 32; //图片的高度
                locationStyle.bShowText = false; //是否显示名称
                locationStyle.textColor = "#000000"; //名称颜色
                locationStyle.fontSize = "12px"; //名称字体大小
                locationStyle.iTextOpacity = 60; //透明度
                locationStyle.iLineOpacity = 50;
                locationStyle.offsetScrnPo = {x: 0, y: 0};
                locationStyle.lineType = 0;//绘制实线、1=虚线
                locationStyle.lineLen = 6;
                locationStyle.dashLen = 4;

                global.g_iLocationStylePos = API_AddCompositeLayerStyleByPos(locationLayerPos, locationStyle);
            }

            //--------------------------------------添加监测船舶的监测区域--------------
            var jianCeLayerInfo = [];
            jianCeLayerInfo.id = global.g_iJianCeLayerId;
            jianCeLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层
            jianCeLayerInfo.name = "定位图层"; //图层名称
            jianCeLayerInfo.bShow = true; //显示
            jianCeLayerInfo.bShowImg = true;//显示图片
            jianCeLayerInfo.minShowScale = 1;//最小显示比例尺
            jianCeLayerInfo.maxShowScale = 5000000; //最大显示比例尺
            var jianCeLayerPos = API_AddNewLayer(jianCeLayerInfo, null); //添加图层，得到图层的pos
            if (jianCeLayerPos > -1) {
                var jianCeStyle = [];
                jianCeStyle.borderWith = 3; //线的粗细
                jianCeStyle.borderColor = "#FF0000"; //线的颜色
                jianCeStyle.bFilled = true; //是否填充颜色
                jianCeStyle.fillColor = "#FFFF00"; //填充的颜色
                jianCeStyle.iOpacity = 10; //透明度
                jianCeStyle.bShowImg = true;
                jianCeStyle.strImgSrc = '../../static/ico/location.png'; //图片地址
                jianCeStyle.iImgWidth = 32; //图片的宽度
                jianCeStyle.iImgHeight = 32; //图片的高度
                jianCeStyle.bShowText = false; //是否显示名称
                jianCeStyle.textColor = "#000000"; //名称颜色
                jianCeStyle.fontSize = "12px"; //名称字体大小
                jianCeStyle.iTextOpacity = 60; //透明度
                jianCeStyle.iLineOpacity = 50;
                jianCeStyle.offsetScrnPo = {x: 0, y: 0};
                jianCeStyle.lineType = 0;//绘制实线、1=虚线
                jianCeStyle.lineLen = 6;
                jianCeStyle.dashLen = 4;

                global.g_iJianCeStylePos = API_AddCompositeLayerStyleByPos(jianCeLayerPos, jianCeStyle);
            }
        }

        function Test_AddShipStyle () {
            // 渔船
            var fishStyle = [];
            fishStyle.bImgSymbol = true;
            fishStyle.strImgSrc = './img/ship1.png';
            fishStyle.iImgWidth = 14;
            fishStyle.iImgHeight = 13;
            fishStyle.minScale = 1;                       //最小显示比例尺
            fishStyle.maxScale = 1000000000;              //最大显示比例尺
            fishStyle.iOpacity = 100;                      //透明度
            fishStyle.fillColor = "#FF0000";              //填充颜色

            var iShipStatePos1 = API_AddNewShipState(0);        //添加第一种船舶状态，状态值0
            var bResult1 = API_AddShipStateStyleByPos(iShipStatePos1, fishStyle); //船舶的状态0

            // 执法船
            var lawStyle = [];
            lawStyle.bImgSymbol = true;
            lawStyle.strImgSrc = './img/ship2.png';
            lawStyle.iImgWidth = 14;
            lawStyle.iImgHeight = 13;
            lawStyle.minScale = 1;                       //最小显示比例尺
            lawStyle.maxScale = 1000000000;              //最大显示比例尺
            lawStyle.iOpacity = 100;                      //透明度
            lawStyle.fillColor = "#FF0000";              //填充颜色

            var iShipStatePos1 = API_AddNewShipState(1);        //添加第一种船舶状态，状态值0
            var bResult1 = API_AddShipStateStyleByPos(iShipStatePos1, lawStyle); //船舶的状态0

            // 监测船
            var monitoringStyle = [];
            monitoringStyle.bImgSymbol = true;
            monitoringStyle.strImgSrc = './img/ship3.png';
            monitoringStyle.iImgWidth = 14;
            monitoringStyle.iImgHeight = 13;
            monitoringStyle.minScale = 1;                       //最小显示比例尺
            monitoringStyle.maxScale = 1000000000;              //最大显示比例尺
            monitoringStyle.iOpacity = 100;                      //透明度
            monitoringStyle.fillColor = "#FF0000";              //填充颜色

            var iShipStatePos1 = API_AddNewShipState(2);        //添加第一种船舶状态，状态值0
            var bResult1 = API_AddShipStateStyleByPos(iShipStatePos1, monitoringStyle); //船舶的状态0

            // 外省渔船
            var provincesStyle = [];
            provincesStyle.bImgSymbol = true;
            provincesStyle.strImgSrc = './img/ship4.png';
            provincesStyle.iImgWidth = 14;
            provincesStyle.iImgHeight = 13;
            provincesStyle.minScale = 1;                       //最小显示比例尺
            provincesStyle.maxScale = 1000000000;              //最大显示比例尺
            provincesStyle.iOpacity = 100;                      //透明度
            provincesStyle.fillColor = "#FF0000";              //填充颜色

            var iShipStatePos1 = API_AddNewShipState(3);        //添加第一种船舶状态，状态值0
            var bResult1 = API_AddShipStateStyleByPos(iShipStatePos1, provincesStyle); //船舶的状态0

            // 重点跟踪渔船
            var importStyle = [];
            importStyle.bImgSymbol = true;
            importStyle.strImgSrc = './img/ship5.png';
            importStyle.iImgWidth = 14;
            importStyle.iImgHeight = 13;
            importStyle.minScale = 1;                       //最小显示比例尺
            importStyle.maxScale = 1000000000;              //最大显示比例尺
            importStyle.iOpacity = 100;                      //透明度
            importStyle.fillColor = "#FF0000";              //填充颜色

            var iShipStatePos1 = API_AddNewShipState(4);        //添加第一种船舶状态，状态值0
            var bResult1 = API_AddShipStateStyleByPos(iShipStatePos1, importStyle); //船舶的状态0

            // 预警渔船
            var warningStyle = [];
            warningStyle.bImgSymbol = true;
            warningStyle.strImgSrc = './img/ship6.png';
            warningStyle.iImgWidth = 14;
            warningStyle.iImgHeight = 13;
            warningStyle.minScale = 1;                       //最小显示比例尺
            warningStyle.maxScale = 1000000000;              //最大显示比例尺
            warningStyle.iOpacity = 100;                      //透明度
            warningStyle.fillColor = "#FF0000";              //填充颜色

            var iShipStatePos1 = API_AddNewShipState(5);        //添加第一种船舶状态，状态值0
            var bResult1 = API_AddShipStateStyleByPos(iShipStatePos1, warningStyle); //船舶的状态0

            // 雷达目标
            var radarStyle = [];
            radarStyle.bImgSymbol = true;
            radarStyle.strImgSrc = './img/ship7.png';
            radarStyle.iImgWidth = 14;
            radarStyle.iImgHeight = 13;
            radarStyle.minScale = 1;                       //最小显示比例尺
            radarStyle.maxScale = 1000000000;              //最大显示比例尺
            radarStyle.iOpacity = 100;                      //透明度
            radarStyle.fillColor = "#FF0000";              //填充颜色

            var iShipStatePos1 = API_AddNewShipState(6);        //添加第一种船舶状态，状态值0
            var bResult1 = API_AddShipStateStyleByPos(iShipStatePos1, radarStyle); //船舶的状态0

            // 商船
            var merchantStyle = [];
            merchantStyle.bImgSymbol = true;
            merchantStyle.strImgSrc = './img/ship8.png';
            merchantStyle.iImgWidth = 14;
            merchantStyle.iImgHeight = 13;
            merchantStyle.minScale = 1;                       //最小显示比例尺
            merchantStyle.maxScale = 1000000000;              //最大显示比例尺
            merchantStyle.iOpacity = 100;                      //透明度
            merchantStyle.fillColor = "#FF0000";              //填充颜色

            var iShipStatePos1 = API_AddNewShipState(7);        //添加第一种船舶状态，状态值0
            var bResult1 = API_AddShipStateStyleByPos(iShipStatePos1, merchantStyle); //船舶的状态0

            API_SetOffLineShipStyle(false, {strFillColor:"#FF0000",iBorderSize:2,strBorderColor:"#FFFFFF"});
        }
        
    $(function () {
      init();
    })
  },
  methods: {
    getShipInfo: function (sec) {
        if(sec == null){
            sec = 20;
        }
        $.ajaxSettings.async = true;
        $.get(global.IP + "/web/GetBDShipPosition?sec="+ sec, function (data, status) {
            // 先清空地图上已有的船舶（如有API可用，可加上清空逻辑）
            // API_ClearAllShips && API_ClearAllShips(); // 如有此API可用

            // 先清空自定义按钮
            var mapDiv = document.getElementById('map');
            var oldBtns = mapDiv.querySelectorAll('.ship-info-btn');
            oldBtns.forEach(function(btn){ btn.remove(); });

            for (var i = 0; i < data.length; i++) {
                var bOnlineOrNot = true;
                var bShowTrack = false;
                var curShipInfo = [];
                curShipInfo.shipId = data[i].staticShipId;
                curShipInfo.shipMMSI = data[i].bdid;
                curShipInfo.shipName = data[i].shipname;
                var ow = 'null';
                var lx = 'null';
                if(data[i].owner != null) ow = data[i].owner;
                if(data[i].lxdh != null) lx = data[i].lxdh;
                curShipInfo.customFields = ow + ',' + lx;
                curShipInfo.shipGeoPoX = data[i].lon;
                curShipInfo.shipGeoPoY = data[i].lat;
                curShipInfo.shipWidth = data[i].width || 0;
                curShipInfo.shipLength = data[i].length || 0;
                curShipInfo.shipSpeed = data[i].speed;
                curShipInfo.shipCourse = data[i].cog;
                curShipInfo.shipTime = data[i].loadTime;
                // 设置船舶状态，决定显示什么图标
                if(data[i].shiptype == 2) {
                    curShipInfo.iShipState = 0;   //渔船
                } else if(data[i].shiptype == 3 || data[i].shiptype == 65 || data[i].shiptype == 75) {
                    curShipInfo.iShipState = 1;   //执法船
                } else if(data[i].shiptype == 4) {
                    curShipInfo.iShipState = 0;   //渔船
                } else if(data[i].shiptype == 6 || data[i].shiptype == 69 ||
                    data[i].shiptype == 62 || data[i].shiptype == 64 || data[i].shiptype == 63 ||
                    data[i].shiptype == 66 || data[i].shiptype == 67 || data[i].shiptype == 68 || data[i].shiptype == 610) {
                    curShipInfo.iShipState = 7;   //商船
                } else if(data[i].shiptype == 61){
                    curShipInfo.iShipState = 3;   //外省渔船
                } else if(data[i].shiptype == 7 || data[i].shiptype == 71 ||
                    data[i].shiptype == 72 || data[i].shiptype == 74 || data[i].shiptype == 73 ||
                    data[i].shiptype == 76 || data[i].shiptype == 77 || data[i].shiptype == 78 || data[i].shiptype == 79 || data[i].shiptype == 710) {
                    curShipInfo.iShipState = 7;   //商船
                } else {
                    curShipInfo.iShipState = 7;
                }
                if(data[i].boutside == 1){
                   curShipInfo.iShipState = 3;
                }
                curShipInfo.bOnlineOrNot = bOnlineOrNot;
                curShipInfo.bShowTrack = bShowTrack;
                curShipInfo.arrExpAttrValue = [];
                // 补充必要字段
                curShipInfo.staticShipId = data[i].staticShipId || data[i].bdid || data[i].bdId;
                curShipInfo.shipName = data[i].shipname || data[i].shipName;
                curShipInfo.lon = data[i].lon;
                curShipInfo.lat = data[i].lat;
                // 打印添加船舶curShipInfo到控制台
                //console.log('添加船舶', curShipInfo);
                // 直接添加到地图（如需去重可加判断）
                API_AddOneShip(curShipInfo);

                // 添加自定义按钮
                if(typeof API_LonLatToScrnPo === 'function') {
                  var scrnPo = API_LonLatToScrnPo({x: data[i].lon, y: data[i].lat});
                  if(scrnPo) {
                    var btn = document.createElement('button');
                    btn.innerText = '详情';
                    btn.className = 'ship-info-btn';
                    btn.style.position = 'absolute';
                    btn.style.left = (scrnPo.x - 20) + 'px';
                    btn.style.top = (scrnPo.y - 20) + 'px';
                    btn.style.zIndex = 9999;
                    btn.onclick = (function(id, name) {
                      return function() {
                        alert('staticShipId: ' + id + '\nshipname: ' + name);
                      }
                    })(data[i].staticShipId, data[i].shipname);
                    mapDiv.appendChild(btn);
                  }
                }
            }
            API_SetShipSpeedLineSize(0, false);
            API_SetShipHeadLineSize(0, false);
        }).fail(function(msg){
            console.log("error：" + JSON.stringify(msg))
        });
        $.ajaxSettings.async = true;
    },

    //更新渔船状态
    updateShipState: function () {
        for(var k = 0; k < global.jianCeUpdateShipInfo.length; k++) {
            var shipId = global.jianCeUpdateShipInfo[k].shipId;
            var shipStste = 0;
            if(global.jianCeUpdateShipInfo[k].isCheck == true) {
                shipStste = 2;
            }
            else {
                if(global.jianCeUpdateShipInfo[k].shiptype == 2) {
                    shipStste = 0;   //状态
                }
                else if(global.jianCeUpdateShipInfo[k].shiptype == 3) {
                    shipStste = 1;   //状态
                }
                else if(global.jianCeUpdateShipInfo[k].shiptype == 4) {
                    shipStste = 0;   //状态
                }
                else if(global.jianCeUpdateShipInfo[k].shiptype == 6 || global.jianCeUpdateShipInfo[k].shiptype == 61 ||
                global.jianCeUpdateShipInfo[k].shiptype == 62 || global.jianCeUpdateShipInfo[k].shiptype == 63 ||
                global.jianCeUpdateShipInfo[k].shiptype == 64 || global.jianCeUpdateShipInfo[k].shiptype == 65 ||
                global.jianCeUpdateShipInfo[k].shiptype == 66 || global.jianCeUpdateShipInfo[k].shiptype == 67 ||
                global.jianCeUpdateShipInfo[k].shiptype == 68 || global.jianCeUpdateShipInfo[k].shiptype == 69 ||
                global.jianCeUpdateShipInfo[k].shiptype == 610) {
                    shipStste = 3;   //状态
                }
                else if(global.jianCeUpdateShipInfo[k].shiptype == 7 || global.jianCeUpdateShipInfo[k].shiptype == 71 ||
                global.jianCeUpdateShipInfo[k].shiptype == 72 || global.jianCeUpdateShipInfo[k].shiptype == 73 ||
                global.jianCeUpdateShipInfo[k].shiptype == 74 || global.jianCeUpdateShipInfo[k].shiptype == 75 ||
                global.jianCeUpdateShipInfo[k].shiptype == 76 || global.jianCeUpdateShipInfo[k].shiptype == 77 ||
                global.jianCeUpdateShipInfo[k].shiptype == 78 || global.jianCeUpdateShipInfo[k].shiptype == 79 ||
                global.jianCeUpdateShipInfo[k].shiptype == 710) {
                    shipStste = 7;   //状态
                }
                else{
                    shipStste = 7;
                }

                if(global.jianCeUpdateShipInfo[k].bOutSide == 1) {
                    shipStste = 3;
                }
                global.jianCeUpdateShipInfo.splice(k, 1);
            }

            var pos = API_GetShipPosById(shipId);
            if(pos != -1) {
                var info = API_GetShipInfoByPos(pos);
                if(info != null) {
                    var curShipDynamicInfo = [];//更新的时候，只要把这些值设置好即可
                    curShipDynamicInfo.shipGeoPoX = info.shipGeoPoX;
                    curShipDynamicInfo.shipGeoPoY = info.shipGeoPoY;
                    curShipDynamicInfo.shipSpeed = info.shipSpeed;
                    curShipDynamicInfo.shipCourse = info.shipCourse;
                    curShipDynamicInfo.shipTime = info.shipTime;
                    curShipDynamicInfo.iShipState = shipStste;
                    curShipDynamicInfo.bOnlineOrNot = info.bOnlineOrNot;

                    API_UpdateOneShipDynamicInfoByPos(pos, curShipDynamicInfo); //更新一艘船舶动态信息
                }
            }
        }

        for(var k = 0; k < global.focusUpdateShipInfo.length; k++) {
            var shipId = global.focusUpdateShipInfo[k].shipId;
            var shipStste = 0;
            if(global.focusUpdateShipInfo[k].isCheck == true) {
                shipStste = 4;
            }
            else {
                if(global.focusUpdateShipInfo[k].shiptype == 2) {
                    shipStste = 0;   //状态
                }
                else if(global.focusUpdateShipInfo[k].shiptype == 3 || global.focusUpdateShipInfo[k].shiptype == 63 ||
                global.focusUpdateShipInfo[k].shiptype == 73) {
                    shipStste = 1;   //状态
                }
                else if(global.focusUpdateShipInfo[k].shiptype == 4) {
                    shipStste = 0;   //状态
                }
                else if(global.focusUpdateShipInfo[k].shiptype == 6 || global.focusUpdateShipInfo[k].shiptype == 61 ||
                global.focusUpdateShipInfo[k].shiptype == 62 ||
                global.focusUpdateShipInfo[k].shiptype == 64 || global.focusUpdateShipInfo[k].shiptype == 65 ||
                global.focusUpdateShipInfo[k].shiptype == 66 || global.focusUpdateShipInfo[k].shiptype == 67 ||
                global.focusUpdateShipInfo[k].shiptype == 68 || global.focusUpdateShipInfo[k].shiptype == 69 ||
                global.focusUpdateShipInfo[k].shiptype == 610) {
                    shipStste = 3;   //状态
                }
                else if(global.focusUpdateShipInfo[k].shiptype == 7 || global.focusUpdateShipInfo[k].shiptype == 71 ||
                global.focusUpdateShipInfo[k].shiptype == 72 ||
                global.focusUpdateShipInfo[k].shiptype == 74 || global.focusUpdateShipInfo[k].shiptype == 75 ||
                global.focusUpdateShipInfo[k].shiptype == 76 || global.focusUpdateShipInfo[k].shiptype == 77 ||
                global.focusUpdateShipInfo[k].shiptype == 78 || global.focusUpdateShipInfo[k].shiptype == 79 ||
                global.focusUpdateShipInfo[k].shiptype == 710) {
                    shipStste = 7;   //状态
                }
                else{
                    shipStste = 7;
                }

                if(global.focusUpdateShipInfo[k].bOutSide == 1) {
                    shipStste = 3;
                }
                global.focusUpdateShipInfo.splice(k, 1);
            }

            var pos = API_GetShipPosById(shipId);
            if(pos != -1) {
                if(shipStste == 4) {
                    API_SetShipAllFollow(pos, true);
                }
                else {
                    API_SetShipAllFollow(pos, false);
                }

                var info = API_GetShipInfoByPos(pos);
                if(info != null) {
                    var curShipDynamicInfo = [];//更新的时候，只要把这些值设置好即可
                    curShipDynamicInfo.shipGeoPoX = info.shipGeoPoX;
                    curShipDynamicInfo.shipGeoPoY = info.shipGeoPoY;
                    curShipDynamicInfo.shipSpeed = info.shipSpeed;
                    curShipDynamicInfo.shipCourse = info.shipCourse;
                    curShipDynamicInfo.shipTime = info.shipTime;
                    curShipDynamicInfo.iShipState = shipStste;
                    curShipDynamicInfo.bOnlineOrNot = info.bOnlineOrNot;

                    API_UpdateOneShipDynamicInfoByPos(pos, curShipDynamicInfo); //更新一艘船舶动态信息
                }
            }
        }

        for(var k = 0; k < global.warningUpdateShipInfo.length; k++) {
            var shipId = global.warningUpdateShipInfo[k].shipId;
            var shipStste = 0;
            if(global.warningUpdateShipInfo[k].isCheck == true && _this.legengShow[8] == true) {
                // 该船是预警船
                shipStste = 5;
            }
            else {
                if(global.warningUpdateShipInfo[k].shiptype == 2) {
                    shipStste = 0;   //状态
                }
                else if(global.warningUpdateShipInfo[k].shiptype == 3 || global.warningUpdateShipInfo[k].shiptype == 65 || global.warningUpdateShipInfo[k].shiptype == 75) {
                    shipStste = 1;   //状态
                }
                else if(global.warningUpdateShipInfo[k].shiptype == 4) {
                    shipStste = 0;   //状态
                }
                else if(global.warningUpdateShipInfo[k].shiptype == 6 || global.warningUpdateShipInfo[k].shiptype == 61 ||
                global.warningUpdateShipInfo[k].shiptype == 62 || global.warningUpdateShipInfo[k].shiptype == 63 ||
                global.warningUpdateShipInfo[k].shiptype == 64 ||
                global.warningUpdateShipInfo[k].shiptype == 66 || global.warningUpdateShipInfo[k].shiptype == 67 ||
                global.warningUpdateShipInfo[k].shiptype == 68 || global.warningUpdateShipInfo[k].shiptype == 69 ||
                global.warningUpdateShipInfo[k].shiptype == 610) {
                    shipStste = 3;   //状态
                }
                else if(global.warningUpdateShipInfo[k].shiptype == 7 || global.warningUpdateShipInfo[k].shiptype == 71 ||
                global.warningUpdateShipInfo[k].shiptype == 72 || global.warningUpdateShipInfo[k].shiptype == 73 ||
                global.warningUpdateShipInfo[k].shiptype == 74 ||
                global.warningUpdateShipInfo[k].shiptype == 76 || global.warningUpdateShipInfo[k].shiptype == 77 ||
                global.warningUpdateShipInfo[k].shiptype == 78 || global.warningUpdateShipInfo[k].shiptype == 79 ||
                global.warningUpdateShipInfo[k].shiptype == 710) {
                    shipStste = 7;   //状态
                }
                else{
                    shipStste = 7;
                }

                if(global.warningUpdateShipInfo[k].bOutSide == 1) {
                    shipStste = 3;
                }
                global.warningUpdateShipInfo.splice(k, 1);
            }

            var pos = API_GetShipPosById(shipId);
            if(pos != -1) {
                var info = API_GetShipInfoByPos(pos);
                if(info != null) {
                    var curShipDynamicInfo = [];//更新的时候，只要把这些值设置好即可
                    curShipDynamicInfo.shipGeoPoX = info.shipGeoPoX;
                    curShipDynamicInfo.shipGeoPoY = info.shipGeoPoY;
                    curShipDynamicInfo.shipSpeed = info.shipSpeed;
                    curShipDynamicInfo.shipCourse = info.shipCourse;
                    curShipDynamicInfo.shipTime = info.shipTime;
                    curShipDynamicInfo.iShipState = shipStste;
                    curShipDynamicInfo.bOnlineOrNot = info.bOnlineOrNot;

                    API_UpdateOneShipDynamicInfoByPos(pos, curShipDynamicInfo); //更新一艘船舶动态信息
                }
            }
        }
    },

  
    handleContextMenu(event) {
      // 处理右键菜单
    },
    //根据船名和终端号的搜索
    handleSearch() {
      console.log('执行搜索' + this.searchQuery + global.IP + "/web/QueryShip?keyword=" + this.searchQuery);
      var searchVal = this.searchQuery;
      if (searchVal == null || searchVal == undefined || searchVal == '') {
        setAlertWindowShow('search-container', '未输入数据', '', 2);
        return;
      }

      $.get(global.IP + "/web/QueryShip?keyword=" + searchVal, function (data, status) {
        if (data.length == 0) {
          _this.showSearchResults = false;
          setAlertWindowShow('search-container', '未找到匹配结果', '', 2);
          return;
        }
        console.log(data);
        _this.searchResults = data.slice(0, 10).map(item => ({
          bdid: item.bdId,
          shipName: item.shipName,
        }));
        _this.showSearchResults = true;
      }).fail(function (msg) {
        console.log("error：" + JSON.stringify(msg))
      });
    },
    handleShipClick(ship) {
      console.log('选中船舶:', ship);
      
      this.closeSearchResults();
    },
    setActiveFilter(filterValue) {
      this.activeFilter = filterValue;
      // 这里可以调用API根据时间筛选船舶
      console.log('筛选:', filterValue);
    },
    closeSearchResults() {
      this.showSearchResults = false;
    },
    viewDetails(ship) {
      // 查看详情逻辑
      console.log('查看详情:', ship);
    },

    editShip(ship) {
      // 编辑逻辑
      console.log('编辑:', ship);
    },
    showLocation: function (shipPos) {
            setTimeout(function () {
                // API_SetMapLevel(10, null);
                API_SetShipToMapViewCenterByPos(shipPos);
                API_ReDrawShips();
            }, 500);
    },

    //显示测距信息
    GetCurMeasureDist: function (CurDis, allMeasureDist, CurDegrees) {
            var allMile = parseInt(allMeasureDist * 1000); //转换成米
            var curMile = parseInt(CurDis * 1000);
            var curHaiLi = curMile / 1852;
            var curAllHaiLi = allMile / 1852;

            var strAllMile = "";
            var strCurMile = "";
            if (allMile > 1000) {
                strAllMile = (allMile / 1000).toFixed(2) + "千米（" + curAllHaiLi.toFixed(2) + "海里）";
            }
            else {
                strAllMile = allMile + "米（" + curHaiLi.toFixed(2) + "海里）";
            }

            if (curMile > 1000) {
                strCurMile = (curMile / 1000).toFixed(2) + "千米（" + curHaiLi.toFixed(2) + "海里）";
            }
            else {
                strCurMile = curMile + "米（" + curHaiLi.toFixed(2) + "海里）";
            }

            // document.getElementById("allMeasureDist").innerHTML = strAllMile;

            // document.getElementById("curDis").innerHTML = strCurMile;
            // var curFangWei = Math.round(CurDegrees * 1000) / 1000;
            // document.getElementById("curDegrees").innerHTML = curFangWei.toFixed(2) + "度";
        },

    //鼠标事件
// =====================return sdk
        ReturnSelectObjByMouseMoveTest: function (objInfo) {

            var lonEl = document.getElementById('lonText');
            if (lonEl) {
              lonEl.innerHTML = parseInt(API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).x) + '°' +
                  + parseInt((API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).x - parseInt(API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).x)) * 60) + "'" +
                  parseInt(((API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).x - parseInt(API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).x)) * 60 - parseInt((API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).x - parseInt(API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).x)) * 60)) * 60) + '"' + 'E';
            }
            var latEl = document.getElementById('latText');
            if (latEl) {
              latEl.innerHTML =parseInt(API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).y) + '°' +
                  + parseInt((API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).y - parseInt(API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).y)) * 60) + "'" +
                  parseInt(((API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).y - parseInt(API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).y)) * 60 - parseInt((API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).y - parseInt(API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).y)) * 60)) * 60) + '"' + 'N';
            }
            //API_SetCurHighLightObjectById(-1, -1);
            if (objInfo) {
                var scrnPo = objInfo.po;
                switch (objInfo.objType) {//{objType,id,po}
                    case 1: //选中了船舶，得到船舶的id,pos
                        if(objInfo.bClusterers == true)
                        {

                        }
                        else
                        {
                            var iShipId = objInfo.id;
                            var bSelPlayTrackShip = objInfo.bSelPlayTrackShip;//是否选中了轨迹回放的船舶
                            var iTrackPos = objInfo.iTrackPos; //假如是轨迹回放，则是选中轨迹点pos
                        }
                        break;
                    case 2: //选中了标注，得到标注所属的图层layerId以及标注objId
                        var layerId = objInfo.layerId;//图层的id
                        var objId = objInfo.objId;//标注的id

                        if(layerId == global.g_iWeatherLayerId) {
                            _this.$options.methods.ShowObjSimpleInfo.bind(this)(layerId, objId, scrnPo);
                        }
                        else if(layerId == global.g_iCameraLayerId) {
                            _this.$options.methods.showCameraInfo.bind(this)(layerId, objId, scrnPo);
                        }
                        else if(layerId == global.g_iMarkLayerId) {
                            _this.$options.methods.showMarkInfo.bind(this)(layerId, objId, scrnPo);
                        }
                        else if(layerId == global.g_iJianCeLayerId) {
                            _this.$options.methods.showJianCeInfo.bind(this)(layerId, objId, scrnPo);
                        }
                        else {
                            if (global.g_showSimpleInfoDiv) {
                                global.g_showSimpleInfoDiv.style.display = "none";
                            }
                        }

                        break;
                    case 3: //选中了台风轨迹信息
                        var typhoonId = objInfo.typhoonId; //台风id
                        var iTruePos = objInfo.iTruePos; //台风真实轨迹点pos
                        var iPredictPos = objInfo.iPredictPos; //真实轨迹点的预测轨迹点pos
                        var iPredictLinePos = objInfo.iPredictLinePos; //真实轨迹点的预测轨迹点pos
                        _this.$options.methods.ShowTyphoonTrackSimpleInfo.bind(this)(typhoonId, iTruePos,iPredictLinePos, iPredictPos, scrnPo);
                        break;
                    case 5: //选中了船舶，得到船舶的id,pos
                        if(objInfo.bClusterers == true)
                        {

                        }
                        else
                        {
                            _this.$options.methods.ShowShipSimpleInfo.bind(this)(objInfo);
                        }
                        break;
                    case 6: //选中了船舶，得到船舶的id,pos
                        if(objInfo.bClusterers == true)
                        {

                        }
                        else
                        {
                            _this.$options.methods.ShowShipSimpleInfo.bind(this)(objInfo);
                        }
                        break;
                }
            }
            else {
                if (global.g_showSimpleInfoDiv) {
                    global.g_showSimpleInfoDiv.style.display = "none";
                }
            }
        },

        //鼠标左击时查询到的对象信息
        ReturnSelectObjByMouseLeftDownTest: function (objInfo) {

            if (objInfo == null) {
                return;
            }

            if (objInfo) {
                console.log(API_GetShipInfoByPos(API_GetShipPosById(500)).arrShipTrackPoints[API_GetShipInfoByPos(API_GetShipPosById(500)).arrShipTrackPoints.length - 1]);
                switch (objInfo.objType) {
                    case 1: //选中了船舶，得到船舶的id,pos
                        break;
                    case 2: //选中了标注，得到标注所属的图层id以及标注id
                        var layerId = objInfo.layerId; //图层的id
                        var objId = objInfo.objId; //标注的id

                        if(layerId == global.g_iCameraLayerId) {
                            _this.$options.methods.showCameraDetialInfo.bind(this)(layerId, objId);
                        }

                        break;
                    case 3:
                        _this.curTyphoonRadiusInfo = [];
                        var typhoonPos= API_GetTyphoonPosById(objInfo.typhoonId);
                        var typhoonInfoBysdk = API_GetTyphoonInfoByPos(typhoonPos);
                        var typhoonBysdk = API_GetTyphoonTrackInfoByPos(typhoonPos, objInfo.iTruePos, -1, null);

                        var radius7 = 0;
                        var radius10 = 0;
                        if(typhoonBysdk.sevenRadius != undefined) {
                            radius7 = typhoonBysdk.sevenRadius;
                        }
                        if(typhoonBysdk.tenRadius != undefined) {
                            radius10 = typhoonBysdk.tenRadius;
                        }
                        _this.curTyphoonRadiusInfo.push({po: typhoonBysdk.po, radius7: radius7, radius10: radius10});
                        break;
                    case 5: //选中了船舶，得到船舶的id,pos
                        if(objInfo.bClusterers == true)
                        {
                            var clustererShipLength = API_GetClustererShipIdInfoById(objInfo.data.id).length;
                            global.g_arrSelectShipInfoObj = {iShipCount: 0, arrShipInfo: []};
                            global.g_arrSelectShipInfoObj.arrShipInfo = [];
                            for(var i = 0; i < clustererShipLength; i++) {
                                var pos = API_GetShipPosById(API_GetClustererShipIdInfoById(objInfo.data.id)[i]);
                                var info = API_GetShipInfoByPos(pos);
                                global.g_arrSelectShipInfoObj.arrShipInfo.push(info);
                            }
                            _this.ShipSearch1Name = "船舶详情(船舶聚合)";
                            _this.wsShipInShanghaiCheck = false;
                            _this.showOutLineShipStart = false;
                            _this.areaSearchShow = false;
                            _this.showOutLineShip = false;
                            _this.ifFishArea = false;
                            _this.$options.methods.changeCurIndex.bind(this)("ShipSearch1");
                            _this.$options.methods.setShipInfoInTable.bind(this)();

                        }
                        else
                        {
                            var shipPos = API_GetShipPosById(objInfo.id);
                            sessionStorage.setItem("SHIPID", objInfo.id);
                            if(shipPos != -1) {
                                API_SelectShipShow(true);
                                API_SetSelectShipByPos(shipPos);
                                API_SetCurShipShowHistory(true);
                                _this.$options.methods.getDetailsAndSurroundingFishInfo.bind(this)(objInfo.id);
                                _this.$options.methods.setAllShipHistory.bind(this)();
                            }
                        }
                        break;
                    case 6: //选中了船舶，得到船舶的id,pos
                        break;
                }
            }
            else {
                if (global.g_showSimpleInfoDiv) {
                    global.g_showSimpleInfoDiv.style.display = "none";
                }
            }
        },

        // 设置当前点击船舶-船舶详细信息、周围渔船
        getDetailsAndSurroundingFishInfo: function (id) {
            var shipPos = API_GetShipPosById(id);
            if(shipPos == -1) {
                _this.$options.methods.centerToShip.bind(this)(id);
            }
            shipPos = API_GetShipPosById(id);
            global.focusOnTrackingShips = [];
            global.focusOnTrackingShips.push({shipId: id});
            var shipInfo = null;
            if(shipPos > -1) {
                API_SetSelectShipByPos(shipPos);
                var info = API_GetShipInfoByPos(shipPos);
                $.get(global.IP+'/web/GetOneShipInfoById?id=' + id, function (data, status) {

                    _this.shipInfoList = [];
                    var shipInfo11 = [];
                    shipInfo11 = data;
                    switch(shipInfo11[0].shiptype){
                        case 2:
                        case 61:
                            shipInfo11[0].shiptype = "渔船";
                            break;
                        case 3:
                            shipInfo11[0].shiptype = "渔政船";
                            break;
                        case 4:
                            shipInfo11[0].shiptype = "救援船";
                            break;
                        case 6:
                            shipInfo11[0].shiptype = "货船";
                            break;
                        case 7:
                            shipInfo11[0].shiptype = "货船";
                            break;
                        case 62:
                        case 72:
                            shipInfo11[0].shiptype = "货船";
                            break;
                        case 63:
                        case 73:
                            shipInfo11[0].shiptype = "客船";
                            break;
                        case 65:
                        case 75:
                        case 13:
                            shipInfo11[0].shiptype = "执法船";
                            break;
                        default:
                            shipInfo11[0].shiptype = "其他船";
                            break;
                    }
                    _this.shipInfoList = shipInfo11;
                    var info = API_GetShipInfoByPos(shipPos);
                    var lon = _this.$options.methods.ToDegrees.bind(this)(info.shipGeoPoX/10000000 + "");
                    var lat = _this.$options.methods.ToDegrees.bind(this)(info.shipGeoPoY/10000000 + "");
                    var point = lon + ' - ' + lat;


                    var lengthCount = 0;
                    if(data[0].length >= 12 && data[0].length < 24) lengthCount = 2;
                    else if(data[0].length >= 24 && data[0].length < 36) lengthCount = 2;
                    else if(data[0].length >= 36 && data[0].length < 45) lengthCount = 2;
                    else if(data[0].length >= 45) lengthCount = 3;
                    var glCount = 0;
                    if(data[0].zjglqw >= 50 && data[0].zjglqw < 250) glCount = 1;
                    else if(data[0].zjglqw >= 250 && data[0].zjglqw < 450) glCount = 2;
                    else if(data[0].zjglqw >= 450 && data[0].zjglqw < 750) glCount = 2;
                    else if(data[0].zjglqw >= 750 && data[0].zjglqw < 3000) glCount = 2;
                    else if(data[0].zjglqw >= 3000) glCount = 3;
                    if(data[0].zjglqw>800) glCount++;

                    _this.shipInfoList1 = [];
                    console.log('7.' + info.bdTime + '-- --' + info.aisTime);
                    _this.shipInfoList1.push({"point": point, 'speed': info.shipSpeed, 'time': info.shipTime, 'bdTime': data[0].bdTime, 'aisTime': data[0].aisTime, 'course': info.shipCourse,"littleCount": 0+lengthCount+glCount});
                    _this.ocusOnTrackingTheDetailsOfFishingVesselsList = data;
                    _this.bdMsgShipInfo = [];
                    _this.bdMsgShipInfo.push({shipId: id, name: data[0].shipname, bdId: data[0].bdid, lastPosTermNo: data[0].lastPosTermNo});
                });
                $.get(global.IP+'/web/GetShipImgUrl?shipId=' + id, function (data, status) {
                    // console.log(data);
                    _this.shipInfoImg = "";
                    _this.shipInfoImg = data.replace(/\s*/g,"");
                });
                API_ReDrawShips();//重绘
                // if(info.bShowTrack == false) {
                //     _this.wakeIsShowOrNot = false;
                // }
                // else {
                //     _this.wakeIsShowOrNot = true;
                // }
                global.curSelectShipId = id;
                document.getElementById('ShipSimpleInformationView').style.display = 'block';
                _this.$options.methods.changeCurIndex.bind(this)("ShipSimpleInformationView");
                _this.surroundingFishingBoatsList = [];
                if(info != null) {
                    _this.surroundingFishingBoatsList.push({shipId: id, x: info.shipGeoPoX, y: info.shipGeoPoY});
                }

                _this.$options.methods.areaLocation.bind(this)(id);
            }
        },
        //鼠标右键事件
        //scrnPo:鼠标在海图上的位置
        ReturnOnMouseRightDownTest: function (scrnPo) {
            var selObjInfo = API_SelectCurScrnShowObjectInfoByScrnPo(scrnPo, true);
            if (selObjInfo) {
                // console.log(selObjInfo);
                // console.log(API_GetCurMapMouseState());
                if(API_GetCurMapMouseState() != 0) {
                    return
                }
                // 选中算法
                var objInfo = _this.$options.methods.selectObjInfoByRight.bind(this)(selObjInfo);

                if(objInfo.layerId == -1 || objInfo.objId == -1) {
                    return
                }

                // 显示港口信息
                if(objInfo.layerId == global.g_iFishingPortLayerId) {
                    for(var i = 0; i < global.allPortInfo.length; i++) {
                        if(global.allPortInfo[i].id == objInfo.objId) {
                            _this.portProfileInfo = objInfo.objId;
                            _this.$options.methods.changeCurIndex.bind(this)("portProfileView");
                            document.getElementById('portProfileView').style.display = 'block';
                        }
                    }
                }

                if(objInfo.layerId == global.g_iAreaFuxiuLayerId || objInfo.layerId == global.g_iAreaGaoweiLayerId || objInfo.layerId == global.g_iAreaTeshuLayerId || objInfo.layerId == global.g_iAreaSelfLayerId || objInfo.layerId == global.g_iComprehensiveLayerId) {
                    _this.$options.methods.showEditFenceInfo.bind(this)(objInfo.objId);
                }

                if (objInfo.layerId == global.g_iMarkLayerId) {
                    var count = 1;
                    var layerPos = API_GetLayerPosById(objInfo.layerId);
                    var obj = API_GetObjectPosById(objInfo.objId, layerPos);
                    _this.marklayerPos = layerPos;
                    _this.markobjPos = obj;
                    _this.markLayerId = objInfo.layerId;
                    _this.markObjId = objInfo.objId;
                    var markInfo = {};
                    $.ajaxSettings.async = false;
                    $.get(global.IP + "/web/GetMarkInfoById?id=" + objInfo.objId, function(data, status) {
                        markInfo["type"] = data.type;
                        markInfo["poscount"] = data.posCount;
                        markInfo["content"] = data.content;
                        markInfo["loadTime"] = data.loadTime;
                        markInfo["userId"] = data.userId;
                        markInfo["ownShow"] = data.ownShow;
                        markInfo["radius"] = data.radius;
                        markInfo["pointStr"] = data.pointStr;
                        markInfo["id"] = data.id;
                        markInfo["lon"] = "";
                        markInfo["lat"] = "";

                    });
                    $.ajaxSettings.async = true;
                    _this.markStr = markInfo;

                    if(_this.markStr["type"] == 2){
                        document.getElementById("markName").innerHTML = _this.markStr.content;
                        _this.editradiusShow = false;
                        _this.editpointOrLineShow = true;
                        var table = document.getElementById("EditSomethingInfoTable");
                        var trs = table.getElementsByTagName("tr");
                        for (var i = trs.length - 1; i > 0; i--) {
                            trs[i].remove();
                        }
                        var pointStrLength = _this.markStr["pointStr"].split('#').length;
                        for(var j = 0; j < pointStrLength; j++) {
                            var geoPo = {x: coordinateTransformation(parseInt(_this.markStr["pointStr"].split('#')[j].split('@')[0])/10000000) , y: coordinateTransformation(parseInt(_this.markStr["pointStr"].split('#')[j].split('@')[1])/10000000) };
                            _this.geoPoAll.push(geoPo);
                            _this.markStr["lon"] += _this.markStr["lon"] + coordinateTransformation(parseInt(_this.markStr["pointStr"].split('#')[j].split('@')[0])/10000000) + ",";
                            _this.markStr["lat"] += _this.markStr["lat"] + coordinateTransformation(parseInt(_this.markStr["pointStr"].split('#')[j].split('@')[1])/10000000) + ",";
                            var str =
                                "<tr style='height: 40px;'>" +
                                "<td data-size='padding-top,padding-left' style='border: 0px; padding-top: 10px; text-align:center;'>" +
                                (j+1) +
                                "</td>" +
                                "<td data-size='padding-top' style='border: 0px; padding-top: 10px; text-align:center;'>" +
                                _this.geoPoAll[j].x +
                                "</td>" +
                                "<td data-size='padding-top' style='border: 0px; padding-top: 10px; text-align:center;'>" +
                                _this.geoPoAll[j].y +
                                "</td></tr>";
                            $("#EditSomethingInfoTable").append(str);
                        }
                    }
                    else if(_this.markStr["type"] == 5){
                        document.getElementById("markName").innerHTML = _this.markStr.content;
                        _this.pointOrLineShow = true;
                        _this.editpointOrLineShow = false;
                        _this.curMarkTypeIndex = 5;
                        _this.editradiusShow = true;
                        _this.markStr["lon"] = coordinateTransformation(parseInt(_this.markStr["pointStr"].split('@')[0])/10000000);
                        _this.markStr["lat"] = coordinateTransformation(parseInt(_this.markStr["pointStr"].split('@')[1])/10000000);
                        document.getElementById("markName").innerHTML = _this.markStr.content;
                        document.getElementById("markLon").innerHTML = _this.markStr.lon;
                        document.getElementById("markLat").innerHTML = _this.markStr.lat;
                        document.getElementById("markRadius").innerHTML = _this.markStr.radius;
                    }
                    else if(_this.markStr["type"] == 1){
                        document.getElementById("markName").innerHTML = _this.markStr.content;

                        _this.editradiusShow = false;
                        _this.editPointShow = true;
                        _this.markStr["lon"] = coordinateTransformation(parseInt(_this.markStr["pointStr"].split('@')[0])/10000000);
                        _this.markStr["lat"] = coordinateTransformation(parseInt(_this.markStr["pointStr"].split('@')[1])/10000000);
                        _this.radiusCenterX = _this.markStr["lon"];
                        _this.radiusCenterY = _this.markStr["lat"];
                        var table = document.getElementById("EditSomethingInfoTable");
                        var trs = table.getElementsByTagName("tr");
                        for (var i = trs.length - 1; i > 0; i--) {
                            trs[i].remove();
                        }
                        var str =
                            "<tr style='height: 40px;'>" +
                            "<td data-size='padding-top,padding-left' style='border: 0px; padding-top: 10px; text-align:center;'>" +
                            count +
                            "</td>" +
                            "<td data-size='padding-top' style='border: 0px; padding-top: 10px; text-align:center;'>" +
                            _this.markStr["lon"] +
                            "</td>" +
                            "<td data-size='padding-top' style='border: 0px; padding-top: 10px; text-align:center;'>" +
                            _this.markStr["lat"] +
                            "</td></tr>";
                        $("#EditSomethingInfoTable").append(str);
                    }

                    _this.aLinkDlg = false;
                    _this.editDlg = true;
                    _this.confirmDlg = false;
                    _this.drawSomethingBoxTitle = "编辑标注";
                    if(_this.leftShow == true) {
                        _this.$options.methods.ShowWindow.bind(this)('left');
                    }

                    if(_this.rightShow == true) {
                        _this.$options.methods.ShowWindow.bind(this)('right');
                    }
                    // _this.ShowWindow('left');
                    // _this.ShowWindow('right');
                    document.getElementById("drawSomethingBox").style.display = "block";
                    _this.markwindow = false;
                    _this.editmarkwindow = true;
                }
            }
            else {
                API_SetCurHighLightObjectById(-1, -1);//取消高亮
            }

            if (global.g_bCurDrawFaceForSelectShip == true) {
                global.g_bCurDrawFaceForSelectShip = false;
                //多边形查询船舶
                var arrGeoPo = API_GetCurDrawDynamicObjGeoPo();//绘制的多边形坐标
                var retShipInfo = API_SelectShipsByGeoPolygon(arrGeoPo, -1); //查询多边形内的船舶
                if (retShipInfo) {
                    _this.$options.methods.selectShipByPolygon.bind(this)(retShipInfo);
                }
            }
        },

        //鼠标左键事件
        //scrnPo:鼠标在海图上的位置
        ReturnOnMouseLeftDownTest: function (scrnPo) {
            var selObjInfo = API_SelectCurScrnShowObjectInfoByScrnPo(scrnPo, true);
            if (selObjInfo) {
                //alert(selObjInfo.length);
            }

            var shipInfo = API_GetShipInfoByScrnPo(scrnPo,50);
            if (shipInfo != null) {
                if(shipInfo.bClusterers == true)//选择聚合
                {
                    // 可选：处理聚合点击
                }
                else
                {
                    // 跳转到SimpleDialog页面，传递shipId参数
                    const shipId = shipInfo.shipId || shipInfo.bdid || shipInfo.bdId;
                    if (shipId) {
                      this.$router.push({ name: 'SimpleDialog', query: { shipId } });
                    }
                }
            }
        },

        //---------------------------------------------SdkReturnInfo--------------------------
        //显示简单的船舶信息（鼠标移动到船舶显示）
        ShowShipSimpleInfo: function (objInfo) {
            //选中的是轨迹回放的船舶
            if (objInfo.bSelPlayTrackShip == true) {
                var iShipPos = API_GetPlayShipPosById(objInfo.id);
                if (iShipPos > -1) {

                    var iMsgBoxHeight = 20;
                    var iMsgBoxWidth = 210;
                    iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;
                    var shipName, shipMmsi, shipGeoPoX, shipGeoPoY, shipSpeed, shipCourse, shipTime;
                    var strTitle;
                    var shipInfoObj = API_GetPlayShipInfoByPos(iShipPos);

                    var curShipPos = API_GetShipPosById(objInfo.id);
                    var shipInfoObj1 = API_GetShipAllExpAttrByPos(curShipPos);

                    if (shipInfoObj) {
                        shipName = shipInfoObj.shipName;
                        shipMmsi = (shipInfoObj.shipMMSI == null || shipInfoObj.shipMMSI == undefined) ? "" : shipInfoObj.shipMMSI;
                        shipGeoPoX = shipInfoObj.shipGeoPoX;
                        shipGeoPoY = shipInfoObj.shipGeoPoY;
                        shipSpeed = shipInfoObj.shipSpeed;
                        shipCourse = shipInfoObj.shipCourse;
                        shipTime = shipInfoObj.shipTime;
                        strTitle = "船舶信息:" + shipName;
                    }
                    if (objInfo.iTrackPos != null) {//选中的是轨迹点
                        var shipInfoObj = API_GetPlayHistroyTrackInfoByPos(iShipPos, objInfo.iTrackPos);
                        if (shipInfoObj) {
                            strTitle = "历史轨迹点信息";
                            shipGeoPoX = shipInfoObj.trackGeoPoX;
                            shipGeoPoY = shipInfoObj.trackGeoPoY;
                            shipSpeed = shipInfoObj.trackSpeed;
                            shipCourse = shipInfoObj.trackCourse;
                            shipTime = shipInfoObj.trackTime;
                        }
                        if (shipSpeed) {
                            shipSpeed = shipSpeed.toFixed(2);
                        }

                        var strInnerHTML = "<span style='float: center; text-align: left; white-space: nowrap'>" + strTitle.big().bold().fontcolor("#f2fa03") + "</span><br>";

                        strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>船名：" + shipName.fontcolor("#f2fa03") + "</span><br>";
                        iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                        strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>终端号：" + shipMmsi.fontcolor("#f2fa03") + "</span><br>";
                        iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                        var strLon = API_LonLatToString(shipGeoPoX / 10000000, true);
                        strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>经度：" + strLon.fontcolor("#f2fa03") + "</span><br>";
                        iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                        var strLat = API_LonLatToString(shipGeoPoY / 10000000, false);
                        strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>纬度：" + strLat.fontcolor("#f2fa03") + "</span><br>";
                        iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                        shipSpeed = shipSpeed + "&nbsp;" + "(节)";
                        strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航速：" + shipSpeed.fontcolor("#f2fa03") + "</span><br>";
                        iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                        shipCourse = shipCourse + "&nbsp;" + "(度)";
                        strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航向：" + shipCourse.fontcolor("#f2fa03") + "</span><br>";
                        iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                        strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>报位时间：" + shipTime.fontcolor("#f2fa03") + "</span><br>";
                        iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;


                        global.g_showSimpleInfoDiv.style.height = iMsgBoxHeight + "px";
                        global.g_showSimpleInfoDiv.style.width = iMsgBoxWidth + "px";
                        global.g_showSimpleInfoDiv.innerHTML = strInnerHTML;

                        this.$options.methods.ShowInfoDivBox.bind(this)(global.g_showSimpleInfoDiv, objInfo.po);
                    }
                    else {
                        $.get(global.IP+'/web/GetOneShipInfoById?id=' + objInfo.id, function (data, status) {
                            if (shipSpeed) {
                                shipSpeed = shipSpeed.toFixed(2);
                            }

                            var strInnerHTML = "<span style='float: center; text-align: left; white-space: nowrap'>" + strTitle.big().bold().fontcolor("#f2fa03") + "</span><br>";

                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>船名：" + shipName.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>终端号：" + shipMmsi.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            var strLon = API_LonLatToString(shipGeoPoX / 10000000, true);
                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>经度：" + strLon.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            var strLat = API_LonLatToString(shipGeoPoY / 10000000, false);
                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>纬度：" + strLat.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            shipSpeed = shipSpeed + "&nbsp;" + "(节)";
                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航速：" + shipSpeed.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            shipCourse = shipCourse + "&nbsp;" + "(度)";
                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航向：" + shipCourse.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>北斗报位时间：" + data[0].bdTime.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>AIS报位时间：" + data[0].aisTime.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;


                            global.g_showSimpleInfoDiv.style.height = iMsgBoxHeight + "px";
                            global.g_showSimpleInfoDiv.style.width = iMsgBoxWidth + "px";
                            global.g_showSimpleInfoDiv.innerHTML = strInnerHTML;

                            _this.$options.methods.ShowInfoDivBox.bind(this)(global.g_showSimpleInfoDiv, objInfo.po);
                        });
                    }

                }
            }
            else {//选中的是当前船舶
                var iShipPos = API_GetShipPosById(objInfo.id);
                if (iShipPos > -1) {
                    var shipInfoObj = API_GetShipInfoByPos(iShipPos);
                    if (shipInfoObj) {

                        var iMsgBoxHeight = 20;
                        var iMsgBoxWidth = 210;

                        var shipName, shipMmsi, shipGeoPoX, shipGeoPoY, shipSpeed, shipCourse, shipTime;

                        var strTitle = "船舶信息:" + shipInfoObj.shipName;
                        shipGeoPoX = shipInfoObj.shipGeoPoX;
                        shipGeoPoY = shipInfoObj.shipGeoPoY;
                        shipName = shipInfoObj.shipName;
                        shipMmsi = (shipInfoObj.shipMMSI == null || shipInfoObj.shipMMSI == undefined) ? "" : shipInfoObj.shipMMSI;
                        shipSpeed = shipInfoObj.shipSpeed;
                        shipCourse = shipInfoObj.shipCourse;
                        shipTime = shipInfoObj.shipTime;

                        if (objInfo.iTrackPos != null) {//选中的是轨迹点
                            var shipInfoObj = API_GetHistroyTrackInfoByPos(iShipPos, objInfo.iTrackPos);
                            if (shipInfoObj) {
                                strTitle = "当前船舶轨迹点信息:";
                                shipGeoPoX = shipInfoObj.trackGeoPoX;
                                shipGeoPoY = shipInfoObj.trackGeoPoY;
                                shipSpeed = shipInfoObj.trackSpeed;
                                shipCourse = shipInfoObj.trackCourse;
                                shipTime = shipInfoObj.trackTime;
                            }
                            var strTitle = "" + shipName + "<br>";
                            var strInnerHTML = "<span style='float: center; text-align: left; white-space: nowrap'>" + strTitle.big().bold().fontcolor("#f2fa03") + "</span>";
                            // strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>船名：" + shipName.fontcolor("#f2fa03") + "</span><br>";
                            // iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>终端号：" + shipMmsi.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            var strLon = API_LonLatToString(shipGeoPoX / 10000000, true);
                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>经度：" + strLon.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            var strLat = API_LonLatToString(shipGeoPoY / 10000000, false);
                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>纬度：" + strLat.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            shipSpeed = shipSpeed + "&nbsp;" + "(节)";
                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航速：" + shipSpeed.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            shipCourse = shipCourse + "&nbsp;" + "(度)";
                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航向：" + shipCourse.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            if(shipTime != null && shipTime != undefined) {
                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>报位时间：" + shipTime.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;
                            }

                            global.g_showSimpleInfoDiv.style.height = iMsgBoxHeight + "px";
                            global.g_showSimpleInfoDiv.style.width = iMsgBoxWidth + "px";
                            global.g_showSimpleInfoDiv.innerHTML = strInnerHTML;

                            this.$options.methods.ShowInfoDivBox.bind(this)(global.g_showSimpleInfoDiv, objInfo.po);
                        }
                        else {
                            if(_this.mouseMoveSelectShipData[0] != null && _this.mouseMoveSelectShipData[0].id == objInfo.id)
                            {
                                var strTitle = "" + shipName + "<br>";
                                var strInnerHTML = "<span style='float: center; text-align: left; white-space: nowrap'>" + strTitle.big().bold().fontcolor("#f2fa03") + "</span>";
                                // strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>船名：" + shipName.fontcolor("#f2fa03") + "</span><br>";
                                // iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>终端号：" + shipMmsi.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                var strLon = API_LonLatToString(shipGeoPoX / 10000000, true);
                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>经度：" + strLon.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                var strLat = API_LonLatToString(shipGeoPoY / 10000000, false);
                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>纬度：" + strLat.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                shipSpeed = shipSpeed + "&nbsp;" + "(节)";
                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航速：" + shipSpeed.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                shipCourse = shipCourse + "&nbsp;" + "(度)";
                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航向：" + shipCourse.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                if(_this.mouseMoveSelectShipData[0].bdTime != null && _this.mouseMoveSelectShipData[0].bdTime != undefined) {
                                    strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>北斗报位时间：" + _this.mouseMoveSelectShipData[0].bdTime.fontcolor("#f2fa03") + "</span><br>";
                                    iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;
                                }

                                if(_this.mouseMoveSelectShipData[0].aisTime != null && _this.mouseMoveSelectShipData[0].aisTime != undefined) {
                                    strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>AIS报位时间：" + _this.mouseMoveSelectShipData[0].aisTime.fontcolor("#f2fa03") + "</span><br>";
                                    iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;
                                }

                                global.g_showSimpleInfoDiv.style.height = iMsgBoxHeight + "px";
                                global.g_showSimpleInfoDiv.style.width = iMsgBoxWidth + "px";
                                global.g_showSimpleInfoDiv.innerHTML = strInnerHTML;

                                _this.$options.methods.ShowInfoDivBox.bind(this)(global.g_showSimpleInfoDiv, objInfo.po);
                            }
                            else
                            {
                                $.ajaxSettings.async = false;
                                $.get(global.IP+'/web/GetOneShipInfoById?id=' + objInfo.id, function (data, status) {
                                _this.mouseMoveSelectShipData = data;
                                var strTitle = "" + shipName + "<br>";
                                var strInnerHTML = "<span style='float: center; text-align: left; white-space: nowrap'>" + strTitle.big().bold().fontcolor("#f2fa03") + "</span>";
                                // strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>船名：" + shipName.fontcolor("#f2fa03") + "</span><br>";
                                // iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>终端号：" + shipMmsi.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                var strLon = API_LonLatToString(shipGeoPoX / 10000000, true);
                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>经度：" + strLon.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                var strLat = API_LonLatToString(shipGeoPoY / 10000000, false);
                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>纬度：" + strLat.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                shipSpeed = shipSpeed + "&nbsp;" + "(节)";
                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航速：" + shipSpeed.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                shipCourse = shipCourse + "&nbsp;" + "(度)";
                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航向：" + shipCourse.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                if(data[0].bdTime != null && data[0].bdTime != undefined) {
                                    strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>北斗报位时间：" + data[0].bdTime.fontcolor("#f2fa03") + "</span><br>";
                                    iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;
                                }

                                if(data[0].aisTime != null && data[0].aisTime != undefined) {
                                    strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>AIS报位时间：" + data[0].aisTime.fontcolor("#f2fa03") + "</span><br>";
                                    iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;
                                }

                                global.g_showSimpleInfoDiv.style.height = iMsgBoxHeight + "px";
                                global.g_showSimpleInfoDiv.style.width = iMsgBoxWidth + "px";
                                global.g_showSimpleInfoDiv.innerHTML = strInnerHTML;

                                _this.$options.methods.ShowInfoDivBox.bind(this)(global.g_showSimpleInfoDiv, objInfo.po);
                            })
                            $.ajaxSettings.async = true;
                            }
                        }
                    }
                }
            }
        },

        //鼠标松开事件
        //scrnPo:鼠标在海图上的位置
        ReturnOnMouseUpTest: function (scrnPo) {
            if(_this.isShowShipSimpleInfo == true) {
                var shipInfo = API_GetShipInfoByScrnPo(scrnPo,50);
                if(shipInfo.bShowTrack == false) {
                    _this.wakeIsShowOrNot = false;
                }
                else {
                    _this.wakeIsShowOrNot = true;
                }

                global.curSelectShipId = shipInfo.shipId;
                $.get(global.IP+'/web/GetShipImgUrl?shipId=' + shipInfo.shipId, function (data, status) {
                    _this.shipInfoImg = "";
                    _this.shipInfoImg = data.replace(/\s*/g,"");
                });
                document.getElementById('ShipSimpleInformationView').style.display = 'block';
                _this.isShowShipSimpleInfo = false;
            }
            var arrObjPo1 = API_GetCurDrawDynamicObjGeoPo();
            if (global.g_bSelectShipByRectModel) {
                var arrObjPo = API_GetCurDrawDynamicObjGeoPo();
                if (arrObjPo.length > 1) {
                    this.$options.methods.SelectShipByRectTest.bind(this)(arrObjPo[0].x, arrObjPo[1].x, arrObjPo[0].y, arrObjPo[1].y, 0)
                }
            }
            else if (global.g_bSelectShipByCircleModel)
            {
                var arrObjPo = API_GetCurDrawDynamicObjGeoPo();
                if (arrObjPo.length > 1) {
                    var disKm = API_GetDistBetwTwoPoint(arrObjPo[0].x, arrObjPo[0].y, arrObjPo[1].x, arrObjPo[1].y);
                    this.$options.methods.SelectShipsByGeoCircleTest.bind(this)(arrObjPo[0].x, arrObjPo[0].y, disKm, 0)
                }
            }
        },

        //动态绘制标注时，选中点之后返回的坐标
        ReturnDrawDynamicObjNewInfoTest: function (objDynamicInfo, curGeoPoInfo) {

            if (objDynamicInfo) {
                switch (objDynamicInfo.type) {
                    case global.drawPoint:
                        // 标注类型为点时：
                        if(_this.curMarkTypeIndex == 1) {
                            _this.$options.methods.GetDynamicObjInfoOfMark.bind(this)(objDynamicInfo);
                        }
                        // 点标注坐标获取
                        // _this.$options.methods.saveMaekInfoToSdkOT.bind(this)(type,);
                        // _this.$options.methods.GetCurDrawObjCurPo.bind(this)(objDynamicInfo.po.x, objDynamicInfo.po.y);
                        // _this.$options.methods.saveMaekInfoToSdkOT.bind(this)(objDynamicInfo.type, objDynamicInfo.po.x, objDynamicInfo.po.y);


                        break;
                    case global.drawLine: //绘制线
                        if(_this.curMarkTypeIndex == 2) {
                            _this.$options.methods.GetDynamicObjInfoOfMark.bind(this)(objDynamicInfo);
                        }
                        // this.$options.methods.GetCurDrawObjCurPo.bind(this)(objDynamicInfo.po.x, objDynamicInfo.po.y);
                        break;
                    case global.drawFace: //绘制面
                        if(_this.getElectronicFence == false) {
                            this.$options.methods.ShowCurDrawObjCurPo.bind(this)(objDynamicInfo.po.x, objDynamicInfo.po.y);
                        }
                        break;
                    case global.drawRect: //绘制矩形
                        if(_this.getAreaPlayBack == false) {
                            this.$options.methods.areaPlayBackArrPointsShow.bind(this)(objDynamicInfo);
                        }
                        // this.$options.methods.GetCurDrawRectInfo.bind(this)(objDynamicInfo.po.x, objDynamicInfo.po.y, objDynamicInfo.w, objDynamicInfo.h, objDynamicInfo.curPo);
                        break;
                    case global.drawCircle: //绘制圆
                        if(_this.curMarkTypeIndex == 5) {
                            _this.$options.methods.GetDynamicObjInfoOfMark.bind(this)(objDynamicInfo);
                        }
                        // this.$options.methods.GetCurDrawCircleInfo.bind(this)(objDynamicInfo.po.x, objDynamicInfo.po.y, objDynamicInfo.r, objDynamicInfo.curPo);
                        break;
                    case global.drawPoint: //测距
                        API_SetCurDrawDynamicUseType(global.drawPoint);
                        break;
                    case global.drawPoint: //测面积
                        API_SetCurDrawDynamicUseType(global.drawPoint);
                        break;
                    case global.drawPoint: //电子方位线
                        API_SetCurDrawDynamicUseType(global.drawPoint);
                        break;
                    case global.drawLineArea: //绘制线区域
                        this.$options.methods.GetCurDrawObjCurPo.bind(this)(objDynamicInfo.po.x, objDynamicInfo.po.y);
                        break;
                }
            }
            else if (curGeoPoInfo) {//动态绘制信息框(线标注和面标注)
                switch (curGeoPoInfo.type) {
                    case global.drawLine: //绘制线
                        // this.$options.methods.ShowCurDrawLineObjInfoBox.bind(this)(curGeoPoInfo.po, curGeoPoInfo.bEndDraw);
                        break;
                    case global.drawFace: //绘制面
                        // this.$options.methods.ShowCurDrawFaceObjInfoBox.bind(this)(curGeoPoInfo.po, curGeoPoInfo.bEndDraw);
                        break;
                }
            }
        },

        //测距时候，鼠标点击激发该方法
        //curGeoPo:鼠标当前点击的经纬度坐标，格式{1210000000,310000000}
        //curDis:当前点击点与上一个点的距离（km)
        //allMeasureDist:累加的距离（km）
        //CurDegrees:当前点与上一个点的角度（度）
        ReturnCurMeasurePoInfoByMouseDownTest: function (curGeoPo, curDis, allMeasureDist, CurDegrees) {
            if (curGeoPo) {
            this.$options.methods.GetCurMeasureDist.bind(this)(curDis, allMeasureDist, CurDegrees);
                //alert("测距点信息："+curGeoPo.x + "," + curGeoPo.y + "_" + curDis + "_" + allMeasureDist + "_" + CurDegrees);
            }
        },

        //拽动海图时候激发
        ReturnDragMapTest: function () {
            var lonLat11 = API_GetCurMapCenterLonLatPo();
            var scale11 = API_GetMapLevel();
            _this.meteorologicalMonitoringList = [];
            _this.meteorologicalMonitoringList.push({lon: lonLat11.x, lat: lonLat11.y, level: scale11});
            //this.$options.methods.ShowCurDrawObjInfoBox.bind(this)();//拽动时候要设置信息框移动
        },

        //缩放之后激发
        ReturnZoomMapForPcTest: function () {
            var lonLat11 = API_GetCurMapCenterLonLatPo();
            var scale11 = API_GetMapLevel();
            _this.meteorologicalMonitoringList = [];
            _this.meteorologicalMonitoringList.push({lon: lonLat11.x, lat: lonLat11.y, level: scale11});
            _this.curLevel = scale11;
            //this.$options.methods.ShowCurDrawObjInfoBox.bind(this)(); //缩放之后要设置信息框移动
        },

        //鼠标移动事件
        ReturnOnMouseMoveTest: function () {

        },

        //测面积时候激发
        //areaSize:当前测量面积(平方米)
        ReturnCurMeasureAreaSizeTest: function (areaSize) {
            // document.getElementById("msg").value = areaSize;;
        },

        //移动端单手势拖动触发该事件
        ReturnTouchmoveByDragTest: function () {
            API_ClearDrawMouseMoveSelObjCanvas();
        },

        //海图视图窗口或者比例级别改变时候触发，返回海图视图的经纬度范围大小信息
        ReturnMapViewAfterDragOrZoomTest: function (mapInfo) {
            if (mapInfo) {
                var type = mapInfo.type;            //1=拽动触发，2=缩放触发
                var level = mapInfo.level;      //比例级数
                var scale = mapInfo.scale;      //比例尺
                var lon = mapInfo.lon;              //中心点经度
                var lat = mapInfo.lat;              //中心点纬度
                var minLon = mapInfo.minLon;        //海图窗口最小经度
                var maxLon = mapInfo.maxLon;        //海图窗口最大经度
                var minLat = mapInfo.minLat;        //海图窗口最小纬度
                var maxLat = mapInfo.maxLat;        //海图窗口最大纬度
                //document.getElementById("msg").value = type + "," + level + "," + lon;
                console.log(mapInfo.level);
            }
        },

        // 编辑
        ReturnEditObjByMouseRightDownCallBack: function (info) {
            _this.$options.methods.saveEditObjInfoToDatabase.bind(this)(info);
        },

        // 动态绘制-取消按钮
        ReturnCancelObjByMouseLeftDownCallBack: function (isCheck) {
            if(isCheck == true) {
                API_AddCancelButtonIsShow(false);
                if(_this.curMarkTypeIndex != 0) {
                    _this.$options.methods.cancelDrawMarkInfo.bind(this)();
                }

                if(_this.getElectronicFence == false) {
                    _this.$options.methods.electronicFence_cancel.bind(this)();
                }

                if(_this.getAreaPlayBack == false) {
                    _this.$options.methods.areaPlayBack.bind(this)();
                }

            }
        },

        ReturnCurPlayTrackTimeInfoTest: function (time, isShow) {
            if(_this.isHisEnd == true) {
                _this.historyTimeNumber = document.getElementById('historyTrackProgressBar').max;
                    document.getElementById('historyTrackProgressBar').value = document.getElementById('historyTrackProgressBar').max;
                    if(isShow == true) {
                        _this.isHisEnd = false;
                    }
            }
            else {
                if(time != null && _this.historyTimeNumber == 0) {
                    _this.historyTimeNumber = _this.historyTimeNumber + 1;
                    document.getElementById('historyTrackProgressBar').value = _this.historyTimeNumber;
                    _this.curHistoryTime = time;
                }
                else if(_this.curHistoryTime != time && isShow == false){
                    _this.historyTimeNumber =  _this.historyTimeNumber + 1;
                    document.getElementById('historyTrackProgressBar').value = _this.historyTimeNumber;
                }
                else if(isShow == true){
                    _this.historyTimeNumber = document.getElementById('historyTrackProgressBar').max;
                    document.getElementById('historyTrackProgressBar').value = document.getElementById('historyTrackProgressBar').max;
                }
            }
        },
        formatLonLat(lon, lat) {
          var lonD = parseInt(lon);
          var lonM = parseInt((lon - lonD) * 60);
          var lonS = parseInt((((lon - lonD) * 60) - lonM) * 60);
          var latD = parseInt(lat);
          var latM = parseInt((lat - latD) * 60);
          var latS = parseInt((((lat - latD) * 60) - latM) * 60);
          return {
            lonText: lonD + '°' + lonM + '′' + lonS + '″E',
            latText: latD + '°' + latM + '′' + latS + '″N'
          };
        },
        // 定位船舶
        locationShip: function (shipId) {
            if(_this.leftShow == true) {
                _this.$options.methods.ShowWindow.bind(this)('left');
                $("#ShipSearch1").animate({left: _this.position6 + 'px'});
            }

            if(_this.rightShow == true) {
                _this.$options.methods.ShowWindow.bind(this)('right');
                $("#ShipSimpleInformationView").animate({left: ((document.querySelector("#bt3").getBoundingClientRect().left) + (document.querySelector("#ShipSimpleInformationView").getBoundingClientRect().width) / 2) + 'px'});
                $("#ShipSimpleInformationView").animate({top: ((document.querySelector("#toolCabinet").getBoundingClientRect().top) + (document.querySelector("#toolCabinet").getBoundingClientRect().height)) + 'px'});
            }

            API_SetCurDrawDynamicUseType(0);

            _this.$options.methods.getDetailsAndSurroundingFishInfo.bind(this)(shipId);

            var shipPos = API_GetShipPosById(shipId);

            if(API_GetMapLevel() < 8) {
                API_SetMapLevel(10, null);
            }

            setTimeout(function () {
                API_SetShipToMapViewCenterByPos(shipPos);
                API_ReDrawShips();
            }, 50)
        },
        shipDetailTwoView: function (params, id, boxNum) {
            if (params === true) {
                _this.whiteIsShow = true;
                document.getElementById('shipDetailsTwoID').style.display = 'block';
                _this.$refs.shipDetails.shipInfos(id);
                _this.$refs.shipDetails.shipCardInfo(id);
                _this.$refs.shipDetails.shipMonitorInfo(id);
                // _this.$refs.shipDetails.lawCaseInfo(id);
                if (boxNum) {
                    _this.$refs.shipDetails.boxNum = boxNum;
                } else {
                    _this.$refs.shipDetails.boxNum = 1;
                }
            }
        },
        // 显示终端信息
        showClientInfo(params){
            if(params == true){
                _this.$refs['showClientInfo'].style.display = 'block';
            }
        },
        //船舶详情界面中的重点跟踪
        changeComprehensiveSituationDataByshipSimpleInformation: function(params, id) {
            switch (params) {
                case 0:
                    break;
                case 1:
                    var isAdd = false;
                    var index = -1;
                    // 判断是不是在重点跟踪队列里面
                    if(global.importShipInfo.length > 0) {
                        for(var i = 0; i < global.importShipInfo.length; i++) {
                            if(global.importShipInfo[i].staticShipId == id){
                                isAdd = true;
                                index = i;
                                break;
                            }
                        }
                    }
                    // 如果在重点跟踪队列则.......
                    $.ajaxSettings.async = false;
                    if(isAdd == true) {
                        $.get(global.IP + "/web/DeleteImportanceShip?id=" + id + '&userId=' + sessionStorage.getItem("userId"), function (data, status) {

                        }).fail(function(msg){

                        });

                        $.get(global.IP + '/web/GetOneShipInfoById?id=' + id, function (data, status) {
                            var pos = API_GetShipPosById(id);
                            API_SetShipAllFollow(pos, false);
                            var info = API_GetShipInfoByPos(pos);
                            var curShipDynamicInfo = [];//更新的时候，只要把这些值设置好即可
                            curShipDynamicInfo.shipGeoPoX = info.shipGeoPoX;
                            curShipDynamicInfo.shipGeoPoY = info.shipGeoPoY;
                            curShipDynamicInfo.shipSpeed = info.shipSpeed;
                            curShipDynamicInfo.shipCourse = info.shipCourse;
                            curShipDynamicInfo.shipTime = info.shipTime;

                            if(data[0].shiptype == 2) {
                                curShipDynamicInfo.iShipState = 0;   //状态
                            }
                            else if(data[0].shiptype == 3 || data[0].shiptype == 13 || data[0].shiptype == 75 || data[0].shiptype == 65 ) {
                                curShipDynamicInfo.iShipState = 1;   //状态
                            }
                            else if(data[0].shiptype == 4) {
                                curShipDynamicInfo.iShipState = 0;   //状态
                            }
                            else if(data[0].shiptype == 6 || data[0].shiptype == 61 ||
                            data[0].shiptype == 62 ||
                            data[0].shiptype == 64 || data[0].shiptype == 63 ||
                            data[0].shiptype == 66 || data[0].shiptype == 67 ||
                            data[0].shiptype == 68 || data[0].shiptype == 69 ||
                            data[0].shiptype == 610) {
                                curShipDynamicInfo.iShipState = 3;   //状态
                            }
                            else if(data[0].shiptype == 7 || data[0].shiptype == 71 ||
                            data[0].shiptype == 72 ||
                            data[0].shiptype == 74 || data[0].shiptype == 73 ||
                            data[0].shiptype == 76 || data[0].shiptype == 77 ||
                            data[0].shiptype == 78 || data[0].shiptype == 79 ||
                            data[0].shiptype == 710) {
                                curShipDynamicInfo.iShipState = 7;   //状态
                            }
                            else{
                                curShipDynamicInfo.iShipState = 7;
                            }

                            if(data[0].boutside == 1){
                                curShipDynamicInfo.iShipState = 3;
                            }

                            curShipDynamicInfo.bOnlineOrNot = info.bOnlineOrNot;
                            API_UpdateOneShipDynamicInfoByPos(pos, curShipDynamicInfo); //更新一艘船舶动态信息
                            API_ReDrawShips();
                        });

                        _this.fouInfo = '0';
                    }
                    // 如果不在重点跟踪队列则......
                    else {
                        $.ajaxSettings.async = false;
                        $.get(global.IP + '/web/SetImportanceShip?id=' + id + '&userId=' + sessionStorage.getItem("userId"), function (data, status) {

                        });
                        setAlertWindowShow('situationView', '已添加到重点跟踪队列', '', 1);

                        var pos = API_GetShipPosById(id);
                        var info = API_GetShipInfoByPos(pos);
                        API_SetShipAllFollow(pos, true);
                        var curShipDynamicInfo = [];    //更新的时候，只要把这些值设置好即可
                        curShipDynamicInfo.shipGeoPoX = info.shipGeoPoX;
                        curShipDynamicInfo.shipGeoPoY = info.shipGeoPoY;
                        curShipDynamicInfo.shipSpeed = info.shipSpeed;
                        curShipDynamicInfo.shipCourse = info.shipCourse;
                        curShipDynamicInfo.shipTime = info.shipTime;
                        curShipDynamicInfo.iShipState = 4;
                        curShipDynamicInfo.bOnlineOrNot = info.bOnlineOrNot;

                        API_UpdateOneShipDynamicInfoByPos(pos, curShipDynamicInfo); //更新一艘船舶动态信息
                        API_ReDrawShips();
                        _this.fouInfo = '1';
                    }
                    $.get(global.IP + "/web/GetImportanceShip?userId=" + sessionStorage.getItem("userId"), function (data, status) {
                        // 这里global.importShipInfo不更新就不会显示到左边的重点关注渔船列表中

                        global.importShipInfo = [];
                        _this.focusOnTrackingFishingBoatsList = [];
                        global.importShipInfo = data;
                        _this.focusOnTrackingFishingBoatsList = global.importShipInfo;
                    }).fail(function(msg){
                        console.log("error：" + JSON.stringify(msg))
                    });

                    $.ajaxSettings.async = true;
                    break;
                case 2:
                    break;
                case 3:
                    break;
                case 4:
                    break;
                case 5:
                    document.getElementById('ShipSimpleInformationView').style.display = 'none';
                    _this.$options.methods.isTrackShow.bind(this)(true);
                    document.getElementById('replay').style.top = '56px';
                    document.getElementById('replay').style.left = '20px';
                    if(_this.leftShow == true) {
                        _this.$options.methods.ShowWindow.bind(this)('left');
                    }
                    var startDate = (new Date().getTime()) / 1000 - parseFloat(_this.getHistoryTime) * 60 * 60; //得到秒数
                    var endDate = (new Date().getTime()) / 1000; //得到秒数

                    var cueStartDate = _this.$options.methods.changeTime.bind(this)(startDate * 1000);
                    var cueEndDate = _this.$options.methods.changeTime.bind(this)(endDate * 1000);
                    document.getElementById('portTableInputStartTime').value = cueStartDate;
                    document.getElementById('portTableInputEndTime').value = cueEndDate;
                    document.getElementById('replay').style.display = 'block';
                    break;
                case 6:
                    _this.$options.methods.setAllShipHistory.bind(this)();
                    break;
                case 7:
                    // $.get(function() {})
                    _this.shipWithAreaTitle = API_GetCurSelectShipInfo().shipName;
                    _this.shipWithAreaShipId = API_GetCurSelectShipInfo().shipId;
                    _this.shipWithAreaAreaId = _this.areaInfos[0].id;
                    document.getElementById("shipWithArea").style.display = "block";
                    break;
            }
        },
        //船舶详情中船员详情的显示
        showCrewInfo: function(params, id){
            if(params === true){
                document.getElementById('crewDetailView').style.display = "block";
                _this.$refs.crewDetails.saveShipId(id);
                // _this.$refs.crewDetails.switchMess(3);
                _this.$refs.crewDetails.getData();
            }
        },
        cancelFloowInCom: function (params) {
            if(params == true && API_GetCurFollowShipInfo() != null) {
                API_FollowShipByPos(-1);
                API_ReDrawShips();//重绘
            }
        },
        isTrackShow: function (params) {
            var count = API_GetShipsCount();
            for(var i = 0; i < count; i++) {
                API_SetShowShipTrackOrNotByPos(i, false);
            }
            API_ReDrawShips();
        },
        //改变控件Index使后出现的在前出现的上层
        changeCurIndex: function(id) {
            global.curIndex += 1;
            document.getElementById(id).style.zIndex = global.curIndex;
        },
        // 更新实时信息
        GetBdRealTimeMsgInfo : function (params) {
            _this.realTimeMsgInfo = params;
        },
        // 船舶简介-打开北斗通信
        openBeiDouShip: function (params) {
            document.getElementById('ShipSimpleInformationView').style.display = 'none';
            document.getElementById('beidouCommunicationView').style.display = 'block';
            _this.beiDouShipId = [];
            _this.beiDouShipId.push(params);
        },
        //船舶详情窗口关闭
        closeShipDiv: function(params){
            if(params === true){
                document.getElementById('shipDetailsTwoID').style.display = "none";
                document.getElementById('earlyWarningMonitoringView').style.filter = 'none';
                document.getElementById('videoSurveillanceView').style.filter = 'none';
                document.getElementById('meteorologicalMonitoringView').style.filter = 'none';
                document.getElementById('Legeng').style.filter = 'none';
                document.getElementById('longitudeAndLatitudeDisplay').style.filter = 'none';
                document.getElementById('leftImg').style.filter = 'none';
                document.getElementById('rightImg').style.filter = 'none';
                document.getElementById('searchDiv').style.filter = 'none';
                document.getElementById('toolCabinet').style.filter = 'none';
                document.getElementById('bt2').style.filter = 'none';
                document.getElementById('bt3').style.filter = 'none';
                document.getElementById('bt4').style.filter = 'none';
                document.getElementById('bt5').style.filter = 'none';
                document.getElementById('bt6').style.filter = 'none';
                document.getElementById('bt7').style.filter = 'none';
                document.getElementById('bt8').style.filter = 'none';
                document.getElementById('bt9').style.filter = 'none';
                document.getElementById('bt10').style.filter = 'none';
                document.getElementById('bt11').style.filter = 'none';
                document.getElementById('bt12').style.filter = 'none';
                document.getElementById('bt13').style.filter = 'none';
                document.getElementById('bt14').style.filter = 'none';
                document.getElementById('bt15').style.filter = 'none';
                document.getElementById('bt16').style.filter = 'none';
                document.getElementById('bt17').style.filter = 'none';
                document.getElementById('bt18').style.filter = 'none';
                document.getElementById('bt19').style.filter = 'none';
                document.getElementById('bt20').style.filter = 'none';
                document.getElementById('topTitle').style.filter = 'none';
                document.getElementById('fishingVesselStatisticsView').style.filter = 'none';
                document.getElementById('map').style.filter = 'none';
                document.getElementById('shipPortSelect').style.filter = 'none';
                document.getElementById('fishingVesselDwellTime').style.filter = 'none';
                document.getElementById('fishingVesselMonitoringView').style.filter = 'none';
                document.getElementById('bdMsgSendView').style.filter = 'none';
            }
        },
        getifadd: function (val) {
            _this.ifadd = val;
        },
  },
}
</script>

<style scoped>
.container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: antiquewhite;
  overflow: hidden;
}

.top-bar {
  padding: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0);
  z-index: 2;
  position: sticky;
  top: 0;
  width: 100%;
}

.search-container {
  display: flex;
  margin: 0 auto;
  max-width: 800px;
}

.search-input {
  flex: 1;
  padding: 8px 15px;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  font-size: 14px;
}

.search-button {
  padding: 0 15px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
}

.search-results-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 500px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 5px;
}

.search-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 15px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.search-results-content {
  padding: 10px;
}

.search-results-content table {
  width: 100%;
  border-collapse: collapse;
}

.search-results-content th,
.search-results-content td {
  padding: 8px 12px;
  border: 1px solid #ddd;
  text-align: left;
}

.search-results-content th {
  background-color: #f9f9f9;
  font-weight: bold;
}

.search-results-content tr:nth-child(even) {
  background-color: #f9f9f9;
}

.search-results-content tr:hover {
  background-color: #f0f0f0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #999;
}

.close-btn:hover {
  color: #666;
}



.search-container {
  position: relative;
}

.map-bar {
  flex: 1;
  position: relative;
  width: 100%;
  min-height: 0;
  display: flex;
}

.map-container {
  pointer-events: auto;
  touch-action: pan-x pan-y;
  -webkit-overflow-scrolling: touch;
  width: calc(100% - 30px);
  height: 100%;
  flex: 1;
  z-index: 1;
}

.sidebar-container {
  width: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
  padding: 5px 0;
  justify-content: space-between;
  pointer-events: none;
}

.sidebar-up-content {
  width: 100%;
  padding: 5px 0px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0);
  flex-grow: 1;
}

.sidebar-down-content {
  width: 100%;
  padding: 5px 0px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0);
  margin-top: auto;
}

.sidebar-btn {
  width: 30px;
  height: 30px;
  padding: 0;
  margin: 3px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  background-color: rgba(249, 249, 249, 1);
  border: none;
  font-size: 14px;
  pointer-events: auto;
}

.sidebar-btn:hover {
  background-color: #e6f7ff;
  color: #1890ff;
  transform: translateX(-3px);
}

.sidebar-btn .icon {
  margin-left: 0;
  margin-right: 0;
  font-size: inherit;
}

.sidebar-btn.active {
  background-color: #1890ff;
  color: white;
}

.bottom-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 15px;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0);
  z-index: 2;
  position: sticky;
  bottom: 0;
  width: 100%;
  height: auto;
  min-height: 60px;
  box-sizing: border-box;
}

.time-filters {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
  max-width: calc(100% - 50px);
}

.filter-btn {
  padding: 4px 8px;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  min-width: 60px;
}

.filter-btn.active {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
  padding: 4px 8px;
}

.filter-btn:hover {
  border-color: #40a9ff;
}
</style>