import global from '../../src/components/Global.vue';
    // 用来记录用户操作的函数,四个参数
    // 1.操作类型码 2.操作具体描述信息 3.记录操作成功/失败后的输出信息
    function SetUserOperationInfo(operationType,operationContent,operationSuccessInfo,operationFailInfo){
        // -------------------------------------start---2022-06-15------peter------------------------------------
              // 在此完成用户操作的记录
  
              // 1.构建operationInfo（调用添加接口用，需要操作人id，操作类型码，操作内容，操作时间）
              var operationInfo = {};
              var myDate = new Date();
              var year = myDate.getFullYear();    //获取完整的年份(4位,1970-????)
              var month = myDate.getMonth() + 1;       //获取当前月份(0-11,0代表1月)
              var date = myDate.getDate();        //获取当前日(1-31)
              var hour = myDate.getHours();       //获取当前小时数(0-23)
              var minute = myDate.getMinutes();     //获取当前分钟数(0-59)
              var second = myDate.getSeconds();     //获取当前秒数(0-59)
              month = month <= 9 ? "0" + month : month;
              date = date <= 9 ? "0" + date : date;
              hour = hour <= 9 ? "0" + hour : hour;
              minute = minute <= 9 ? "0" + minute : minute;
              second = second <= 9 ? "0" + second : second;
              var time = year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second;
              operationInfo["userId"] = sessionStorage.getItem('userId');
              operationInfo["type"] = operationType;
              operationInfo["content"] = operationContent;
              operationInfo["loadTime"] = time;
              operationInfo["ipAdress"] = "***********";
              // 2.调用接口
              var setOperationUrl = global.IP + "/web/SetUserOperationInfo";
              $.ajax({
                  url: setOperationUrl,
                  type:"POST",
                  data: JSON.stringify(operationInfo),
                  dataType: "json",
                  contentType:"application/json",
                  dateString: true,
  
                  success: function(data){
                    //   console.log(data);
                  },
                  error: function(data){
                      console.log(data);
                  },
              });
  
          // ----------------------------------------end-------------------------------------------------------------- 
      }
    
      export {SetUserOperationInfo}