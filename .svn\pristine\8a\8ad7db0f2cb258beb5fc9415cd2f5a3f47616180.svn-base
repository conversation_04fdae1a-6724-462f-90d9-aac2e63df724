package com.bd.entity;

import lombok.Data;

@Data
public class PortShipInfo {
    private int shipId;
    private String shipName;
    private String portName;
    private String owner;
    private String lxdh;
    private int bOutSide;
    private int lon;
    private int lat;
    private String bdId;
    private int mmsi;
    private String loadTime;
    private String outLineReason;//离线原因
    //新增渔区
    private int fishAreaId;
    // 船长
    private String captain;
    private String captainDH;//船长电话
    private Integer maxPeopleCount;//核载人数
    private Integer inPortState;//0 停港 1 生产（仅限实时船舶报表）
    private String sjOperator;//实际经营人
    private String sjOperatorLxdh;//实际经营人联系电话
    private String inFishAreaId;//所在渔区Id
}
