package com.bd.entity;

import lombok.Data;

/*
 *
 * 渔船静态信息总 表
 * */
@Data
public class ShipStaticInfo_all {
    private int id;
    private int SHIPID = 0;
    private int termShipId;
    private String BDID;
    private int MMSI;
    private String SHIPNAME;
    private int IMO;
    private String CallSign;
    private String Owner;
    private String LXDH;
    private String LXDZ;
    private String CTCL;
    private int DW;
    private int ZJGLQW;
    private String SSDW;
    private int SHIPTYPE = 2;
    private float length;
    private float width;
    private int bOutside = 1;
    private int nation = 1;
    private String LoadTime;
    private String BdTime;
    private String AisTime;
    private int bFocus = 0;
    private int bBlackFuXiu = 0;
    private int bWhiteFuXiu = 0;
    private String LastPosTermNo;
    private String LastPosBDId;

    private int maxPeopleCount;//核载人数
    private int minPeopleCount;//最低配员
    private String captain;//船长
    private String captainlxdh;//船长电话
    private String wxlxdh;//卫星电话

    private String emergencyContact;//紧急联系人
    private String emergencyContactLxdh;//紧急联系人电话
    private String sjOperator;//实际经营人
    private String sjOperatorLxdh;//实际经营人联系电话

    ///////////////////下面不要看/////////////////////

    private String bOnline;//在线状态
    private String onlineCount;//在线数

    private String portName;

    private String DISTRICT;//属地
    private String overTime;//建造完工时间
    private String dictShipType;//船舶类型
    private String dictJobType;//作业类型
    private String inNetTime;//入网时间
    private String vLength;//船長
    private String vWidth;
    private String vCall;
    private String jobPlace;//作业场所
    private String ZYLX;//船舶类型
    private String BLXKZ;//许可

    private String APP_JOB_BEGDATE;//申请作业开始时间
    private String APP_JOB_ENDDATE;//结束
    private String APP_JOB_PLACE;//场所
    private String APP_JOB_TYPE;//类型
    private String APP_JOB_WAY;//方式
    private String FISHING_PERMIT_NUMBER;//捕捞许可证号
    private String DICT_APP_TYPE;//申请许可类型
    private String FISHINGGEAR_AMOUNT;//渔具数量
    private String FISHINGGEAR_NAME;//渔具名称
    private String FISHINGGEAR_SPEC;//渔具规格
    private String MAIN_BREED;//主要捕捞品种
    private String MAIN_QUOTA;//捕捞限额
    private String SHIP_OWNER;//持证人
    private String ADDRESS;//持证人地址
    private String LINK_RATED_LOAD_NUM;//核载人数

    private String CTCZ;// 船体材质

    private String zzylx; //作业类型
    private String ZZYDYZYFS_ZYCS; // 作业场所

    private String ycxxwybs;
    private String gxsj;
    private String gxcz;
    private String ycssdqdm;
    private String ycssdqmc;
    private String ycbm;
    private String cm;
    private String cmyw;
    private String cbzl;
    private String cz;
    private String xk;
    private String xs;
    private String zdw;
    private String jdw;
    private String zjzgl;
    private String zjxhy;
    private String zjxhe;
    private String zjxhs;
    private String zjgly;
    private String zjgle;
    private String zjgls;
    private String ctcz;
    private String ctczyw;
    private String jzwgrq;
    private String zjsl;
    private String cbhhsbm;
    private String zczjsl;
    private String zczjxh;
    private String zccm;
    private String zcsl;
    private String zczgl;
    private String hyyyblxkzlb;
    private String cbsyrmc;
    private String cbsyrmcyw;
    private String cbsyrdz;
    private String cbsyrdzyw;
    private String cbsyrdh;
    private String cblx;
    private String cblxyw;
    private String zjjhy;
    private String zjjhe;
    private String zjjhs;
    private String cwgjzbpzsbh;
    private String skgl;
    private String hzcz;
    private String hzzd;
    private String etl_time;
    private String cc;

}
