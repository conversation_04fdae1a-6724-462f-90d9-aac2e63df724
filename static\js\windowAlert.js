// id-父级id
// content-文字
// type-1:3秒消失，2：确定，3：确定、取消
// alertType：
function setAlertWindowShow(id, content, bGColor, type, alertType, zIndex) {
    var parent = document.getElementById(id);
    
    var div = document.createElement("div");
    div.style.position = 'absolute';
    div.style.width = '200px';
    if(type == 1) {
        div.style.top = '60%';
        div.style.left = '45%';
        div.style.height = 'auto';
    }
    else {
        div.style.top = '50%';
        div.style.left = '50%';
        div.style.marginTop = '-50px';
        div.style.marginLeft = '-100px'
        div.style.height = '130px';
    }
    
    div.style.color = 'white';//添加背景色
    if(zIndex != "" && zIndex != undefined) {
        div.style.zIndex = zIndex;
    }
    else {
        div.style.zIndex = '100';
    }
    
    div.style.border = '0';
    div.style.borderRadius = '5px';
    div.style.backgroundColor = '#00143F';

    if(alertType == 1) {
        var img = document.createElement("img");
        img.style.width = "20px";
        img.style.height = "20px";
        img.style.position = 'absolute';
        img.style.top = '35px';
        img.style.left = '20px';
        img.style.marginTop = '10px';
        img.src = "../../static/img/warning.png";
        div.appendChild(img);
    }
    else if(alertType == 2) {
        var img = document.createElement("img");
        img.style.width = "20px";
        img.style.height = "20px";
        img.style.position = 'absolute';
        img.style.top = '35px';
        img.style.left = '20px';
        img.style.marginTop = '10px';
        img.src = "../../static/img/success.png";
        div.appendChild(img);
    }

    var span = document.createElement("span");
    span.style.width = '160px';
    span.style.height = '50px';
    span.style.position = 'absolute';
    span.style.top = '20px';
    span.style.left = '40px';
    span.style.marginTop = '10px';
    span.style.borderRadius = '5px';
    span.style.lineHeight = '50px';
    // span.style.border = 'solid 2px rgb(72,94,188)';
    span.style.backgroundColor = '#00143F';
    
    span.style.color = 'white';//添加背景色
    span.style.textAlign = 'center'
    span.style.fontSize = '12px';
    span.innerHTML = content;//添加文字
    div.appendChild(span);

    if(type == 2) {
        var buttonOk = document.createElement("button");
        buttonOk.style.width = '40px';
        buttonOk.style.height = '20px';
        buttonOk.style.position = 'absolute';
        buttonOk.style.bottom = '20px';
        buttonOk.style.left = '80px';
        if(bGColor != '') {
            buttonOk.style.backgroundColor = bGColor;
        }
        else {
            buttonOk.style.backgroundColor = 'rgb(13, 38, 92)';
        }
        buttonOk.style.color = 'white';//添加背景色
        buttonOk.style.fontSize = '12px';
        buttonOk.style.border = '1px solid rgba(50, 128, 205, 0.8)';
        buttonOk.innerHTML = '确定';//添加文字
        buttonOk.addEventListener('click', function() {
            div.style.display = 'none';
        });
        div.appendChild(buttonOk);
    }

    if(type == 3) {
        var buttonOk = document.createElement("button");
        buttonOk.style.width = '40px';
        buttonOk.style.height = '20px';
        buttonOk.style.position = 'absolute';
        buttonOk.style.bottom = '20px';
        buttonOk.style.left = '10px';
        if(bGColor != '') {
            buttonOk.style.backgroundColor = bGColor;
        }
        else {
            buttonOk.style.backgroundColor = 'rgb(13, 38, 92)';
        }
        buttonOk.style.color = 'white';//添加背景色
        buttonOk.style.fontSize = '12px';
        buttonOk.style.border = '1px solid #FF0000';
        buttonOk.innerHTML = '确定';//添加文字
        buttonOk.addEventListener('click', function() {
            div.style.display = 'none';
        });
        div.appendChild(buttonOk);

        var buttonCanel = document.createElement("button");
        buttonCanel.style.width = '40px';
        buttonCanel.style.height = '20px';
        buttonCanel.style.position = 'absolute';
        buttonCanel.style.bottom = '20px';
        buttonCanel.style.right = '10px';
        if(bGColor != '') {
            buttonCanel.style.backgroundColor = bGColor;
        }
        else {
            buttonCanel.style.backgroundColor = 'rgb(13, 38, 92)';
        }
        buttonCanel.style.color = 'white';//添加背景色
        buttonCanel.style.fontSize = '12px';
        buttonCanel.style.border = '0';
        buttonCanel.innerHTML = '取消';//添加文字
        buttonCanel.addEventListener('click', function() {
            div.style.display = 'none';
        });
        div.appendChild(buttonCanel);
    }
    parent.appendChild(div);

    if(type == 1) {
        setTimeout(function () {
            div.style.display = 'none';
        }, 3000)
    }
}

function saveRealTimeMessages (type, content, arrayInfo) {
    var returnInfo = [];
    var myDate = new Date();
    var year = myDate.getFullYear();    //获取完整的年份(4位,1970-????)
    var month = myDate.getMonth() + 1;       //获取当前月份(0-11,0代表1月)
    var date = myDate.getDate();        //获取当前日(1-31)
    var hour = myDate.getHours();       //获取当前小时数(0-23)
    var minute = myDate.getMinutes();     //获取当前分钟数(0-59)
    var second = myDate.getSeconds();     //获取当前秒数(0-59)
    month = month <= 9 ? "0" + month : month;
    date = date <= 9 ? "0" + date : date;
    hour = hour <= 9 ? "0" + hour : hour;
    minute = minute <= 9 ? "0" + minute : minute;
    second = second <= 9 ? "0" + second : second;
    var time = year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second;
    var info = {
        type: type,
        content: content,
        time: time,
    }
    if(arrayInfo.length == 0) {
        returnInfo.push(info);
    }
    else {
        if(arrayInfo.length == 20) {
            arrayInfo.splice(19, 1);
        }
        arrayInfo.splice(0, 0, info);
    }
    return returnInfo;
}