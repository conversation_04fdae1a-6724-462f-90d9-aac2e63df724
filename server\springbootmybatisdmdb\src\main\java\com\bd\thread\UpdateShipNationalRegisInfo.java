package com.bd.thread;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bd.entity.ShipNationalRegisInfo;
import com.bd.mapper.CrewExamMapper;
import com.bd.service.UserService;
import com.bd.util.HttpTool;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Controller
@EnableScheduling
public class UpdateShipNationalRegisInfo {
    @Resource
    private CrewExamMapper crewExamMapper;

    @Resource
    private UserService userService;

    public List<String> GetShipNationalRegisInfoList(int pageNum) throws IOException {
        String token = userService.GetOpenCenterToken();
        //System.out.println("-.-" + token);
        String header = "Bearer " + token;
        String url = "http://*********:18080/api/service/share/P427892879352926208?conditions=null&pageNum=" + pageNum + "&pageSize=" + 100;
        String lawData = HttpTool.doGet(url, header);
        //System.out.println("[data]" + lawData);
        JSONArray jsonArray = JSONArray.parseArray(lawData);

        List<String> list = new ArrayList<>();
        for (Object obj : jsonArray) {
            list.add(obj.toString());
        }
        return list;
    }

    public void updateShipNationalRegisInfo() throws Exception{
        int pageNum = 1;
        List<String> infoList = new ArrayList<>();
        while (true){
            try{
                infoList = GetShipNationalRegisInfoList(pageNum);
            }catch (Exception e){
                continue;
            }
            List<ShipNationalRegisInfo> shipNationalRegisInfos = new ArrayList<>();
            if(infoList.size() < 1) break;
            for (String shipStr: infoList){
                JSONObject shipJson = new JSONObject();
                //System.out.println("shipStr = " + infoList);
                try {
                    shipJson = JSONObject.parseObject(shipStr);
                }catch (Exception e){
                    System.out.println("异常:"+e);
                    System.out.println("shipStr = " + shipStr);
                }

                ShipNationalRegisInfo shipInfo = new ShipNationalRegisInfo();
                shipInfo.setGjsyqdjxxwybs(shipJson.getString("gjsyqdjxxwybs"));
                shipInfo.setYcxxwybs(shipJson.getString("ycxxwybs"));
                shipInfo.setGxsj(shipJson.getString("gxsj"));
                shipInfo.setGxcz(shipJson.getString("gxcz"));
                shipInfo.setYcssdqdm(shipJson.getString("ycssdqdm"));
                shipInfo.setYcssdqmc(shipJson.getString("ycssdqmc"));
                shipInfo.setYycbsyqdjzsbh(shipJson.getString("yycbsyqdjzsbh"));
                shipInfo.setYycbgjzsbh(shipJson.getString("yycbgjzsbh"));
                shipInfo.setYycbdjzsyxq(shipJson.getString("yycbdjzsyxq"));
                shipInfo.setYcssdq(shipJson.getString("ycssdq"));
                shipInfo.setCjg(shipJson.getString("cjg"));
                shipInfo.setCjgyw(shipJson.getString("cjgyw"));
                shipInfo.setSyqgjdjspjg(shipJson.getString("syqgjdjspjg"));
                shipInfo.setSyqgjdjspjgyj(shipJson.getString("syqgjdjspjgyj"));
                shipInfo.setSyqgjdjspr(shipJson.getString("syqgjdjspr"));
                shipInfo.setSyqgjdjspsj(shipJson.getString("syqgjdjspsj"));
                shipInfo.setSyqgjdjsqsbh(shipJson.getString("syqgjdjsqsbh"));
                shipInfo.setSyqgjdjsqrq(shipJson.getString("syqgjdjsqrq"));
                shipInfo.setSyrszgf(shipJson.getString("syrszgf"));
                shipInfo.setYycblsgjzsbh(shipJson.getString("yycblsgjzsbh"));
                shipInfo.setYycbzjsyqzsbh(shipJson.getString("yycbzjsyqzsbh"));
                shipInfo.setCzrmc(shipJson.getString("czrmc"));
                shipInfo.setCzrmcyw(shipJson.getString("czrmcyw"));
                shipInfo.setCzrdz(shipJson.getString("czrdz"));
                shipInfo.setCzrdzyw(shipJson.getString("czrdzyw"));
                shipInfo.setCzrjmsfzhmhgszch(shipJson.getString("czrjmsfzhmhgszch"));
                shipInfo.setCzrdh(shipJson.getString("czrdh"));
                shipInfo.setYycbgjzsyxq(shipJson.getString("yycbgjzsyxq"));
                //shipInfo.setGyrxx(shipJson.getString("gyrxx"));
                shipInfo.setCbjyf(shipJson.getString("cbjyf"));
                shipInfo.setCbjyfyw(shipJson.getString("cbjyfyw"));
                shipInfo.setCqg(shipJson.getString("cqg"));
                shipInfo.setCqgyw(shipJson.getString("cqgyw"));
                shipInfo.setCbjyfdz(shipJson.getString("cbjyfdz"));
                shipInfo.setCbjyfdzyw(shipJson.getString("cbjyfdzyw"));
                shipInfo.setYcbm(shipJson.getString("ycbm"));
                shipInfo.setCm(shipJson.getString("cm"));
                shipInfo.setCmyw(shipJson.getString("cmyw"));
                shipInfo.setCbzl(shipJson.getString("cbzl"));
                shipInfo.setCc(shipJson.getString("cc"));
                shipInfo.setXk(shipJson.getString("xk"));
                shipInfo.setXs(shipJson.getString("xs"));
                shipInfo.setZdw(shipJson.getString("zdw"));
                shipInfo.setJdw(shipJson.getString("jdw"));
                shipInfo.setZjzgl(shipJson.getString("zjzgl"));
                shipInfo.setZjxhy(shipJson.getString("zjxhy"));
                shipInfo.setZjxhe(shipJson.getString("zjxhe"));
                shipInfo.setZjxhs(shipJson.getString("zjxhs"));
                shipInfo.setZjgly(shipJson.getString("zjgly"));
                shipInfo.setZjgle(shipJson.getString("zjgle"));
                shipInfo.setZjgls(shipJson.getString("zjgls"));
                shipInfo.setCtcz(shipJson.getString("ctcz"));
                shipInfo.setCtczyw(shipJson.getString("ctczyw"));
                shipInfo.setJzwgrq(shipJson.getString("jzwgrq"));
                shipInfo.setZjsl(shipJson.getString("zjsl"));
                shipInfo.setCbhhsbm(shipJson.getString("cbhhsbm"));
                shipInfo.setZccm(shipJson.getString("zccm"));
                shipInfo.setZcsl(shipJson.getString("zcsl"));
                shipInfo.setZczgl(shipJson.getString("zczgl"));
                shipInfo.setCbsyrmc(shipJson.getString("cbsyrmc"));
                shipInfo.setCbsyrmcyw(shipJson.getString("cbsyrmcyw"));
                shipInfo.setCbsyrdz(shipJson.getString("cbsyrdz"));
                shipInfo.setCbsyrdzyw(shipJson.getString("cbsyrdzyw"));
                shipInfo.setCbsyrdh(shipJson.getString("cbsyrdh"));
                shipInfo.setCblx(shipJson.getString("cblx"));
                shipInfo.setCblxyw(shipJson.getString("cblxyw"));
                shipInfo.setZjjhy(shipJson.getString("zjjhy"));
                shipInfo.setZjjhe(shipJson.getString("zjjhe"));
                shipInfo.setZjjhs(shipJson.getString("zjjhs"));
                shipInfo.setCwgjzbpzsbh(shipJson.getString("cwgjzbpzsbh"));
                shipInfo.setSkgl(shipJson.getString("skgl"));
                shipInfo.setHzcc(shipJson.getString("hzcc"));
                shipInfo.setHzzd(shipJson.getString("hzzd"));
                shipInfo.setBcffzslx(shipJson.getString("bcffzslx"));
                shipInfo.setEtl_dt(shipJson.getString("etl_dt"));
                //System.out.println(shipInfo);
                shipNationalRegisInfos.add(shipInfo);
            }
            System.out.println("updateShipNationalRegisInfo-pageNum:" + pageNum);
            pageNum ++;

            crewExamMapper.insertShipNationalRegisInfo(shipNationalRegisInfos);

        }
    }

    //@Scheduled(cron = "0 0 1 * * ?")
    public void run() throws Exception {
        crewExamMapper.deleteShipNationRegisInfo();
        updateShipNationalRegisInfo();
    }
}
