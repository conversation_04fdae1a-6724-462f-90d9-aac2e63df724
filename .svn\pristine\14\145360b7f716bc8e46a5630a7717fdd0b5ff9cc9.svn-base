package com.bd.entity;

import com.bd.util.M_POINT;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class PortInfo {
    private int id;//数据库id
    private String portName;
    private int pointCount;
    private String pointStr;
    private List<M_POINT> points;

    private String pointStr_tmp = "";
    private List<M_POINT> points_tmp = new ArrayList<>();

    private int maxshapcount;//港容量
    private int windlevel;//抗风等级
    private String navmarkmmsi;//航标

    public List<M_POINT> pointsByStr(){
        if (pointStr == "")
            return null;
        List<M_POINT> points = new ArrayList<>();
        for (int i = 0; i < pointStr.split("#").length; i++){
            M_POINT point = new M_POINT();
            String sLon = pointStr.split("#")[i].split("@")[0];
            String sLat = pointStr.split("#")[i].split("@")[1];
            //System.out.println(String.format("%10s0", sLon));
            point.x = Integer.parseInt(String.format("%10s0", sLon).trim());
            point.y = Integer.parseInt(String.format("%9s0", sLat).trim());

            points.add(point);
        }
        this.points = points;
        return points;
    }

    public List<M_POINT> pointsByStr_tmp(){
        if (pointStr_tmp.length() < 2)
            return null;
        List<M_POINT> points = new ArrayList<>();
        for (int i = 0; i < pointStr_tmp.split("#").length; i++){
            M_POINT point = new M_POINT();
            String sLon = pointStr_tmp.split("#")[i].split("@")[0];
            String sLat = pointStr_tmp.split("#")[i].split("@")[1];
            //System.out.println(String.format("%10s0", sLon));
            point.x = Integer.parseInt(String.format("%10s0", sLon).trim());
            point.y = Integer.parseInt(String.format("%9s0", sLat).trim());

            points.add(point);
        }
        this.points_tmp = points;
        return points;
    }

    private String content; //渔港简介
    private String lifeSaving; //救生
    private String fireControl; //消防
}
