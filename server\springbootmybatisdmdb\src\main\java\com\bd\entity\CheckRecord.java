package com.bd.entity;

import lombok.Data;

/**
 * 执法检查记录
 */
@Data
public class CheckRecord {
    private String VIEW_ID;
    private String BILL_NO;
    private String DEPT_IDS;
    private String DEPT_CNAMES;
    private String ENF_LE_IDS;
    private String ENF_LE_NAMES;
    private String CATEGORY_IDS;
    private String SUOBJ_TYPE;
    private String SUOBJ_CNAME;
    private String FONT_NAME;
    private String TYSHXYDM;
    private String LEGAL;
    private String LEGAL_ID_TYPE;
    private String LEGAL_ID_NO;
    private String ADDRESS;
    private String TELEPHONE;
    private String LONGITUDE;
    private String LATITUDE;
    private String CHECK_PLACE;
    private String CHECK_CONTENT;
    private String CHECK_RESULT;
    private String ENF_RESULT;
    private String OTHER_CHECK_CONTENT;
    private String HANDL_OPINIONS;
    private String HANDL_TYPE;
    private String SUOBJ_OPINION;
    private String SIGN_DATE;
    private String RESULT;
    private String RESULT_TIME;
    private String ETL_TIME;
}
