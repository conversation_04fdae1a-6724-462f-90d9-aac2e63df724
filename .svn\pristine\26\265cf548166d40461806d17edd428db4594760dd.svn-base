<template>
    <div class="typhoon-list-card">
        <div class="card-header">
            <h3>台风列表</h3>
            <span class="typhoon-count">总数: {{ typhoonList.length }}</span>
            <button class="close-btn" @click="handleClose">×</button>
        </div>
        <div class="card-body">
            <!-- 加载状态 -->
            <div v-if="loading" class="loading-message">
                <span class="loading-text">加载中...</span>
            </div>

            <!-- 台风列表 -->
            <div class="typhoon-list">
                <div class="list-header">
                    <div class="header-item">2025</div>
                    <div class="header-item">编号</div>
                    <div class="header-item">名称</div>
                    <div class="header-item">英文名称</div>
                </div>

                <div class="list-content">
                    <div v-for="(typhoon, index) in typhoonList" :key="typhoon.id" class="typhoon-item">
                        <label class="checkbox-container">
                            <input type="checkbox" class="typhoon-checkbox" v-model="typhoon.isChecked"
                                @change="handleCheckboxChange(typhoon)">
                            <span class="checkbox-custom"></span>
                        </label>
                        <div class="typhoon-number">{{ typhoon.number }}</div>
                        <div class="typhoon-name">{{ typhoon.chineseName }}</div>
                        <div class="typhoon-english-name">{{ typhoon.englishName }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import global from './Global.vue';

export default {
    name: 'TyphoonListCard',
    data() {
        return {
            selectedYear: '2025',
            typhoonList: [],
            loading: false,
        }
    },

    mounted() {
        this.fetchTyphoonData();
    },
    methods: {
        // 处理关闭按钮点击
        handleClose() {
            console.log('关闭按钮被点击');
            this.$emit('close');
        },

        handleCheckboxChange(typhoon) {
            if (!typhoon.isChecked) {
                var typhoonPos = API_GetTyphoonPosById(typhoon.id);
                API_SetTyphoonShowOrNotByPos(typhoonPos, false);
                API_ReDrawTyphoon();
                return;
            }
            this.AddTyphoon(typhoon.id);
            // var typhoonPos = API_GetTyphoonPosById(typhoon.id);
            // API_SetMapLevel(5, null);
            // API_SetTyphoonShowOrNotByPos(typhoonPos, true);
            // API_SetTyphoonToMapViewCenterByPos(typhoonPos);
        },

        // 添加台风
        AddTyphoon(typhoonId) {

            var url = global.IP + "/web/GetTyphoonInfoById?id=" + typhoonId;
            $.get(url, function (data, status) {
                data = JSON.parse(data);

                var objTyphoonStyle = [];//台风样式结构体
                objTyphoonStyle.strTropicalDepressionColor = "#ed910b"; //热带低压样式
                objTyphoonStyle.strTropicalStormColor = "#e65408"; //热带风暴
                objTyphoonStyle.strSevereTropicalStormColor = "#ec4d30"; //强热带风暴
                objTyphoonStyle.strTyphoonColor = "#ed3434"; //台风
                objTyphoonStyle.strSevereTyphoonColor = "#d50e0e"; //强台风
                objTyphoonStyle.strSuperTyphoonColor = "#b80c0c"; //超强台风
                objTyphoonStyle.sevenCircleColor = "#8001ec"; //7级圆样式
                objTyphoonStyle.tenCircleColor = "#082aee"; //10级圆样式

                API_SetTyphoonStyleColor(objTyphoonStyle); //设置台风样式

                var curTyphoonInfo = [];
                curTyphoonInfo.id = data.lslj[0].tfbh;
                curTyphoonInfo.name = data.lslj[0].tfm;
                curTyphoonInfo.startTime = data.lslj[0].gqsj;
                curTyphoonInfo.endTime = data.lslj[data.lslj.length - 1].gqsj;
                var arrExpAttrValue = null;
                var curTyphoonPos = API_AddTyphoon(curTyphoonInfo, arrExpAttrValue);
                API_SetTyphoonShowOrNotByPos(curTyphoonPos, true); //设置该台风显示

                var ybName = new Array();
                var index = 0;
                for (var i = 0; i < data.yblj.length; i++) {
                    if (i == 0) {
                        ybName[index] = data.yblj[i].ybtm;
                        index = index + 1;
                    }
                    else {
                        var isCheck = false;
                        for (var j = 0; j < ybName.length; j++) {
                            if (ybName[j] == data.yblj[i].ybtm) {
                                isCheck = true;
                                break;
                            }
                        }
                        if (isCheck == false) {
                            ybName[index] = data.yblj[i].ybtm;
                            index = index + 1;
                        }
                    }
                }

                for (var i = 0; i < ybName.length; i++) {
                    switch (i) {
                        case 0:
                            API_AddTyphoonPredictTrackLineStyleByPos(curTyphoonPos, 2, "#00FF00", 50);
                            break;
                        case 1:
                            API_AddTyphoonPredictTrackLineStyleByPos(curTyphoonPos, 2, "#0000FF", 50);
                            break;
                        case 2:
                            API_AddTyphoonPredictTrackLineStyleByPos(curTyphoonPos, 2, "#000000", 50);
                            break;
                        case 3:
                            API_AddTyphoonPredictTrackLineStyleByPos(curTyphoonPos, 2, "#0000FF", 50);
                            break;
                        case 4:
                            API_AddTyphoonPredictTrackLineStyleByPos(curTyphoonPos, 2, "#000000", 50);
                            break;
                        case 5:
                            API_AddTyphoonPredictTrackLineStyleByPos(curTyphoonPos, 2, "#000000", 50);
                            break;
                        case 6:
                            API_AddTyphoonPredictTrackLineStyleByPos(curTyphoonPos, 2, "#000000", 50);
                            break;
                        case 7:
                            API_AddTyphoonPredictTrackLineStyleByPos(curTyphoonPos, 2, "#000000", 50);
                            break;
                        case 8:
                            API_AddTyphoonPredictTrackLineStyleByPos(curTyphoonPos, 2, "#000000", 50);
                            break;
                        case 9:
                            API_AddTyphoonPredictTrackLineStyleByPos(curTyphoonPos, 2, "#000000", 50);
                            break;
                        case 10:
                            API_AddTyphoonPredictTrackLineStyleByPos(curTyphoonPos, 2, "#000000", 50);
                            break;
                    }
                }

                for (var j = 0; j < data.lslj.length; j++) {
                    var arrPredictLineTrack = [];

                    if (true) {
                        // if(j == data.lslj.length - 1) {
                        for (var k = 0; k < ybName.length; k++) {
                            var arrPredictTracks = [];
                            for (var m = 0; m < data.yblj.length; m++) {
                                if (ybName[k] == data.yblj[m].ybtm) {
                                    var curPredictTrackInfo = [];
                                    if (data.lslj[j].gqsj != data.yblj[m].gqsj) {
                                        continue
                                    }
                                    curPredictTrackInfo.time = data.yblj[m].ybsj;
                                    curPredictTrackInfo.po = { x: data.yblj[m].lon * 10000000, y: data.yblj[m].lat * 10000000 };
                                    if (data.yblj[m].zxfs >= 0 && data.yblj[m].zxfs <= 0.2) {
                                        curPredictTrackInfo.windPower = 0;
                                    }
                                    else if (data.yblj[m].zxfs >= 0.3 && data.yblj[m].zxfs <= 1.5) {
                                        curPredictTrackInfo.windPower = 1;
                                    }
                                    else if (data.yblj[m].zxfs >= 1.6 && data.yblj[m].zxfs <= 3.3) {
                                        curPredictTrackInfo.windPower = 2;
                                    }
                                    else if (data.yblj[m].zxfs >= 3.4 && data.yblj[m].zxfs <= 5.4) {
                                        curPredictTrackInfo.windPower = 3;
                                    }
                                    else if (data.yblj[m].zxfs >= 5.5 && data.yblj[m].zxfs <= 7.9) {
                                        curPredictTrackInfo.windPower = 4;
                                    }
                                    else if (data.yblj[m].zxfs >= 8.0 && data.yblj[m].zxfs <= 10.7) {
                                        curPredictTrackInfo.windPower = 5;
                                    }
                                    else if (data.yblj[m].zxfs >= 10.8 && data.yblj[m].zxfs <= 13.8) {
                                        curPredictTrackInfo.windPower = 6;
                                    }
                                    else if (data.yblj[m].zxfs >= 13.9 && data.yblj[m].zxfs <= 17.1) {
                                        curPredictTrackInfo.windPower = 7;
                                    }
                                    else if (data.yblj[m].zxfs >= 17.2 && data.yblj[m].zxfs <= 20.7) {
                                        curPredictTrackInfo.windPower = 8;
                                    }
                                    else if (data.yblj[m].zxfs >= 20.8 && data.yblj[m].zxfs <= 24.4) {
                                        curPredictTrackInfo.windPower = 9;
                                    }
                                    else if (data.yblj[m].zxfs >= 24.5 && data.yblj[m].zxfs <= 28.4) {
                                        curPredictTrackInfo.windPower = 10;
                                    }
                                    else if (data.yblj[m].zxfs >= 28.5 && data.yblj[m].zxfs <= 32.6) {
                                        curPredictTrackInfo.windPower = 11;
                                    }
                                    else if (data.yblj[m].zxfs >= 32.7 && data.yblj[m].zxfs <= 36.9) {
                                        curPredictTrackInfo.windPower = 12;
                                    }
                                    else if (data.yblj[m].zxfs >= 37 && data.yblj[m].zxfs <= 41) {
                                        curPredictTrackInfo.windPower = 13;
                                    }
                                    else if (data.yblj[m].zxfs >= 42 && data.yblj[m].zxfs <= 45) {
                                        curPredictTrackInfo.windPower = 14;
                                    }
                                    else if (data.yblj[m].zxfs >= 46 && data.yblj[m].zxfs <= 51) {
                                        curPredictTrackInfo.windPower = 15;
                                    }
                                    else if (data.yblj[m].zxfs >= 52 && data.yblj[m].zxfs <= 59) {
                                        curPredictTrackInfo.windPower = 16;
                                    }
                                    else if (data.yblj[m].zxfs >= 60 && data.yblj[m].zxfs <= 61.2) {
                                        curPredictTrackInfo.windPower = 17;
                                    }
                                    else if (data.yblj[m].zxfs > 61.2) {
                                        curPredictTrackInfo.windPower = 18;
                                    }
                                    curPredictTrackInfo.windSpeed = data.yblj[m].zxfs;;
                                    curPredictTrackInfo.airPressure = data.yblj[m].zxqy;;
                                    curPredictTrackInfo.strReportStation = data.yblj[m].ybtm;

                                    arrPredictTracks.push(curPredictTrackInfo); //添加真实轨迹点的一个预测轨迹线
                                }
                            }
                            arrPredictLineTrack.push(arrPredictTracks); //记录真实轨迹点的预测轨迹线
                        }
                    }

                    var objTrackInfo = []; //轨迹点数据结构体
                    objTrackInfo.po = { x: data.lslj[j].lon * Math.pow(10, 7), y: data.lslj[j].lat * Math.pow(10, 7) };  //坐标
                    objTrackInfo.time = data.lslj[j].gqsj; //时间
                    if (data.lslj[j].zxfs >= 0 && data.lslj[j].zxfs <= 0.2) {
                        objTrackInfo.windPower = 0;
                    }
                    else if (data.lslj[j].zxfs >= 0.3 && data.lslj[j].zxfs <= 1.5) {
                        objTrackInfo.windPower = 1;
                    }
                    else if (data.lslj[j].zxfs >= 1.6 && data.lslj[j].zxfs <= 3.3) {
                        objTrackInfo.windPower = 2;
                    }
                    else if (data.lslj[j].zxfs >= 3.4 && data.lslj[j].zxfs <= 5.4) {
                        objTrackInfo.windPower = 3;
                    }
                    else if (data.lslj[j].zxfs >= 5.5 && data.lslj[j].zxfs <= 7.9) {
                        objTrackInfo.windPower = 4;
                    }
                    else if (data.lslj[j].zxfs >= 8.0 && data.lslj[j].zxfs <= 10.7) {
                        objTrackInfo.windPower = 5;
                    }
                    else if (data.lslj[j].zxfs >= 10.8 && data.lslj[j].zxfs <= 13.8) {
                        objTrackInfo.windPower = 6;
                    }
                    else if (data.lslj[j].zxfs >= 13.9 && data.lslj[j].zxfs <= 17.1) {
                        objTrackInfo.windPower = 7;
                    }
                    else if (data.lslj[j].zxfs >= 17.2 && data.lslj[j].zxfs <= 20.7) {
                        objTrackInfo.windPower = 8;
                    }
                    else if (data.lslj[j].zxfs >= 20.8 && data.lslj[j].zxfs <= 24.4) {
                        objTrackInfo.windPower = 9;
                    }
                    else if (data.lslj[j].zxfs >= 24.5 && data.lslj[j].zxfs <= 28.4) {
                        objTrackInfo.windPower = 10;
                    }
                    else if (data.lslj[j].zxfs >= 28.5 && data.lslj[j].zxfs <= 32.6) {
                        objTrackInfo.windPower = 11;
                    }
                    else if (data.lslj[j].zxfs >= 32.7 && data.lslj[j].zxfs <= 36.9) {
                        objTrackInfo.windPower = 12;
                    }
                    else if (data.lslj[j].zxfs >= 37 && data.lslj[j].zxfs <= 41) {
                        objTrackInfo.windPower = 13;
                    }
                    else if (data.lslj[j].zxfs >= 42 && data.lslj[j].zxfs <= 45) {
                        objTrackInfo.windPower = 14;
                    }
                    else if (data.lslj[j].zxfs >= 46 && data.lslj[j].zxfs <= 51) {
                        objTrackInfo.windPower = 15;
                    }
                    else if (data.lslj[j].zxfs >= 52 && data.lslj[j].zxfs <= 59) {
                        objTrackInfo.windPower = 16;
                    }
                    else if (data.lslj[j].zxfs >= 60 && data.lslj[j].zxfs <= 61.2) {
                        objTrackInfo.windPower = 17;
                    }
                    else if (data.lslj[j].zxfs > 61.2) {
                        objTrackInfo.windPower = 18;
                    }

                    objTrackInfo.windSpeed = data.lslj[j].zxfs; //风速
                    objTrackInfo.airPressure = data.lslj[j].zxqy; //气压
                    objTrackInfo.moveDirection = data.lslj[j].ydfx; //移向
                    objTrackInfo.moveSpeed = data.lslj[j].ydsd; //移速
                    objTrackInfo.sevenRadius = data.lslj[j].radiusseven; //7级半径
                    objTrackInfo.tenRadius = data.lslj[j].radiusten; //10级半径
                    API_AddOneTyphoonTrack(curTyphoonPos, objTrackInfo, arrPredictLineTrack); //添加一个真实轨迹点
                }

                API_ReDrawTyphoon();

                API_SetMapLevel(5, null);
                var typhoonPos = API_GetTyphoonPosById(data.lslj[0].tfbh);
                API_SetTyphoonToMapViewCenterByPos(typhoonPos);
            });
        },

        // 获取台风数据
        fetchTyphoonData() {
            this.loading = true;
            $.get(global.IP + '/web/GetTyphoonInfoByYear?year=' + this.selectedYear, (response, status) => {
                this.loading = false;

                // 如果返回的是字符串，需要解析为JSON
                let data = response;
                if (typeof response === 'string') {
                    try {
                        data = JSON.parse(response);
                    } catch (e) {
                        console.error('JSON解析失败:', e, response);
                        return;
                    }
                }

                if (data && data.list && data.list.length > 0) {
                    this.typhoonList = data.list.slice(0, 10).map((item) => {
                        return {
                            id: item.id,
                            number: item.tfbh || item.id,
                            chineseName: item.tfm || '未知',
                            englishName: item.tfme || 'Unknown',
                            isChecked: false,
                            trackData: null
                        };
                    });
                }
            }).fail((jqXHR, textStatus, errorThrown) => {
                this.loading = false;
                console.error('获取台风列表时发生网络错误:', textStatus, errorThrown);
            });
        },
    }
}
</script>

<style scoped>
.typhoon-list-card {
    position: fixed;
    bottom: 20px;
    width: 90%;
    max-width: 450px;
    left: 50%;
    transform: translateX(-50%);
    height: auto;
    max-height: 80vh;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    background-color: #2196f3;
    color: white;
    border-radius: 10px 10px 0 0;
    flex-shrink: 0;
}

.card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
}

.typhoon-count {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 4px 8px; /* 增加点击区域 */
    line-height: 1;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.close-btn:active {
    background-color: rgba(255, 255, 255, 0.2);
}

.card-body {
    padding: 12px 15px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.loading-message {
    text-align: center;
    padding: 20px;
    margin-bottom: 12px;
}

.loading-text {
    font-size: 14px;
    color: #666;
}

.typhoon-list {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 12px;
}

.list-header {
    display: flex;
    background-color: #2196f3;
    color: white;
    padding: 8px 0;
    font-weight: bold;
    font-size: 14px;
    flex-shrink: 0;
}

.header-item {
    flex: 1;
    text-align: center;
    padding: 0 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-item:first-child {
    flex: 0.8;
    /* 与复选框列宽度一致 */
}

.list-content {
    flex: 1;
    overflow-y: auto;
    max-height: 300px;
}

.typhoon-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
}

.typhoon-item:hover {
    background-color: #f5f5f5;
}

.typhoon-item:last-child {
    border-bottom: none;
}

.checkbox-container {
    position: relative;
    flex: 0.8;
    /* 与表头第一列宽度一致 */
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    padding: 0 8px;
    /* 与表头padding一致 */
}

.checkbox-custom {
    width: 16px;
    height: 16px;
    border: 2px solid #ddd;
    border-radius: 3px;
    background-color: white;
    position: relative;
    display: inline-block;
}

.typhoon-checkbox:checked+.checkbox-custom {
    background-color: #2196f3;
    border-color: #2196f3;
}

.typhoon-checkbox:checked+.checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.typhoon-checkbox {
    opacity: 0;
    position: absolute;
    cursor: pointer;
}

.typhoon-number,
.typhoon-name,
.typhoon-english-name {
    flex: 1;
    text-align: center;
    padding: 0 8px;
    /* 与表头padding一致 */
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
}

.list-content::-webkit-scrollbar {
    width: 6px;
}

.list-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.list-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.list-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>