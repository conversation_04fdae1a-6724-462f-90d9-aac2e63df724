<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="web" name="Web">
      <configuration>
        <webroots />
        <sourceRoots>
          <root url="file://$MODULE_DIR$/src/main/java" />
          <root url="file://$MODULE_DIR$/src/main/resources" />
        </sourceRoots>
      </configuration>
    </facet>
    <facet type="Spring" name="Spring">
      <configuration />
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="koalii_svs_client_v3.0.3_jdk1.8_20210512" level="project" />
    <orderEntry type="library" name="kgsp_core-2.1.4" level="project" />
    <orderEntry type="library" name="koalii_svs_client_v3.0.3_jdk1.8_20210512-dependencies" level="project" />
    <orderEntry type="library" name="koalii_svs_client_v3.0.3_jdk1.8_20210512-dependencies (2)" level="project" />
    <orderEntry type="library" name="koalii_svs_client_v3.0.3_jdk1.8_20210512-dependencies (3)" level="project" />
    <orderEntry type="library" name="koalii_svs_client_v3.0.3_jdk1.8_20210512-dependencies (4)" level="project" />
    <orderEntry type="library" name="httpclient-4.5.13-sources" level="project" />
    <orderEntry type="library" name="koalii_svs_client_v3.0.3_jdk1.8_20210512-dependencies (5)" level="project" />
    <orderEntry type="library" name="koalii_svs_client_3.1.1-jdk8_20230518-192258" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-web:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-logging:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-classic:1.2.3" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-core:1.2.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-to-slf4j:2.10.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.10.0" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jul-to-slf4j:1.7.25" level="project" />
    <orderEntry type="library" name="Maven: javax.annotation:javax.annotation-api:1.3.2" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.yaml:snakeyaml:1.19" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-json:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.9.6" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.9.0" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.9.6" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.9.6" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.6" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-parameter-names:2.9.6" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-tomcat:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-core:8.5.31" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-el:8.5.31" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-websocket:8.5.31" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.validator:hibernate-validator:6.0.10.Final" level="project" />
    <orderEntry type="library" name="Maven: javax.validation:validation-api:2.0.1.Final" level="project" />
    <orderEntry type="library" name="Maven: org.jboss.logging:jboss-logging:3.3.2.Final" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml:classmate:1.3.4" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-web:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webmvc:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-starter-test:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test-autoconfigure:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.jayway.jsonpath:json-path:2.4.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:json-smart:2.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:accessors-smart:1.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.ow2.asm:asm:5.0.4" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: junit:junit:4.12" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.assertj:assertj-core:3.9.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.mockito:mockito-core:2.15.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.bytebuddy:byte-buddy:1.7.11" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.bytebuddy:byte-buddy-agent:1.7.11" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.objenesis:objenesis:2.6" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest-core:1.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest-library:1.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.skyscreamer:jsonassert:1.5.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.vaadin.external.google:android-json:0.0.20131108.vaadin1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jcl:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework:spring-test:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.xmlunit:xmlunit-core:2.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis.spring.boot:mybatis-spring-boot-starter:2.1.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-jdbc:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.zaxxer:HikariCP:2.7.9" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jdbc:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis.spring.boot:mybatis-spring-boot-autoconfigure:2.1.0" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis:3.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis-spring:2.0.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.baomidou:mybatis-plus-boot-starter-test:3.4.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.baomidou:mybatis-plus-boot-starter:3.4.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.baomidou:mybatis-plus:3.4.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.baomidou:mybatis-plus-extension:3.4.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.baomidou:mybatis-plus-core:3.4.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.baomidou:mybatis-plus-annotation:3.4.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-autoconfigure:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-tx:5.0.7.RELEASE" level="project" />
    <orderEntry type="module-library">
      <library name="Maven: com.dm:DmJdbcDriver18:1.8">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/src/lib/Dm7JdbcDriver18.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="library" name="Maven: com.alibaba:easyexcel:3.1.5" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:easyexcel-core:3.1.5" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:easyexcel-support:3.1.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi:4.1.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-collections4:4.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-math3:3.6.1" level="project" />
    <orderEntry type="library" name="Maven: com.zaxxer:SparseBitSet:1.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi-ooxml:4.1.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-compress:1.19" level="project" />
    <orderEntry type="library" name="Maven: com.github.virtuald:curvesapi:1.06" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi-ooxml-schemas:4.1.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.xmlbeans:xmlbeans:3.1.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-csv:1.8" level="project" />
    <orderEntry type="library" name="Maven: org.ehcache:ehcache:3.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.25" level="project" />
    <orderEntry type="library" name="Maven: org.projectlombok:lombok:1.16.22" level="project" />
    <orderEntry type="library" name="Maven: org.junit.jupiter:junit-jupiter:5.11.4" level="project" />
    <orderEntry type="library" name="Maven: org.junit.jupiter:junit-jupiter-api:5.1.1" level="project" />
    <orderEntry type="library" name="Maven: org.apiguardian:apiguardian-api:1.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.opentest4j:opentest4j:1.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.junit.platform:junit-platform-commons:1.1.1" level="project" />
    <orderEntry type="library" name="Maven: org.junit.jupiter:junit-jupiter-params:5.1.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.junit.jupiter:junit-jupiter-engine:5.1.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.junit.platform:junit-platform-engine:1.1.1" level="project" />
    <orderEntry type="library" name="Maven: com.github.pagehelper:pagehelper:5.2.0" level="project" />
    <orderEntry type="library" name="Maven: com.github.jsqlparser:jsqlparser:3.2" level="project" />
    <orderEntry type="library" name="Maven: commons-httpclient:commons-httpclient:3.1" level="project" />
    <orderEntry type="library" name="Maven: commons-logging:commons-logging:1.0.4" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:fastjson:1.2.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpclient:4.5.13" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpcore:4.4.9" level="project" />
    <orderEntry type="library" name="Maven: com.belerweb:pinyin4j:2.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.jsoup:jsoup:1.14.3" level="project" />
    <orderEntry type="library" name="Maven: cn.hutool:hutool-all:5.8.15" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-devtools:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.14" level="project" />
    <orderEntry type="library" name="Maven: org.json:json:20210307" level="project" />
  </component>
</module>