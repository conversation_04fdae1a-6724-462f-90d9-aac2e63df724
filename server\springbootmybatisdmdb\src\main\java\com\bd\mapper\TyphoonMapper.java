package com.bd.mapper;

import com.bd.entity.BusinessManagement.AlarmRecord;
import com.bd.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TyphoonMapper {

    List<TyphoonInfo> GetTyphoonInfo(@Param("time")String time);

    List<Integer> GetTyphoonCountInfo(@Param("time")String time);

    List<TyphoonInfo> GetTyphoonInfoByYear(@Param("year")int year);

    List<Integer> GetTyphoonInfoByYearCount();

    List<TyphoonInfo> GetTyphoonInfoById(@Param("id")int id);

    List<TyphoonInfo> GetYBTyphoonInfoById(@Param("id")int id);

    List<TyphoonInfo> GetTodayTyphoonInfo(@Param("startTime")String startTime, @Param("endTime")String endTime);

    int GetTodayTyphoonInfoCount(@Param("startTime")String startTime, @Param("endTime")String endTime);

    List<TyphoonInfo> GetYBQDById(int id);
}
