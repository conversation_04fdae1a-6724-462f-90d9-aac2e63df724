package com.bd.entity;
import lombok.Data;

@Data
public class Ship {
    private int id;
    private int shipId;
    private String shipName;
    private int mmsi;
    private int lon;
    private int lat;
    private int reportTime;
    private int head;
    private int speed;
    private int height;
    private int length;
    private int width;
    private String callSign;
    private String shipType;
    private int imo;
    private int inPortState; //0=不在 1=在
    private String portName = "";
    private String termNo;
    private String loadTime;
    private String inPortStateStr;

    public String getInPortStateStr() {
        inPortStateStr = inPortState == 0 ? "出港" : "进港";
        return inPortStateStr;
    }

    public void setInPortStateStr(String inPortStateStr) {
        this.inPortStateStr = inPortStateStr;
    }
}
