

/*
http://10.89.9.251:9092/api/resource/v1/cameras/previewURLs

{
    "token":"MmM5MGYyYmQ5Y2FkZTNjOWRmMmQ3YzUxNjdhZjBlMGE=",
    "streamType": 0,
    "protocol": "hls",
    "transmode": 0,
    "cameraIndexCode":["6a2a5631da734318b777642cf62ace24","6a2a5631da734318b777642cf62ace24"]
}

[
    {
        "cameraIndexCode": "6a2a5631da734318b777642cf62ace24",
        "url": "http://10.0.154.226:25439/openUrl/slsu9So/live.m3u8"
    },
    {
        "cameraIndexCode": "6a2a5631da734318b777642cf62ace24",
        "url": "http://10.0.154.226:25439/openUrl/slziwZW/live.m3u8"
    }
]

*/
function getHLSFromZhongtai (token, cameraIndex){
    $.ajaxSettings.async = false;
    $.post("http://10.89.9.251:9092/api/resource/v1/cameras/previewURLs",
    {
        "token":token,
        "streamType": 0,
        "protocol": "hls",
        "transmode": 0,
        "cameraIndexCode":[cameraIndex]
    },
    function(result){
        //console.log(result);
    });
    $.ajaxSettings.async = true;
}