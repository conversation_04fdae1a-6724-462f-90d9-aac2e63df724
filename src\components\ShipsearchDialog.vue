<template>
  <!-- 船舶搜索对话框的遮罩层，覆盖整个屏幕 -->
  <div class="ship-search-dialog-overlay">
    <!-- 船舶搜索对话框主体 -->
    <div class="ship-search-dialog">
      <!-- 对话框头部 -->
      <div class="dialog-header">
        <span>搜索船舶详情</span>
        <!-- 关闭按钮，点击时触发closeDialog方法 -->
        <button class="close-btn" @click="closeDialog">X</button>
      </div>
      <!-- 对话框内容区域 -->
      <div class="dialog-body">
        <!-- 工具栏：包含导出、北斗通信按钮和总数显示 -->
        <div class="toolbar">
          <div class="toolbar-buttons">
            <!-- <button class="btn-export">导出</button> -->
            <button class="btn-beidou">北斗通信</button>
          </div>
          <!-- 显示船舶总数 -->
          <div class="total-count">总数: {{ ships.length }}</div>
        </div>
        <!-- 表格容器：显示船舶列表 -->
        <div class="table-container">
          <div class="swipe-hint-container">
            <div class="swipe-hint" v-if="isMobile">左右滑动查看更多信息</div>
          </div>
          <div class="table-wrapper">
            <table>
              <thead>
                <tr>
                  <th>序号</th>
                  <th>船只名称</th>
                  <th>MMSI</th>
                  <th>经度</th>
                  <th>纬度</th>
                  <th>航速(节)</th>
                  <th>航向(度)</th>
                  <th>报位时间</th>
                </tr>
              </thead>
              <tbody>
                <!-- 遍历ships数组，显示每艘船的信息 -->
                <tr v-for="(ship, index) in ships" :key="index">
                  <td>{{ index + 1 }}</td>
                  <td>{{ ship.shipName }}</td>
                  <td>{{ ship.mmsi }}</td>
                  <td>{{ ship.lon }}</td>
                  <td>{{ ship.lat }}</td>
                  <td>{{ ship.speed }}</td>
                  <td>{{ ship.course }}</td>
                  <td>{{ ship.time }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ShipsearchDialog",
  // 通过props接收从父组件传入的船舶数据
  props: {
    ships: {
      type: Array,
      required: true,
      default: () => [] // 默认为空数组
    }
  },
  data() {
    return {
      isMobile: false
    }
  },
  mounted() {
    // 检测是否是移动设备
    this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  },
  methods: {
    // 关闭对话框方法：触发close事件，通知父组件关闭对话框
    closeDialog() {
      this.$emit('close');
    },
  },
};
</script>

<style scoped>
/* 对话框遮罩层样式：覆盖整个屏幕，半透明黑色背景 */
.ship-search-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* 对话框主体样式 */
.ship-search-dialog {
  width: 90%; /* 更改为百分比宽度，使其适应不同设备 */
  max-width: 600px; /* 设置最大宽度，避免在大屏幕上过宽 */
  height: auto; /* 高度自适应 */
  max-height: 70vh; /* 减小最大高度为视口高度的70% */
  background-color: white; /* 更改为白色背景 */
  border: 1px solid #e8e8e8; /* 浅灰色边框 */
  color: #333; /* 深灰色文字 */
  font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 确保内容不会溢出 */
  border-radius: 4px; /* 添加圆角 */
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); /* 添加阴影 */
}

/* 对话框头部样式 */
.dialog-header {
  background-color: #1890ff; /* 蓝色主题 */
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  border-bottom: 1px solid #e8e8e8;
  color: white; /* 头部使用白色文字 */
}

/* 关闭按钮样式 */
.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

/* 关闭按钮悬停效果 */
.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 对话框内容区域样式 */
.dialog-body {
  padding: 10px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

/* 工具栏按钮容器 */
.toolbar-buttons {
  display: flex;
}

/* 工具栏按钮样式 */
.toolbar button {
  background-color: #1890ff; /* 蓝色主题 */
  border: 1px solid #1890ff;
  color: white;
  padding: 5px 15px;
  cursor: pointer;
  margin-right: 10px;
  border-radius: 4px; /* 圆角按钮 */
}

/* 最后一个按钮不需要右边距 */
.toolbar button:last-child {
  margin-right: 0;
}

/* 工具栏按钮悬停效果 */
.toolbar button:hover {
  background-color: #40a9ff; /* 浅蓝色 */
  border-color: #40a9ff;
}

/* 总数显示样式 */
.total-count {
  font-size: 14px;
  color: #333; /* 深灰色文字 */
}

/* 表格容器样式：支持垂直和水平滚动 */
.table-container {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 100px; /* 设置最小高度 */
  max-height: calc(80vh - 120px); /* 动态计算最大高度 */
}

/* 滑动提示容器 */
.swipe-hint-container {
  width: 100%;
  position: relative;
  z-index: 3;
}

/* 表格包装器，处理滚动 */
.table-wrapper {
  width: 100%;
  flex: 1;
  overflow-y: auto; /* 垂直滚动 */
  overflow-x: auto; /* 水平滚动，适应更多列 */
  position: relative; /* 为固定头部和列添加定位上下文，这是实现固定效果的基础 */
}

/* 表格基本样式 */
table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0; /* 确保单元格之间没有间距 */
}

/* 表头行样式 */
thead tr {
  background-color: #f5f5f5; /* 浅灰色背景 */
  position: sticky; /* 关键属性：使表头固定在顶部 */
  top: 0; /* 固定到容器顶部 */
  z-index: 2; /* 确保表头在普通单元格之上，但在固定列交叉处之下 */
}

/* 表格单元格通用样式 */
th, td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #e8e8e8; /* 浅灰色边框 */
  white-space: nowrap; /* 防止文本换行，保持表格布局 */
  box-sizing: border-box; /* 确保padding不会增加元素宽度 */
}

/* 表头单元格样式 */
th {
  font-weight: bold;
  color: #333; /* 深灰色文字 */
  background-color: #f5f5f5; /* 确保背景色覆盖整个单元格，滚动时不会露出内容 */
  position: sticky; /* 关键属性：使表头固定 */
  top: 0; /* 固定到容器顶部 */
  z-index: 2; /* 层级确保表头在普通内容之上 */
}

/* 固定第一列（序号） */
th:first-child, td:first-child {
  position: sticky; /* 关键属性：使列固定 */
  left: 0; /* 固定到容器最左侧 */
  z-index: 1; /* 确保固定列在普通单元格之上 */
}

/* 固定第二列（船名） */
th:nth-child(2), td:nth-child(2) {
  position: sticky; /* 关键属性：使列固定 */
  left: 45px; /* 固定位置 = 第一列宽度，确保紧贴第一列 */
  z-index: 1; /* 确保固定列在普通单元格之上 */
}

/* 表头第一列和第二列的Z-index要更高，确保它们在交叉点位于最上层 */
th:first-child {
  z-index: 3; /* 交叉点需要最高层级，确保既在表头上方又在固定列上方 */
}

th:nth-child(2) {
  z-index: 3; /* 交叉点需要最高层级，确保既在表头上方又在固定列上方 */
}

/* 表格偶数行样式 */
tbody tr:nth-child(even) {
  background-color: #fafafa; /* 非常浅的灰色 */
}

/* 表格奇数行样式 */
tbody tr:nth-child(odd) {
  background-color: white; /* 白色 */
}

/* 表格数据单元格样式 */
td {
  color: #666; /* 中灰色文字 */
}

/* 固定列的背景色需要与行背景色相匹配 */
tbody tr:nth-child(even) td:first-child,
tbody tr:nth-child(even) td:nth-child(2) {
  background-color: #fafafa; /* 非常浅的灰色，与偶数行背景一致 */
}

tbody tr:nth-child(odd) td:first-child,
tbody tr:nth-child(odd) td:nth-child(2) {
  background-color: white; /* 白色，与奇数行背景一致 */
}

/* 确保滚动时边缘有阴影效果 */
td:nth-child(2) {
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1); /* 添加右侧阴影，提供视觉分隔，增强用户体验 */
}

/* 确保表头行的固定列有正确的样式 */
th:first-child, th:nth-child(2) {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* 添加底部阴影，增强表头与内容的分隔 */
}

/* 第二列与第一列交界处的边界 */
th:nth-child(2)::before, td:nth-child(2)::before {
  content: '';
  position: absolute;
  left: -1px;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: inherit; /* 使用当前元素的背景色，防止两列之间出现缝隙 */
}

/* 自定义滚动条样式（Webkit浏览器） */
.table-wrapper::-webkit-scrollbar {
  width: 8px; /* 垂直滚动条宽度 */
  height: 8px; /* 水平滚动条高度 */
}

/* 滚动条轨道样式 */
.table-wrapper::-webkit-scrollbar-track {
  background: #f5f5f5; /* 浅灰色背景 */
}

/* 滚动条滑块样式 */
.table-wrapper::-webkit-scrollbar-thumb {
  background-color: #1890ff; /* 蓝色主题 */
  border-radius: 4px;
}

/* 滚动条滑块悬停样式 */
.table-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #40a9ff; /* 浅蓝色 */
}

/* 滑动提示样式 */
.swipe-hint {
  text-align: center;
  color: white;
  padding: 5px 0;
  font-size: 12px;
  background-color: #1890ff; /* 蓝色主题 */
  border-radius: 4px 4px 0 0;
  position: relative;
  top: 0;
  width: 100%;
}

/* 针对移动设备的优化表格样式 */
@media (max-width: 768px) {
  .ship-search-dialog {
    width: 95%;
    max-width: none;
    max-height: 80vh;
  }
  
  .dialog-header {
    padding: 8px 10px;
    font-size: 14px;
  }
  
  .toolbar {
    flex-direction: row; /* 保持水平排列 */
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }
  
  .toolbar button {
    padding: 4px 10px;
    font-size: 12px;
    margin-right: 6px; /* 减小按钮间距 */
  }
  
  .total-count {
    font-size: 12px; /* 减小字体大小 */
    white-space: nowrap; /* 防止文本换行 */
  }
  
  th, td {
    padding: 6px;
    font-size: 12px;
  }
  
  /* 表格样式优化 */
  table {
    min-width: 600px; /* 确保表格有最小宽度，保证内容完整显示 */
  }
  
  .table-wrapper {
    -webkit-overflow-scrolling: touch; /* 增加iOS滚动流畅度，改善移动端用户体验 */
    max-height: 50vh; /* 限制表格最大高度，适应移动设备屏幕 */
  }
  
  /* 滑动提示样式增强 */
  .swipe-hint {
    position: sticky;
    top: 0;
    z-index: 2; /* 确保提示显示在表格上方 */
    width: 100%;
    margin-bottom: 0;
    padding: 8px 0;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background-color: #1890ff;
  }
  
  /* 固定表头样式 - 移动设备特定优化 */
  thead tr {
    position: sticky; /* 使表头固定在顶部 */
    top: 0; /* 固定到容器顶部 */
    z-index: 2; /* 确保正确的层叠顺序 */
    background-color: #f5f5f5; /* 与PC端保持一致的背景色 */
  }
  
  th {
    position: sticky; /* 重申固定表头属性，确保在移动端生效 */
    top: 0; /* 固定位置 */
    background-color: #f5f5f5; /* 背景色，防止滚动时看到下方内容 */
    z-index: 2; /* 层级确保覆盖普通内容 */
  }
  
  /* 固定序号列和船只名称列 - 移动设备特定优化 */
  th:nth-child(1), td:nth-child(1) { /* 序号列 */
    position: sticky; /* 使列固定 */
    left: 0; /* 固定到容器最左侧 */
    z-index: 1; /* 确保固定列在普通单元格之上 */
    width: 45px; /* 明确设置宽度，确保布局稳定 */
    min-width: 45px; /* 最小宽度，防止内容挤压 */
    max-width: 45px; /* 最大宽度，保持列宽一致 */
    padding-right: 0; /* 优化内边距，节省空间 */
    padding-left: 8px; /* 保持内容与边缘有合适间距 */
  }
  
  th:nth-child(2), td:nth-child(2) { /* 船名列 */
    position: sticky; /* 使列固定 */
    left: 45px; /* 固定位置 = 第一列宽度 */
    z-index: 1; /* 层级确保覆盖普通内容 */
    padding-left: 8px; /* 优化内边距 */
    border-left: none; /* 移除左侧边框，避免与第一列边框重叠 */
    background-clip: padding-box; /* 防止背景色溢出到边框 */
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1); /* 添加阴影，提供视觉分隔 */
  }
  
  /* 表头的固定列需要更高的z-index - 处理交叉区域 */
  th:nth-child(1) {
    z-index: 3; /* 确保表头第一列在最上层，覆盖所有其他元素 */
  }
  
  th:nth-child(2) {
    z-index: 3; /* 确保表头第二列在最上层，覆盖所有其他元素 */
  }
  
  /* 确保第二列覆盖第一列的边缘，防止出现缝隙 */
  th:nth-child(2)::before, td:nth-child(2)::before {
    content: '';
    position: absolute;
    left: -1px;
    top: 0;
    bottom: 0;
    width: 1px;
    background-color: inherit; /* 使用当前元素的背景色，实现无缝连接 */
  }
  
  /* 为序号列和船名列添加共同的背景样式 */
  th:nth-child(1), th:nth-child(2) {
    background-color: #f5f5f5; /* 表头背景色 */
  }
  
  tbody tr:nth-child(even) td:nth-child(1),
  tbody tr:nth-child(even) td:nth-child(2) {
    background-color: #fafafa; /* 偶数行背景色 */
  }
  
  tbody tr:nth-child(odd) td:nth-child(1),
  tbody tr:nth-child(odd) td:nth-child(2) {
    background-color: white; /* 奇数行背景色 */
  }
  
  /* 优化MMSI列的位置，确保无缝滚动 */
  th:nth-child(3), td:nth-child(3) {
    padding-left: 10px; /* 增加左侧内边距，与固定列保持适当距离，提高可读性 */
  }
}
</style>
