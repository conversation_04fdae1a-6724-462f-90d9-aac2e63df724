.Bus_head{
    height:6%;
    background-color:rgb(16,68,168); 
    color:white;
    text-align:left;
    font-size: 16px;
    padding:10px;
  }
  
  .Bus_serch{
    padding:10px; 
    width: 100%; 
    height: 16%; 
    background-color: rgb(29,53,112); 
    color:white; 
    text-align:left;
  }
  
  .Bus_serch input,select{
    width: 150px;
    height: 25px;
    background-color: rgb(29,53,112); 
    border: 1px solid rgb(27,103,244);
  }
  
  .Bus_serch select{
    width: 80px;
    height: 25px;
    background-color: rgb(29,53,112); 
    border: 1px solid rgb(27,103,244);
  }
  
  .Bus_serch_button{
    padding-top: 10px;
  }
  
  .Bus_serch button{
    height: 25px;
    background-color: rgb(25, 86, 200); 
    border: 1px solid rgb(27,103,244);
    border: 0; 
    border-radius: 3px;
  }
  
  .Bus_result {
    width: 100%;
    height: 76%;
    color: white;
    text-align: left;
    padding-top: 2%;
  }
  
  .Bus_result_table{
    width: 100%; 
    height: 85%; 
    overflow-y: scroll;
    color: white;
  }


  .Bus_result_page{
    float: right; 
    padding-top: 10px; 
    padding-right: 20px;
  }
  .Bus_result_bg{
    background-color: rgb(16, 68, 168);
    width: 100%; 
    height: 12%; 
  }
  .Bus_result_total{
    float: left; 
    padding-top: 35px; 
    padding-left: 30px
  }
  .Bus_sys_page{
    float: right; 
    margin-top: 7px;
    padding-right: 20px
  }
  .Bus_sys_total{
    float: left; 
    padding-top: 13px; 
    padding-left: 30px
  }
  .btncs{
    width: 40px;
    height: 40px; 
    position: absolute; 
    /* top: 195px; 
    right: 511px;  */
    z-index: 7; 
    background-color:  #0b1654; 
    border: 1px solid #011540; 
    border-radius: 25%; 
    -moz-border-radius: 25%; 
    -webkit-border-radius: 25%; 
    opacity: 0.9;
    cursor: pointer;
  }
  .btncs:hover {
    background-color: rgb(43, 78, 184);
  }
  .btncs1{
    width: 40px; 
    height: 80px; 
    position: absolute; 
    /* top: 105px; 
    right: 511px;  */
    z-index: 7; 
    background-color:  rgb(12, 12, 83); 
    border-radius: 10px; 
    transform-origin: center; 
    opacity: 0.9;
    cursor: pointer;
  }
  .btncs1:hover{
    background-color: rgb(43, 78, 184);
  }
.tooltiptext {
    font-size: 16px;
    visibility: hidden;
    width: 90px;
    background-color: black;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    right: 48px;

    /* 定位 */
    position: absolute;
    z-index: 1;
}

.btncs:hover .tooltiptext {
    visibility: visible;
}