package com.bd.entity;
import lombok.Data;

/**
 *船名登记信息
 */
@Data
public class ShipNameRegisInfo {
    private String zjzgl;
    private String yycbcmsqsbh;
    private String yyyblxkzbh;
    private String ycm;
    private String cbsyrdz;
    private String yyxmpzwh;
    private String cbzl;
    private String sqrdz;
    private String pzwh;
    private String cblxyw;
    private String cbsyrmc;
    private String ycssdqmc;
    private String zxhzzdjzmsbh;
    private String cblx;
    private String gxcz;
    private String cm;
    private String sqrxm;
    private String cbsyrdh;
    private String ctczyw;
    private String yycbcmdjbahzsbh;
    private String cz;
    private String cmdjxxwybs;
    private String sqrq;
    private String cbsyrmcyw;
    private String ctcz;
    private String cwgjzbpzsbh;
    private String cmyw;
    private String czsm;
    private String sqrdh;
    private String sqrjmsfzhmhgszch;
    private String zdw;
    private String cbsyrdzyw;
    private String gxsj;
    private String ycxxwybs;
    private String ycssdqdm;
    private String etl_dt;
}
