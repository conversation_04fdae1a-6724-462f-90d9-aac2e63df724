package com.bd.entity;

import lombok.Data;

@Data
public class TyphoonInfo {
    private int id;
    // 台风编号
    private String tfbh;
    // 入库时间
    private String loadTime;
    // 台风登陆时间
    private String tfland;
    // 台风中文名
    private String tfm;
    // 台风英文名
    private String tfme;
    // 台风登陆年份
    private String tfyear;
    // 10级风圈半径
    private int radiusten;
    // 7级风圈半径
    private int radiusseven;
    // 经度
    private float lon;
    // 纬度
    private float lat;
    // 移动方向
    private String ydfx;
    // 移动速度
    private int ydsd;
    // 中心风速
    private int zxfs;
    // 中心气压
    private int zxqy;
    // 过去时间
    private String gqsj;

    // 预报轨迹
    //预报台名
    private String ybtm;
    // 预报时间
    private String ybsj;

}
