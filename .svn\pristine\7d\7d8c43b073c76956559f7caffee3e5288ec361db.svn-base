package com.bd.service;

import com.bd.entity.*;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface AreaService {

    List<AreaInfo> GetAreaInfo();
    List<AreaInfo> GetAreaInfoByUserId(String userId);
    AreaInfo GetAreaInfoById(int id);
    void UpdateAreaInfo(AreaInfo areaInfo);
    void DeleteAreaInfo(int id);
    void InsertAreaInfo(AreaInfo areaInfo);

    List<AreaInfo> GetAllAreaInfo(int pageNum);
    int GetAllAreaInfoCount();

    List<MarkInfo> GetAllMarkInfo(int userId);
    int GetAllMarkInfoCount(int userId);

    void InsertMarkInfo(MarkInfo markInfo);

    void deleteMarkInfoById(int markId, int userId);

    void UpdateMarkInfo(MarkInfo markInfo);

    MarkInfo GetMarkInfoById(int id);

    List<AreaInfo> GetAreaInfoByUserIdPageNum(int userId, int pageNum);

    int GetAreaInfoByUserIdPageNumCount(int userId);

    List<FishArea> GetFishAreaInfo();

    List<ShipSerch> GetMarkInfoByName(int userId, String keyword);
}
