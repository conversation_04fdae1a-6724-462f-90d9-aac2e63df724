package com.bd.mapper;

import com.bd.entity.AreaInfo;
import com.bd.entity.FishArea;
import com.bd.entity.MarkInfo;
import com.bd.entity.ShipSerch;
import com.bd.service.impl.ShipStaticInfoImpl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AreaMapper {

    List<AreaInfo> GetAreaInfo();
    List<AreaInfo> GetAreaInfoByUserId(@Param("userId")String userId);
    AreaInfo GetAreaInfoById(@Param("id")int id);
    void UpdateAreaInfo(@Param("info")AreaInfo areaInfo);
    void DeleteAreaInfo(@Param("id")int id);
    void InsertAreaInfo(@Param("info")AreaInfo areaInfo);

    List<AreaInfo> GetAllAreaInfo(@Param("pageNum")int pageNum);
    int GetAllAreaInfoCount();

    List<MarkInfo> GetAllMarkInfo(@Param("userId")int userId);
    int GetAllMarkInfoCount(@Param("userId")int userId);

    void InsertMarkInfo(@Param("markInfo")MarkInfo markInfo);

    void deleteMarkInfoById(@Param("markId")int markId, @Param("userId")int userId);

    void UpdateMarkInfo(@Param("markInfo")MarkInfo markInfo);

    MarkInfo GetMarkInfoById(@Param("id")int id);

    List<AreaInfo> GetAreaInfoByUserIdPageNum(@Param("userId")int userId, @Param("pageNum")int pageNum);

    int GetAreaInfoByUserIdPageNumCount(@Param("userId") int userId);

    List<FishArea> GetFishAreaInfo();

    List<ShipSerch> GetMarkInfoByName(@Param("userId")int userId, @Param("keyword")String keyword);
}
