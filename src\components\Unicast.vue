<template>
  <div class="unicast-container">
    <div class="header-wrapper">
      <button class="back-btn" @click="goBack"></button>
      <div class="switch" :class="{ 'multicast-active': !isUnicast }">
        <div class="switch-item unicast-item" @click="goToUnicast">单播</div>
        <div class="switch-item multicast-item" @click="goToMulticast">组播</div>
      </div>
    </div>
    <div class="ship-list">
      <label
        v-for="ship in ships"
        :key="ship.id"
        class="ship-btn"
        :class="{ selected: selectedShipIds.includes(ship.id) }"
      >
        <input
          type="checkbox"
          v-model="selectedShipIds"
          :value="ship.id"
          style="display:none"
        />
        {{ ship.shipName }}
      </label>
      <button class="add-btn" @click="addShip">新建</button>
    </div>
    <div class="chat-area">
      <!-- 聊天内容可以用 v-for 渲染 -->
    </div>
    <div class="input-area">
      <input v-model="input" class="chat-input" />
      <button
        class="send-btn"
        @mousedown="sendBtnActive = true"
        @mouseup="sendBtnActive = false"
        @mouseleave="sendBtnActive = false"
        @touchstart="sendBtnActive = true"
        @touchend="sendBtnActive = false"
        @touchcancel="sendBtnActive = false"
        :class="{active: sendBtnActive}"
        @click="send"
      >发送</button>
    </div>
  </div>
</template>

<script>
import global from './Global.vue'
export default {
  data() {
    return {
      isUnicast: true,
      ships: [
        { id: 1, bdid: 'BD001', shipName: '沪崇渔12001' },
        { id: 2, bdid: 'BD002', shipName: '沪崇渔12002' },
        { id: 3, bdid: 'BD003', shipName: '沪崇渔12003' }
      ],
      selectedShipIds: [],
      input: '',
      sendBtnActive: false
    }
  },
  computed: {
    // modeText is no longer needed
  },
  methods: {
    goBack() {
      this.$router.go(-1);
      setTimeout(() => {
        window.location.reload();
      }, 100);
    },
    goToUnicast() {
      if (!this.isUnicast) {
        this.isUnicast = true;
        this.$router.replace('/unicast');
      }
    },
    goToMulticast() {
      if (this.isUnicast) {
        this.isUnicast = false;
        this.$router.replace('/multicast');
      }
    },
    addShip() {
      // 添加船只逻辑
    },
    send() {
      if (this.selectedShipIds.length === 0) {
        alert('请选择要发送的船舶');
        return;
      }
      const mess = this.input;
      const userId = sessionStorage.getItem('userId');
      this.selectedShipIds.forEach(id => {
        const ship = this.ships.find(s => s.id === id);
        if (!ship) return;
        const data = {
          staticShipId: ship.id,
          bdId: ship.bdid,
          msg: mess,
          userId
        };
        $.ajax({
          url: global.IP + "/web/InsertBdMsg",
          type: "POST",
          data: JSON.stringify(data),
          dataType: "json",
          contentType: "application/json",
          success: function(data) {
            // ...你的成功逻辑
          },
          error: function() {
            // ...你的失败逻辑
          }
        });
      });
      this.input = '';
    }
  }
}
</script>

<style scoped>
.unicast-container {
  width: 100%;
  height: 100vh;
  background: #f3f3f3;
  display: flex;
  flex-direction: column;
}

.header-wrapper {
  position: relative;
  background-color: #1890ff;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0;
}

.switch {
  display: flex;
  position: relative;
  width: 140px;
  border-radius: 20px;
  background: #fff;
  padding: 2px;
  cursor: pointer;
  user-select: none;
}

.switch::before {
  content: '';
  position: absolute;
  width: calc(50% - 2px);
  height: calc(100% - 4px);
  top: 2px;
  left: 2px;
  background: #1890ff;
  border-radius: 18px;
  transition: left 0.25s ease-out;
  z-index: 1;
}

.switch.multicast-active::before {
  left: 50%;
}

.switch-item {
  flex: 1;
  padding: 6px;
  text-align: center;
  z-index: 2;
  font-weight: 500;
  font-size: 14px;
  transition: color 0.25s ease-out;
}

.switch .unicast-item {
  color: white;
}

.switch .multicast-item {
  color: #333;
}

.switch.multicast-active .unicast-item {
  color: #333;
}

.switch.multicast-active .multicast-item {
  color: white;
}

.back-btn {
  position: absolute;
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}
.back-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  border-top: 2px solid white;
  border-left: 2px solid white;
  transform: translate(-50%, -50%) rotate(-45deg);
}
.header {
  background: #e5e5e5;
  text-align: center;
  font-size: 32px;
  padding: 16px 0;
  font-weight: bold;
}
.ship-list {
  display: inline-flex;
  align-items: center;
  padding: 8px 8px;
  margin: 3px 0;
  gap: 4px 6px;
  justify-content: flex-start;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  min-height: 44px;
  margin-bottom: 0;
  white-space: nowrap;
  overflow-x: auto;
  width: 100%;
  box-sizing: border-box;
}
.ship-list > .ship-btn {
  margin-bottom: 0;
}
.ship-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  border: 2px solid #1890ff;
  border-radius: 4px;
  padding: 4px 16px;
  font-size: 14px;
  color: #1890ff;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(.4,0,.2,1), background 0.2s, color 0.2s, box-shadow 0.2s;
  font-weight: 500;
  letter-spacing: 0.2px;
  margin-right: 4px;
  min-width: 70px;
  max-width: 140px;
  white-space: nowrap;
  box-shadow: none;
  user-select: none;
  position: relative;
}
.ship-btn.selected {
  background: #1890ff;
  color: #fff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.18);
  border: 2px solid #1890ff;
}
.add-btn {
  width: 60px;
  height: 28px;
  font-size: 14px;
  border: 1.5px dashed #1890ff;
  border-radius: 4px;
  background: #fff;
  color: #1890ff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 1px 4px rgba(24, 144, 255, 0.13);
  margin-left: 0;
  margin-bottom: 0;
}
.add-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}
.add-btn:active {
  transform: scale(0.95);
  background-color: #fff;
  color: #1890ff;
  border-color: #1890ff;
  border-style: solid;
}
.chat-area {
  flex: 1;
  border: 2px solid #f3f3f3;
  background: #f3f3f3;
  margin: 0 0 0 0;
  padding: 0;
  overflow-y: auto;
}
.input-area {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #e5e5e5;
  border-top: 1px solid #ccc;
}
.chat-input {
  flex: 1;
  height: 40px;
  font-size: 18px;
  border: 2px solid #ccc;
  border-radius: 4px;
  padding: 0 8px;
  margin-right: 16px;
}
.chat-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}
.send-btn {
  position: relative;
  overflow: hidden;
  width: 80px;
  height: 40px;
  font-size: 20px;
  background: #fff;
  border: 2px solid #1890ff;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.15s cubic-bezier(.4,0,.2,1), background 0.15s, color 0.15s, box-shadow 0.15s, border-color 0.15s;
  box-shadow: 0 1px 4px rgba(24, 144, 255, 0.10);
  outline: none;
}
.send-btn.active {
  transform: scale(0.93);
  background: #1890ff;
  color: #fff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.18);
  border-color: #1890ff;
}
.send-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
  border-color: #1890ff;
}
.send-btn .ripple {
  position: absolute;
  border-radius: 50%;
  transform: scale(0);
  animation: ripple 0.5s linear;
  background-color: rgba(74, 111, 165, 0.3);
  pointer-events: none;
  z-index: 2;
}
@keyframes ripple {
  to {
    transform: scale(2.5);
    opacity: 0;
  }
}
</style>
