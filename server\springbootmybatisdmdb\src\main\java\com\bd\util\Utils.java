package com.bd.util;

import com.bd.entity.ShipTrack;
import lombok.SneakyThrows;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.junit.jupiter.api.Test;

import java.lang.management.ManagementFactory;
import java.lang.management.OperatingSystemMXBean;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

public class Utils {

    public static void main(String[] args) {
        //int seconds = 17854, msec = 383561418;// 秒，毫秒
        //System.out.println(secToTime(seconds));
        //System.out.println(msec + "毫秒转换格式时间：\t" + msecToTime(msec));
        //System.out.println("当前时间（时:分：秒.毫秒）\t" + new SimpleDateFormat("HH:mm:ss.SSS").format(new Date()));
        //System.out.println(System.currentTimeMillis());

        // 当前时间
        //long currentTime= new Date().getTime()/1000;
        // 未来时间
        //long futureTime = new Date("2010/01/01 00:00:00").getTime()/1000;
        //System.out.println(currentTime);
        //System.out.println(futureTime);
        //System.out.println(currentTime - futureTime);
        //System.out.println(currentTime - futureTime - 60 - 28800);
        //String str = GetStrTimeForSec(1663214398);
        //long s = GetLongTimeForString(str);

        System.out.println(GetLastDay());
        System.out.println(GetYesterDay());
    }

    /**
     * 秒转换小时-分-秒analytics/util/DateUtil.java
     *
     * @param seconds 秒为单位 比如..600秒
     * @return 比如...2小时3分钟52秒
     */
    public static String secToTime(int seconds) {
        int hour = seconds / 3600;
        int minute = (seconds - hour * 3600) / 60;
        int second = (seconds - hour * 3600 - minute * 60);

        StringBuffer sb = new StringBuffer();
        if (hour > 0) {
            sb.append(hour + "小时");
        }
        if (minute > 0) {
            sb.append(minute + "分");
        }
        if (second > 0) {
            sb.append(second + "秒");
        }
        if (second == 0) {
            sb.append("<1秒");
        }
        return sb.toString();
    }

    /**
     * 将int类型数字转换成时分秒毫秒的格式数据
     *
     * @param time long类型的数据
     * @return HH:mm:ss.SSS
     * <AUTHOR> 2019/04/11
     */
    public static String msecToTime(int time) {
        String timeStr = null;
        int hour = 0;
        int minute = 0;
        int second = 0;
        int millisecond = 0;
        if (time <= 0)
            return "00:00:00.000";
        else {
            second = time / 1000;
            minute = second / 60;
            millisecond = time % 1000;
            if (second < 60) {
                timeStr = "00:00:" + unitFormat(second) + "." + unitFormat2(millisecond);
            } else if (minute < 60) {
                second = second % 60;
                timeStr = "00:" + unitFormat(minute) + ":" + unitFormat(second) + "." + unitFormat2(millisecond);
            } else {// 数字>=3600 000的时候
                hour = minute / 60;
                minute = minute % 60;
                second = second - hour * 3600 - minute * 60;
                timeStr = unitFormat(hour) + ":" + unitFormat(minute) + ":" + unitFormat(second) + "."
                        + unitFormat2(millisecond);
            }
        }
        return timeStr;
    }

    public static String unitFormat(int i) {// 时分秒的格式转换
        String retStr = null;
        if (i >= 0 && i < 10)
            retStr = "0" + Integer.toString(i);
        else
            retStr = "" + i;
        return retStr;
    }

    public static String unitFormat2(int i) {// 毫秒的格式转换
        String retStr = null;
        if (i >= 0 && i < 10)
            retStr = "00" + Integer.toString(i);
        else if (i >= 10 && i < 100) {
            retStr = "0" + Integer.toString(i);
        } else
            retStr = "" + i;
        return retStr;
    }

    public static int GetNowTimeFrom2010(){
        // 当前时间
        long currentTime= new Date().getTime()/1000;
        // 未来时间
        long futureTime = new Date("2010/01/01 00:00:00").getTime()/1000;

        return (int)(currentTime - futureTime - 60 - 28800);
    }

    public static long GetNowTimelong(){
        return new Date().getTime()/1000;
    }

    public static String GetNowTimeString(int seclast){
        Date day = new Date(new Date().getTime() - seclast*1000L);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(day);
    }

    public static String GetStrTimeForSec(long seclast){
        Date day = new Date(seclast*1000);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(day);
    }

    @SneakyThrows
    public static boolean CompareTime(String time1, String time2){
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (sf.parse(time1).getTime() < sf.parse(time2).getTime()){
            return false;
        }else{
            return true;
        }
    }

    @SneakyThrows
    public static boolean CompareDate(String time1, String time2){
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        if (sf.parse(time1).getTime() < sf.parse(time2).getTime()){
            return false;
        }else{
            return true;
        }
    }

    @SneakyThrows
    public static float GetHourByTwoTime(String time1, String time2){
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return (sf.parse(time2).getTime()/1000 - sf.parse(time1).getTime()/1000)/3600;
    }

    public static String GetNowTimeString(){
        Date day = new Date(new Date().getTime());
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(day);
    }

    public static String GetTodayTimeString(){
        Date day = new Date(new Date().getTime());
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        return df.format(day);
    }

    public static String GetNowdayTimeString(){
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dayStart = now.with(LocalTime.MIN);
        return dayStart.format(fmt);
    }

    public static String GetNowMonthTimeString(){
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime monthStart = now.with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
        return monthStart.format(fmt);
    }

    @SneakyThrows
    public static long GetLongTimeForString(String strTime){
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sf.parse(strTime).getTime()/1000;
    }

    public static int GetNowTimeString_year(){
        Date day = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        return Integer.parseInt(df.format(day).split("-")[0]);
    }

    public static int GetNowTimeString_month(){
        Date day = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        return Integer.parseInt(df.format(day).split("-")[1]);
    }

    public static int GetNowTimeString_day(){
        Date day = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        return Integer.parseInt(df.format(day).split("-")[2]);
    }

    public static String GetTimeFromString(String time){
        Long timestamp = Long.parseLong(time) * 1000;
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(new Date(timestamp));
    }

    public static boolean IsPointInPolygon(M_POINT pt, int polygonPoCount, List<M_POINT> poly){
        boolean bResult = false;

        int i,j;

        for (i = 0,j = polygonPoCount - 1;i < polygonPoCount;j = i++)
        {
            if ((((poly.get(i).y <= pt.y) && (pt.y < poly.get(j).y)) ||
                    ((poly.get(j).y <= pt.y) && (pt.y < poly.get(i).y)))
                    && (pt.x < (double)(poly.get(j).x - poly.get(i).x) * (double)(pt.y - poly.get(i).y)/(double)(poly.get(j).y - poly.get(i).y) + poly.get(i).x))
            {
                bResult = !bResult;
            }
        }
        return bResult;
    }

    public static float GetDisByTracks(List<ShipTrack> tracks){
        float dis = 0;
        for (int i = 0; i < tracks.size() - 1; i++){
            dis += GetDistBetwTwoSpherePo(new M_POINT(tracks.get(i).getLon(),tracks.get(i).getLat()),
                    new M_POINT(tracks.get(i+1).getLon(),tracks.get(i+1).getLat()));
        }
        return dis;
    }

    public static double GetDistBetwTwoSpherePo(M_POINT poStart, M_POINT poEnd){
        return Utils.GetDistBetwTwoPoint(
                poStart.x / (double)10000000,
                poStart.y / (double)10000000,
                poEnd.x / (double)10000000,
                poEnd.y / (double)10000000);
    }

    public static double GetDistBetwTwoPoint(double Lon1, double Lat1, double Lon2, double Lat2){
        double PI = 3.141592653589;
        double aLon1 = Lon1 / 180 * PI;
        double aLat1 = Lat1 / 180 * PI;
        double aLon2 = Lon2 / 180 * PI;
        double aLat2 = Lat2 / 180 * PI;

        double dlon = aLon2 - aLon1;
        double dlat = aLat2 - aLat1;

        double a = Math.pow(Math.sin(dlat / 2), 2) + Math.cos(aLat1) * Math.cos(aLat2) * Math.pow(Math.sin(dlon/2), 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a) );

        double d = 6378137 / 1852 * c;
        return d;
    }

    /*
    * "上海",  0
    * "浦东","浦东南汇",  1 2
    * "崇明","长兴海星","横沙远洋","横沙海鸿","奚家港","六滧","开港","堡镇","华渔","渔政执法",
    * "奉贤", 13
    * "宝山", 14
    * "金山", 15
    * "海洋科研", 16
    * "松江", 17
    * "嘉定", 18
    * "青浦" 19
    *  上面按顺序 0 - 19    0 上海  19 青浦
    * */
    public static List<Long> GetMangerIdByPos(int pos){
        long[] arr_mangerId;
        switch (pos) {
            case 0:
                arr_mangerId = new long[20];
                arr_mangerId[0] =  2550136832L;
                arr_mangerId[1] =  2558525440L;
                arr_mangerId[2] =  2559049728L;
                arr_mangerId[3] =  2566914048L;
                arr_mangerId[4] =  2567438336L;
                arr_mangerId[5] =  2567962624L;
                arr_mangerId[6] =  2568486912L;
                arr_mangerId[7] =  2569011200L;
                arr_mangerId[8] =  2569535488L;
                arr_mangerId[9] =  2570059776L;
                arr_mangerId[10] = 2570584064L;
                arr_mangerId[11] = 2571108352L;
                arr_mangerId[12] = 2571632640L;
                arr_mangerId[13] = 2583691264L;
                arr_mangerId[14] = 2592079872L;
                arr_mangerId[15] = 2600468480L;
                arr_mangerId[16] = 2608857088L;
                arr_mangerId[17] = 2625634304L;
                arr_mangerId[18] = 2634022912L;
                arr_mangerId[19] = 2642411520L;
                break;
            case 1:
                arr_mangerId = new long[2];
                arr_mangerId[0] = 2558525440L;
                arr_mangerId[1] = 2559049728L;
                break;
            case 2:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2559049728L;
                break;
            case 3:
                arr_mangerId = new long[10];
                arr_mangerId[0] = 2566914048L;
                arr_mangerId[1] = 2567438336L;
                arr_mangerId[2] = 2567962624L;
                arr_mangerId[3] = 2568486912L;
                arr_mangerId[4] = 2569011200L;
                arr_mangerId[5] = 2569535488L;
                arr_mangerId[6] = 2570059776L;
                arr_mangerId[7] = 2570584064L;
                arr_mangerId[8] = 2571108352L;
                arr_mangerId[9] = 2571632640L;
                break;
            case 4:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2567438336L;
                break;
            case 5:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2567962624L;
                break;
            case 6:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2568486912L;
                break;
            case 7:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2569011200L;
                break;
            case 8:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2569535488L;
                break;
            case 9:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2570059776L;
                break;
            case 10:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2570584064L;
                break;
            case 11:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2571108352L;
                break;
            case 12:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2571632640L;
                break;
            case 13:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2583691264L;
                break;
            case 14:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2592079872L;
                break;
            case 15:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2600468480L;
                break;
            case 16:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2608857088L;
                break;
            case 17:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2625634304L;
                break;
            case 18:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2634022912L;
                break;
            case 19:
                arr_mangerId = new long[1];
                arr_mangerId[0] = 2642411520L;
                break;
            default:
                arr_mangerId = new long[0];
        }
        ArrayList<Long> list = new ArrayList<Long>() ;
        for(long l : arr_mangerId){
            list.add(l);
        }
        return list;
    }

    public static String GetNowDateTimeString(){
        Date day = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        return df.format(day);
    }

    public static String convertToPinyin(String input) {
        StringBuilder pinyin = new StringBuilder();
        char[] chars = input.toCharArray();
        for (char c : chars) {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c);
            if (pinyinArray != null && pinyinArray.length > 0) {
                // 取第一个拼音作为结果，并去掉声调数字
                pinyin.append(pinyinArray[0].replaceAll("\\d", ""));
            } else {
                // 如果不是汉字，则直接追加
                pinyin.append(c);
            }
        }
        return pinyin.toString().toUpperCase().replace(" ", "");
    }

    public boolean containsChinese(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        // 使用正则表达式匹配中文字符
        return str.matches(".*[\u4E00-\u9FA5]+.*");
    }

    public static String GetLastDay(){
        LocalDate today = LocalDate.now();
        LocalDate lastday = today.minusDays(2);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return lastday.format(formatter);
    }

    public static String GetYesterDay(){
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return yesterday.format(formatter);
    }

    public static String GetTomorrow(){
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return tomorrow.format(formatter);
    }

    public static String GetStringTimeFromLong(long time){
        Date day = new Date(time*1000);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(day);
    }
}
