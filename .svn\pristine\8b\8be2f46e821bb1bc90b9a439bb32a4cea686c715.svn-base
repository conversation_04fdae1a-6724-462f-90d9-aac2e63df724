package com.bd.entity.ship;

import lombok.Data;

@Data
public class Ship_Permit {
    public String dept_app_dept;
    public String app_identify;
    public String app_job_begdate;
    public String app_job_begdate_2;
    public String app_job_begdate_3;
    public String app_job_enddate;
    public String app_job_enddate_2;
    public String app_job_enddate_3;
    public String app_job_place;
    public String app_job_place_2;
    public String app_job_place_3;
    public String app_job_place_class;
    public String app_job_place_class_2;
    public String app_job_place_class_3;
    public String app_job_remarks;
    public String app_job_remarks_2;
    public String app_job_remarks_3;
    public String app_job_type;
    public String app_job_type_2;
    public String app_job_way;
    public String app_job_way_2;
    public String app_job_way_3;
    public String ship_owner;
    public String address;
    public String app_number;
    public String reason;
    public String app_reason_first;
    public String dict_app_sendcard;
    public String app_time;
    public String dict_app_type;
    public String cabin_amount;
    public String cabin_cubage;
    public String fisher_check_code;
    public String fisher_code;
    public String fisher_register_code;
    public String fishing_permit_number;
    public String fishinggear_amount;
    public String fishinggear_amount_2;
    public String fishinggear_name;
    public String fishinggear_name_2;
    public String fishinggear_spec;
    public String fishinggear_spec_2;
    public String in_port_name;
    public String main_breed;
    public String main_breed_2;
    public String main_power;
    public String main_power_old;
    public String main_quota;
    public String main_quota_2;
    public String main_unit;
    public String main_unit_2;
    public String need_buy_ship_name;
    public String old_ship_owner;
    public String dept_operate_dept_id;
    public String dist_operate_district_id;
    public String operate_time;
    public String user_operator;
    public String org_permit_id;
    public String print_count;
    public String restrict_condition;
    public String restrict_condition_2;
    public String restrict_condition_3;
    public String ship_amount_sub;
    public String ship_call;
    public String ship_deep;
    public String dist_ship_district;
    public String ship_engine_count;
    public String ship_engine_count_sub;
    public String ship_engine_type;
    public String ship_engine_type_sub;
    public String ship_engine_type2;
    public String ship_engine_type3;
    public String ship_exam_number;
    public String ship_fact_ton;
    public String ship_finish_date;
    public String ship_len;
    public String ship_license;
    public String dict_ship_material;
    public String ship_name;
    public String ship_name_sub;
    public String ship_port;
    public String ship_power;
    public String ship_power_sub;
    public String ship_power2;
    public String ship_power3;
    public String ship_register_nation_number;
    public String ship_target_number;
    public String ship_ton;
    public String ship_width;
    public String signatory_date;
    public String signatory_dept;
    public String signatory_user;
    public String telephone;
    public String zipcode;
    public String logout_date;
    public String user_logout_user;
    public String dept_logout_dept;
    public String logout_code;
    public String logout_info;
    public String case_logout_resion;
    public String logout_holder_date;
    public String log_fishing;
    public String op_state;
    public String create_by;
    public String create_time;
    public String update_by;
    public String update_time;
    public String is_deleted;

}
