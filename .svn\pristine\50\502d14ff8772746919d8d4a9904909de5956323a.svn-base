package com.bd.entity.ship;

import lombok.Data;

@Data
public class Ship_Nation {
    public String apply_dept;
    public String host_dept;
    public String apply_cert_name;
    public String apply_cert_number;
    public String dict_ship_business_type;
    public String dict_app_type;
    public String ownership_app_number;
    public String norm_permit_number;
    public String ownership_number;
    public String register_number;
    public String nationality_cert_number;
    public String ownership_number_old;
    public String ownership_get_date;
    public String ownership_cert_period_date;
    public String register_period_date_old;
    public String registration_number_old;
    public String cert_period_date_old;
    public String owner_name;
    public String owner_name_en;
    public String owner_share;
    public String owner_tel;
    public String owner_addr;
    public String owner_addr_en;
    public String owner_no;
    public String ship_manager;
    public String ship_manager_en;
    public String ship_manager_no;
    public String ship_manager_addr;
    public String ship_manager_addr_en;
    public String ship_manager_tel;
    public String ship_name;
    public String ship_name_en;
    public String ship_port;
    public String ship_port_en;
    public String dist_ship_district;
    public String ship_no;
    public String ship_call;
    public String dict_ship_type;
    public String job_way;
    public String job_way_en;
    public String dict_job_way_fuzhu;
    public String breed_number;
    public String ship_yard;
    public String ship_yard_addr;
    public String dict_ship_material;
    public String ship_build_comp_date;
    public String ship_length;
    public String ship_width;
    public String ship_deep;
    public String ship_tot_ton;
    public String ship_net_ton;
    public String ship_tot_power;
    public String ship_engine_amount;
    public String ship_engine1;
    public String ship_power1;
    public String ship_engine2;
    public String ship_power2;
    public String ship_engine3;
    public String ship_power3;
    public String sub_ship_amount;
    public String sub_ship_tot_power;
    public String vessel_cert_number;
    public String vessel_cert_period_date;
    public String other_owners;
    public String log_ownership;
    public String op_state;
    public String create_by;
    public String create_time;
    public String update_by;
    public String update_time;
    public String is_deleted;

}
