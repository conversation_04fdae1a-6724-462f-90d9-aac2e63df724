﻿/*
*文件:SdkReturnInfo.js,功能：此文件的方法为海图操作的时候，YimaEnc SDK返回来的信息，
*比如绘制标注返回坐标、测距返回距离、鼠标选中船舶返回船舶信息等
*/

//鼠标移动时，选中对象的信息（手机版本这个方法不会被调用）
function ReturnSelectObjByMouseMove(objInfo) {               //没有响应
    ReturnSelectObjByMouseMoveTest(objInfo);
}

//鼠标左击时查询到的对象信息
function ReturnSelectObjByMouseLeftDown(objInfo) {           //没有响应
    ReturnSelectObjByMouseLeftDownTest(objInfo);
}

function ReturnCurPlayTrackTimeInfo(time, isShow) {           //没有响应
    ReturnCurPlayTrackTimeInfoTest(time, isShow);
}


//鼠标右键事件
//scrnPo:鼠标在海图上的位置
function ReturnOnMouseRightDown(scrnPo) {                     //产生响应
    ReturnOnMouseRightDownTest(scrnPo);
}

//鼠标左键事件
//scrnPo:鼠标在海图上的位置
function ReturnOnMouseLeftDown(scrnPo) {                      //产生响应
    ReturnOnMouseLeftDownTest(scrnPo);
    
}

//鼠标松开事件
//scrnPo:鼠标在海图上的位置
function ReturnOnMouseUp(scrnPo) {                            //产生响应
    ReturnOnMouseUpTest(scrnPo);
}


//动态绘制物标时，选中点之后返回的坐标
function ReturnDrawDynamicObjNewInfo(objDynamicInfo,curGeoPoInfo) {        //没有响应
    ReturnDrawDynamicObjNewInfoTest(objDynamicInfo,curGeoPoInfo);
}

//返回测距时的距离：鼠标移动激发该方法
//CurDis：当前段距离（km)
//allMeasureDist：累加距离（km）
//CurDegrees：当前方位（度）
function ReturnCurMeasureDist(CurDis, allMeasureDist, CurDegrees) {            //没有响应
    GetCurMeasureDist(CurDis, allMeasureDist, CurDegrees);
}

//测距时候，鼠标点击激发该方法
//curGeoPo:鼠标当前点击的经纬度坐标，格式{1210000000,310000000}
//curDis:当前点击点与上一个点的距离（km)
//allMeasureDist:累加的距离（km）
//CurDegrees:当前点与上一个点的角度（度）
function ReturnCurMeasurePoInfoByMouseDown(curGeoPo, curDis, allMeasureDist, CurDegrees) {        //产生响应
    ReturnCurMeasurePoInfoByMouseDownTest(curGeoPo, curDis, allMeasureDist, CurDegrees);
}

//拽动海图时候激发
function ReturnDragMap() {           //产生响应
    ReturnDragMapTest();
}

//缩放之后激发
function ReturnZoomMapForPc() {      //产生响应
    ReturnZoomMapForPcTest();
}

//鼠标移动事件
function ReturnOnMouseMove() {         //空函数
    ReturnOnMouseMoveTest();
}

//测面积时候激发
//areaSize:当前测量面积(平方米)
function ReturnCurMeasureAreaSize(areaSize) {        //产生响应
    ReturnCurMeasureAreaSizeTest(areaSize);
}

//移动端单手势拖动触发该事件
function ReturnTouchmoveByDrag() {
    ReturnTouchmoveByDragTest();
}

//海图视图窗口或者比例级别改变时候触发，返回海图视图的经纬度范围大小信息
function ReturnMapViewAfterDragOrZoom(mapInfo) {              //产生响应
    ReturnMapViewAfterDragOrZoomTest(mapInfo);
}

// 编辑物标
function ReturnEditObjByMouseRightDown(objInfo) {
    ReturnEditObjByMouseRightDownCallBack(objInfo);
}

function ReturnCancelObjByMouseLeftDown(isCheck) {
    ReturnCancelObjByMouseLeftDownCallBack(isCheck);
}