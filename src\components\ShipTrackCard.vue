<template>
    <div class="ship-track-card">
        <div class="card-header">
            <h3>轨迹回放</h3>
            <button class="close-btn" @click="$emit('close')">×</button>
        </div>
        <div class="card-body">
            <!-- 开始时间行 -->
            <div class="date-row">
                <span class="date-label">开始</span>
                <input type="datetime-local" class="date-input" v-model="startTimeValue">
            </div>

            <!-- 结束时间行 -->
            <div class="date-row">
                <span class="date-label">结束</span>
                <input type="datetime-local" class="date-input" v-model="endTimeValue">
            </div>

            <!-- 近几时行 -->
            <div class="time-row">
                <span class="time-label">近几时</span>
                <select class="time-select" v-model="recentHours">
                    <option value="1">近1小时</option>
                    <option value="3">近3小时</option>
                    <option value="6">近6小时</option>
                    <option value="12">近12小时</option>
                    <option value="24">近24小时</option>
                </select>
            </div>

            <!-- 时间滑动条 -->
            <div class="slider-container">
                <input type="range" min="0" :max="trackData.length - 1" v-model="currentTrackIndex" class="time-slider"
                    @input="onSliderChange">
            </div>

            <!-- 轨迹信息和选项 -->
            <div class="track-info">
                <div class="track-options">
                    <label class="checkbox-container">
                        <input type="checkbox" v-model="showBDTrack" class="track-checkbox">
                        <span class="checkbox-custom"></span>
                        <span class="checkbox-label">回放北斗轨迹</span>
                    </label>
                    <label class="checkbox-container">
                        <input type="checkbox" v-model="showAISTrack" class="track-checkbox">
                        <span class="checkbox-custom"></span>
                        <span class="checkbox-label">回放AIS轨迹</span>
                    </label>
                </div>
            </div>

            <!-- 控制按钮 -->
            <div class="action-buttons">
                <button class="action-btn" @click="fetchTrackData">查询</button>
                <button class="action-btn" @click="PlayShipHistory('restart')">重放</button>
                <button class="action-btn" @click="PlayShipHistory('end')">结束</button>
                <!-- <button class="action-btn" @click="exportShipHistoryInfo('渔船轨迹回放表')">轨迹导出</button> -->
            </div>
        </div>
    </div>
</template>

<script>
import global from './Global.vue';
// import exportExcel from '../../static/js/exportExcel.js'
export default {
    name: 'ShipTrackCard',
    props: {
        bOutSide: {
            type: Number,
            required: true
        }
    },
    data() {
        const now = new Date();
        const useTime = new Date(now);
        useTime.setHours(now.getHours() - 1); // 默认减去1小时

        return {
            shipInfo: [],
            selectShipId: '',
            startTime: useTime,
            endTime: now,
            recentHours: "1", // 默认值为1小时
            currentTrackIndex: 0,
            playSpeed: 1,
            showBDTrack: true,
            showAISTrack: true,
            trackData: [
                {
                    "id": 0,
                    "lon": 1223281340,
                    "lat": 312303684,
                    "head": 6,
                    "speed": 7,
                    "loadTime": "2025-07-14 09:10:29",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223282490,
                    "lat": 312324561,
                    "head": 6,
                    "speed": 7,
                    "loadTime": "2025-07-14 09:13:29",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223280563,
                    "lat": 312345468,
                    "head": 353,
                    "speed": 7,
                    "loadTime": "2025-07-14 09:14:11",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223282406,
                    "lat": 312375787,
                    "head": 357,
                    "speed": 7,
                    "loadTime": "2025-07-14 09:14:58",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223286558,
                    "lat": 312406736,
                    "head": 0,
                    "speed": 7,
                    "loadTime": "2025-07-14 09:16:13",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223288255,
                    "lat": 312433853,
                    "head": 359,
                    "speed": 7,
                    "loadTime": "2025-07-14 09:16:52",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223288668,
                    "lat": 312457529,
                    "head": 4,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:17:46",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223288153,
                    "lat": 312481867,
                    "head": 4,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:18:41",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223289749,
                    "lat": 312506901,
                    "head": 8,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:19:47",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223285721,
                    "lat": 312533160,
                    "head": 353,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:20:52",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223283901,
                    "lat": 312570197,
                    "head": 4,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:23:27",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223279964,
                    "lat": 312596187,
                    "head": 0,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:24:01",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223274635,
                    "lat": 312618421,
                    "head": 349,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:24:37",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223269331,
                    "lat": 312645235,
                    "head": 355,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:25:40",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223266618,
                    "lat": 312669800,
                    "head": 356,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:26:42",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223260759,
                    "lat": 312697853,
                    "head": 344,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:27:55",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223259359,
                    "lat": 312723692,
                    "head": 0,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:29:00",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223253905,
                    "lat": 312749077,
                    "head": 345,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:30:05",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223242387,
                    "lat": 312775462,
                    "head": 335,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:31:20",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223233957,
                    "lat": 312800379,
                    "head": 335,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:33:49",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223227190,
                    "lat": 312829801,
                    "head": 335,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:34:18",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223214192,
                    "lat": 312860433,
                    "head": 338,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:35:05",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223204490,
                    "lat": 312885658,
                    "head": 339,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:36:03",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223191695,
                    "lat": 312907219,
                    "head": 333,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:37:05",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223176381,
                    "lat": 312929654,
                    "head": 331,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:38:07",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223157495,
                    "lat": 312952613,
                    "head": 323,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:39:13",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223137519,
                    "lat": 312976075,
                    "head": 322,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:40:18",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223117983,
                    "lat": 312995641,
                    "head": 322,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:41:23",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223090478,
                    "lat": 313022647,
                    "head": 316,
                    "speed": 8,
                    "loadTime": "2025-07-14 09:43:24",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223067781,
                    "lat": 313044106,
                    "head": 311,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:43:49",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223046461,
                    "lat": 313063138,
                    "head": 312,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:44:50",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223028270,
                    "lat": 313083982,
                    "head": 326,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:45:51",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1223004337,
                    "lat": 313102034,
                    "head": 309,
                    "speed": 3,
                    "loadTime": "2025-07-14 09:47:07",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222977021,
                    "lat": 313125739,
                    "head": 315,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:48:11",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222954931,
                    "lat": 313142791,
                    "head": 313,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:49:16",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222932688,
                    "lat": 313162856,
                    "head": 317,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:50:21",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222909932,
                    "lat": 313183741,
                    "head": 313,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:52:22",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222886920,
                    "lat": 313207991,
                    "head": 325,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:52:59",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222860550,
                    "lat": 313224963,
                    "head": 306,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:53:45",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222835990,
                    "lat": 313245049,
                    "head": 313,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:54:47",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222813578,
                    "lat": 313263382,
                    "head": 315,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:55:46",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222787508,
                    "lat": 313280943,
                    "head": 306,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:56:54",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222760946,
                    "lat": 313293569,
                    "head": 298,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:57:53",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222743578,
                    "lat": 313316998,
                    "head": 329,
                    "speed": 9,
                    "loadTime": "2025-07-14 09:58:55",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222727961,
                    "lat": 313344062,
                    "head": 332,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:00:00",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222707866,
                    "lat": 313374227,
                    "head": 326,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:02:12",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222688452,
                    "lat": 313404265,
                    "head": 329,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:02:55",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222672035,
                    "lat": 313428900,
                    "head": 328,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:03:47",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222655276,
                    "lat": 313454485,
                    "head": 330,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:04:49",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222641551,
                    "lat": 313480392,
                    "head": 337,
                    "speed": 11,
                    "loadTime": "2025-07-14 10:05:54",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222623709,
                    "lat": 313502776,
                    "head": 325,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:06:53",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222606521,
                    "lat": 313525894,
                    "head": 327,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:07:54",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222587944,
                    "lat": 313551663,
                    "head": 328,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:09:02",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222565789,
                    "lat": 313573988,
                    "head": 317,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:10:07",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222543649,
                    "lat": 313602657,
                    "head": 331,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:12:16",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222526497,
                    "lat": 313629233,
                    "head": 331,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:13:09",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222508381,
                    "lat": 313657631,
                    "head": 327,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:13:49",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222491529,
                    "lat": 313681854,
                    "head": 327,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:14:42",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222475688,
                    "lat": 313703960,
                    "head": 323,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:15:44",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222458563,
                    "lat": 313729615,
                    "head": 323,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:16:46",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222447130,
                    "lat": 313758031,
                    "head": 323,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:17:48",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222429608,
                    "lat": 313783287,
                    "head": 337,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:18:49",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222414812,
                    "lat": 313811289,
                    "head": 336,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:19:53",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222400119,
                    "lat": 313838547,
                    "head": 324,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:20:56",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222381048,
                    "lat": 313862031,
                    "head": 326,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:22:37",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222361332,
                    "lat": 313890308,
                    "head": 328,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:23:04",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222345392,
                    "lat": 313917259,
                    "head": 334,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:24:02",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222331994,
                    "lat": 313943077,
                    "head": 336,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:25:04",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222312898,
                    "lat": 313972147,
                    "head": 336,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:26:12",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222294041,
                    "lat": 314000594,
                    "head": 334,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:27:17",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222279604,
                    "lat": 314028166,
                    "head": 334,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:28:20",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222267451,
                    "lat": 314057328,
                    "head": 333,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:29:20",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222253566,
                    "lat": 314089576,
                    "head": 333,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:30:29",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222246441,
                    "lat": 314125330,
                    "head": 333,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:32:28",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222255972,
                    "lat": 314135686,
                    "head": 355,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:33:31",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222255972,
                    "lat": 314135686,
                    "head": 355,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:34:33",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222234096,
                    "lat": 314244006,
                    "head": 10,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:35:40",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222236347,
                    "lat": 314273667,
                    "head": 10,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:36:41",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222235640,
                    "lat": 314303012,
                    "head": 10,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:37:43",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222234599,
                    "lat": 314341384,
                    "head": 18,
                    "speed": 14,
                    "loadTime": "2025-07-14 10:38:51",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222228366,
                    "lat": 314372649,
                    "head": 343,
                    "speed": 12,
                    "loadTime": "2025-07-14 10:39:52",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222230825,
                    "lat": 314402373,
                    "head": 354,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:40:57",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222229691,
                    "lat": 314437302,
                    "head": 8,
                    "speed": 13,
                    "loadTime": "2025-07-14 10:42:33",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222229628,
                    "lat": 314467147,
                    "head": 4,
                    "speed": 11,
                    "loadTime": "2025-07-14 10:43:06",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222225013,
                    "lat": 314501247,
                    "head": 348,
                    "speed": 12,
                    "loadTime": "2025-07-14 10:44:18",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222227678,
                    "lat": 314515018,
                    "head": 2,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:45:19",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222220033,
                    "lat": 314581350,
                    "head": 4,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:47:18",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222220984,
                    "lat": 314614684,
                    "head": 4,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:48:23",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222222186,
                    "lat": 314642495,
                    "head": 4,
                    "speed": 10,
                    "loadTime": "2025-07-14 10:49:24",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222222573,
                    "lat": 314672812,
                    "head": 10,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:52:33",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                },
                {
                    "id": 0,
                    "lon": 1222222573,
                    "lat": 314672812,
                    "head": 10,
                    "speed": 9,
                    "loadTime": "2025-07-14 10:52:53",
                    "shipName": null,
                    "length": 0,
                    "width": 0,
                    "bdid": null
                }
            ],
        }
    },
    computed: {
        startTimeValue: {
            get() {
                const d = this.startTime;
                const year = d.getFullYear();
                const month = (d.getMonth() + 1).toString().padStart(2, '0');
                const day = d.getDate().toString().padStart(2, '0');
                const hours = d.getHours().toString().padStart(2, '0');
                const minutes = d.getMinutes().toString().padStart(2, '0');
                return `${year}-${month}-${day}T${hours}:${minutes}`;
            },
            set(value) {
                this.startTime = new Date(value);
            }
        },
        endTimeValue: {
            get() {
                const d = this.endTime;
                const year = d.getFullYear();
                const month = (d.getMonth() + 1).toString().padStart(2, '0');
                const day = d.getDate().toString().padStart(2, '0');
                const hours = d.getHours().toString().padStart(2, '0');
                const minutes = d.getMinutes().toString().padStart(2, '0');
                return `${year}-${month}-${day}T${hours}:${minutes}`;
            },
            set(value) {
                this.endTime = new Date(value);
            }
        }
    },
    mounted() {
        this.shipInfo=API_GetCurSelectShipInfo();
    },
    methods: {
        // 获取并处理船舶轨迹数据
        fetchTrackData() {
            if (!this.startTime || !this.endTime || !this.showAISTrack || !this.showBDTrack) {
                console.warn("查询参数不完整");
                return;
            }
            if (this.startTime >= this.endTime) {
                alert("开始时间必须小于结束时间。");
                return;
            }

            const params = {
                id: this.shipInfo.shipId,
                startTime: this.startTime.getTime(),
                endTime: this.endTime.getTime(),
                addAis: this.showAISTrack ? 1 : 0,
                addBd: this.showBDTrack ? 1 : 0,
                bOutSide: this.bOutSide
            };
            console.log(params);



            this.selectShipId = this.shipInfo.shipId;
            var shipPos = API_GetShipPosById(this.shipInfo.shipId);
            var shipInfo = API_GetShipInfoByPos(shipPos);
            var shipId = this.shipInfo.shipId;
            var shipMMSI = shipInfo.shipMMSI; //mmsi
            var shipName = shipInfo.shipName; //船名
            var shipLength = shipInfo.shipLength; //船长度
            var shipWidth = 30; //船宽度
            var iShipState = shipInfo.iShipState; //船舶的状态，当前演示只设置了2种(状态值0和1),见Test_AddShipStyle方法
            var bShowTrack = true; //是否显示轨迹

            global.playShipHistoryInfo = [];
            global.playShipHistoryInfo.push({ id: shipId, name: shipName, bdId: shipMMSI, historyInfo: this.trackData });
            var curShipInfo = []; //当前船舶的结构体信息
            curShipInfo.shipId = shipId;       //船舶的id
            curShipInfo.shipMMSI = shipMMSI;     //mmsi
            curShipInfo.shipName = shipName;     //船名名称
            curShipInfo.shipWidth = shipWidth;     //宽度
            curShipInfo.shipLength = shipLength;    //长度
            curShipInfo.shipSpeed = shipInfo.shipSpeed;     //速度
            curShipInfo.iShipState = iShipState;   //状态
            curShipInfo.bShowTrack = bShowTrack;//是否显示轨迹

            var arrCurShipHistroryTracks = [];//保存轨迹点的数组
            for (var iTrackPos = 0; iTrackPos < this.trackData.length; iTrackPos++) {
                var curHistroyTrack = [];
                curHistroyTrack.trackGeoPoX = this.trackData[iTrackPos].lon;//经度，例如1210000000
                curHistroyTrack.trackGeoPoY = this.trackData[iTrackPos].lat; //纬度，例如31000000
                curHistroyTrack.trackCourse = this.trackData[iTrackPos].head; //航向，单位度(int)
                curHistroyTrack.trackSpeed = this.trackData[iTrackPos].speed; //航速
                curHistroyTrack.trackTime = this.trackData[iTrackPos].loadTime; //时间，格式例如"2015/5/31 12:1:3"
                arrCurShipHistroryTracks.push(curHistroyTrack);
            }
            API_AddOnePlayShipInfo(curShipInfo, arrCurShipHistroryTracks);
            let color = "#ff0000";
            API_SetPlayShipTrackStyleById(shipId, color, 1);
            API_StartPlayShipHistoryTrack();
            API_EndPlayHistoryTrack();
            this.currentTrackIndex = this.trackData.length - 1;
            var pos = API_GetPlayShipPosById(shipId);
            API_setHistoryTrackOverview(pos);

            // console.log('global.curHistoryShipId',global.curHistoryShipId);

            var shipPos = API_GetShipPosById(this.shipInfo.shipId);
            API_SetOneShipShowOrNotByPos(shipPos, false);
            API_ReDrawShips();

            // $.get(global.IP + "/web/GetShipHistoryTrackById", params, (response) => {
            //     // if (response && Array.isArray(response)) {
            //     //     const trackData = response;
            //     //     console.log("成功获取到原始轨迹数据:", trackData);

            //     //     if (trackData.length === 0) {
            //     //         console.log("API返回成功，但没有有效的轨迹数据。");
            //     //         return;
            //     //     }
            //     // } else {
            //     //     console.error("获取轨迹数据失败: 响应格式不正确或为空", response);
            //     // }


            // }).fail((jqXHR, textStatus, errorThrown) => {
            //     console.error("请求轨迹数据时发生网络错误:", textStatus, errorThrown);
            // });
        },

        //进度条
        onSliderChange() {
            // 1. 获取用户设置的开始时间（转换为秒级时间戳）
            const startDate = this.startTime.getTime() / 1000;

            // 2. 获取进度条当前值和最大值
            const historyTimeNumber = this.currentTrackIndex;
            const rangeMax = this.trackData.length - 1;

            // 3. 计算轨迹回放的时间点（小时转换为秒）
            const timeLength = (historyTimeNumber / rangeMax) * (parseInt(this.recentHours) * 3600);

            // 4. 设置地图API的轨迹回放时间点
            const playTime = this.secondToDate(startDate + timeLength);
            API_SetCurTrackPlayTime(playTime);
        },

        //获得秒级时间
        secondToDate(time) {
            var date = new Date(time * 1000);  // 参数需要毫秒数，所以这里将秒数乘于 1000
            let Y = date.getFullYear() + '/';
            let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '/';
            let D = date.getDate() + ' ';
            let h = date.getHours() + ':';
            let m = date.getMinutes() + ':';
            let s = date.getSeconds();

            return Y + M + D + h + m + s;
        },

        //轨迹 开始/结束/重放
        PlayShipHistory(strType) {
            if (strType == "start") {//开始
                API_SetPlayHistoryTrackTimeStep(this.playSpeed * 60); //设置播放速度（1分钟/秒）
                this.currentTrackIndex = 0;
                API_StartPlayShipHistoryTrack();
                var pos = API_GetPlayShipPosById(this.selectShipId);
                API_setHistoryTrackOverview(pos);
            }
            else if (strType == "end") {//结束
                this.currentTrackIndex = this.trackData.length - 1;
                API_EndPlayHistoryTrack();
            }
            else if (strType == "restart") {//重放
                API_SetPlayHistoryTrackTimeStep(this.playSpeed * 10); //设置播放速度（1分钟/秒）
                this.currentTrackIndex = 0;
                API_ReStartPlayHistoryTrack();
            }
        },

        //轨迹导出
        // exportShipHistoryInfo: function (fileName) {
        //     let filename = fileName;
        //     var titleList = ["渔船名","终端号","经度","纬度","航向","航速","时间"];
        //     var contentShunXu = ["name","bdId","lon","lat","head","speed","loadTime"];
        //     let arrData = global.playShipHistoryInfo;
        //     // 处理信息中的坐标信息
        //     for(var i = 0; i < arrData.length; i++) {
        //         for(var j = 0; j < arrData[i].historyInfo.length; j++){
        //             if(arrData[i].historyInfo[j].lon != undefined){
        //                 arrData[i].lon = coordinateTransformation(arrData[i].historyInfo[j].lon / 10000000);

        //             }else{
        //                 arrData[i].x = coordinateTransformation(arrData[i].historyInfo[j].x / 10000000);
        //             }

        //             if(arrData[i].historyInfo[j].lat != undefined){
        //                 arrData[i].lat = coordinateTransformation(arrData[i].historyInfo[j].lat / 10000000);

        //             }else{
        //                 arrData[i].y = coordinateTransformation(arrData[i].historyInfo[j].y / 10000000);
        //             }
        //             arrData[i].head = arrData[i].historyInfo[j].head;
        //             arrData[i].speed = arrData[i].historyInfo[j].speed;
        //             arrData[i].loadTime = arrData[i].historyInfo[j].loadTime;
        //         }
        //     }

        //     exportExcel(filename, titleList, arrData,contentShunXu);
        // },
    }
}
</script>

<style scoped>
.ship-track-card {
    position: fixed;
    bottom: 20px;
    width: 90%;
    max-width: 400px;
    left: 50%;
    transform: translateX(-50%);
    height: auto;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow: hidden;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    background-color: #2196f3;
    color: white;
    border-radius: 10px 10px 0 0;
}

.card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.card-body {
    padding: 12px 15px;
}

.date-row,
.time-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    white-space: nowrap;
}

.date-label,
.time-label {
    width: 50px;
    font-size: 14px;
    color: #333;
}

.date-input {
    width: 160px;
    padding: 0 8px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    margin-left: 5px;
    font-family: inherit;
    color: #333;
    background-color: white;
}

.date-value {
    width: 120px;
    font-size: 14px;
    margin-left: 5px;
    color: #666;
}

.calendar-icon {
    font-size: 14px;
    margin-left: 5px;
    color: #2196f3;
    opacity: 0.8;
}

.time-select {

    width: 160px;
    padding: 0 30px 0 8px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    margin-left: 5px;
    appearance: none;
    background-color: white;
    color: #333;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
}

.time-select option {
    background-color: white;
    color: #333;
}

.slider-container {
    margin: 15px 0;
}

.time-slider {
    width: 100%;
    height: 6px;
    background: #e8e8e8;
    border-radius: 4px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.time-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: white;
    border: 2px solid #2196f3;
    cursor: pointer;
    margin-top: -4px;
    /* 向上移动4px，使其垂直居中 */
}

.time-slider::-moz-range-thumb {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: white;
    border: 2px solid #2196f3;
    cursor: pointer;
}

.time-slider::-webkit-slider-runnable-track {
    background: #2196f3;
    height: 6px;
    border-radius: 4px;
}

.track-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 12px;
}


.track-options {
    display: flex;
}

.checkbox-container {
    display: flex;
    align-items: center;
    margin-left: 15px;
    cursor: pointer;
    position: relative;
}

.track-checkbox {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkbox-custom {
    position: relative;
    display: inline-block;
    width: 16px;
    height: 16px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 2px;
}

.track-checkbox:checked~.checkbox-custom {
    background-color: #4caf50;
    border-color: #4caf50;
}

.track-checkbox:checked~.checkbox-custom::after {
    content: "";
    position: absolute;
    display: block;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-label {
    font-size: 12px;
    margin-left: 5px;
    color: #333;
}

.action-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}

.action-btn {
    flex: 1;
    padding: 8px 0;
    margin: 0 5px;
    background-color: #2196f3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: normal;
    transition: background-color 0.2s;
}

.action-btn:hover {
    background-color: #0d8aee;
}

.action-btn:first-child {
    margin-left: 0;
}

.action-btn:last-child {
    margin-right: 0;
}

@media (max-width: 600px) {
    .ship-track-card {
        width: 95%;
        max-width: none;
        bottom: 10px;
    }

    .track-info {
        flex-direction: column;
        align-items: flex-start;
    }

    .track-options {
        margin-top: 5px;
        width: 100%;
        justify-content: space-between;
    }

    .checkbox-container {
        margin-left: 0;
    }

    .action-buttons {
        flex-wrap: wrap;
    }

    .action-btn {
        margin-bottom: 5px;
    }
}
</style>
