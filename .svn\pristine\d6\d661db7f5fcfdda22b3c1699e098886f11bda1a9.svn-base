package com.bd.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;

import java.util.*;

public class ExcelUtils {

    /**
     * Reads an Excel file without a Java model and uses the first row as column headers.
     *
     * @param filePath the path of the Excel file to read
     * @return a list of maps, where each map represents a row of data and uses column headers as keys
     */
    public static List<Map<String, Object>> readExcel(String filePath) {
        List<Map<String, Object>> dataList = new ArrayList<>();
        // create excel reader
        EasyExcel.read(filePath, new NoModelDataListener(dataList)).sheet().doRead();

        return dataList;
    }

    /**
     * An implementation of AnalysisEventListener that reads data rows into maps without using a Java model.
     */
    private static class NoModelDataListener extends AnalysisEventListener<Map<String, String>> {
        // store header row
        private Map<Integer, String> head = new HashMap<>();
        // store data rows as maps with column headers as keys
        private List<Map<String, Object>> dataList;

        public NoModelDataListener(List<Map<String, Object>> dataList) {
            this.dataList = dataList;
        }

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            // get header row
            head = headMap;
        }

        @Override
        public void invoke(Map<String, String> rowData, AnalysisContext context) {
            // create data map and add to list
            Map<String, Object> dataMap = new LinkedHashMap<>();
            for (Map.Entry<Integer, String> entry : head.entrySet()) {
                // get key (column header) and value (cell value)
                String key = entry.getValue();
                Object value = rowData.get(entry.getKey());
                // add key-value pair to map
                dataMap.put(key, value);
            }
            // add map to list
            dataList.add(dataMap);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // do something after all data is read
        }
    }


}
