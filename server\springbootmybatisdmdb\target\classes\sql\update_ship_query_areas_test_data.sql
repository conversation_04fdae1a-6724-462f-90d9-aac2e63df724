-- 为用户添加SHIP_QUERY_AREAS测试数据的SQL脚本
-- 执行日期: 2025-07-29
-- 说明: 为现有用户设置可查询的船舶区域权限

-- 更新管理员用户，可以查询所有区域的船舶
UPDATE SHIP.USERINFO 
SET SHIP_QUERY_AREAS = '崇明,浦东,黄浦,宝山,奉贤,金山,青浦,嘉定,松江,闵行'
WHERE USERNAME = 'admin' OR LEVEL = 0;

-- 更新崇明用户，只能查询崇明区域的船舶
UPDATE SHIP.USERINFO 
SET SHIP_QUERY_AREAS = '崇明'
WHERE USER_AREA = '崇明' AND USERNAME != 'admin';

-- 更新浦东用户，只能查询浦东区域的船舶
UPDATE SHIP.USERINFO 
SET SHIP_QUERY_AREAS = '浦东'
WHERE USER_AREA = '浦东' AND USERNAME != 'admin';

-- 更新黄浦用户，只能查询黄浦区域的船舶
UPDATE SHIP.USERINFO 
SET SHIP_QUERY_AREAS = '黄浦'
WHERE USER_AREA = '黄浦' AND USERNAME != 'admin';

-- 为其他区域用户设置默认权限（根据USER_AREA设置相同的SHIP_QUERY_AREAS）
UPDATE SHIP.USERINFO 
SET SHIP_QUERY_AREAS = USER_AREA
WHERE SHIP_QUERY_AREAS IS NULL 
AND USER_AREA IS NOT NULL 
AND USER_AREA != ''
AND USERNAME != 'admin';

-- 为没有区域信息的普通用户设置空权限
UPDATE SHIP.USERINFO 
SET SHIP_QUERY_AREAS = ''
WHERE SHIP_QUERY_AREAS IS NULL 
AND (USER_AREA IS NULL OR USER_AREA = '')
AND USERNAME != 'admin';

-- 验证更新结果
SELECT 
    ID,
    USERNAME,
    NAME,
    LEVEL,
    USER_AREA,
    SHIP_QUERY_AREAS,
    CASE 
        WHEN LEVEL = 0 THEN '管理员'
        WHEN LEVEL = 1 THEN '普通用户'
        ELSE '其他'
    END AS USER_TYPE
FROM SHIP.USERINFO 
ORDER BY LEVEL, USERNAME;

-- 统计各区域权限分布
SELECT 
    SHIP_QUERY_AREAS,
    COUNT(*) AS USER_COUNT
FROM SHIP.USERINFO 
GROUP BY SHIP_QUERY_AREAS
ORDER BY USER_COUNT DESC;
