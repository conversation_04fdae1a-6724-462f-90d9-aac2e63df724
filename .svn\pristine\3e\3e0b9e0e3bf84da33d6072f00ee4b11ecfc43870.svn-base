<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bd.mapper.ShipStaticInfoMapper">

    <resultMap id="BaseResultMap" type="com.bd.entity.ShipStaticInfo">
        <result column="CM" jdbcType="VARCHAR" property="CM" />
        <result column="BDZDH" jdbcType="VARCHAR" property="BDZDH" />
        <result column="CZ" jdbcType="VARCHAR" property="CZ" />
        <result column="LXDH" jdbcType="VARCHAR" property="LXDH" />
        <result column="LXDZ" jdbcType="VARCHAR" property="LXDZ" />
        <result column="CTCL" jdbcType="VARCHAR" property="CTCL" />
        <result column="DW" jdbcType="INTEGER" property="DW" />
        <result column="CCDM" jdbcType="INTEGER" property="CCDM" />
        <result column="ZJGLQW" jdbcType="INTEGER" property="ZJGLQW" />
        <result column="SSDW" jdbcType="VARCHAR" property="SSDW" />
        <result column="CBLX" jdbcType="VARCHAR" property="CBLX" />
        <result column="ETL_TIME" jdbcType="VARCHAR" property="ETL_TIME" />
    </resultMap>

    <resultMap id="ShipSerchResultMap" type="com.bd.entity.ShipSerch">
        <result column="ID" jdbcType="INTEGER" property="staticShipId" />
        <result column="SHIPNAME" jdbcType="VARCHAR" property="shipName" />
        <result column="BDID" jdbcType="VARCHAR" property="bdId" />
    </resultMap>

    <insert id="InsertShipNameCard">
        insert into "SHIP"."V_SHIP_SHIPNAME"("USER_SEND_USER_ID", "DEPT_SEND_DEPT_ID", "DICT_SHIP_BUSINESS_TYPE",
        "SHIPNAME_APP_NUMBER", "DICT_SHIP_SOURCE", "DICT_SHIP_TYPE", "NORM_PERMIT_NUMBER", "PERMIT_NUMBER",
        "OWNER_NAME", "OWNER_NO", "OWNER_ADDR", "OWNER_TEL", "DIST_SHIP_DISTRICT", "SHIP_PORT", "SHIP_PORT_EN",
        "STATE_OWNED_ENTER_ABB", "APP_SHIP_NAME", "APP_SHIP_NAME_EN", "SHIP_LENGTH", "DICT_SHIP_MATERIAL",
        "SHIP_TOT_POWER", "SHIP_TOT_TON", "DICT_JOB_TYPE", "JOB_PLACE", "SHIP_YARD", "SHIP_YARD_TEL", "SHIP_YARD_ADDR",
        "SHIP_NAME_OLD", "SHIP_NO", "LOGOUT_SHIPREGISTER_NUMBER", "FISHING_PERMIT_NUMBER", "SHIP_REGISTER_NUMBER",
        "SHIP_CREATE_NUMBER", "SHIP_NATIONALITY", "APP_REASON_OTHER", "SHIPNAME_NUMBER", "PRINT_NUM",
        "DEPT_ACCEPT_DEPT_ID", "ACCEPT_MESSAGE", "USER_ACCEPT_USER_ID", "ACCEPT_TIME", "DEPT_ISSUE_DEPT_ID",
        "ISSUE_MESSAGE", "ISSUE_USER_ID", "ISSUE_DATE", "DICT_APP_REASON", "NOTE", "OCEAN_PERMIT_NUMBER", "JOB_WAY",
        "BREED_NUMBER", "JOB_WAY2", "DICT_JOB_TYPE2", "DICT_JOB_WAY_FUZHU", "LOG_SHIPNAME", "OP_STATE", "CREATE_BY",
        "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "IS_DELETED")
        VALUES
        <foreach collection="shipList" item="it" separator=",">
            (
            #{it.user_send_user_id},
            #{it.dept_send_dept_id},
            #{it.dict_ship_business_type},
            #{it.shipname_app_number},
            #{it.dict_ship_source},
            #{it.dict_ship_type},
            #{it.norm_permit_number},
            #{it.permit_number},
            #{it.owner_name},
            #{it.owner_no},
            #{it.owner_addr},
            #{it.owner_tel},
            #{it.dist_ship_district},
            #{it.ship_port},
            #{it.ship_port_en},
            #{it.state_owned_enter_abb},
            #{it.app_ship_name},
            #{it.app_ship_name_en},
            #{it.ship_length},
            #{it.dict_ship_material},
            #{it.ship_tot_power},
            #{it.ship_tot_ton},
            #{it.dict_job_type},
            #{it.job_place},
            #{it.ship_yard},
            #{it.ship_yard_tel},
            #{it.ship_yard_addr},
            #{it.ship_name_old},
            #{it.ship_no},
            #{it.logout_shipregister_number},
            #{it.fishing_permit_number},
            #{it.ship_register_number},
            #{it.ship_create_number},
            #{it.ship_nationality},
            #{it.app_reason_other},
            #{it.shipname_number},
            #{it.print_num},
            #{it.dept_accept_dept_id},
            #{it.accept_message},
            #{it.user_accept_user_id},
            #{it.accept_time},
            #{it.dept_issue_dept_id},
            #{it.issue_message},
            #{it.issue_user_id},
            #{it.issue_date},
            #{it.dict_app_reason},
            #{it.note},
            #{it.ocean_permit_number},
            #{it.job_way},
            #{it.breed_number},
            #{it.job_way2},
            #{it.dict_job_type2},
            #{it.dict_job_way_fuzhu},
            #{it.log_shipname},
            #{it.op_state},
            #{it.create_by},
            #{it.create_time},
            #{it.update_by},
            #{it.update_time},
            #{it.is_deleted})
        </foreach>
    </insert>
    <insert id="InsertShipNation">
        insert into "SHIP"."V_SHIP_OWNER"(
        "APPLY_DEPT", "HOST_DEPT", "APPLY_CERT_NAME", "APPLY_CERT_NUMBER", "DICT_SHIP_BUSINESS_TYPE", "DICT_APP_TYPE",
        "OWNERSHIP_APP_NUMBER", "NORM_PERMIT_NUMBER", "OWNERSHIP_NUMBER", "REGISTER_NUMBER", "NATIONALITY_CERT_NUMBER",
        "OWNERSHIP_NUMBER_OLD", "OWNERSHIP_GET_DATE", "OWNERSHIP_CERT_PERIOD_DATE", "REGISTER_PERIOD_DATE_OLD",
        "REGISTRATION_NUMBER_OLD", "CERT_PERIOD_DATE_OLD", "OWNER_NAME", "OWNER_NAME_EN", "OWNER_SHARE", "OWNER_TEL",
        "OWNER_ADDR", "OWNER_ADDR_EN", "OWNER_NO", "SHIP_MANAGER", "SHIP_MANAGER_EN", "SHIP_MANAGER_NO",
        "SHIP_MANAGER_ADDR", "SHIP_MANAGER_ADDR_EN", "SHIP_MANAGER_TEL", "SHIP_NAME", "SHIP_NAME_EN", "SHIP_PORT",
        "SHIP_PORT_EN", "DIST_SHIP_DISTRICT", "SHIP_NO", "SHIP_CALL", "DICT_SHIP_TYPE", "JOB_WAY", "JOB_WAY_EN",
        "DICT_JOB_WAY_FUZHU", "BREED_NUMBER", "SHIP_YARD", "SHIP_YARD_ADDR", "DICT_SHIP_MATERIAL",
        "SHIP_BUILD_COMP_DATE", "SHIP_LENGTH", "SHIP_WIDTH", "SHIP_DEEP", "SHIP_TOT_TON", "SHIP_NET_TON",
        "SHIP_TOT_POWER", "SHIP_ENGINE_AMOUNT", "SHIP_ENGINE1", "SHIP_POWER1", "SHIP_ENGINE2", "SHIP_POWER2",
        "SHIP_ENGINE3", "SHIP_POWER3", "SUB_SHIP_AMOUNT", "SUB_SHIP_TOT_POWER", "VESSEL_CERT_NUMBER",
        "VESSEL_CERT_PERIOD_DATE", "OTHER_OWNERS", "LOG_OWNERSHIP", "OP_STATE", "CREATE_BY", "CREATE_TIME", "UPDATE_BY",
        "UPDATE_TIME", "IS_DELETED")
        VALUES
        <foreach collection="shipList" item="it" separator=",">
            (
            #{it.apply_dept},
            #{it.host_dept},
            #{it.apply_cert_name},
            #{it.apply_cert_number},
            #{it.dict_ship_business_type},
            #{it.dict_app_type},
            #{it.ownership_app_number},
            #{it.norm_permit_number},
            #{it.ownership_number},
            #{it.register_number},
            #{it.nationality_cert_number},
            #{it.ownership_number_old},
            #{it.ownership_get_date},
            #{it.ownership_cert_period_date},
            #{it.register_period_date_old},
            #{it.registration_number_old},
            #{it.cert_period_date_old},
            #{it.owner_name},
            #{it.owner_name_en},
            #{it.owner_share},
            #{it.owner_tel},
            #{it.owner_addr},
            #{it.owner_addr_en},
            #{it.owner_no},
            #{it.ship_manager},
            #{it.ship_manager_en},
            #{it.ship_manager_no},
            #{it.ship_manager_addr},
            #{it.ship_manager_addr_en},
            #{it.ship_manager_tel},
            #{it.ship_name},
            #{it.ship_name_en},
            #{it.ship_port},
            #{it.ship_port_en},
            #{it.dist_ship_district},
            #{it.ship_no},
            #{it.ship_call},
            #{it.dict_ship_type},
            #{it.job_way},
            #{it.job_way_en},
            #{it.dict_job_way_fuzhu},
            #{it.breed_number},
            #{it.ship_yard},
            #{it.ship_yard_addr},
            #{it.dict_ship_material},
            #{it.ship_build_comp_date},
            #{it.ship_length},
            #{it.ship_width},
            #{it.ship_deep},
            #{it.ship_tot_ton},
            #{it.ship_net_ton},
            #{it.ship_tot_power},
            #{it.ship_engine_amount},
            #{it.ship_engine1},
            #{it.ship_power1},
            #{it.ship_engine2},
            #{it.ship_power2},
            #{it.ship_engine3},
            #{it.ship_power3},
            #{it.sub_ship_amount},
            #{it.sub_ship_tot_power},
            #{it.vessel_cert_number},
            #{it.vessel_cert_period_date},
            #{it.other_owners},
            #{it.log_ownership},
            #{it.op_state},
            #{it.create_by},
            #{it.create_time},
            #{it.update_by},
            #{it.update_time},
            #{it.is_deleted}
            )
        </foreach>
    </insert>
    <insert id="InsertShipNet">
        insert into "SHIP"."V_SHIP_NET"("APP_TIME", "COMPANY_NAME", "COMPANY_ADDRESS", "COMPANY_NATURE",
        "COMPANY_POSTCODE", "COMPANY_PHONE", "COMPANY_LAWMAN", "COMPANY_SHIP_NUM",
        "COMPANY_ALL_POWER", "COMPANY_APPLY_REASON", "PERSON_NAME", "PERSON_IDENTIFY",
        "PERSON_ADDRESS", "PERSON_POSTCODE", "PERSON_PHONE", "PERSON_JOB",
        "PERSON_SHIP_NUM", "PERSON_ALL_POWER", "PERSON_APPLY_REASON",
        "DICT_APP_PRO_TYPE", "DICT_APP_SHIP_TYPE", "DICT_APP_JOB_TYPE1",
        "DICT_APP_JOB_TYPE2", "APP_JOB_PLACE", "APP_MAIN_POWER", "APP_SUBSHIP_POWER",
        "APP_SHIP_LENGTH", "APP_ALL_TON", "DICT_APP_SHIP_MATERIAL", "APP_POWER_NORM",
        "OLD_POWER_NORM", "NORM_APPLY_NUMBER", "NORM_APPLY_DISTRICT_ID",
        "NORM_PERMIT_NUMBER_OLD", "NORM_APPLY_REISSUE_COUNT", "USER_OPERATOR",
        "OPERATE_TIME", "NORM_APPLY_PRINT_COUNT", "NORM_APPLY_REL_NUMBER",
        "APP_SUBSHIP_NUM", "COMPANY_REG_NO", "DIST_NORM_DISTRICT_ID", "REMARK",
        "DIST_SHIP_DISTRICT_ID", "APP_JOB_WAY_FUZHU", "SIGNATE_PERSON", "SIGNATE_TIME",
        "NORM_PERMIT_NUMBER", "NORM_PERMIT_PRINT_COUNT", "NORM_REL_PERMIT_NUMBER",
        "DEPT_SIGNATE_DEPT", "LOG_NORM", "OP_STATE", "CREATE_BY", "CREATE_TIME",
        "UPDATE_BY", "UPDATE_TIME", "IS_DELETED")
        VALUES
        <foreach collection="shipList" item="it" separator=",">
            (
            #{it.app_time},
            #{it.company_name},
            #{it.company_address},
            #{it.company_nature},
            #{it.company_postcode},
            #{it.company_phone},
            #{it.company_lawman},
            #{it.company_ship_num},
            #{it.company_all_power},
            #{it.company_apply_reason},
            #{it.person_name},
            #{it.person_identify},
            #{it.person_address},
            #{it.person_postcode},
            #{it.person_phone},
            #{it.person_job},
            #{it.person_ship_num},
            #{it.person_all_power},
            #{it.person_apply_reason},
            #{it.dict_app_pro_type},
            #{it.dict_app_ship_type},
            #{it.dict_app_job_type1},
            #{it.dict_app_job_type2},
            #{it.app_job_place},
            #{it.app_main_power},
            #{it.app_subship_power},
            #{it.app_ship_length},
            #{it.app_all_ton},
            #{it.dict_app_ship_material},
            #{it.app_power_norm},
            #{it.old_power_norm},
            #{it.norm_apply_number},
            #{it.norm_apply_district_id},
            #{it.norm_permit_number_old},
            #{it.norm_apply_reissue_count},
            #{it.user_operator},
            #{it.operate_time},
            #{it.norm_apply_print_count},
            #{it.norm_apply_rel_number},
            #{it.app_subship_num},
            #{it.company_reg_no},
            #{it.dist_norm_district_id},
            #{it.remark},
            #{it.dist_ship_district_id},
            #{it.app_job_way_fuzhu},
            #{it.signate_person},
            #{it.signate_time},
            #{it.norm_permit_number},
            #{it.norm_permit_print_count},
            #{it.norm_rel_permit_number},
            #{it.dept_signate_dept},
            #{it.log_norm},
            #{it.op_state},
            #{it.create_by},
            #{it.create_time},
            #{it.update_by},
            #{it.update_time},
            #{it.is_deleted}
            )
        </foreach>
    </insert>
    <insert id="InsertShipPermit">
        insert into "SHIP"."V_SHIP_PERMIT"(
        "DEPT_APP_DEPT", "APP_IDENTIFY", "APP_JOB_BEGDATE", "APP_JOB_BEGDATE_2", "APP_JOB_BEGDATE_3", "APP_JOB_ENDDATE",
        "APP_JOB_ENDDATE_2", "APP_JOB_ENDDATE_3", "APP_JOB_PLACE", "APP_JOB_PLACE_2", "APP_JOB_PLACE_3",
        "APP_JOB_PLACE_CLASS", "APP_JOB_PLACE_CLASS_2", "APP_JOB_PLACE_CLASS_3", "APP_JOB_REMARKS", "APP_JOB_REMARKS_2",
        "APP_JOB_REMARKS_3", "APP_JOB_TYPE", "APP_JOB_TYPE_2", "APP_JOB_WAY", "APP_JOB_WAY_2", "APP_JOB_WAY_3",
        "SHIP_OWNER", "ADDRESS", "APP_NUMBER", "REASON", "APP_REASON_FIRST", "DICT_APP_SENDCARD", "APP_TIME",
        "DICT_APP_TYPE", "CABIN_AMOUNT", "CABIN_CUBAGE", "FISHER_CHECK_CODE", "FISHER_CODE", "FISHER_REGISTER_CODE",
        "FISHING_PERMIT_NUMBER", "FISHINGGEAR_AMOUNT", "FISHINGGEAR_AMOUNT_2", "FISHINGGEAR_NAME", "FISHINGGEAR_NAME_2",
        "FISHINGGEAR_SPEC", "FISHINGGEAR_SPEC_2", "IN_PORT_NAME", "MAIN_BREED", "MAIN_BREED_2", "MAIN_POWER",
        "MAIN_POWER_OLD", "MAIN_QUOTA", "MAIN_QUOTA_2", "MAIN_UNIT", "MAIN_UNIT_2", "NEED_BUY_SHIP_NAME",
        "OLD_SHIP_OWNER", "DEPT_OPERATE_DEPT_ID", "DIST_OPERATE_DISTRICT_ID", "OPERATE_TIME", "USER_OPERATOR",
        "ORG_PERMIT_ID", "PRINT_COUNT", "RESTRICT_CONDITION", "RESTRICT_CONDITION_2", "RESTRICT_CONDITION_3",
        "SHIP_AMOUNT_SUB", "SHIP_CALL", "SHIP_DEEP", "DIST_SHIP_DISTRICT", "SHIP_ENGINE_COUNT", "SHIP_ENGINE_COUNT_SUB",
        "SHIP_ENGINE_TYPE", "SHIP_ENGINE_TYPE_SUB", "SHIP_ENGINE_TYPE2", "SHIP_ENGINE_TYPE3", "SHIP_EXAM_NUMBER",
        "SHIP_FACT_TON", "SHIP_FINISH_DATE", "SHIP_LEN", "SHIP_LICENSE", "DICT_SHIP_MATERIAL", "SHIP_NAME",
        "SHIP_NAME_SUB", "SHIP_PORT", "SHIP_POWER", "SHIP_POWER_SUB", "SHIP_POWER2", "SHIP_POWER3",
        "SHIP_REGISTER_NATION_NUMBER", "SHIP_TARGET_NUMBER", "SHIP_TON", "SHIP_WIDTH", "SIGNATORY_DATE",
        "SIGNATORY_DEPT", "SIGNATORY_USER", "TELEPHONE", "ZIPCODE", "LOGOUT_DATE", "USER_LOGOUT_USER",
        "DEPT_LOGOUT_DEPT", "LOGOUT_CODE", "LOGOUT_INFO", "CASE_LOGOUT_RESION", "LOGOUT_HOLDER_DATE", "LOG_FISHING",
        "OP_STATE", "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "IS_DELETED")
        VALUES
        <foreach collection="shipList" item="it" separator=",">
            (
            #{it.dept_app_dept},
            #{it.app_identify},
            #{it.app_job_begdate},
            #{it.app_job_begdate_2},
            #{it.app_job_begdate_3},
            #{it.app_job_enddate},
            #{it.app_job_enddate_2},
            #{it.app_job_enddate_3},
            #{it.app_job_place},
            #{it.app_job_place_2},
            #{it.app_job_place_3},
            #{it.app_job_place_class},
            #{it.app_job_place_class_2},
            #{it.app_job_place_class_3},
            #{it.app_job_remarks},
            #{it.app_job_remarks_2},
            #{it.app_job_remarks_3},
            #{it.app_job_type},
            #{it.app_job_type_2},
            #{it.app_job_way},
            #{it.app_job_way_2},
            #{it.app_job_way_3},
            #{it.ship_owner},
            #{it.address},
            #{it.app_number},
            #{it.reason},
            #{it.app_reason_first},
            #{it.dict_app_sendcard},
            #{it.app_time},
            #{it.dict_app_type},
            #{it.cabin_amount},
            #{it.cabin_cubage},
            #{it.fisher_check_code},
            #{it.fisher_code},
            #{it.fisher_register_code},
            #{it.fishing_permit_number},
            #{it.fishinggear_amount},
            #{it.fishinggear_amount_2},
            #{it.fishinggear_name},
            #{it.fishinggear_name_2},
            #{it.fishinggear_spec},
            #{it.fishinggear_spec_2},
            #{it.in_port_name},
            #{it.main_breed},
            #{it.main_breed_2},
            #{it.main_power},
            #{it.main_power_old},
            #{it.main_quota},
            #{it.main_quota_2},
            #{it.main_unit},
            #{it.main_unit_2},
            #{it.need_buy_ship_name},
            #{it.old_ship_owner},
            #{it.dept_operate_dept_id},
            #{it.dist_operate_district_id},
            #{it.operate_time},
            #{it.user_operator},
            #{it.org_permit_id},
            #{it.print_count},
            #{it.restrict_condition},
            #{it.restrict_condition_2},
            #{it.restrict_condition_3},
            #{it.ship_amount_sub},
            #{it.ship_call},
            #{it.ship_deep},
            #{it.dist_ship_district},
            #{it.ship_engine_count},
            #{it.ship_engine_count_sub},
            #{it.ship_engine_type},
            #{it.ship_engine_type_sub},
            #{it.ship_engine_type2},
            #{it.ship_engine_type3},
            #{it.ship_exam_number},
            #{it.ship_fact_ton},
            #{it.ship_finish_date},
            #{it.ship_len},
            #{it.ship_license},
            #{it.dict_ship_material},
            #{it.ship_name},
            #{it.ship_name_sub},
            #{it.ship_port},
            #{it.ship_power},
            #{it.ship_power_sub},
            #{it.ship_power2},
            #{it.ship_power3},
            #{it.ship_register_nation_number},
            #{it.ship_target_number},
            #{it.ship_ton},
            #{it.ship_width},
            #{it.signatory_date},
            #{it.signatory_dept},
            #{it.signatory_user},
            #{it.telephone},
            #{it.zipcode},
            #{it.logout_date},
            #{it.user_logout_user},
            #{it.dept_logout_dept},
            #{it.logout_code},
            #{it.logout_info},
            #{it.case_logout_resion},
            #{it.logout_holder_date},
            #{it.log_fishing},
            #{it.op_state},
            #{it.create_by},
            #{it.create_time},
            #{it.update_by},
            #{it.update_time},
            #{it.is_deleted}
            )
        </foreach>
    </insert>
    <insert id="InsertShipCheck">
        insert into "SHIP"."V_SHIP_CHECK"(
        "SHEET_ID1", "SHEET_ID2", "CHECK_PERSON_NAMES", "SHEET_ID", "DICT_MAIN_WORK_CATEGORY", "LINK_SHIP_NAME_CH",
        "LINK_SHIP_OWNER_CH", "LINK_SHIP_OWNER_ADDRESS", "LINK_VERIFYENR_NO", "LINK_SHIP_CODING", "LINK_BUILD_COMPDATE",
        "DICT_MAIN_VERIFYENR_TYPE", "DICT_MAIN_VERIFYENR_WARRANT", "DIST_MAIN_SHIP_DISTRICT", "MAIN_NORM_NUM_NEW",
        "DICT_MAIN_NORM_MATERIAL", "DICT_MAIN_NORM_WORK_TYPE", "MAIN_WORK_AREA", "MAIN_NORM_NUM_POWER",
        "MAIN_NORM_CAPTAIN", "MAIN_NORM_TOTAL_TONNAGE", "DICT_LINK_SHIP_TYPE_CH", "LINK_PACTSHIP_LONG",
        "LINK_TOTAL_LENGHT", "LINK_SHIP_WIDE", "LINK_SHIP_DEEP", "LINK_TOTAL_TONNAGE", "LINK_NET_TONNAGE",
        "LINK_SHIP_TYPE_CODE", "LINK_DESIGN_SPEED", "LINK_RATED_LOAD_NUM", "LINK_TOTAL_POWER", "LINK_SHIP_ID",
        "LINK_PUT_KEEL_TIME", "LINK_FISHING_FACTORY_CH", "LINK_SHIP_OPERATING_CH", "MAIN_VERIFY_START_TIME",
        "MAIN_VERIFY_END_TIME", "MAIN_NEXT_VERIFY_TIME", "MAIN_VERIFY_DEPT_ID", "MAIN_CERTIFICATE_VALIDITY",
        "MAIN_EXHIBITION_VALIDITY", "MAIN_LAST_VISA_VALIDITY_TERM", "MAIN_VERIFYENR_ADDRESS_CH", "MAIN_ADDRESS_CH",
        "DICT_MAIN_NEXT_VERIFY_TYPE", "MAIN_CERITIFICATE_DATE", "MAIN_CERTIFICATE_NUMBER", "LOG_VESSEL", "OP_STATE",
        "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "IS_DELETED")
        VALUES
        <foreach collection="shipList" item="it" separator=",">
            (
            #{it.sheet_id1},
            #{it.sheet_id2},
            #{it.check_person_names},
            #{it.sheet_id},
            #{it.dict_main_work_category},
            #{it.link_ship_name_ch},
            #{it.link_ship_owner_ch},
            #{it.link_ship_owner_address},
            #{it.link_verifyenr_no},
            #{it.link_ship_coding},
            #{it.link_build_compdate},
            #{it.dict_main_verifyenr_type},
            #{it.dict_main_verifyenr_warrant},
            #{it.dist_main_ship_district},
            #{it.main_norm_num_new},
            #{it.dict_main_norm_material},
            #{it.dict_main_norm_work_type},
            #{it.main_work_area},
            #{it.main_norm_num_power},
            #{it.main_norm_captain},
            #{it.main_norm_total_tonnage},
            #{it.dict_link_ship_type_ch},
            #{it.link_pactship_long},
            #{it.link_total_lenght},
            #{it.link_ship_wide},
            #{it.link_ship_deep},
            #{it.link_total_tonnage},
            #{it.link_net_tonnage},
            #{it.link_ship_type_code},
            #{it.link_design_speed},
            #{it.link_rated_load_num},
            #{it.link_total_power},
            #{it.link_ship_id},
            #{it.link_put_keel_time},
            #{it.link_fishing_factory_ch},
            #{it.link_ship_operating_ch},
            #{it.main_verify_start_time},
            #{it.main_verify_end_time},
            #{it.main_next_verify_time},
            #{it.main_verify_dept_id},
            #{it.main_certificate_validity},
            #{it.main_exhibition_validity},
            #{it.main_last_visa_validity_term},
            #{it.main_verifyenr_address_ch},
            #{it.main_address_ch},
            #{it.dict_main_next_verify_type},
            #{it.main_ceritificate_date},
            #{it.main_certificate_number},
            #{it.log_vessel},
            #{it.op_state},
            #{it.create_by},
            #{it.create_time},
            #{it.update_by},
            #{it.update_time},
            #{it.is_deleted}
            )
        </foreach>
    </insert>
    <insert id="InsertShipSave">
        insert into "SHIP"."V_SHIP_SAFE"(
        "SHIP_NAME", "SHIP_NO", "VERIFYENR_NO", "SEAWORTHY_TO_AREA", "LINK_RATED_LOAD_NUM", "SAFE_FISHERY_REMARK",
        "PASSENGERS_NUM", "SAVE_EQUIPMENT_CAN_USED_NUM", "SHIP_CHECK_LEFT_TYPE", "SAVE_EQT_LEFT_NUM",
        "SAVE_EQT_LEFT_ACCOMMODATED", "SHIP_CHECK_RIGHT_TYPE", "SAVE_EQT_RIGHT_NUM", "SAVE_EQT_RIGHT_ACCOMMODATED",
        "LIFE_SAVING_DATA_MODEL_CN1", "LIFE_SAVING_DATA_MAX_PERMIT1", "LIFE_SAVING_DATA_AMOUNT1",
        "LIFE_SAVING_DATA_MODEL_CN2", "LIFE_SAVING_DATA_MAX_PERMIT2", "LIFE_SAVING_DATA_AMOUNT2",
        "LIFE_SAVING_FLOAT_TYPE_CN", "LIFE_SAVING_FLOAT_CREW_QUOTA", "LIFE_SAVING_FLOAT_AMOUNT",
        "LIFE_BUOY_MODEL_NAME_CN", "LIFE_BUOY_NUM", "LIFE_JACKET_MODEL", "LIFE_JACKET_NUM", "VHF_TYPE", "VHF_POWER",
        "CGR_RMHF_TYPE", "CNGRRMFH_POWER", "CNGRRTEL_TYPE", "CNGRR_NUM", "CNGRRES_TYPE", "CNGRRES_NUM", "CNGRRAS_TYPE",
        "CNGRRAS_NUM", "CNGRRR_TYPE", "CNGRRR_NUM", "CNGRRN_TYPE", "CNGRRN_NUM", "CNGRROTHER_EQ", "OP_STATE",
        "CREATE_BY", "CREATE_TIME", "UPDATE_BY", "UPDATE_TIME", "IS_DELETED")
        VALUES
        <foreach collection="shipList" item="it" separator=",">
            (
            #{it.ship_name},
            #{it.ship_no},
            #{it.verifyenr_no},
            #{it.seaworthy_to_area},
            #{it.link_rated_load_num},
            #{it.safe_fishery_remark},
            #{it.passengers_num},
            #{it.save_equipment_can_used_num},
            #{it.ship_check_left_type},
            #{it.save_eqt_left_num},
            #{it.save_eqt_left_accommodated},
            #{it.ship_check_right_type},
            #{it.save_eqt_right_num},
            #{it.save_eqt_right_accommodated},
            #{it.life_saving_data_model_cn1},
            #{it.life_saving_data_max_permit1},
            #{it.life_saving_data_amount1},
            #{it.life_saving_data_model_cn2},
            #{it.life_saving_data_max_permit2},
            #{it.life_saving_data_amount2},
            #{it.life_saving_float_type_cn},
            #{it.life_saving_float_crew_quota},
            #{it.life_saving_float_amount},
            #{it.life_buoy_model_name_cn},
            #{it.life_buoy_num},
            #{it.life_jacket_model},
            #{it.life_jacket_num},
            #{it.vhf_type},
            #{it.vhf_power},
            #{it.cgr_rmhf_type},
            #{it.cngrrmfh_power},
            #{it.cngrrtel_type},
            #{it.cngrr_num},
            #{it.cngrres_type},
            #{it.cngrres_num},
            #{it.cngrras_type},
            #{it.cngrras_num},
            #{it.cngrrr_type},
            #{it.cngrrr_num},
            #{it.cngrrn_type},
            #{it.cngrrn_num},
            #{it.cngrrother_eq},
            #{it.op_state},
            #{it.create_by},
            #{it.create_time},
            #{it.update_by},
            #{it.update_time},
            #{it.is_deleted}
            )
        </foreach>
    </insert>
    <delete id="ClearShipInfo">
        delete from "SHIP"."V_SHIP_SHIPNAME";
        delete from "SHIP"."V_SHIP_SAFE";
        delete from "SHIP"."V_SHIP_PERMIT";
        delete from "SHIP"."V_SHIP_OWNER";
        delete from "SHIP"."V_SHIP_NET";
        delete from "SHIP"."V_SHIP_CHECK";
    </delete>
    <delete id="ClearOldInfo">
        delete from SHIP.V_SHIP_OWNER where OWNERSHIP_CERT_PERIOD_DATE &lt; sysdate;
        delete from SHIP.V_SHIP_PERMIT where APP_JOB_ENDDATE &lt; sysdate;
        delete from SHIP.V_SHIP_CHECK where MAIN_CERTIFICATE_VALIDITY &lt; sysdate;
    </delete>

    <select id="GetShipInfoByNameOrTerminalNumber" parameterType="String" resultMap="ShipSerchResultMap">
        select ID,SHIPNAME,BDID
        from SHIP.SHIP_STATICINFO
        where
        SHIPNAME like concat('%',#{name},'%')
        or
        IFNULL(bdId,'-') like concat('%',#{name},'%')
        or
        MMSI like concat('%',#{name},'%')
        or
        OWNER like concat('%',#{name},'%')
        limit 50

    </select>

    <select id="GetOneShipInfoByShipName" parameterType="String" resultMap="BaseResultMap">
        select distinct s.*,d.LASTPOSTERMNO,d.LASTPOSBDID,d.BDTIME,d.AISTIME,
                        vs.DIST_SHIP_DISTRICT as DISTRICT,
                        vo.SHIP_BUILD_COMP_DATE as overTime,
                        vo.DICT_SHIP_TYPE as dictShipType,
                        vp.APP_JOB_TYPE as dictJobType,
                        vo.SHIP_CALL as vCall,
                        vo.SHIP_LENGTH as vLength,
                        vo.SHIP_WIDTH as vWidth,
                        vp.APP_JOB_PLACE as jobPlace,
                        vp.*,
                        vc.LINK_RATED_LOAD_NUM
        from SHIP.SHIP_STATICINFO s
                 inner join SHIP.SHIP_DYNAMICTION d
                            on s.id = d.staticshipid
                 left join SHIP.V_SHIP_SHIPNAME vs
                           on s.shipName = vs.APP_SHIP_NAME
                 left join SHIP.V_SHIP_OWNER vo
                           on s.shipName = vo.SHIP_NAME
                 left join SHIP.V_SHIP_PERMIT vp
                           on s.shipName = vp.SHIP_NAME
                 left join SHIP.V_SHIP_CHECK vc
                           on s.shipName = vc.LINK_SHIP_NAME_CH
        where s.SHIPNAME like concat('%',#{name},'%')

    </select>
    <select id="GetOneShipInfoById" parameterType="int" resultType="com.bd.entity.ShipStaticInfo_all">
        select distinct s.*,d.LASTPOSTERMNO,d.LASTPOSBDID,d.BDTIME,d.AISTIME,
               FBI.*,FPI.zzylx,FPI.ZZYDYZYFS_ZYCS
        from SHIP.SHIP_STATICINFO s
        inner join SHIP.SHIP_DYNAMICTION d
        on s.id = d.staticshipid
        left join SHIP.FISHING_BOAT_INFO FBI
        on s.SHIPNAME = FBI.CM
        left join SHIP.FISHERY_PERMIT_INFO FPI
        on s.SHIPNAME = FPI.CM
        where s.id = #{id}
        order by FBI.ETL_TIME desc
        limit 1
    </select>
    <select id="GetOneOtherProvincesShipInfoById" parameterType="int" resultType="com.bd.entity.ShipStaticInfo">
        select
            SHIPNAME as CM,
            TERMNO as BDZDH,
            SHIPOWNER as CZ,
            SHIPTEL as LXDH
        from "SHIP"."BDTERM_OTHERPROVINCES" where ID = #{id};
    </select>
    <select id="GetOneOtherProvincesShipInfoByShipName" resultType="com.bd.entity.ShipStaticInfo" resultMap="BaseResultMap">

    </select>
    <select id="GetOneWSShipInfoById" resultType="com.bd.entity.ShipStaticInfo_all">
        select
            SHIPNAME,
            TERMNO as BDID,
            SHIPOWNER as OWNER,
            SHIPTEL as LXDH
        from "SHIP"."BDTERM_OTHERPROVINCES" where ID = #{id};
    </select>
    <select id="GetAllFishShip" resultType="com.bd.entity.ShipStaticInfo_all">
        select distinct s.ID,s.BDID,s.MMSI,s.SHIPNAME,s.IMO,s.CALLSIGN,s.OWNER,s.LXDH,s.LXDZ,s.CTCL,
        s.DW,s.ZJGLQW,s.SSDW,s.SHIPTYPE,s.LENGTH,s.WIDTH,s.BOUTSIDE,s.NATION,s.BFOCUS,
        s.BBLACKFUXIU,s.BWHITEFUXIU,s.ZYLX,s.BLXKZ,d.*,p.PORTNAME,f.CBSYRMC
        from SHIP.SHIP_STATICINFO s
        left join SHIP.SHIP_DYNAMICTION d on s.id = d.staticshipid
        left join SHIP.PORTINFO p on d.INPORTID = p.id
        LEFT JOIN (
            SELECT f1.*
            FROM SHIP.FISHING_BOAT_INFO f1
            JOIN (
            SELECT CM, MAX(ETL_TIME) AS MaxETL
            FROM SHIP.FISHING_BOAT_INFO
            GROUP BY CM
            ) f2 ON f1.CM = f2.CM AND f1.ETL_TIME = f2.MaxETL
        ) AS f ON s.SHIPNAME = f.CM
            LEFT JOIN (
            SELECT fp1.*
            FROM SHIP.FISHERY_PERMIT_INFO fp1
            JOIN (
            SELECT CM, MAX(ETL_DT) AS MaxETL
            FROM SHIP.FISHERY_PERMIT_INFO
            GROUP BY CM
            ) fp2 ON fp1.CM = fp2.CM AND fp1.ETL_DT = fp2.MaxETL
        ) AS fp ON s.SHIPNAME = fp.CM
        where
        (s.shipName like concat('%',#{shipName},'%') or s.owner like concat('%',#{shipName},'%'))
        and IFNULL(s.bdId,'-') like concat('%',#{bdId},'%')
        and IFNULL(s.mmsi,'-') like concat('%',#{mmsi},'%')
        and IFNULL(s.mmsi,'-') like concat('%',#{nineDigitCode},'%')
        and IFNULL(f.YCBM,'-') like concat('%',#{shipCode},'%')
        and IFNULL(f.CBSYRMC,'-') like concat('%',#{shipOwnerName},'%')
        and IFNULL(f.YCSSDQMC,'-') like concat('%',#{regionOfShip},'%')
        and IFNULL(fp.YYBLXKZBH,'-') like concat('%',#{licenseNumber},'%')
        and IFNULL(fp.CBHHSBM,'-') like concat('%',#{householdNumber},'%')
        <if test="minEnginPower != null and minEnginPower != ''">
            and F.ZJZGL &gt;= #{minEnginPower}
        </if>
        <if test="maxEnginPower != null and maxEnginPower != ''">
            and F.ZJZGL &lt;= #{maxEnginPower}
        </if>
        <if test="startDateOfConstruction != null and startDateOfConstruction != ''">
            and F.JZWGRQ &gt;= #{startDateOfConstruction}
        </if>
        <if test="endDateOfConstruction != null and endDateOfConstruction != ''">
            and F.JZWGRQ &lt;= #{endDateOfConstruction}
        </if>
        <if test="minTonnage != null and minTonnage != ''">
            and s.dw &gt;= #{minTonnage}
        </if>
        <if test="maxTonnage != null and maxTonnage != ''">
            and s.dw &lt;= #{maxTonnage}
        </if>
        <if test="minShipLength != null and minShipLength != ''">
            and s.LENGTH &gt;= #{minShipLength}
        </if>
        <if test="maxShipLength != null and maxShipLength != ''">
            and s.LENGTH &lt;= #{maxShipLength}
        </if>
        <if test="shipMaterial.size > 0">
            and f.CTCZ in
            <foreach collection="shipMaterial" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="shipTypes.size > 0">
            and f.CBZL in
            <foreach collection="shipTypes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="workType1.size > 0">
            and
            <foreach collection="workType1" item="name" separator=" OR ">
                fp.ZZYDYZYFS LIKE CONCAT('%', #{name}, '%')
            </foreach>
        </if>
        <if test="workType2.size > 0">
            and
            <foreach collection="workType2" item="name" separator=" OR ">
                fp.ZZYDEZYFS_ZYFS LIKE CONCAT('%', #{name}, '%')
            </foreach>
        </if>
        <if test="shipType == 1">
            and s.shipType = 2
        </if>
        <if test="shipType == 2">
            and s.shipType = 3
        </if>
        <if test="shipType == 3">
            and s.shipType = 4
        </if>
        <if test="shipType == 4">
            and s.shipType in (6, 7, 62, 72)
        </if>
        <if test="shipType == 5">
            and s.shipType in (63, 73)
        </if>
        <if test="shipType == 6">
            and s.shipType = 3
        </if>
        <if test="shipSmallOrBig == 1">
            and s.LENGTH &lt; #{shipLength}
        </if>
        <if test="shipSmallOrBig == 2">
            and s.LENGTH &gt; #{shipLength}
        </if>
        <if test="personSmallOrBig == 1">
            and s.MAXPEOPLECOUNT &lt;= #{shipPerson}
        </if>
        <if test="personSmallOrBig == 2">
            and s.MAXPEOPLECOUNT &gt;= #{shipPerson}
        </if>
        and s.bOutSide = 0
        and s.MANAGERID in
        <foreach item="item" index="index" collection="managerIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY s.SHIPNAME ASC

    </select>
    <select id="getAllFishShip" resultType="com.bd.entity.ShipStaticInfo_all">
        select distinct  s.ID,s.BDID,s.MMSI,s.SHIPNAME,s.IMO,s.CALLSIGN,s.OWNER,s.LXDH,s.LXDZ,s.CTCL,
                         s.DW,s.ZJGLQW,s.SSDW,s.SHIPTYPE,s.LENGTH,s.WIDTH,s.BOUTSIDE,s.NATION,s.BFOCUS,
                         s.BBLACKFUXIU,s.BWHITEFUXIU,s.ZYLX,s.BLXKZ,d.*,p.PORTNAME,f.CBSYRMC
        from SHIP.SHIP_STATICINFO s
                 left join SHIP.SHIP_DYNAMICTION d on s.id = d.staticshipid
                 left join SHIP.PORTINFO p on d.INPORTID = p.id
                 join SHIP.FISHING_BOAT_INFO f on s.SHIPNAME = f.CM
        where
            (s.shipName like concat('%',#{shipName},'%') or s.owner like concat('%',#{shipName},'%'))
          and IFNULL(s.bdId,'-') like concat('%',#{bdId},'%')
          and IFNULL(s.mmsi,'-') like concat('%',#{mmsi},'%')
        <if test="shipType == 1">
            and s.shipType = 2
        </if>
        <if test="shipType == 2">
            and s.shipType = 3
        </if>
        <if test="shipType == 3">
            and s.shipType = 4
        </if>
        <if test="shipType == 4">
            and s.shipType in (6, 7, 62, 72)
        </if>
        <if test="shipType == 5">
            and s.shipType in (63, 73)
        </if>
        <if test="shipType == 6">
            and s.shipType = 3
        </if>
        <if test="shipSmallOrBig == 1">
            and s.LENGTH &lt; #{shipLength}
        </if>
        <if test="shipSmallOrBig == 2">
            and s.LENGTH &gt; #{shipLength}
        </if>
        <if test="personSmallOrBig == 1">
            and s.MAXPEOPLECOUNT &lt;= #{shipPerson}
        </if>
        <if test="personSmallOrBig == 2">
            and s.MAXPEOPLECOUNT &gt;= #{shipPerson}
        </if>
        and s.bOutSide = 0
        and s.MANAGERID in
        <foreach item="item" index="index" collection="managerIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by s.SHIPNAME
    </select>
    <select id="GetAllFishShipCount" resultType="java.lang.Integer">
        select count(*)
        from SHIP.SHIP_STATICINFO s
        join SHIP.FISHING_BOAT_INFO f on s.SHIPNAME = f.CM
        where
        (shipName like concat('%',#{shipName},'%') or owner like concat('%',#{shipName},'%'))
          and IFNULL(bdId,'-') like concat('%',#{bdId},'%')
        and IFNULL(mmsi,'-') like concat('%',#{mmsi},'%')
        and IFNULL(f.YCBM,'-') like concat('%',#{shipCode},'%')
        and IFNULL(f.CBSYRMC,'-') like concat('%',#{shipOwnerName},'%')
        <if test="minEnginPower != null and minEnginPower != ''">
            and F.ZCZGL &gt;= #{minEnginPower}
        </if>
        <if test="maxEnginPower != null and maxEnginPower != ''">
            and F.ZCZGL &lt;= #{maxEnginPower}
        </if>
        <if test="startDateOfConstruction != null and startDateOfConstruction != ''">
            and F.JZWGRQ &gt;= #{startDateOfConstruction}
        </if>
        <if test="endDateOfConstruction != null and endDateOfConstruction != ''">
            and F.JZWGRQ &lt;= #{endDateOfConstruction}
        </if>
        <if test="minTonnage != null and minTonnage != ''">
            and F.ZDW &gt;= #{minTonnage}
        </if>
        <if test="maxTonnage != null and maxTonnage != ''">
            and F.ZDW &lt;= #{maxTonnage}
        </if>
        <if test="minShipLength != null and minShipLength != ''">
            and s.LENGTH &gt;= #{minShipLength}
        </if>
        <if test="maxTonnage != null and maxTonnage != ''">
            and s.LENGTH &lt;= #{maxShipLength}
        </if>
        <if test="shipMaterial.size > 0">
            and f.CTCZ in
            <foreach collection="shipMaterial" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="shipTypes.size > 0">
            and f.CBZL in
            <foreach collection="shipTypes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="shipType == 1">
            and shipType = 2
        </if>
        <if test="shipType == 2">
            and shipType = 3
        </if>
        <if test="shipType == 3">
            and shipType = 4
        </if>
        <if test="shipType == 4">
            and shipType in (6, 7, 62, 72)
        </if>
        <if test="shipType == 5">
            and shipType in (63, 73)
        </if>
        <if test="shipType == 6">
            and shipType = 3
        </if>
        <if test="shipSmallOrBig == 1">
            and LENGTH &lt;= #{shipLength}
        </if>
        <if test="shipSmallOrBig == 2">
            and LENGTH &gt;= #{shipLength}
        </if>
        <if test="personSmallOrBig == 1">
            and MAXPEOPLECOUNT &lt;= #{shipPerson}
        </if>
        <if test="personSmallOrBig == 2">
            and MAXPEOPLECOUNT &gt;= #{shipPerson}
        </if>
        and bOutSide = 0
        and MANAGERID in
        <foreach item="item" index="index" collection="managerIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="GetAllFishShip_special" resultType="com.bd.entity.ShipStaticInfo_all">
        select *
        from SHIP.SHIP_STATICINFO
        where shipName like concat('%',#{shipName},'%')
        and IFNULL(bdId,'-') like concat('%',#{bdId},'%')
        and mmsi like concat('%',#{mmsi},'%')
        <if test="shipType == 1 or shipType == 2 or shipType == 3 or shipType == 4">
            and shipType =  #{shipType}
        </if>

        <if test="specialType != 1 and specialType != 2 and specialType != 3">
            and (bFocus = 1
            or bBlackFuXiu = 1
            or bWhiteFuXiu = 1)
        </if>
        <if test="specialType == 1">
            and bFocus = 1
        </if>
        <if test="specialType == 2">
            and bBlackFuXiu = 1
        </if>
        <if test="specialType == 3">
            and bWhiteFuXiu = 1
        </if>
        ORDER BY SHIPNAME ASC
        limit #{pageNum}, 10;
    </select>

    <select id="GetFishShip_special" resultType="com.bd.entity.ShipStaticInfo_all">
        select *
        from SHIP.SHIP_STATICINFO
        where shipName like concat('%',#{shipName},'%')
        and IFNULL(bdId,'-') like concat('%',#{bdId},'%')
        and mmsi like concat('%',#{mmsi},'%')
        <if test="shipType == 1 or shipType == 2 or shipType == 3 or shipType == 4">
            and shipType =  #{shipType}
        </if>

        <if test="specialType != 1 and specialType != 2 and specialType != 3">
            and (bFocus = 1
            or bBlackFuXiu = 1
            or bWhiteFuXiu = 1)
        </if>
        <if test="specialType == 1">
            and bFocus = 1
        </if>
        <if test="specialType == 2">
            and bBlackFuXiu = 1
        </if>
        <if test="specialType == 3">
            and bWhiteFuXiu = 1
        </if>
    </select>

    <select id="GetAllFishShipCount_special" resultType="java.lang.Integer">
        select count(*)
        from SHIP.SHIP_STATICINFO
        where shipName like concat('%',#{shipName},'%')
        and IFNULL(s.bdId,'-') like concat('%',#{bdId},'%')
        and mmsi like concat('%',#{mmsi},'%')
        <if test="shipType == 1 or shipType == 2 or shipType == 3 or shipType == 4">
            and shipType =  #{shipType}
        </if>

        <if test="specialType != 1 and specialType != 2 and specialType != 3">
            and (bFocus = 1
            or bBlackFuXiu = 1
            or bWhiteFuXiu = 1)
        </if>
        <if test="specialType == 1">
            and bFocus = 1
        </if>
        <if test="specialType == 2">
            and bBlackFuXiu = 1
        </if>
        <if test="specialType == 3">
            and bWhiteFuXiu = 1
        </if>
    </select>

    <select id="GetOnlineShipCount" resultType="java.lang.Integer">
        select
            SUM(CASE WHEN d.loadTIme > #{time} THEN 1 ELSE 0 END)
        from SHIP.SHIP_STATICINFO s
        left join SHIP.SHIP_DYNAMICTION d
        on s.id = d.staticshipid
        where s.bOutSide = 0
    </select>

    <select id="GetTotalShipCount" resultType="java.lang.Integer">
        select count(0)
        from SHIP.SHIP_STATICINFO s
                 left join SHIP.SHIP_DYNAMICTION d
                           on s.id = d.staticshipid
        where s.bOutSide = 0
    </select>

    <select id="GetAllInPortShip" resultType="com.bd.entity.ShipStaticInfo_all">
        select s.ID,s.BDID,s.MMSI,s.SHIPNAME,s.IMO,s.CALLSIGN,s.OWNER,s.LXDH,s.LXDZ,s.CTCL,
               s.DW,s.ZJGLQW,s.SSDW,s.SHIPTYPE,s.LENGTH,s.WIDTH,s.BOUTSIDE,s.NATION,s.BFOCUS,
               s.BBLACKFUXIU,s.BWHITEFUXIU,s.ZYLX,s.BLXKZ,d.*,p.PORTNAME
        from SHIP.SHIP_STATICINFO s
        left join SHIP.SHIP_DYNAMICTION d on s.id = d.staticshipid
        left join SHIP.PORTINFO p on d.INPORTID = p.id
        where p.portName like concat('%',#{portName},'%')
        and s.bOutSide = 0
        limit #{pageNum}, 10;
    </select>

    <select id="GetAllInPortShipCount" resultType="com.bd.entity.ShipStaticInfo_all">
        select s.ID,s.BDID,s.MMSI,s.SHIPNAME,s.IMO,s.CALLSIGN,s.OWNER,s.LXDH,s.LXDZ,s.CTCL,
               s.DW,s.ZJGLQW,s.SSDW,s.SHIPTYPE,s.LENGTH,s.WIDTH,s.BOUTSIDE,s.NATION,s.BFOCUS,
               s.BBLACKFUXIU,s.BWHITEFUXIU,s.ZYLX,s.BLXKZ,d.*,p.PORTNAME
        from SHIP.SHIP_STATICINFO s
                 left join SHIP.SHIP_DYNAMICTION d on s.id = d.staticshipid
                 left join SHIP.PORTINFO p on d.INPORTID = p.id
        where p.portName like concat('%',#{portName},'%')
          and s.bOutSide = 0
    </select>

    <select id="GetAllInPortShip_Export" resultType="com.bd.entity.ShipStaticInfo_all">
        select s.ID,s.BDID,s.MMSI,s.SHIPNAME,s.IMO,s.CALLSIGN,s.OWNER,s.LXDH,s.LXDZ,s.CTCL,
               s.DW,s.ZJGLQW,s.SSDW,s.SHIPTYPE,s.LENGTH,s.WIDTH,s.BOUTSIDE,s.NATION,s.BFOCUS,
               s.BBLACKFUXIU,s.BWHITEFUXIU,s.ZYLX,s.BLXKZ,d.*,p.PORTNAME
        from SHIP.SHIP_STATICINFO s
                 left join SHIP.SHIP_DYNAMICTION d on s.id = d.staticshipid
                 left join SHIP.PORTINFO p on d.INPORTID = p.id
        where p.portName like concat('%',#{portName},'%')
          and s.bOutSide = 0;
    </select>

    <select id="GetAllShutDownInfo" resultType="com.bd.entity.BusinessManagement.AlarmRecord">
        SELECT l.*, d.lastpostermno, d.LASTPOSBDID, s.owner, s.lxdh, s.ssdw
        FROM SHIP.ALARMRECORDINFO l
        left join SHIP.SHIP_STATICINFO s on l.staticshipid = s.id
        left join SHIP.SHIP_DYNAMICTION d on l.staticshipid = d.staticshipid
        WHERE l.SHIPNAME like concat('%',#{shipName},'%')
        and l.TYPE = 310
        <if test="model == 0">
            and l.MODEL = #{model}
        </if>
        <if test="model == 1">
            and l.MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and l.MODEL in (#{model}, 0)
        </if>
        ORDER BY l.loadTime DESC
        limit #{pageNum}, 10;
    </select>

    <select id="GetAllShutDownInfoCount" resultType="java.lang.Integer">
        SELECT count(l.id)
        FROM SHIP.ALARMRECORDINFO l
        left join SHIP.SHIP_STATICINFO s on l.staticshipid = s.id
        left join SHIP.SHIP_DYNAMICTION d on l.staticshipid = d.staticshipid
        WHERE l.SHIPNAME like concat('%',#{shipName},'%')
          and l.TYPE = 310
        <if test="model == 0">
            and l.MODEL = #{model}
        </if>
        <if test="model == 1">
            and l.MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and l.MODEL in (#{model}, 0)
        </if>
    </select>

    <select id="GetAllShutDownInfo_Export" resultType="com.bd.entity.BusinessManagement.AlarmRecord">
        SELECT l.*, d.lastpostermno, d.LASTPOSBDID, s.owner, s.lxdh, s.ssdw
        FROM SHIP.ALARMRECORDINFO l
                 left join SHIP.SHIP_STATICINFO s on l.staticshipid = s.id
                 left join SHIP.SHIP_DYNAMICTION d on l.staticshipid = d.staticshipid
        WHERE l.SHIPNAME like concat('%',#{shipName},'%')
          and l.TYPE = 310
        <if test="model == 0">
            and l.MODEL = #{model}
        </if>
        <if test="model == 1">
            and l.MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and l.MODEL in (#{model}, 0)
        </if>
        ORDER BY l.loadTime DESC;
    </select>

    <select id="GetAllFishShip_Export" resultType="com.bd.entity.ShipStaticInfo_all">
        select distinct s.ID,s.BDID,s.MMSI,s.SHIPNAME,s.IMO,s.CALLSIGN,s.OWNER,s.LXDH,s.LXDZ,s.CTCL,
        s.DW,s.ZJGLQW,s.SSDW,s.SHIPTYPE,s.LENGTH,s.WIDTH,s.BOUTSIDE,s.NATION,s.BFOCUS,
        s.BBLACKFUXIU,s.BWHITEFUXIU,s.ZYLX,s.BLXKZ,d.*, s.length as vLength,s.length,f.CBSYRMC,f.CC
        from SHIP.SHIP_STATICINFO s
        left join SHIP.SHIP_DYNAMICTION d on s.id = d.staticshipid
        join SHIP.SHIP_NATIONAL_REGIS_INFO f on s.SHIPNAME = f.CM
        where
        (s.shipName like concat('%',#{shipName},'%') or s.owner like concat('%',#{shipName},'%'))
        and IFNULL(s.bdId,'-') like concat('%',#{bdId},'%')
        and IFNULL(s.mmsi,'-') like concat('%',#{mmsi},'%')
        <if test="shipType == -1">
            and s.shipType = 2
        </if>

        <if test="shipType == 1">
            and s.shipType = 2
        </if>
        <if test="shipType == 2">
            and s.shipType = 3
        </if>
        <if test="shipType == 3">
            and s.shipType = 4
        </if>
        <if test="shipType == 4">
            and s.shipType in (6, 7, 62, 72)
        </if>
        <if test="shipType == 5">
            and s.shipType in (63, 73)
        </if>
        <if test="shipType == 6">
            and s.shipType = 3
        </if>
        <if test="shipSmallOrBig == 1">
            and s.LENGTH &lt;= #{shipLength}
        </if>
        <if test="shipSmallOrBig == 2">
            and s.LENGTH &gt;= #{shipLength}
        </if>
        <if test="personSmallOrBig == 1">
            and s.MAXPEOPLECOUNT &lt;= #{shipPerson}
        </if>
        <if test="personSmallOrBig == 2">
            and s.MAXPEOPLECOUNT &gt;= #{shipPerson}
        </if>
        and s.bOutSide = 0
        and s.MANAGERID in
        <foreach item="item" index="index" collection="managerIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY s.SHIPNAME ASC

    </select>

    <select id="GetOneShipCardById" resultType="com.bd.entity.ShipStaticInfo_card">
        select distinct s.*,
                        t2.*,
                        t1.*
        from SHIP.SHIP_STATICINFO s
        left join SHIP.SHIP_NATIONAL_REGIS_INFO t2
        on s.SHIPNAME = t2.CM
        left join SHIP.FISHERY_PERMIT_INFO t1
        on s.SHIPNAME = t1.CM
        where s.id = #{id}
        and t1.sqxkzlx = '海洋'
        order by t1.ZSYXQ desc
        limit 1
    </select>
    <select id="GetPlayShipInfoById" resultType="com.bd.entity.PlayShipInfo">
        SELECT * FROM SHIP.SHIP_STATICINFO WHERE ID = #{id}
    </select>

    <select id="GetAllVoyageInfo" resultType="com.bd.entity.Ship_Voyage">
        SELECT v.*, p1.PORTNAME as STARTPORTNAME, p2.PORTNAME as ENDPORTNAME, s.SSDW
        FROM SHIP.SHIP_VOYAGE v
        LEFT JOIN SHIP.PORTINFO p1 on v.STARTPORT = p1.ID
        LEFT JOIN SHIP.PORTINFO p2 on v.ENDPORT = p2.ID
        left join SHIP.SHIP_STATICINFO s on v.staticshipid = s.id
        WHERE v.SHIPNAME like concat('%',#{shipName},'%')
        ORDER BY v.ENDTIME DESC
        limit #{pageNum}, 10;
    </select>
    <select id="GetAllVoyageInfoCount" resultType="java.lang.Integer">
        SELECT COUNT(v.ID)
        FROM SHIP.SHIP_VOYAGE v
        WHERE v.SHIPNAME like concat('%',#{shipName},'%')
    </select>

    <select id="GetAllVoyageInfo_Export" resultType="com.bd.entity.Ship_Voyage">
        SELECT v.*, p1.PORTNAME as STARTPORTNAME, p2.PORTNAME as ENDPORTNAME, s.SSDW
        FROM SHIP.SHIP_VOYAGE v
                 LEFT JOIN SHIP.PORTINFO p1 on v.STARTPORT = p1.ID
                 LEFT JOIN SHIP.PORTINFO p2 on v.ENDPORT = p2.ID
                 left join SHIP.SHIP_STATICINFO s on v.staticshipid = s.id
        WHERE v.SHIPNAME like concat('%',#{shipName},'%')
        ORDER BY v.ENDTIME DESC;
    </select>

    <select id="GetAllShipWorkInfo" resultType="com.bd.entity.Ship_WorkInfo">
        select s.SHIPNAME as shipName, s.BDID as bdid, s.OWNER as owner, s.LXDH as lxdh,
        (
            select count(*) from (
            SELECT *, Row_Number() OVER (partition by trunc(loadtime) order by loadtime) rank
            FROM SHIP.SHIP_WORKCOUNT
            WHERE s.id =  staticshipid and loadtime &gt; #{startTime} and loadtime &lt; #{endTime}
            <if test="bdOrAis == 1">
                and bdorais = 0
            </if>
            <if test="bdOrAis == 2">
                and bdorais = 1
            </if>
            )where RANK = 1
            ) as WORKDATE,
        (SELECT COUNT(ID) FROM SHIP.SHIP_WORKDAYCOUNT
         WHERE s.id =  staticshipid and loadtime &gt; #{startTime} and loadtime &lt; #{endTime} ) as WORKTIME
        from SHIP.SHIP_STATICINFO s
        WHERE s.SHIPNAME like concat('%',#{shipName},'%')
        and s.bOutSide = 0 and shipType = 2
        order by shipName asc
        limit #{pageNum}, 10;
    </select>
    <select id="GetAllShipWorkInfoCount" resultType="com.bd.entity.Ship_WorkInfo">
        select s.id
        from SHIP.SHIP_STATICINFO s
        WHERE s.SHIPNAME like concat('%',#{shipName},'%')
          and s.bOutSide = 0 and shipType = 2
    </select>

    <select id="GetAllShipWorkInfo_Export" resultType="com.bd.entity.Ship_WorkInfo">
        select s.SHIPNAME as shipName, s.BDID as bdid, s.OWNER as owner, s.LXDH as lxdh,
        (
        select count(*) from (
        SELECT *, Row_Number() OVER (partition by trunc(loadtime) order by loadtime) rank
        FROM SHIP.SHIP_WORKCOUNT
        WHERE s.id =  staticshipid and loadtime &gt; #{startTime} and loadtime &lt; #{endTime}
        <if test="bdOrAis == 1">
            and bdorais = 0
        </if>
        <if test="bdOrAis == 2">
            and bdorais = 1
        </if>
        )where RANK = 1
        ) as WORKDATE,
        (SELECT COUNT(ID) FROM SHIP.SHIP_WORKDAYCOUNT
        WHERE s.id =  staticshipid and loadtime &gt; #{startTime} and loadtime &lt; #{endTime} ) as WORKTIME
        from SHIP.SHIP_STATICINFO s
        WHERE s.SHIPNAME like concat('%',#{shipName},'%')
        and s.bOutSide = 0 and shipType = 2
        order by shipName asc
    </select>

    <update id="SetSpecialShip">
        update SHIP.SHIP_STATICINFO
            set
                <if test="specialType == 1">
                    bFocus = 1
                </if>
                <if test="specialType == 2">
                    bBlackFuXiu = 1
                </if>
                <if test="specialType == 3">
                    bWhiteFuXiu = 1
                </if>
            where
                ID = #{id}
    </update>

    <update id="DeleteSpecialShip">
        update SHIP.SHIP_STATICINFO
            set
                <if test="specialType == 1">
                    bFocus = 0
                </if>
                <if test="specialType == 2">
                    bBlackFuXiu = 0
                </if>
                <if test="specialType == 3">
                    bWhiteFuXiu = 0
                </if>
            where
                ID = #{id}
    </update>

    <select id="GetFocusShipType" resultType="com.bd.entity.AllShipType">
        select s.ID as shipId, s.SHIPTYPE as shipType, s.BOUTSIDE as bOutSide, 1 as newShipType
                FROM SHIP.SHIP_DYNAMICTION d
                INNER JOIN SHIP.SHIP_STATICINFO s on
                d.STATICSHIPID = s.id
                WHERE s.BFOCUS = 1
    </select>

    <select id="GetWarningShipType" resultType="com.bd.entity.AllShipType">
        select s.ID as shipId, s.SHIPTYPE as shipType, s.BOUTSIDE as bOutSide, 2 as newShipType
                from SHIP.ALARMRECORDINFO a left join SHIP.SHIP_STATICINFO s
                on a.STATICSHIPID = s.ID
                where a.LOADTIME > #{time} and a.USERID in (0, #{userId})
                <if test="model == 0">
                    and a.MODEL = #{model}
                </if>
                <if test="model == 1">
                    and a.MODEL in (#{model}, 0)
                </if>
                <if test="model == 2">
                    and a.MODEL in (#{model}, 0)
                </if>
    </select>

    <select id="GetJianCeShipType" resultType="com.bd.entity.AllShipType">
        select j.STATICSHIPID as shipId, s.SHIPTYPE as shipType, s.BOUTSIDE as bOutSide, 3 as newShipType
                from SHIP.SHIP_JIANCESHIP j left join SHIP.SHIP_STATICINFO s
                on j.STATICSHIPID = s.ID
                where j.STATICSHIPID is not null
    </select>

    <select id="GetShipIdByName" resultType="com.bd.entity.ShipStaticInfo_all">
        select * from SHIP.SHIP_STATICINFO where SHIPNAME = #{shipName}
    </select>
    <select id="GetManagerCount" resultType="java.lang.Integer">
        select count(id) from ship.ship_staticinfo
        where boutside = 0
          and shiptype = 2
        and MANAGERID in
        <foreach item="item" index="index" collection="managerIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="GetShipInfoByShipNameList" resultType="com.bd.entity.ShipSerch" resultMap="ShipSerchResultMap">
        select ID,SHIPNAME,BDID
        from SHIP.SHIP_STATICINFO
        where
        SHIPNAME in
        <foreach item="item" index="index" collection="shipNameList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectShipName" resultType="com.bd.entity.ShipStaticInfo_all">
        select * from ship.ship_staticinfo where shipname is not null
    </select>
    <select id="GetOneShipCardTexuById" resultType="com.bd.entity.FisheryPermitInfo">
        select distinct t1.*
        from SHIP.SHIP_STATICINFO s
        left join SHIP.FISHERY_PERMIT_INFO t1
        on s.SHIPNAME = t1.CM
        where s.id = #{id}
          and t1.sqxkzlx = '专项'
        order by t1.ZSYXQ desc
        limit 1
    </select>
    <select id="GetCurrentInPortShips" resultType="com.bd.entity.Ship">
        select p.portname, s.shipname from SHIP.SHIP_DYNAMICTION d
        left join ship.ship_staticinfo s on s.id = d.staticshipid
        left join ship.portinfo p on p.id = d.inportid
        where d.inportstate = 1
          and s.boutside = 0
          and s.shiptype = 2
        order by p.portname desc
    </select>
    <select id="GetCurrentAllPorts" resultType="com.bd.entity.Port">
        select  DISTINCT p.portname  from SHIP.SHIP_DYNAMICTION d
        left join ship.ship_staticinfo s on s.id = d.staticshipid
        left join ship.portinfo p on p.id = d.inportid
        where d.inportstate = 1
          and s.boutside = 0
          and s.shiptype = 2
        order by p.portname desc
    </select>
    <select id="GetCurrentShipsByPortName" resultType="com.bd.entity.Ship">
        select  DISTINCT s.shipname  from SHIP.SHIP_DYNAMICTION d
        left join ship.ship_staticinfo s on s.id = d.staticshipid
        left join ship.portinfo p on p.id = d.inportid
        where p.portname = #{portName} and d.inportstate = 1
          and s.boutside = 0
          and s.shiptype = 2
        order by s.shipname ASC
    </select>
    <select id="GetOutsideInPorts" resultType="com.bd.entity.Port">
        select DISTINCT p.portname from SHIP.SHIP_DYNAMICTION d
        left join ship.ship_staticinfo s on s.id = d.staticshipid
        left join ship.portinfo p on p.id = d.inportid
        where d.inportstate = 1
          and s.boutside = 1
          and s.shiptype = 2
        order by p.portname desc
    </select>
    <select id="GetOutsideShipsByPortName" resultType="com.bd.entity.Ship">
        select  DISTINCT s.shipname  from SHIP.SHIP_DYNAMICTION d
          left join ship.ship_staticinfo s on s.id = d.staticshipid
          left join ship.portinfo p on p.id = d.inportid
        where p.portname = #{portName} and d.inportstate = 1
          and s.boutside = 1
          and s.shiptype = 2
        order by s.shipname ASC
    </select>
</mapper>