<component name="libraryTable">
  <library name="Maven: org.junit.jupiter:junit-jupiter-params:5.1.1">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/junit/jupiter/junit-jupiter-params/5.1.1/junit-jupiter-params-5.1.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/junit/jupiter/junit-jupiter-params/5.1.1/junit-jupiter-params-5.1.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/junit/jupiter/junit-jupiter-params/5.1.1/junit-jupiter-params-5.1.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>