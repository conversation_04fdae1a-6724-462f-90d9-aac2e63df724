package com.bd.thread;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bd.entity.CrewInOutInfo;
import com.bd.entity.FisheryBoatInOutReport;
import com.bd.mapper.CrewExamMapper;
import com.bd.service.UserService;
import com.bd.util.HttpTool;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@EnableScheduling
public class UpdateInOutPortInfo {

    @Resource
    private CrewExamMapper crewExamMapper;

    @Resource
    private UserService userService;

    /**
     * 更新进出港报告
     *
     * @param inOrOut 大于0时出港
     */
    public void UpdateShipInOrOut(int inOrOut) {
        String token = userService.GetOpenCenterToken();
        // 船舶出港
        String url1 = "http://*********:18080/api/service/share/P431503764839469056?conditions=null";
        // 船员出港
        String url2 = "http://*********:18080/api/service/share/P576236820975587328?conditions=null";
        // 船舶进港
        String url3 = "http://*********:18080/api/service/share/P431503088868659200?conditions=null";
        // 船员进港
        String url4 = "http://*********:18080/api/service/share/P565603490622083072?conditions=null";
        int pageNum = 1;
        List<String> dataList1; // 船舶
        List<String> dataList2; // 船员
        List<FisheryBoatInOutReport> list1 = new ArrayList<>();
        List<CrewInOutInfo> list2 = new ArrayList<>();
        while (true) {
            try {
                dataList1 = inOrOut > 0 ? getJsonList(url1, token, pageNum) : getJsonList(url3, token, pageNum);
                dataList2 = inOrOut > 0 ? getJsonList(url2, token, pageNum) : getJsonList(url4, token, pageNum);
            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }

            if (dataList1.size() < 2 && dataList2.size() < 2) break;
            for (String data : dataList1) {
                JSONObject jsonObject = JSONObject.parseObject(data);
                FisheryBoatInOutReport obj = new FisheryBoatInOutReport();

                obj.setPortId(jsonObject.getString("gkwybs"));
                obj.setPortName(getPortName(jsonObject.getString("gkwybs")));
                obj.setShipName(jsonObject.getString("ycmc"));
                obj.setCrewStr(jsonObject.getString("cywybs"));
                obj.setTime(inOrOut > 0 ? jsonObject.getString("cgsj") : jsonObject.getString("jgsj"));
                obj.setInOrOut(inOrOut);

                list1.add(obj);
            }
            for (String data : dataList2) {
                JSONObject jsonObject = JSONObject.parseObject(data);
                CrewInOutInfo obj = new CrewInOutInfo();

                obj.setId(jsonObject.getString("zj"));
                obj.setName(jsonObject.getString("xm"));
                obj.setIdcard(jsonObject.getString("sfzh"));
                obj.setZw(jsonObject.getString("zw"));
                obj.setZsdj(jsonObject.getString("zsdj"));
                obj.setInOrOut(inOrOut);

                list2.add(obj);
            }
            System.out.println("pageNum:" + pageNum);

            pageNum++;
        }
//        System.out.println("list1.size() = " + list1.size());
//        System.out.println("list2.size() = " + list2.size());
        for (FisheryBoatInOutReport boat : list1) {
            String crewStr = boat.getCrewStr();
            if (crewStr == null) {
                System.out.println("continued boat = " + boat);
                continue;
            }
            String[] crewArr = crewStr.split(",");
            boat.setCrewStr("");
            for (String id : crewArr) {
                for (CrewInOutInfo crew : list2) {
                    if (id.equals(crew.getId())) {
                        boat.setCrewStr(String.format("%s%s#%s#%s#%s@", boat.getCrewStr(), crew.getName(), crew.getIdcard(), crew.getZw(), crew.getZsdj()));
//                        boat.setZw(crew.getZw());
//                        boat.setZsdj(crew.getZsdj());
                        break;
                    }
                }
            }
        }
        try {
            // 分批插入，防止缓存区爆满异常
            int batchSize = 1000;
            for (int i = 0; i < list1.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, list1.size());
                crewExamMapper.insertShipInOutReport(list1.subList(i, endIndex));
            }
        } catch (Exception e) {
            System.err.println(e.getMessage());
        }
    }

    public String getPortName(String portId) {
        switch (portId) {
            case "1392307540380889090":
                return "六滧港";
            case "1392307620848611330":
                return "金汇港";
            case "1392307690536972289":
                return "奚家港";
            case "1392307762326679553":
                return "堡镇";
            case "1392307884846493698":
                return "中港";
            case "1392307954694238209":
                return "芦潮港";
            case "1392307995861331970":
                return "大治河";
            case "583":
                return "上海横沙一级渔港";
            default:
                return "-";
        }
    }


    public static List<String> getJsonList(String url, String token, int pageNum) {
        String header = "Bearer " + token;
        String jsonData = HttpTool.doGet(url + "&pageNum=" + pageNum + "&pageSize=" + 500, header);
        JSONArray jsonArray = JSONArray.parseArray(jsonData);


        List<String> list = new ArrayList<>();
        if (jsonArray != null) {
            for (Object obj : jsonArray) {
                list.add(obj.toString());
            }
        }
        return list;

    }

    //@Scheduled(cron = "0 0 */2 * * ?")
    //@Scheduled(fixedRate = Integer.MAX_VALUE)
    public void run() throws Exception {
        userService.deletePortNode();
        UpdateShipInOrOut(0); // 进港
        UpdateShipInOrOut(1); // 出港
        System.out.println("==============end!");
    }
}
