<template>
  <div class="unicast-container">
    <div class="header">单播</div>
    <div class="ship-list">
      <label
        v-for="ship in ships"
        :key="ship.id"
        class="ship-btn"
        :class="{ selected: selectedShipIds.includes(ship.id) }"
      >
        <input
          type="checkbox"
          v-model="selectedShipIds"
          :value="ship.id"
          style="display:none"
        />
        {{ ship.shipName }}
      </label>
      <button class="add-btn" @click="addShip">+</button>
    </div>
    <div class="chat-area">
      <!-- 聊天内容可以用 v-for 渲染 -->
    </div>
    <div class="input-area">
      <input v-model="input" class="chat-input" />
      <button
        class="send-btn"
        @mousedown="sendBtnActive = true"
        @mouseup="sendBtnActive = false"
        @mouseleave="sendBtnActive = false"
        @touchstart="sendBtnActive = true"
        @touchend="sendBtnActive = false"
        @touchcancel="sendBtnActive = false"
        :class="{active: sendBtnActive}"
        @click="send"
      >发送</button>
    </div>
  </div>
</template>

<script>
import global from './Global.vue'
export default {
  data() {
    return {
      ships: [
        { id: 1, bdid: 'BD001', shipName: '沪崇渔12001' },
        { id: 2, bdid: 'BD002', shipName: '沪崇渔12002' },
        { id: 3, bdid: 'BD003', shipName: '沪崇渔12003' }
      ],
      selectedShipIds: [],
      input: '',
      sendBtnActive: false
    }
  },
  methods: {
    addShip() {
      // 添加船只逻辑
    },
    send() {
      if (this.selectedShipIds.length === 0) {
        alert('请选择要发送的船舶');
        return;
      }
      const mess = this.input;
      const userId = sessionStorage.getItem('userId');
      this.selectedShipIds.forEach(id => {
        const ship = this.ships.find(s => s.id === id);
        if (!ship) return;
        const data = {
          staticShipId: ship.id,
          bdId: ship.bdid,
          msg: mess,
          userId
        };
        $.ajax({
          url: global.IP + "/web/InsertBdMsg",
          type: "POST",
          data: JSON.stringify(data),
          dataType: "json",
          contentType: "application/json",
          success: function(data) {
            // ...你的成功逻辑
          },
          error: function() {
            // ...你的失败逻辑
          }
        });
      });
      this.input = '';
    }
  }
}
</script>

<style scoped>
.unicast-container {
  width: 100%;
  height: 100vh;
  background: #f3f3f3;
  display: flex;
  flex-direction: column;
}
.header {
  background: #e5e5e5;
  text-align: center;
  font-size: 32px;
  padding: 16px 0;
  font-weight: bold;
}
.ship-list {
  display: inline-flex;
  align-items: center;
  padding: 8px 8px;
  margin: 3px 0;
  gap: 4px 6px;
  justify-content: flex-start;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  min-height: 44px;
  margin-bottom: 0;
  white-space: nowrap;
  overflow-x: auto;
  width: 100%;
  box-sizing: border-box;
}
.ship-list > .ship-btn {
  margin-bottom: 0;
}
.ship-btn {
  display: inline-block;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 4px;
  padding: 4px 16px;
  font-size: 14px;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(.4,0,.2,1), background 0.2s, color 0.2s, box-shadow 0.2s;
  font-weight: 500;
  letter-spacing: 0.2px;
  margin-right: 4px;
  min-width: 70px;
  max-width: 140px;
  white-space: nowrap;
  box-shadow: 0 1px 4px rgba(102, 126, 234, 0.13);
  user-select: none;
  position: relative;
}
.ship-btn.selected {
  background: #4a6fa5;
  color: #fff;
  box-shadow: 0 2px 8px rgba(74, 111, 165, 0.18);
  border: 2px solid #2a4a8a;
}
.add-btn {
  width: 28px;
  height: 28px;
  font-size: 18px;
  border: 1.5px dashed #6c757d;
  border-radius: 4px;
  background: #fff;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
  vertical-align: middle;
  box-shadow: 0 1px 4px rgba(108, 117, 125, 0.13);
  margin-left: 0;
  margin-bottom: 0;
}
.add-btn:hover {
  border-color: #667eea;
  color: #667eea;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}
.add-btn:active {
  transform: scale(0.95);
}
.chat-area {
  flex: 1;
  border: 2px solid #2a4a8a;
  background: #f3f3f3;
  margin: 0 0 0 0;
  padding: 0;
  overflow-y: auto;
}
.input-area {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #e5e5e5;
  border-top: 1px solid #ccc;
}
.chat-input {
  flex: 1;
  height: 40px;
  font-size: 18px;
  border: 2px solid #ccc;
  border-radius: 4px;
  padding: 0 8px;
  margin-right: 16px;
}
.send-btn {
  position: relative;
  overflow: hidden;
  width: 80px;
  height: 40px;
  font-size: 20px;
  background: #fff;
  border: 2px solid #4a6fa5;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.15s cubic-bezier(.4,0,.2,1), background 0.15s, color 0.15s, box-shadow 0.15s, border-color 0.15s;
  box-shadow: 0 1px 4px rgba(74, 111, 165, 0.10);
  outline: none;
}
.send-btn.active {
  transform: scale(0.93);
  background: #4a6fa5;
  color: #fff;
  box-shadow: 0 2px 8px rgba(74, 111, 165, 0.18);
  border-color: #2a4a8a;
}
.send-btn:hover {
  background: #eaf1ff;
  color: #4a6fa5;
  border-color: #2a4a8a;
}
.send-btn .ripple {
  position: absolute;
  border-radius: 50%;
  transform: scale(0);
  animation: ripple 0.5s linear;
  background-color: rgba(74, 111, 165, 0.3);
  pointer-events: none;
  z-index: 2;
}
@keyframes ripple {
  to {
    transform: scale(2.5);
    opacity: 0;
  }
}
</style>
