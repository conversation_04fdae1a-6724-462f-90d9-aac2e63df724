package com.bd.service;
import com.bd.entity.*;
import com.bd.entity.dto.CrewCertificationQueryDto;
import com.bd.entity.dto.PeopleQueryDto;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Service
public interface PeopleService {

    //业务查询
    void DeleteLawRecordInfo(int id);

    List<People> GetAllPeople(PeopleQueryDto queryDto);
    int GetAllPeopleCount(PeopleQueryDto queryDto);
    List<People> GetAllPeople_Export(PeopleQueryDto queryDto);
    //执法记录
    int InsertLawRecordInfo(LawRecordInfo lawRecordInfo);
    int InsertBdMsg(BdMsg bdMsg);

    List<BdMsg> GetBdMsg(String shipName, String bdId, String startTime, String endTime, int pageNum);

    List<BdMsg> GetAllBdMsg(String shipName, String bdId, String startTime, String endTime);

    List<BdMsg> GetAllBdMsgByUserId(int userId, int shipId);

    void DeleteBdMsg(int id);

    int GetBdMsgCount(String shipName, String bdId, String startTime, String endTime);

    //实时预警执法记录查询
    List<LawRecordInfo> GetLawRecordTodayWarning(int model);
    LawRecordInfo GetLawRecordInfoById(int id, int model);

    List<LawRecordInfo> GetAllLawRecordInfo(String name, String startTime, String endTime, int pageNum);

    List<LawRecordInfo> GetLawRecordInfo(String name, String startTime, String endTime);

    int GetAllLawRecordInfoCount(String name, String startTime, String endTime);

    List<People> GetPeopleByShipId(int shipId, int pageNum);

    int GetPeopleByShipIdCount(int shipId);

    List<String> GetPeopleByShipName(String shipName, int pageNum);

    int GetPeopleByShipNameCount(List<String> shipName);

    List<LawPeople> GetLawPeopleInfo();


    void InsertCrewInfo(List<People> crewList);

    void InsertBdMsgExample(String content, int userId);

    void DeleteBdMsgExample(int id);

    void UpdateBdMsgExample(int id, int userId, String content);

    List<BDMsgExample> GetBdMsgExample(int userId);

    void ClearCrewInfo();

    List<People> GetPeopleByIdCard(List<String> idCardStr, int pageNum);

    List<People> GetCrewPeopleByShipId(int shipId);

    FisheryBoatInOutReport GetPortNodeByShipId(int shipId, Integer state);

    List<FisheryBoatInOutReport> GetPortNodeByShipIds(List<Integer> shipIds);

    List<CrewBx> GetCrewBxByShipName(String shipName);

    List<CrewBx> GetCrewBxByShipNames(List<String> shipNames);

    List<CrewBx> GetPeopleByIdcards(List<String> idcards);

    List<LawCase> GetlawCaseByShipId(List<String> idcards);

    List<String> GetShipNameByPeople(String keyword);

    int GetPersonCount(String shipName);

    String GetApplinameByShipName(String shipName);

    String GetZshmByIdCard(String idcard);

    String GetEndTimeByIdCard(String idcard);

    void UpdateCrewTimeState(String time);

    List<People> GetCrewCertificateStatistics(CrewCertificationQueryDto queryDto);
    String GetIdcardSignCodeByIdcard(String idcard);

}
