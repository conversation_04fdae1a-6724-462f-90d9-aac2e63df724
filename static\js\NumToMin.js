//小数表示的经纬度转换成分秒制表示的经纬度
    //小数表示的经纬度转换成分秒制表示的经纬度
    //lonLat：经纬度值，例如121.023
    //bIsLon:是否经度，true=经度，false=纬度
function NumToMin(lonLat, bIsLon, decimalCount) {
    var strNumber = lonLat;
    try
    {
        strNumber = parseFloat(lonLat);
    }
    catch(e)
    {
        return lonLat;
    }

    while (Math.abs(strNumber) > 360) {
        if (strNumber > 0) {
            strNumber -= 360;
        }
        else {
            strNumber += 360;
        }
    }

    strNumber = strNumber.toFixed(5);
    var aryStringNumber = strNumber.split(".");
    //获取小数部分
    var tranNumber = parseFloat("0." + aryStringNumber[1]);
    tranNumber = tranNumber * 60;

    var fen = parseInt(tranNumber);
    var miaoNumber = (parseFloat(tranNumber) - fen) * 60;

    //处理整书部分
    var sign;
    if (bIsLon == true) {
        if (lonLat >= 0 && lonLat <= 180) {
            sign = " E "; 
        }
        else {
            aryStringNumber[0] = parseInt(-aryStringNumber[0]);
            sign = " W "; 
        }
    }
    else {
        if (lonLat > 0) {
            sign = " N "; 
        }
        else {
            aryStringNumber[0] = parseInt(-aryStringNumber[0]);
            sign = " S "; 
        }
    }

    if (isNaN(decimalCount) == false) {
        decimalCount = 2;
    }
    //return (aryStringNumber[0] + "°" + tranNumber.toFixed(decimalCount) + sign); 
    return (aryStringNumber[0] + "°" + fen + "′" + miaoNumber.toFixed(2) +"″" + sign); 
  }
  export {NumToMin}