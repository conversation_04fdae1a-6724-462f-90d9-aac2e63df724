package com.bd.mapper;

import com.bd.entity.*;
import com.bd.entity.BusinessManagement.AlarmRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface UserMapper {

    UserInfo GetUser(@Param("userInfo")UserInfo userInfo);
    int GetUsername(@Param("username")String username);

    List<UserInfo> GetAllUser(@Param("userName")String userName, @Param("pageNum")int pageNum);
    List<UserInfo> GetAllUserByType(@Param("userName")String userName, @Param("userType")int userType, @Param("pageNum")int pageNum);
    int GetAllUserCount(@Param("userName")String userName);
    int GetAllUserCountByType(@Param("userName")String userName, @Param("userType")int userType);

    void InsertUser(@Param("user")UserInfo user);

    List<UserInfo> GetUserInfo();
    void UpdateUser(@Param("userId")int userId, @Param("userInfo")UserInfo userInfo);
    void DeleteUser(@Param("id")int id);

    void addUserOperationInfo(@Param("userOperation")UserOperation userOperation);

    List<UserOperation> GetUserOperation(@Param("userName")String userName,
                                         @Param("startTime")String startTime,
                                         @Param("endTime")String endTime,
                                         @Param("type")int type,
                                         @Param("pageNum")int pageNum);

    int GetUserOperationCount(@Param("userName")String userName,
                              @Param("startTime")String startTime,
                              @Param("endTime")String endTime,
                              @Param("type")int type);

    int GetUserCount(@Param("time")String time);

    UserInfo GetUserInfoByUsername(@Param("username")String username);

    UserInfo GetUserInfoById(@Param("id")int id);

    void UpdateToken(@Param("access_token")String access_token);

    String GetMapToken();

    int GetErrorCount(@Param("name")String name);

    void SetErrorCount(@Param("name")String name, @Param("count")int count);

    void SetUserSetting(@Param("userSettingInfo")UserSettingInfo userSettingInfo);

    void UpdateUserSetting(@Param("userSettingInfo")UserSettingInfo userSettingInfo);

    void DeleteUserSetting(@Param("userId")int userId, @Param("setting")int setting);

    List<UserSettingInfo> GetUserSetting(@Param("userId")int userId);

    UserInfo isCheckAccount(@Param("sendName")String sendName);

    void SendWarningToOtherAccount(@Param("sendId")int sendId, @Param("receiveId")int receiveId, @Param("warningId")int warningId);

    String GetOpenCenterToken();

    void UpdateOpenCenterToken(@Param("token")String token);

    List<PushInformation> GetWarningToOtherAccount(@Param("userId")int userId, @Param("model")int model);

    void SetWarningToOtherAccountIsRead(@Param("userId")int userId);

    String GetOpenCenterToken_HIK();

    void UpdateOpenCenterToken_HIK(@Param("token")String token);

    void deletePortNode();
}
