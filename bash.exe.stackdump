Stack trace:
Frame         Function      Args
0007FFFFB750  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA650) msys-2.0.dll+0x1FE8E
0007FFFFB750  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA28) msys-2.0.dll+0x67F9
0007FFFFB750  000210046832 (000210286019, 0007FFFFB608, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB750  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB750  000210068E24 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA30  00021006A225 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD4EBE0000 ntdll.dll
7FFD4D200000 KERNEL32.DLL
7FFD4C200000 KERNELBASE.dll
7FFD4D2C0000 USER32.dll
7FFD4C0B0000 win32u.dll
7FFD4DDF0000 GDI32.dll
7FFD4C0E0000 gdi32full.dll
7FFD4C710000 msvcp_win.dll
7FFD4C5F0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD4E5F0000 advapi32.dll
7FFD4D510000 msvcrt.dll
7FFD4EB00000 sechost.dll
7FFD4DCD0000 RPCRT4.dll
7FFD4B920000 CRYPTBASE.DLL
7FFD4C7B0000 bcryptPrimitives.dll
7FFD4D1C0000 IMM32.DLL
