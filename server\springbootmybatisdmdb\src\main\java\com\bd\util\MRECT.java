package com.bd.util;

public class MRECT {
    int leftTopPointX;
    int leftTopPointY;
    int rightDownPointX;
    int rightDownPointY;

    public int getLeftTopPointX() {
        return leftTopPointX;
    }

    public void setLeftTopPointX(int leftTopPointX) {
        this.leftTopPointX = leftTopPointX;
    }

    public int getLeftTopPointY() {
        return leftTopPointY;
    }

    public void setLeftTopPointY(int leftTopPointY) {
        this.leftTopPointY = leftTopPointY;
    }

    public int getRightDownPointX() {
        return rightDownPointX;
    }

    public void setRightDownPointX(int rightDownPointX) {
        this.rightDownPointX = rightDownPointX;
    }

    public int getRightDownPointY() {
        return rightDownPointY;
    }

    public void setRightDownPointY(int rightDownPointY) {
        this.rightDownPointY = rightDownPointY;
    }
}
