-- 为USERINFO表插入数据（包含ID值）的SQL脚本
-- 执行日期: 2025-07-29
-- 说明: 开启IDENTITY_INSERT来插入指定ID值的用户数据

-- 开启IDENTITY_INSERT，允许为自增列赋值
SET IDENTITY_INSERT SHIP.USERINFO ON;

-- 插入用户数据（包含ID）
-- 修复：为崇明用户指定ID值
INSERT INTO SHIP.USERINFO (ID, USERNAME, PASSWORD, NAME, LXDH, LEVEL, TOKEN, BM, ERRORCOUNT, USER_AREA)
VALUES (3, 'chongming01', '123456', '崇明', null, 1, null, null, 0, null);

-- 关闭IDENTITY_INSERT
SET IDENTITY_INSERT SHIP.USERINFO OFF;

-- 验证插入结果
SELECT * FROM SHIP.USERINFO WHERE USERNAME = 'admin';
