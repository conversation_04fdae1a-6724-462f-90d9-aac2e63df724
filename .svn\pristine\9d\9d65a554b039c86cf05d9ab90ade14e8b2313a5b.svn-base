<script>
const none = 0;//无状态
const drawPoint = 1;//绘制点
const drawLine = 2;//绘制线
const drawFace = 3;//绘制面
const drawRect = 4;//绘制矩形
const drawCircle = 5;//绘制圆
const measureDist = 6;//测距
const measureArea = 7;//测面积
const directionLine = 8;//电子方位线
const drawLineArea = 9;//绘制电缆

const drawSomeThingPoints = [] //绘制物标时存储坐标用

const drawTest = 0;   //绘制测试

const g_showSimpleInfoDiv= null;
const g_showDrawObjInfoByMouseMoveDiv= null;
const g_showDetailShipInfoDiv= null;
const g_iPointLayerId= 100; //点图层的id
const g_iPointStylePos= 0;//样式pos
const g_iLineLayerId= 200; //线图层的id
const g_iLineStylePos= 0;
const g_iFaceLayerId= 300; //面图层的id
const g_iFaceStylePos= 1;
const g_iComprehensiveLayerId = 400;
const g_iComprehensiveStylePos = 400;
const g_iCameraLayerId = 500; //摄像头图层
const g_iCameraStylePos = 500;
const g_iMarkLayerId = 600; //标注图层
const g_iMarkStylePos = 600;
const g_iLocationLayerId = 700; //定位点图层
const g_iLocationStylePos = 700;
const g_iJianCeLayerId =800; //监测图层
const g_iJianCeStylePos = 800;
const g_iFishingPortLayerId= 4000; //渔港图层的id
const g_iFishingPortStylePos= 4000;
const g_iAreaFuxiuLayerId= 4001; //伏休
const g_iAreaFuxiuStylePos= 4001;
const g_iAreaGaoweiLayerId= 4002; //高危
const g_iAreaGaoweiStylePos= 4002;
const g_iAreaTeshuLayerId= 4003; //特殊
const g_iAreaTeshuStylePos= 4003;
const g_iAreaSelfLayerId= 4004; //自定义
const g_iAreaSelfStylePos= 4004;
const g_iAddObjId= 0;
const g_iSimpleBoxOneLineSize= 20; //信息面板添加一行对应增加了多少高度

const g_iWeatherLayerId= 1000; //气象图层id
const g_iPortLayerId= 2000; //港口图层的id
const g_iOceanCirculationLayerId= 3000;//洋流图层
const g_strTestMsg= "";
const g_drawObjectInfoBoxGeoPo= null;//动态绘制物标时候，显示物标信息框的经纬度位置

const ajaxType= "Get";
const ajaxAsync= false;
const ajaxContentType= "application/json; charset=utf-8";
const ajaxDataType= "json";

const m_curDrayBoxObj= null;
const m_curMousePoX= 0;
const m_curMousePoY= 0;
const m_offsetPoX= 0;
const m_offsetPoY= 0;

const abc= false;

const g_iSeaCableLayerId= 1100; //海缆图层id
const g_iWarnLine1LayerId= 1101; //一级警戒区
const g_iWarnLine2LayerId= 1102; //二级警戒区
const g_iWarnLine3LayerId= 1103; //三级警戒区

const g_iWarnLine2Id= 0; //二级警戒区id，每添加一条就递增。这个值应该是从数据库中获取的，唯一标识警戒区的id，即可以为数据库中警戒区的id字段

const g_iWarnLine3Id= 0; //三级警戒区id，每添加一条就递增。这个值应该是从数据库中获取的，唯一标识警戒区的id，即可以为数据库中警戒区的id字段

const m_arrRadarData= []; //雷达数据
const m_radarObj= [];
const iCurUpdateSectorNum= 0;//更新雷达帧数

const iAddShipCount= 200; //三十万艘船舶

const RealObjectShipPos= new Map();
const RealObjectShipId= new Array();

const g_iDrawObjPoNum= 0;

const bAddOceanCirculation= false;

const g_arrSelectShipInfoObj= null;//查询船舶的信息

const m_bAddFishAreaOk= false;
const m_bShowFishArea= false;

const g_bSelectShipByRectModel = false;//是否进入框选船舶信息模式
const g_bSelectShipByCircleModel = false; //是否进入圆选船舶信息模式
const g_bCurDrawFaceForSelectShip = false;//当前是否绘制多边形来查询船舶
const g_shipHistory = false;    //船舶回放
const g_shipHistoryArea = false;//区域回放

const shipList = [];
const portInfo = [];
const shipCount = 0;
const divId = 0;

const currentPageNum = 0;
const currentPageNum1 = 0;
const allPortInfo = [];
const curPort = 0;
const typhoonInfoList = [];

const focusOnTrackingShips = [];//重点船舶跟踪

const warningChangeNumber = 0;

const IP = 'http://localhost:7001';
// const IP = 'http://***********:7001';
//const IP = 'http://***********:7003';

const mapToken = '';
const mapModel = 'day_b';

const importShipInfo = [];
const warningShipInfo = [];
const specialShipInfo = [];
const inOrOutShipInfo = [];
const getVedioInfo = [];
const areaInfo = [];
const isInToPan = false;
const clustererShipInfo = [];

const userId = 0;

const getAllBDShipCurIndex = 1;
const getAllBDShipMax = 1;
const editObjInfo = [];
const editObjId = -1;
const editLayerId = -1;

const editObjAllInfo = [];

// 用来保存特殊渔船
const allFocusShip = [];

// 存储当前居中函数获取的船舶的所有信息
const curShipInfoByCenter = [];

const curTyphoonInfoByYear = [];

// 设置当前的id-轨迹回放
const curHistoryShipId = -1;

// 设置当前选中的船舶的id-API_GetCurSelectShipInfo
const curSelectShipId = -1;

// 使用yima还是wms
// false-wms
const yimaOrWms = false;

// 用来设置层级
const curIndex = 1000;

// 报警声音
const isCheckOpenVedio = true;

// 保存查询轨迹的信息-轨迹回放、区域回放
const playShipHistoryInfo = [];

// 仅用于更新
const focusUpdateShipInfo = [];
const warningUpdateShipInfo = [];
const jianCeUpdateShipInfo = [];

// 保存台风信息
const allTyphoonInfo = [];
// 当前显示台风
const curTyphoonInfoId = -1;

const isCheckJianOrOther = false;

const isTodayOrOtherTyphoon = false;

const shipShowRule = null;

const realTimeMessage = [];

const warningModel = -1;
const warningModelLast = -1;


const isCheckInit = false;

export default {
    none,
    drawPoint,
    drawLine,
    drawFace,
    drawRect,
    drawCircle,
    measureDist,
    measureArea,
    directionLine,
    drawLineArea,

    drawSomeThingPoints,

    drawTest,

    g_showSimpleInfoDiv,
    g_showDrawObjInfoByMouseMoveDiv,
    g_showDetailShipInfoDiv,
    g_iPointLayerId,
    g_iPointStylePos,
    g_iLineLayerId,
    g_iLineStylePos,
    g_iFaceLayerId,
    g_iFaceStylePos,
    g_iComprehensiveLayerId,
    g_iComprehensiveStylePos,
    g_iCameraLayerId,
    g_iCameraStylePos,
    g_iMarkLayerId,
    g_iMarkStylePos,
    g_iLocationLayerId,
    g_iLocationStylePos,
    g_iJianCeLayerId,
    g_iJianCeStylePos,
    g_iFishingPortLayerId,
    g_iFishingPortStylePos,
    //伏休
    g_iAreaFuxiuStylePos,
    g_iAreaGaoweiLayerId, //高危
    g_iAreaGaoweiStylePos,
    g_iAreaTeshuLayerId, //特殊
    g_iAreaTeshuStylePos,
    g_iAreaSelfLayerId, //自定义
    g_iAreaSelfStylePos,
    g_iAddObjId,
    g_iSimpleBoxOneLineSize,

    g_iWeatherLayerId,
    g_iPortLayerId,
    g_iOceanCirculationLayerId,
    g_strTestMsg,
    g_drawObjectInfoBoxGeoPo,

    ajaxType,
    ajaxAsync,
    ajaxContentType,
    ajaxDataType,

    m_curDrayBoxObj,
    m_curMousePoX,
    m_curMousePoY,
    m_offsetPoX,
    m_offsetPoY,

    abc,

    g_iSeaCableLayerId,
    g_iWarnLine1LayerId,
    g_iWarnLine2LayerId,
    g_iWarnLine3LayerId,

    g_iWarnLine2Id,

    g_iWarnLine3Id,

    m_arrRadarData,
    m_radarObj,
    iCurUpdateSectorNum,

    iAddShipCount,

    RealObjectShipPos,
    RealObjectShipId,

    g_iDrawObjPoNum,

    bAddOceanCirculation,

    g_arrSelectShipInfoObj,

    m_bAddFishAreaOk,
    m_bShowFishArea,

    g_bSelectShipByRectModel,
    g_bSelectShipByCircleModel,

    g_bCurDrawFaceForSelectShip,

    g_shipHistory,
    g_shipHistoryArea,
    shipList,
    portInfo,
    shipCount,
    divId,
    currentPageNum,
    currentPageNum1,
    allPortInfo,
    curPort,
    typhoonInfoList,
    focusOnTrackingShips,
    warningChangeNumber,
    IP,
    importShipInfo,
    warningShipInfo,
    specialShipInfo,
    getVedioInfo,
    inOrOutShipInfo,
    areaInfo,
    isInToPan,
    clustererShipInfo,

    userId,

    getAllBDShipCurIndex,
    getAllBDShipMax,
    editObjInfo,
    editObjId,
    editLayerId,
    editObjAllInfo,
    allFocusShip,

    curShipInfoByCenter,

    curTyphoonInfoByYear,

    curHistoryShipId,

    curSelectShipId,

    yimaOrWms,
    curIndex,
    isCheckOpenVedio,
    mapModel,
    mapToken,
    playShipHistoryInfo,
    focusUpdateShipInfo,
    warningUpdateShipInfo,
    jianCeUpdateShipInfo,

    allTyphoonInfo,
    curTyphoonInfoId,
    isCheckJianOrOther,
    isTodayOrOtherTyphoon,
    shipShowRule,
    realTimeMessage,
    warningModel,
    warningModelLast,
    isCheckInit,
}
</script>
