package com.bd.mapper;

import com.bd.entity.*;
import com.bd.entity.BusinessManagement.AlarmRecord;
import com.bd.entity.dto.ShipQueryDto;
import com.bd.entity.ship.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.PathVariable;

import javax.websocket.server.PathParam;
import java.util.List;
import java.util.Queue;

@Mapper
public interface ShipStaticInfoMapper {

    List<ShipSerch> GetShipInfoByNameOrTerminalNumber(@Param("name") String name);

    List<ShipStaticInfo_all> GetOneShipInfoByShipName(String name);

    List<ShipStaticInfo> GetOneOtherProvincesShipInfoByShipName(String name);

    List<ShipStaticInfo_all> GetOneShipInfoById(int id);

    List<ShipStaticInfo> GetOneOtherProvincesShipInfoById(int id);

    List<ShipStaticInfo_all> GetOneWSShipInfoById(int id);

    List<ShipStaticInfo_all> GetAllFishShip(ShipQueryDto queryDto);

    List<ShipStaticInfo_all> getAllFishShip(ShipQueryDto queryDto);

    int GetAllFishShipCount(ShipQueryDto queryDto);

    List<ShipStaticInfo_all> GetAllFishShip_special(@Param("shipName")String shipName,
                                            @Param("bdId")String bdId,
                                            @Param("mmsi")String mmsi,
                                            @Param("shipType")int shipType,
                                            @Param("specialType")int specialType,
                                            @Param("pageNum")int pageNum);

    List<ShipStaticInfo_all> GetFishShip_special(@Param("shipName")String shipName,
                                                    @Param("bdId")String bdId,
                                                    @Param("mmsi")String mmsi,
                                                    @Param("shipType")int shipType,
                                                    @Param("specialType")int specialType);

    int GetAllFishShipCount_special(@Param("shipName")String shipName,
                            @Param("bdId")String bdId,
                            @Param("mmsi")String mmsi,
                            @Param("shipType")int shipType,
                            @Param("specialType")int specialType);

    void SetSpecialShip(@Param("id")int id,
                        @Param("specialType")int specialType);

    void DeleteSpecialShip(@Param("id")int id, @Param("specialType")int specialType);

    int GetOnlineShipCount(@Param("time")String time);

    int GetTotalShipCount();

    List<ShipStaticInfo_all> GetAllInPortShip(@Param("portName")String portName, @Param("pageNum")int pageNum);

    List<ShipStaticInfo_all> GetAllInPortShipCount(@Param("portName")String portName);

    List<ShipStaticInfo_all> GetAllInPortShip_Export(@Param("portName")String portName);

    List<AlarmRecord> GetAllShutDownInfo(@Param("shipName")String shipName, @Param("pageNum") int pageNum, @Param("model")int model);

    int GetAllShutDownInfoCount(@Param("shipName")String shipName, @Param("model")int model);

    List<AlarmRecord> GetAllShutDownInfo_Export(@Param("shipName")String shipName, @Param("model")int model);

    List<ShipStaticInfo_all> GetAllFishShip_Export(@Param("shipName")String shipName,
                                                   @Param("bdId")String bdId,
                                                   @Param("mmsi")String mmsi,
                                                   @Param("shipType")int shipType,
                                                   @Param("managerIdList")List<Long> managerIdList,
                                                   @Param("shipLength")int shipLength,
                                                   @Param("shipSmallOrBig")int shipSmallOrBig,
                                                   @Param("shipPerson")int shipPerson,
                                                   @Param("personSmallOrBig")int personSmallOrBig);

    List<ShipStaticInfo_card> GetOneShipCardById(@Param("id")int id);

    PlayShipInfo GetPlayShipInfoById(@Param("id")int id);

    List<Ship_Voyage> GetAllVoyageInfo(@Param("shipName")String shipName, @Param("pageNum")int pageNum);

    int GetAllVoyageInfoCount(@Param("shipName")String shipName);

    List<Ship_Voyage> GetAllVoyageInfo_Export(@Param("shipName")String shipName);

    List<Ship_WorkInfo> GetAllShipWorkInfo(@Param("shipName")String shipName, @Param("startTime")String startTime, @Param("endTime")String endTime, @Param("bdOrAis")int bdOrAis, @Param("pageNum")int pageNum);

    List<Ship_WorkInfo> GetAllShipWorkInfoCount(@Param("shipName")String shipName, @Param("startTime")String startTime, @Param("endTime")String endTime, @Param("bdOrAis")int bdOrAis);

    List<Ship_WorkInfo> GetAllShipWorkInfo_Export(@Param("shipName")String shipName, @Param("startTime")String startTime, @Param("endTime")String endTime, @Param("bdOrAis")int bdOrAis);

    List<AllShipType> GetFocusShipType();

    List<AllShipType> GetWarningShipType(@Param("userId")int userId, @Param("time")String time, @Param("model")int model);

    List<AllShipType> GetJianCeShipType();

    void InsertShipNameCard(@Param("shipList")List<Ship_NameCard> shipList);

    void InsertShipNation(@Param("shipList")List<Ship_Nation> shipList);

    void InsertShipNet(@Param("shipList")List<Ship_Net> shipList);

    void InsertShipPermit(@Param("shipList")List<Ship_Permit> shipList);

    void InsertShipCheck(@Param("shipList")List<Ship_Check> shipList);

    void InsertShipSave(@Param("shipList")List<Ship_Save> shipList);

    void ClearShipInfo();

    ShipStaticInfo_all GetShipIdByName(@Param("shipName")String shipName);

    void ClearOldInfo();

    int GetManagerCount(@Param("managerIdList")List<Long> managerIdList);

    List<ShipSerch> GetShipInfoByShipNameList(@Param("shipNameList")List<String> shipNameList);

    List<ShipStaticInfo_all> selectShipName();

    FisheryPermitInfo GetOneShipCardTexuById(@Param("id")int id);
    //查询每个港口当前停靠的渔船
    List<Ship> GetCurrentInPortShips();
    //查询当前的所有港口
    List<Port> GetCurrentAllPorts();
    //查询某个港口下当前的所有在港船只
    List<Ship> GetCurrentShipsByPortName(@Param("portName") String portName);

    List<Port> GetOutsideInPorts();

    List<Ship> GetOutsideShipsByPortName(String portName);

}
