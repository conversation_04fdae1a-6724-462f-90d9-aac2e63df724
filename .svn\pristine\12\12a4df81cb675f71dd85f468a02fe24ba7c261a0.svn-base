<template>
    <div class="ship-detail">
        <!-- 移动端菜单按钮 -->
        <div class="mobile-menu-header">
            <button @click="toggleMenu" class="menu-btn">☰</button>
            <button @click="goBack" class="back-btn"></button>
        </div>

        <!-- 整体布局：左侧菜单 + 右侧内容 -->
        <div class="detail-layout">
            <!-- 左侧菜单（移动端时变为侧滑抽屉） -->
            <div class="menu-sidebar" :class="{ 'mobile-visible': menuVisible }">
                <div class="sidebar-header">
                    <span>菜单</span>
                    <button @click="toggleMenu" class="close-menu-btn">×</button>
                </div>
                <div v-for="tab in tabs" :key="tab.id" :class="{ 'menu-item': true, active: activeTab === tab.id }"
                    @click="setActiveTab(tab.id)">
                    {{ tab.name }}
                </div>
            </div>

            <!-- 右侧详情内容区域 -->
            <div class="content-area">
                <!-- 终端信息 -->
                <div v-if="activeTab === 'terminal'" class="info-section">
                    <h2>终端信息</h2>
                    <div class="main-content">
                        <div class="section terminal-info">
                            <div class="terminal-block">
                                <div class="terminal-title">{{ ship.terminal }}</div>
                                <div>终端号: {{ ship.bdid }}</div>
                                <div>经度: {{ ship.lon }}</div>
                                <div>纬度: {{ ship.lat }}</div>
                                <div>航向: {{ ship.cog }}</div>
                                <div>速度: {{ ship.speed }}</div>
                                <div>报位时间: {{ ship.bdTime }}</div>
                            </div>
                        </div>
                    </div>
                    <!-- 底部操作按钮 -->
                    <div class="footer-btns">
                        <button>短信</button>
                        <button>调位</button>
                        <button>航迹</button>
                        <button>收藏</button>
                    </div>
                </div>

                <!-- 船舶基本信息 -->
                <div v-if="activeTab === 'basic'" class="info-section">
                    <h2>船舶基本信息</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">船名：</span>
                            <span class="value">{{ ship.shipName }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船舶类型：</span>
                            <span class="value">{{ ship.shipType || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">组织机构：</span>
                            <span class="value">{{ ship.organization || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">呼号：</span>
                            <span class="value">{{ ship.callSign || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船长：</span>
                            <span class="value">{{ ship.length }}米</span>
                        </div>
                        <div class="info-item">
                            <span class="label">型宽：</span>
                            <span class="value">{{ ship.width }}米</span>
                        </div>
                        <div class="info-item">
                            <span class="label">作业场所：</span>
                            <span class="value">{{ ship.operationArea || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">属地：</span>
                            <span class="value">{{ ship.location || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">作业类型：</span>
                            <span class="value">{{ ship.operationType || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船舶所有人：</span>
                            <span class="value">{{ ship.owner || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">功率：</span>
                            <span class="value">{{ ship.power }}千瓦</span>
                        </div>
                        <div class="info-item">
                            <span class="label">吨位：</span>
                            <span class="value">{{ ship.tonnage }}吨</span>
                        </div>
                        <div class="info-item">
                            <span class="label">建造完工日期：</span>
                            <span class="value">{{ ship.completionDate || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船体材料：</span>
                            <span class="value">{{ ship.hullMaterial || '-' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 捕捞许可证信息 -->
                <div v-if="activeTab === 'license'" class="info-section">
                    <h2>捕捞许可证</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">捕捞许可证号：</span>
                            <span class="value">{{ ship.yyblxkzbh || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">持证人：</span>
                            <span class="value">{{ ship.czr || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">持证人地址：</span>
                            <span class="value">{{ ship.czrdz || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">证书签发日期：</span>
                            <span class="value">{{ ship.qfsj || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">证书有效期：</span>
                            <span class="value">{{ ship.zsyxq || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">作业类型：</span>
                            <span class="value">{{ ship.zzylx || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">作业场所：</span>
                            <span class="value">{{ ship.zzydyzyfs_zycs || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">作业方式：</span>
                            <span class="value">{{ ship.zzydyzyfs || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">申请许可类型：</span>
                            <span class="value">{{ ship.sqxkzlx || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">渔具数量：</span>
                            <span class="value">{{ ship.zzydyzyfs_yjsl || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">渔具名称：</span>
                            <span class="value">{{ ship.zzydyzyfs_yjmc || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">渔具规格：</span>
                            <span class="value">{{ ship.zzydyzyfs_yjgg || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">主要捕捞品种：</span>
                            <span class="value">{{ ship.zzydyzyfs_zyblpz || '-' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 特许捕捞许可证信息 -->
                <div v-if="activeTab === 'special'" class="info-section">
                    <h2>特许捕捞许可证</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">专项特许证编号：</span>
                            <span class="value">{{ ship.yyblxkzbh || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">发证单位：</span>
                            <span class="value">{{ ship.qfbm || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">发放对象：</span>
                            <span class="value">{{ ship.czr || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">许可事项：</span>
                            <span class="value">{{ ship.sqxkzlx || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">作业时间：</span>
                            <span class="value">{{ ship.zzydyzyfs_zykssj__zzydyzyfs_zyjssj || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">作业水域：</span>
                            <span class="value">{{ ship.zzydyzyfs_zycs || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">作业方式：</span>
                            <span class="value">{{ ship.zzydyzyfs || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">捕捞品种：</span>
                            <span class="value">{{ ship.zzydyzyfs_zyblpz || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">作业类型：</span>
                            <span class="value">{{ ship.zzylx || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">作业场所：</span>
                            <span class="value">{{ ship.zzydyzyfs_jtcsmc || '-' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 船舶检验信息 -->
                <div v-if="activeTab === 'inspection'" class="info-section">
                    <h2>船舶检验信息</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">证书编号：</span>
                            <span class="value">{{ ship.main_CERTIFICATE_NUMBER || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">证书有效期：</span>
                            <span class="value">{{ ship.main_CERTIFICATE_VALIDITY || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">发证日期：</span>
                            <span class="value">{{ ship.main_CERITIFICATE_DATE || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">发证地点：</span>
                            <span class="value">{{ ship.main_ADDRESS_CH || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">检验地点：</span>
                            <span class="value">{{ ship.main_VERIFYENR_ADDRESS_CH || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">检验类型：</span>
                            <span class="value">{{ ship.dict_MAIN_VERIFYENR_TYPE || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">检验部门：</span>
                            <span class="value">{{ ship.main_VERIFY_DEPT_ID || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">下次检验日期：</span>
                            <span class="value">{{ ship.main_NEXT_VERIFY_TIME || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">下次检验类型：</span>
                            <span class="value">{{ ship.dict_MAIN_NEXT_VERIFY_TYPE || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船舶制造厂：</span>
                            <span class="value">{{ ship.link_FISHING_FACTORY_CH || '-' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 船舶安全设备信息 -->
                <div v-if="activeTab === 'safety'" class="info-section">
                    <h2>船舶安全设备信息</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">检验登记号：</span>
                            <span class="value">{{ ship.verifyenr_NO || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">核载人数：</span>
                            <span class="value">{{ ship.link_RATED_LOAD_NUM || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船舶定员总人数：</span>
                            <span class="value">{{ ship.passengers_NUM || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">救生设备可用总人数：</span>
                            <span class="value">{{ ship.save_EQUIPMENT_CAN_USED_NUM || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">救生圈数量：</span>
                            <span class="value">{{ ship.life_BUOY_NUM || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">甚高频设备型号：</span>
                            <span class="value">{{ ship.vhf_TYPE || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">渔船无线电话型号：</span>
                            <span class="value">{{ ship.cngrrtel_TYPE || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">左舷救生艇型式：</span>
                            <span class="value">{{ ship.ship_CHECK_LEFT_TYPE || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">左舷救生艇数量：</span>
                            <span class="value">{{ ship.save_EQT_LEFT_NUM || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">左舷救生艇共载人数：</span>
                            <span class="value">{{ ship.save_EQT_LEFT_ACCOMMODATED || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">右舷救生艇型式：</span>
                            <span class="value">{{ ship.ship_CHECK_RIGHT_TYPE || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">右舷救生艇数量：</span>
                            <span class="value">{{ ship.save_EQT_RIGHT_NUM || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">右舷救生艇共载人数：</span>
                            <span class="value">{{ ship.lsave_EQT_RIGHT_ACCOMMODATED || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">渔船无线电话识别码：</span>
                            <span class="value">{{ ship.cngrr_NUM || '-' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 国籍证书信息 -->
                <div v-if="activeTab === 'certificate'" class="info-section">
                    <h2>国籍证书</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">国籍证书编号：</span>
                            <span class="value">{{ ship.yycbgjzsbh || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船名：</span>
                            <span class="value">{{ ship.cm || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船籍港：</span>
                            <span class="value">{{ ship.cjg || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船舶种类：</span>
                            <span class="value">{{ ship.cbzl || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船体材质：</span>
                            <span class="value">{{ ship.ctcz || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">主尺度：</span>
                            <span class="value">{{ ship.cc || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船舶所有人名称：</span>
                            <span class="value">{{ ship.cbsyrmc || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">身份证号/统一代码：</span>
                            <span class="value">{{ ship.czrjmsfzhmhgszch || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">渔船编号：</span>
                            <span class="value">{{ ship.ycbm || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船舶呼号：</span>
                            <span class="value">{{ ship.cbhhsbm || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">生产方式/养殖证号：</span>
                            <span class="value">{{ ship.cblx || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">建造完工日期：</span>
                            <span class="value">{{ ship.jzwgrq || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">吨位：</span>
                            <span class="value">{{ ship.zdw || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船舶所有人地址：</span>
                            <span class="value">{{ ship.cbsyrdz || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">所有权登记证书编号：</span>
                            <span class="value">{{ ship.syqgjdjsqsbh || '-' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 所有权登记证书信息 -->
                <div v-if="activeTab === 'ownership'" class="info-section">
                    <h2>所有权登记证书</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">所有权证书编号：</span>
                            <span class="value">{{ ship.yycbsyqdjzsbh || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船网指标批准书：</span>
                            <span class="value">{{ ship.yycbsyqdjzsbh || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">国籍证书编号：</span>
                            <span class="value">{{ ship.yycbgjzsbh || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">取得所有权日期：</span>
                            <span class="value">{{ ship.syqgjdjspsj || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">证书有效期：</span>
                            <span class="value">{{ ship.yycbdjzsyxq__yycbgjzsyxq || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">申请人姓名：</span>
                            <span class="value">{{ ship.czrmc || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">联系电话：</span>
                            <span class="value">{{ ship.cczrdh || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船籍港：</span>
                            <span class="value">{{ ship.cjg || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">渔船所属地：</span>
                            <span class="value">{{ ship.ycssdqmc || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船舶种类：</span>
                            <span class="value">{{ ship.cbzl || '-' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 执法检查信息 -->
                <div v-if="activeTab === 'enforcement'" class="info-section">
                    <h2>执法检查信息</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">船长：</span>
                            <span class="value">{{ ship.cbsyrmc || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">执法部门：</span>
                            <span class="value">{{ ship.dEPT_CNAMES || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">检查地点：</span>
                            <span class="value">{{ ship.cHECK_PLACE || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">检查时间：</span>
                            <span class="value">{{ ship.sIGN_DATE || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">处理意见：</span>
                            <span class="value">{{ ship.oTHER_CHECK_CONTENT || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">当事人签字时间：</span>
                            <span class="value">{{ ship.sIGN_DATE || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">当事人意见：</span>
                            <span class="value">{{ ship.sUOBJ_OPINION || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">船东：</span>
                            <span class="value">{{ ship.cd || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">电话：</span>
                            <span class="value">{{ ship.dh || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">执法人员：</span>
                            <span class="value">{{ ship.eNF_LE_NAMES || '-' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">检查内容：</span>
                            <span class="value">{{ ship.cHECK_CONTENT || '-' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import global from './Global.vue';
import setAlertWindowShow from '../../static/js/windowAlert.js'

export default {
    name: "ShipDetail",
    data() {
        return {
            ship: {
                //终端信息
                zylx: "",//作业类型
                terminal: "",//终端类型
                lxdh: "",//船舶电话
                ssdw: "",//组织机构
                //终端信
                bdid: "",//终端号
                lon: "",//经度
                lat: "",//纬度
                cog: "",//航向
                speed: "",//速度
                bdTime: "",//报位时间

                //基本信息
                shipName: '',//船名
                shipType: '',//船舶类型
                organization: '',//组织机构
                callSign: '',//呼号
                length: '',//船长
                width: '',//型宽
                operationArea: '', // 作业场所
                location: '',//属地
                operationType: '',//作业类型
                owner: '',//船舶所有人
                power: '',//功率
                tonnage: '',//吨位
                completionDate: '',//建造完工日期
                hullMaterial: '',//船体材料

                //捕捞许可证
                yyblxkzbh: "",//捕捞许可证号
                czr: "",//持证人
                czrdz: "",//持证人地址
                qfsj: "",//证书签发日期
                zsyxq: "",//证书有效期
                zzylx: "",//作业类型
                zzydyzyfs_zycs: "",//作业场所
                zzydyzyfs: "",//作业方式
                sqxkzlx: "",//申请许可类型
                zzydyzyfs_yjsl: "",//渔具数量
                zzydyzyfs_yjmc: "",//渔具名称
                zzydyzyfs_yjgg: "",//渔具规格
                zzydyzyfs_zyblpz: "",//主要捕捞品种

                //特许捕捞许可证
                yyblxkzbh: "",//专项特许证编号
                qfbm: "",//发证单位
                czr: "",//发放对象
                sqxkzlx: "",//许可事项
                zzydyzyfs_zykssj__zzydyzyfs_zyjssj: "",//作业时间
                zzydyzyfs_zycs: "",//作业水域
                zzydyzyfs: "",//作业方式
                zzydyzyfs_zyblpz: "",//捕捞品种
                //zzylx: "",//作业类型
                zzydyzyfs_jtcsmc: "",//作业场所

                //船舶检验信息
                main_CERTIFICATE_NUMBER: "",//证书编号
                main_CERTIFICATE_VALIDITY: "",//证书有效期
                main_CERITIFICATE_DATE: "",//发证日期
                main_ADDRESS_CH: "",//发证地点
                main_VERIFYENR_ADDRESS_CH: "",//检验地点
                dict_MAIN_VERIFYENR_TYPE: "",//检验类型
                main_VERIFY_DEPT_ID: "",//检验部门
                main_NEXT_VERIFY_TIME: "",//下次检验日期
                dict_MAIN_NEXT_VERIFY_TYPE: "",//下次检验类型
                link_FISHING_FACTORY_CH: "",//船舶制造厂

                //船检安全设备信息
                verifyenr_NO: "",//检验登记号
                link_RATED_LOAD_NUM: "",//核载人数
                passengers_NUM: "",//船舶定员总人数
                save_EQUIPMENT_CAN_USED_NUM: "",//救生设备可用总人数
                life_BUOY_NUM: "",//救生圈数量
                vhf_TYPE: "",//甚高频设备型号
                cngrrtel_TYPE: "",//渔船无线电话型号
                ship_CHECK_LEFT_TYPE: "",//左舷救生艇型式
                save_EQT_LEFT_NUM: "",//左舷救生艇数量
                save_EQT_LEFT_ACCOMMODATED: "",//左舷救生艇共载人数
                ship_CHECK_RIGHT_TYPE: "",//右舷救生艇型式
                save_EQT_RIGHT_NUM: "",//右舷救生艇数量
                lsave_EQT_RIGHT_ACCOMMODATED: "",//右舷救生艇共载人数
                cngrr_NUM: "",//渔船无线电话识别码

                //国籍证书信息
                yycbgjzsbh: "",//国籍证书编号
                cm: "",//船名
                cjg: "",//船籍港
                cbzl: "",//船舶种类
                ctcz: "",//船体材质
                cc: "",//主尺度
                cbsyrmc: "",//船舶所有人名称
                czrjmsfzhmhgszch: "",//公民身份证号/统一社会信用代码
                ycbm: "",//渔船编号
                cbhhsbm: "",//船舶呼号
                cblx: "",//生产方式/养殖证号
                jzwgrq: "",//建造完工日期
                zdw: "",//吨位
                cbsyrdz: "",//船舶所有人地址
                syqgjdjsqsbh: "",//船舶所有权登记证书编号

                //所有权登记证书信息
                yycbsyqdjzsbh: "",//所有权证书编号
                yycbsyqdjzsbh: "",//船网指标批准书
                yycbgjzsbh: "",//国籍证书编号
                syqgjdjspsj: "",//取得所有权日期
                yycbdjzsyxq__yycbgjzsyxq: "",//证书有效期
                czrmc: "",//申请人姓名
                cczrdh: "",//联系电话
                cjg: "",//船籍港
                ycssdqmc: "",//渔船所属地
                cbzl: "",//船舶种类

                //执法检查信息
                cbsyrmc: "",//船长
                dEPT_CNAMES: "",//执法部门
                cHECK_PLACE: "",//检查地点
                sIGN_DATE: "",//检查时间
                oTHER_CHECK_CONTENT: "",//处理意见
                sIGN_DATE: "",//当事人签字时间
                sUOBJ_OPINION: "",//当事人意见
                cd: "",//船东
                dh: "",//电话
                eNF_LE_NAMES: "",//执法人员
                cHECK_CONTENT: "",//检查内容
            },
            activeTab: 'terminal',
            tabs: [
                { id: 'terminal', name: '终端信息' },
                { id: 'basic', name: '船舶基本信息' },
                { id: 'license', name: '捕捞许可证' },
                { id: 'special', name: '特许捕捞许可证' },
                { id: 'inspection', name: '船舶检验信息' },
                { id: 'safety', name: '船舶安全设备信息' },
                { id: 'certificate', name: '国籍证书' },
                { id: 'ownership', name: '所有权登记证书' },
                { id: 'enforcement', name: '执法检查信息' }
            ],
            loading: true,
            menuVisible: false
        };
    },
    props: {
        shipId: {
            type: Number,
            required: true
        },
        shipName: {
            type: String,
            required: true
        },
    },
    mounted() {
        this.fetchShipInfoByID();
        this.fetchShipCardByID();
        this.fetchShipCardTexuByID();
        this.fetchCheckRecordByShipName();
    },
    methods: {
        fetchShipInfoByID() {
            this.loading = true;
            console.log(global.IP + "/web/GetOneShipInfoById?id=" + this.shipId);

            $.get(global.IP + "/web/GetOneShipInfoById?id=" + this.shipId, (data, status) => {
                if (data.length == 0) {
                    setAlertWindowShow('search-container', '未查到详细信息', '', 2);
                    return;
                }

                //终端信息
                this.ship.zylx = data[0].zylx || '-';
                this.ship.terminal = data[0].bdid ? '北斗终端' : '-';
                this.ship.lxdh = data[0].lxdh || '-';
                this.ship.ssdw = data[0].ssdw || '-';

                this.ship.bdid = data[0].bdid || '-';
                this.ship.lon = data[0].lon || '-';
                this.ship.lat = data[0].lat || '-';
                this.ship.cog = data[0].cog || '-';
                this.ship.speed = data[0].speed || '-';
                this.ship.bdTime = data[0].bdTime || '-';

                // 👉 基本信息
                this.ship.shipName = data[0].shipname || '-';
                this.ship.shipType = data[0].cblx || '-';
                this.ship.organization = data[0].ycssdqmc || '-';
                this.ship.callSign = data[0].cbhhsbm || '-';
                this.ship.length = data[0].cz || '-';
                this.ship.width = data[0].xk || '-';
                this.ship.operationArea = data[0].zzydyzyfs_ZYCS || '-';
                this.ship.location = data[0].ycssdqmc || '-';
                this.ship.operationType = data[0].zzylx || '-';
                this.ship.owner = data[0].owner || '-';
                this.ship.power = data[0].zjzgl || '-';
                this.ship.tonnage = data[0].zdw || '-';
                this.ship.completionDate = data[0].jzwgrq || '-';
                this.ship.hullMaterial = data[0].ctcz || '-';

            }).fail(function (msg) {
                console.log("error：" + JSON.stringify(msg))
            }).always(() => {
                this.loading = false;
            });
        },
        fetchShipCardTexuByID() {
            this.loading = true;

            $.get(global.IP + "/web/GetOneShipCardTexuById?id=" + this.shipId, (data, status) => {
                if (data.length == 0) {
                    setAlertWindowShow('search-container', '未查到详细信息', '', 2);
                    return;
                }
                // 特许捕捞许可证信息
                this.ship.yyblxkzbh = data.yyblxkzbh || '-';
                this.ship.qfbm = data.qfbm || '-';
                this.ship.czr = data.czr || '-';
                this.ship.sqxkzlx = data.sqxkzlx || '-';
                this.ship.zzydyzyfs_zykssj__zzydyzyfs_zyjssj =
                    (data.zzydyzyfs_zykssj || '') +
                    (data.zzydyzyfs_zyjssj ? ' - ' + data.zzydyzyfs_zyjssj : '') || '-';
                this.ship.zzydyzyfs_zycs = data.zzydyzyfs_zycs || '-';
                this.ship.zzydyzyfs_jtcsmc = data.zzydyzyfs_jtcsmc || '-';
                this.ship.zzydyzyfs = data.zzydyzyfs || '-';
                this.ship.zzydyzyfs_zyblpz = data.zzydyzyfs_zyblpz || '-';
                this.ship.zzylx = data.zzylx || '-';

            }).fail(function (msg) {
                console.log("error：" + JSON.stringify(msg))
            }).always(() => {
                this.loading = false;
            });
        },
        fetchShipCardByID() {
            this.loading = true;

            $.get(global.IP + "/web/GetOneShipCardById?id=" + this.shipId, (data, status) => {
                if (data.length == 0) {
                    setAlertWindowShow('search-container', '未查到详细信息', '', 2);
                    return;
                }
                // 👉 捕捞许可证
                this.ship.yyblxkzbh = data[0].yyblxkzbh || '-';
                this.ship.czr = data[0].czr || '-';
                this.ship.czrdz = data[0].czrdz || '-';
                this.ship.qfsj = data[0].qfsj || '-';
                this.ship.zsyxq = data[0].zsyxq || '-';
                //this.ship.zzylx = data[0].zzylx || '-';
                this.ship.zzydyzyfs_zycs = data[0].zzydyzyfs_zycs || '-';
                this.ship.zzydyzyfs = data[0].zzydyzyfs || '-';
                this.ship.sqxkzlx = data[0].sqxkzlx || '-';
                this.ship.zzydyzyfs_yjsl = data[0].zzydyzyfs_yjsl || '-';
                this.ship.zzydyzyfs_yjmc = data[0].zzydyzyfs_yjmc || '-';
                this.ship.zzydyzyfs_yjgg = data[0].zzydyzyfs_yjgg || '-';
                this.ship.zzydyzyfs_zyblpz = data[0].zzydyzyfs_zyblpz || '-';

                // 船舶检验信息
                this.ship.main_CERTIFICATE_NUMBER = "";
                this.ship.main_CERTIFICATE_VALIDITY = "";
                this.ship.main_CERITIFICATE_DATE = "";
                this.ship.main_ADDRESS_CH = "";
                this.ship.main_VERIFYENR_ADDRESS_CH = "";
                this.ship.dict_MAIN_VERIFYENR_TYPE = "";
                this.ship.main_VERIFY_DEPT_ID = "";
                this.ship.main_NEXT_VERIFY_TIME = "";
                this.ship.dict_MAIN_NEXT_VERIFY_TYPE = "";
                this.ship.link_FISHING_FACTORY_CH = "";

                // 船舶安全设备信息
                this.ship.verifyenr_NO = data[0].verifyenr_NO || '-';
                this.ship.link_RATED_LOAD_NUM = data[0].link_RATED_LOAD_NUM || '-';
                this.ship.passengers_NUM = data[0].passengers_NUM || '-';
                this.ship.save_EQUIPMENT_CAN_USED_NUM = data[0].save_EQUIPMENT_CAN_USED_NUM || '-';
                this.ship.life_BUOY_NUM = data[0].life_BUOY_NUM || '-';
                this.ship.vhf_TYPE = data[0].vhf_TYPE || '-';
                this.ship.cngrrtel_TYPE = data[0].cngrrtel_TYPE || '-';
                this.ship.ship_CHECK_LEFT_TYPE = data[0].ship_CHECK_LEFT_TYPE || '-';
                this.ship.save_EQT_LEFT_NUM = data[0].save_EQT_LEFT_NUM || '-';
                this.ship.save_EQT_LEFT_ACCOMMODATED = data[0].save_EQT_LEFT_ACCOMMODATED || '-';
                this.ship.ship_CHECK_RIGHT_TYPE = data[0].ship_CHECK_RIGHT_TYPE || '-';
                this.ship.save_EQT_RIGHT_NUM = data[0].save_EQT_RIGHT_NUM || '-';
                this.ship.lsave_EQT_RIGHT_ACCOMMODATED = data[0].lsave_EQT_RIGHT_ACCOMMODATED || '-';
                this.ship.cngrr_NUM = data[0].cngrr_NUM || '-';

                // 国籍证书信息
                this.ship.yycbgjzsbh = data[0].yycbgjzsbh || '-';
                this.ship.cm = data[0].cm || '-';
                this.ship.cjg = data[0].cjg || '-';
                this.ship.cbzl = data[0].cbzl || '-';
                this.ship.ctcz = data[0].ctcz || '-';
                this.ship.cc = data[0].cc || '-';
                this.ship.cbsyrmc = data[0].cbsyrmc || '-';
                this.ship.czrjmsfzhmhgszch = data[0].czrjmsfzhmhgszch || '-';
                this.ship.ycbm = data[0].ycbm || '-';
                this.ship.cbhhsbm = data[0].cbhhsbm || '-';
                this.ship.cblx = data[0].cblx || '-';
                this.ship.jzwgrq = data[0].jzwgrq || '-';
                this.ship.zdw = data[0].zdw || '-';
                this.ship.cbsyrdz = data[0].cbsyrdz || '-';
                this.ship.syqgjdjsqsbh = data[0].syqgjdjsqsbh || '-';

                // 所有权登记证书信息
                this.ship.yycbsyqdjzsbh = data[0].yycbsyqdjzsbh || '-';
                this.ship.yycbgjzsbh = data[0].yycbgjzsbh || '-';
                this.ship.syqgjdjspsj = data[0].syqgjdjspsj || '-';
                this.ship.yycbdjzsyxq__yycbgjzsyxq = data[0].yycbdjzsyxq__yycbgjzsyxq || '-';
                this.ship.czrmc = data[0].czrmc || '-';
                this.ship.cczrdh = data[0].cczrdh || '-';
                this.ship.cjg = data[0].cjg || '-';
                this.ship.ycssdqmc = data[0].ycssdqmc || '-';
                this.ship.cbzl = data[0].cbzl || '-';

                // 执法检查信息
                this.ship.cbsyrmc = data[0].cbsyrmc || '-';
            }).fail(function (msg) {
                console.log("error：" + JSON.stringify(msg))
            }).always(() => {
                this.loading = false;
            });
        },
        fetchCheckRecordByShipName() {
            this.loading = true;

            $.get(global.IP + "/web/getCheckRecordByShipName?shipName=" + this.shipName, (response, status) => {
                try {
                    if (!response || !response.data) {
                        setAlertWindowShow('search-container', '未查到详细信息', '', 2);
                        return;
                    }

                    const shipData = response.data;

                    ////执法检查信息
                    this.ship.dEPT_CNAMES = shipData.dEPT_CNAMES || '-';
                    this.ship.cHECK_PLACE = shipData.cHECK_PLACE || '-';
                    this.ship.sIGN_DATE = shipData.sIGN_DATE || '-';
                    this.ship.oTHER_CHECK_CONTENT = shipData.oTHER_CHECK_CONTENT || '-';
                    this.ship.sUOBJ_OPINION = shipData.sUOBJ_OPINION || '-';
                    this.ship.eNF_LE_NAMES = shipData.eNF_LE_NAMES || '-';
                    this.ship.cHECK_CONTENT = shipData.cHECK_CONTENT || '-';
                    const otherContent = shipData.oTHER_CHECK_CONTENT || '-';
                    const handlOpinions = shipData.hANDL_OPINIONS || '-';
                    this.ship.oTHER_CHECK_CONTENT = `${otherContent} ${handlOpinions}`;

                } catch (error) {
                    console.error("数据处理错误:", error);
                    setAlertWindowShow('search-container', '数据处理出错', '', 2);
                }
            }, 'json').fail(function (msg) {
                console.log("请求失败：" + JSON.stringify(msg));
            }).always(() => {
                this.loading = false;
            });
        },
        goBack() {
            this.$router.go(-1);
            setTimeout(() => {
                window.location.reload();
            }, 300);
        },
        setActiveTab(tabId) {
            this.activeTab = tabId;
            // 在移动端选择后自动关闭菜单
            if (window.innerWidth < 768) {
                this.menuVisible = false;
            }
        },
        toggleMenu() {
            this.menuVisible = !this.menuVisible;
        }
    }
};
</script>

<style scoped>
.ship-detail {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
}

.mobile-menu-header {
    display: none;
    position: relative;
    background-color: #1890ff;
    height: 40px;
    align-items: center;
    justify-content: flex-end;
    padding: 8px 20px 8px 0;
}

@media (max-width: 768px) {
    .mobile-menu-header {
        display: flex;
    }
}

.menu-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: white;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    position: relative;
    right: 0;
}

.back-btn {
    position: absolute;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    cursor: pointer;
    width: 30px;
    height: 30px;
    padding: 0;
}

.back-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    border-top: 2px solid white;
    border-left: 2px solid white;
    transform: translate(-50%, -50%) rotate(-45deg);
}

.menu-btn:hover,
.back-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.detail-layout {
    display: flex;
    flex: 1;
    background-color: white;
}

/* 左侧菜单样式 */
.menu-sidebar {
    width: 160px;
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
}

.sidebar-header {
    display: none;
    padding: 15px;
    background-color: #1890ff;
    color: white;
    justify-content: space-between;
    align-items: center;
}

.close-menu-btn {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
}

.menu-item {
    padding: 12px 15px;
    cursor: pointer;
    border-bottom: 1px solid #e9ecef;
    font-size: 14px;
    transition: all 0.2s;
}

.menu-item:hover {
    background-color: #e9ecef;
}

.menu-item.active {
    background-color: #1890ff;
    color: white;
    font-weight: bold;
}

/* 右侧内容区域 */
.content-area {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: white;
}

.info-section {
    margin-bottom: 20px;
}

.info-section h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #1890ff;
    color: #333;
    font-size: 18px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-top: 15px;
}

.info-item {
    display: flex;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
}

.label {
    font-weight: bold;
    color: #555;
    min-width: 120px;
    font-size: 14px;
}

.value {
    color: #333;
    flex: 1;
    font-size: 14px;
}

.placeholder {
    text-align: center;
    padding: 20px;
    color: #999;
    font-style: italic;
    border: 1px dashed #ddd;
    border-radius: 8px;
    margin-top: 20px;
}

.main-content {
    min-height: calc(100vh - 56px - 70px);
    padding-bottom: 70px;
    background: #fff;
    overflow-y: auto;
}

.section {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    text-align: left;
    background: #fff;
}

.terminal-info {
    background: #fafbfc;
}

.terminal-block {
    margin-bottom: 18px;
    padding-bottom: 10px;
    border-bottom: 1px dashed #e0e0e0;
}

.terminal-title {
    font-weight: bold;
    color: black;
    margin-bottom: 4px;
}

.footer-btns {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #e3f2fd;
  display: flex;
  justify-content: flex-start;
  padding: 10px 0 10px 16px;
  border-top: 1px solid #1890ff;
  z-index: 20;
}
.footer-btns button {
  background: #1890ff;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 10px 22px;
  font-size: 16px;
  font-weight: bold;
  box-shadow: 0 2px 6px rgba(24,144,255,0.08);
  cursor: pointer;
  transition: background 0.2s;
  margin-right: 16px;
}
.footer-btns button:active {
  background: #1890ff;
}

/* 移动端响应式设计 */
@media (max-width: 768px) {
    .menu-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100%;
        width: 50%;
        max-width: 220px;
        z-index: 1000;
        transform: translateX(-100%);
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    }

    .menu-sidebar.mobile-visible {
        transform: translateX(0);
    }

    .sidebar-header {
        display: flex;
        padding: 10px 15px;
    }

    .content-area {
        padding: 15px;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .label {
        min-width: 110px;
    }
}

/* 平板设备优化 */
@media (min-width: 769px) and (max-width: 1024px) {
    .menu-sidebar {
        width: 140px;
    }

    .menu-item {
        padding: 10px 12px;
        font-size: 13px;
    }

    .content-area {
        padding: 15px;
    }

    .info-grid {
        gap: 12px;
    }

    .label {
        min-width: 100px;
        font-size: 13px;
    }

    .value {
        font-size: 13px;
    }
}
</style>