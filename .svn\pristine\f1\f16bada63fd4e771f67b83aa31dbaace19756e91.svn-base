package com.bd.service.impl;
import com.bd.entity.*;
import com.bd.entity.BusinessManagement.AlarmRecord;
import com.bd.mapper.PortMapper;
import com.bd.service.PortService;
import com.bd.util.Utils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
@Service
public class PortImpl implements PortService {
    @Resource
    private PortMapper portMapper;
    @Override
    public List<PortInfo> GetAllPortInfo() {
        return portMapper.GetAllPortInfo();
    }
    @Override
    public List<PortInfo> GetPortInfo(int pageNum) {
        return portMapper.GetPortInfo(pageNum);
    }
    @Override
    public int GetPortInfoCount() {
        return portMapper.GetPortInfoCount();
    }
    @Override
    public List<PortInfo> GetOnePortInfo(int portId) {
        return portMapper.GetOnePortInfo(portId);
    }
    @Override
    public void AddPort(String name, int pointCount, String pointStr, String content, int maxshapcount, int windlevel, String navmarkmmsi) {
        portMapper.AddPort(name, pointCount, pointStr, content, maxshapcount, windlevel, navmarkmmsi);
    }
    @Override
    public void EditPort(int portId, String newName, int pointCount, String pointStr, String content, int maxshapcount, int windlevel, String navmarkmmsi) {
        portMapper.EditPort(portId, newName, pointCount, pointStr, content, maxshapcount, windlevel, navmarkmmsi);
    }
    @Override
    public void DeletePort(int portId) {
        portMapper.DeletePort(portId);
    }
    @Override
    public List<OutInPortRecord> GetOutInPortRecord() {
        long time = Utils.GetNowTimelong() - 3600*24;
        return portMapper.GetOutInPortRecord(time);
    }
    @Override
    public List<PortShipCount> GetPortShipCount() {
        return portMapper.GetPortShipCount(Utils.GetNowTimeString(7200),Utils.GetNowTimeString(24*60*60));
    }
    @Override
    public PageInfo<FisheryBoatInOutReport> getShipInoutReport(String name,String portname, int status, String startTime, String endTime, int pageNum) {
        return PageHelper.startPage(pageNum, 10).doSelectPageInfo(() -> portMapper.selectShipInoutReport(name, portname, status, startTime, endTime));
    }
    @Override
    public int queryOnePortCountInfo(Port_InPortShipCount info) {
        return portMapper.queryOnePortCountInfo(info);
    }
    @Override
    public void insertOnePortCountInfo(Port_InPortShipCount info) {
        portMapper.insertOnePortCountInfo(info);
    }
    @Override
    public void updateOnePortCountInfo(Port_InPortShipCount info) {
        portMapper.updateOnePortCountInfo(info);
    }
    @Override
    public void InsertOneAlarmInfo(ShipDynamicInfo shipDynamicInfo, int type, String msg, int userId, String areaName) {
        portMapper.InsertOneAlarmInfo(shipDynamicInfo, type, msg, userId, areaName);
    }
    @Override
    public List<AlarmRecord> GetTodayWarningInfo(int userId, String updateTime, int model) {
        if(updateTime.equals("0")) {
            return portMapper.GetTodayWarningAllInfo(userId, Utils.GetTodayTimeString(), model);
        }
        else {
            return portMapper.GetTodayWarningInfo(userId, Utils.GetTodayTimeString(), updateTime, model);
        }
    }
    @Override
    public List<AlarmRecord> GetTodayWarningInfo_noResolve() {
        return portMapper.GetTodayWarningInfo_noResolve(Utils.GetTodayTimeString());
    }
    @Override
    public void UpdateAlarmState(int id, String updateTime) {
        portMapper.UpdateAlarmState(id, updateTime);
    }
    @Override
    public AlarmRecord GetAlarmDetialById(int id, int model) {
        return portMapper.GetAlarmDetialById(id, model);
    }
    @Override
    public List<AlarmRecord> GetAllAlarmRecordInfo(String shipName, String startTime, String endTime, int type, int pageNum, int model) {
        return portMapper.GetAllAlarmRecordInfo(shipName, startTime, endTime, type, pageNum, model);
    }
    @Override
    public List<AlarmRecord> GetAlarmRecordInfo(String shipName, String startTime, String endTime, int type, int model) {
        return portMapper.GetAlarmRecordInfo(shipName, startTime, endTime, type, model);
    }
    @Override
    public int GetAllAlarmRecordInfoCount(String shipName, String startTime, String endTime, int type, int model) {
        return portMapper.GetAllAlarmRecordInfoCount(shipName, startTime, endTime, type, model);
    }
    @Override
    public void DeleteAlarmRecordInfo(int alarmRecordId) {
        portMapper.DeleteAlarmRecordInfo(alarmRecordId);
    }
    @Override
    public List<OutInPortRecord> GetShip_inOrOutPortInfoDetail(String name, String portName, int statue, String startTime, String endTime, int pageNum) {
        return portMapper.GetShip_inOrOutPortInfoDetail(name, portName, statue, startTime, endTime, pageNum);
    }
    @Override
    public List<OutInPortRecord> GetShip_inOrOutPortInfoDetailCount(String name, String portName, int statue, String startTime, String endTime) {
        return portMapper.GetShip_inOrOutPortInfoDetailCount(name, portName, statue, startTime, endTime);
    }
    @Override
    public List<OutInPortRecord> GetAllShip_inOrOutPortInfoDetail(String name, String portName, int statue, String startTime, String endTime) {
        return portMapper.GetAllShip_inOrOutPortInfoDetail(name, portName, statue, startTime, endTime);
    }
    @Override
    public int GetTodayWarningAllStatistics(int userId, String updateTime, int model) {
        if(updateTime.equals("0")) {
            return portMapper.GetTodayWarningAllStatistics(userId, Utils.GetTodayTimeString(), model);
        }
        else {
            return portMapper.GetTodayWarningAllStatisticsByUpdateTime(userId, Utils.GetTodayTimeString(), updateTime, model);
        }
    };
    @Override
    public List<WarningInfo> GetTodayWarningNewUpdateTime(int userId, String updateTime, int model) {
        if(updateTime.equals("0")) {
            return portMapper.GetTodayWarningNewUpdateTimeByZero(userId, Utils.GetTodayTimeString(), model);
        }
        else {
            System.out.println(Utils.GetTodayTimeString());
            System.out.println(updateTime);
            System.out.println(portMapper.GetTodayWarningNewUpdateTime(userId, Utils.GetTodayTimeString(), updateTime, model));
            return portMapper.GetTodayWarningNewUpdateTime(userId, Utils.GetTodayTimeString(), updateTime, model);
        }
    }
    @Override
    public int GetTodayWarningPortAllInfo(int userId, int model) {
        return portMapper.GetTodayWarningPortAllInfo(userId, Utils.GetTodayTimeString(), model);
    };
    @Override
    public int GetTodayWarningPortUntreatedCountInfo(int userId, int model) {
        return portMapper.GetTodayWarningPortUntreatedCountInfo(userId, Utils.GetTodayTimeString(), model);
    };
    @Override
    public int GetTodayWarningFishCountInfo(int userId, int model) {
        return portMapper.GetTodayWarningFishCountInfo(userId, Utils.GetTodayTimeString(), model);
    };
    @Override
    public int GetTodayWarningFishUntreatedCountInfo(int userId, int model) {
        return portMapper.GetTodayWarningFishUntreatedCountInfo(userId, Utils.GetTodayTimeString(), model);
    };
    @Override
    public int GetTodayWarningCrewCountInfo(int userId, int model) {
        return portMapper.GetTodayWarningCrewCountInfo(userId, Utils.GetTodayTimeString(), model);
    };
    @Override
    public int GetTodayWarningCrewUntreatedCountInfo(int userId, int model) {
        return portMapper.GetTodayWarningCrewUntreatedCountInfo(userId, Utils.GetTodayTimeString(), model);
    }
    @Override
    public int GetTodayWarningFishCountInfo2(int userId, int model) {
        //System.out.println("--------------------------------------");
        //System.out.println(portMapper.GetTodayWarningFishCountInfo2(userId, Utils.GetTodayTimeString(), model));
        return portMapper.GetTodayWarningFishCountInfo2(userId, Utils.GetTodayTimeString(), model);
    }
    @Override
    public int GetTodayWarningFishUntreatedCountInfo2(int userId, int model) {
        return portMapper.GetTodayWarningFishUntreatedCountInfo2(userId, Utils.GetTodayTimeString(), model);
    }
    @Override
    public TongjiModel GetAllAlarmCount_month(int model) {
        return portMapper.GetAllAlarmCount_month(Utils.GetNowTimeString_year(), Utils.GetNowTimeString_month(), model);
    }
    @Override
    public TongjiModel GetAllAlarmCount_year(int model) {
        return portMapper.GetAllAlarmCount_year(model);
    }
    @Override
    public TongjiModel GetFXAlarmCount_month(int model) {
        return portMapper.GetFXAlarmCount_month(Utils.GetNowTimeString_year(), Utils.GetNowTimeString_month(), model);
    }
    @Override
    public TongjiModel GetFXAlarmCount_year(int model) {
        return portMapper.GetFXAlarmCount_year(model);
    }
    @Override
    public TongjiModel GetJJAlarmCount_month(int model) {
        return portMapper.GetJJAlarmCount_month(Utils.GetNowTimeString_year(), Utils.GetNowTimeString_month(), model);
    }
    @Override
    public TongjiModel GetJJAlarmCount_year(int model) {
        return portMapper.GetJJAlarmCount_year(model);
    }
    @Override
    public TongjiModel GetZYAlarmCount_month(int model) {
        return portMapper.GetZYAlarmCount_month(Utils.GetNowTimeString_year(), Utils.GetNowTimeString_month(), model);
    }
    @Override
    public TongjiModel GetZYAlarmCount_year(int model) {
        return portMapper.GetZYAlarmCount_year(model);
    }
    @Override
    public TongjiModel GetTXAlarmCount_month() {
        return portMapper.GetTXAlarmCount_month(Utils.GetNowTimeString_year(), Utils.GetNowTimeString_month());
    }
    @Override
    public TongjiModel GetTXAlarmCount_year() {
        return portMapper.GetTXAlarmCount_year();
    }
    @Override
    public TongjiModel GetZFAlarmCount_month() {
        return portMapper.GetZFAlarmCount_month(Utils.GetNowTimeString_year(), Utils.GetNowTimeString_month());
    }
    @Override
    public TongjiModel GetZFAlarmCount_year() {
        return portMapper.GetZFAlarmCount_year();
    }
    // 视频
    @Override
    public String GetHlsImg(int id, int portId) {
        return portMapper.GetHlsImg(id, portId);
    }
    @Override
    public List<CameraInfo> GetCameraInfo() {
        return portMapper.GetCameraInfo();
    }
    @Override
    public int GetCameraInfoPortCount() {
        return portMapper.GetCameraInfoPortCount();
    }
    @Override
    public List<String> GetCameraIndexByPortId(int portId) {
        return portMapper.GetCameraIndexByPortId(portId);
    }
    @Override
    public String GetHlsById(int id) {
        return portMapper.GetHlsById(id);
    }
    @Override
    public List<CameraInfo> GetCameraInfoBySearch(String searchName) {
        return portMapper.GetCameraInfoBySearch(searchName);
    }
    @Override
    public List<AlarmRecord> GetHistoryWarningInfo(int type, String startTime, String endTime, int model) {
        return portMapper.GetHistoryWarningInfo(type, startTime, endTime, model);
    }
    @Override
    public AlarmRecord GetWarningShipInfoByShipId(int id, int userId, int model) {
        return portMapper.GetWarningShipInfoByShipId(id, userId, Utils.GetTodayTimeString(), model);
    }
    @Override
    public List<PortShipInfo> GetShipInfoByPort(int portId) {
        return portMapper.GetShipInfoByPort(portId, Utils.GetNowTimeString(24*60*60));
    }
    @Override
    public List<PortShipInfo> GetAllShipInfo() {
        return portMapper.GetAllShipInfo();
    }
    @Override
    public List<PortShipInfo> GetOnLineShipInfo() {
        return portMapper.GetOnLineShipInfo(Utils.GetNowTimeString(24*3600));
    }
    @Override
    public List<PortShipInfo> GetOutLineShipInfo(int type) {
        List<PortShipInfo> portShipInfoList = new ArrayList<>();
        switch (type) {
            case 2:
                portShipInfoList = portMapper.GetOutLineShipInfo(0, Utils.GetNowTimeString(26*3600), Utils.GetNowTimeString(24*3600));
                break;
            case 12:
                portShipInfoList = portMapper.GetOutLineShipInfo(0, Utils.GetNowTimeString(36*3600), Utils.GetNowTimeString(26*3600));
                break;
            case 24:
                portShipInfoList = portMapper.GetOutLineShipInfo(0, Utils.GetNowTimeString(48*3600), Utils.GetNowTimeString(36*3600));
                break;
            case 48:
                portShipInfoList = portMapper.GetOutLineShipInfo(0, Utils.GetNowTimeString(72*3600), Utils.GetNowTimeString(48*3600));
                break;
            case 99:
                portShipInfoList = portMapper.GetOutLineShipInfo(1, Utils.GetNowTimeString(72*3600), Utils.GetNowTimeString(24*3600));
                break;
        }
        return portShipInfoList;
    }
    @Override
    public List<PortShipInfo> GetOutPortShipInfo() {
        return portMapper.GetOutPortShipInfo(Utils.GetNowTimeString(24*3600));
    }
    @Override
    public List<PortShipInfo> GetInPortShipInfo(int portId) {
        return portMapper.GetInPortShipInfo(Utils.GetNowTimeString(24*3600), portId);
    }
    @Override
    public List<PortShipInfo> GetWSInShangHaiShipInfo() {
        return portMapper.GetWSInShangHaiShipInfo(Utils.GetNowTimeString(24*3600));
    }
    @Override
    public List<PortShipInfo> GetWSInJinBuShipInfo() {
        return portMapper.GetWSInJinBuShipInfo(Utils.GetNowTimeString(24*3600));
    }
    @Override
    public List<PortShipInfo> GetWSInPortShipInfo(int portId) {
        return portMapper.GetWSInPortShipInfo(Utils.GetNowTimeString(24*3600), portId);
    }
    @Override
    public List<PortShipInfo> GetWSIn168169ShipInfo() {
        return portMapper.GetWSIn168169ShipInfo(Utils.GetNowTimeString(24*3600));
    }
    @Override
    public int GetWarningStatistics(int isCheck, int userId, int type, int model) {
        if(isCheck == 0) {
            return portMapper.GetWarningStatistics(userId, type, Utils.GetNowdayTimeString(), model);
        }
        else {
            return portMapper.GetWarningStatistics(userId, type, Utils.GetNowMonthTimeString(), model);
        }
    }
    @Override
    public int GetInPortShipCount(int id) {
        return portMapper.GetInPortShipCount(id, Utils.GetNowTimeString(24*3600));
    }
    @Override
    public List<ShipDynamicInfo> GetInportShip() {
        return portMapper.GetInportShip();
    }
    @Override
    public void updateInportState(int staticShipId) {
        portMapper.updateInportState(staticShipId);
    }
    @Override
    public void updateInportState2(int staticShipId, int id) {
        portMapper.updateInportState2(staticShipId, id);
    }
    @Override
    public List<ShipDynamicInfo> GetUpdateShip() {
        return portMapper.GetUpdateShip();
    }
    @Override
    public List<PortShipInfo> GetFishAreaShipInfo(int areaName) {
        return portMapper.GetFishAreaShipInfo(areaName);
    }
    @Override
    public int GetOutPortCount() {
        long time = Utils.GetNowTimelong() - 3600*24;
        return portMapper.GetOutPortCount(time);
    }
    @Override
    public int GetInPortCount() {
        long time = Utils.GetNowTimelong() - 3600*24;
        return portMapper.GetInPortCount(time);
    }
    @Override
    public int getTyphoonAlarm(ShipDynamicInfo shipDynamicInfo, String time) {
        return portMapper.getTyphoonAlarm(shipDynamicInfo, time);
    }
    @Override
    public void UpdateOneAlarmInfo(ShipDynamicInfo shipDynamicInfo, String time) {
        portMapper.UpdateOneAlarmInfo(shipDynamicInfo, time);
    }
    @Override
    public int GetTodayFuxiuAlarmByShipName(String shipname) {
        return portMapper.GetTodayFuxiuAlarmByShipName(shipname);
    }

    @Override
    public List<OutInPortRecord> GetOutInPortRecordByShipId(String shipId, int state) {
        return portMapper.GetOutInPortRecordByShipId(shipId,state);
    }

    @Override
    public List<FisheryBoatInOutReport> get7DaysRecord() {
        return portMapper.select7DayRecord();
    }

    @Override
    public void UpdateOutlineReason(int id, String newOutLineReason) {
        portMapper.UpdateOutlineReason(id, newOutLineReason);
    }

    @Override
    public List<PortShipInfo> GetAllShanghaiShipsInfo() {
        return portMapper.GetAllShanghaiShipsInfo();
    }

}
