-- 为USERINFO表添加SHIP_QUERY_AREAS字段的SQL脚本
-- 执行日期: 2025-07-29
-- 说明: 添加用户可查询的船舶区域字段，多个区域用逗号分隔

-- 检查字段是否已存在，如果不存在则添加
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'USERINFO' 
    AND TABLE_SCHEMA = 'SHIP' 
    AND COLUMN_NAME = 'SHIP_QUERY_AREAS'
)
BEGIN
    -- 添加SHIP_QUERY_AREAS字段
    ALTER TABLE SHIP.USERINFO 
    ADD SHIP_QUERY_AREAS NVARCHAR(500) NULL;
    
    PRINT '成功添加SHIP_QUERY_AREAS字段到USERINFO表';
END
ELSE
BEGIN
    PRINT 'SHIP_QUERY_AREAS字段已存在，跳过添加';
END

-- 为字段添加注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'用户可查询的船舶区域，多个区域用逗号分隔', 
    @level0type = N'SCHEMA', @level0name = N'SHIP',
    @level1type = N'TABLE', @level1name = N'USERINFO',
    @level2type = N'COLUMN', @level2name = N'SHIP_QUERY_AREAS';

-- 验证字段添加结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'USERINFO' 
AND TABLE_SCHEMA = 'SHIP' 
AND COLUMN_NAME = 'SHIP_QUERY_AREAS';

-- 查看表结构
SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'USERINFO' 
AND TABLE_SCHEMA = 'SHIP'
ORDER BY ORDINAL_POSITION;
