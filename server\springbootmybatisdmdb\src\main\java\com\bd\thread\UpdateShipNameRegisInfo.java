package com.bd.thread;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bd.entity.ShipNameRegisInfo;
import com.bd.entity.ShipNationalRegisInfo;
import com.bd.mapper.CrewExamMapper;
import com.bd.service.UserService;
import com.bd.util.HttpTool;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Controller
@EnableScheduling
public class UpdateShipNameRegisInfo {
    @Resource
    private CrewExamMapper crewExamMapper;

    @Resource
    private UserService userService;

    public List<String> GetShipNameRegisInfoList(int pageNum) {
        String token = userService.GetOpenCenterToken();
        System.out.println("-.-" + token);
        String header = "Bearer " + token;
        String url = "http://*********:18080/api/service/share/P431503651282882560?conditions=null&pageNum=" + pageNum + "&pageSize=" + 500;
        String lawData = HttpTool.doGet(url, header);
        JSONArray jsonArray = JSONArray.parseArray(lawData);

        List<String> list = new ArrayList<>();
        for (Object obj : jsonArray) {
            list.add(obj.toString());
        }
        return list;
    }

    public void ShipNameRegisInfo() throws Exception{
        int pageNum = 1;
        List<String> infoList = new ArrayList<>();
        while (true){
            try{
                infoList = GetShipNameRegisInfoList(pageNum);
            }catch (Exception e){
                continue;
            }

            if(infoList.size() < 2) break;
            List<ShipNameRegisInfo> shipNameRegisInfos = new ArrayList<>();
            for (String shipNameRegisInfoStr : infoList) {
                JSONObject shipNameRegisInfoJson = JSONObject.parseObject(shipNameRegisInfoStr);
                ShipNameRegisInfo shipNameRegisInfo = new ShipNameRegisInfo();
                shipNameRegisInfo.setZjzgl(shipNameRegisInfoJson.getString("zjzgl"));
                shipNameRegisInfo.setYycbcmsqsbh(shipNameRegisInfoJson.getString("yycbcmsqsbh"));
                shipNameRegisInfo.setYyyblxkzbh(shipNameRegisInfoJson.getString("yyyblxkzbh"));
                shipNameRegisInfo.setYcm(shipNameRegisInfoJson.getString("ycm"));
                shipNameRegisInfo.setCbsyrdz(shipNameRegisInfoJson.getString("cbsyrdz"));
                shipNameRegisInfo.setYyxmpzwh(shipNameRegisInfoJson.getString("yyxmpzwh"));
                shipNameRegisInfo.setCbzl(shipNameRegisInfoJson.getString("cbzl"));
                shipNameRegisInfo.setSqrdz(shipNameRegisInfoJson.getString("sqrdz"));
                shipNameRegisInfo.setPzwh(shipNameRegisInfoJson.getString("pzwh"));
                shipNameRegisInfo.setCblxyw(shipNameRegisInfoJson.getString("cblxyw"));
                shipNameRegisInfo.setCbsyrmc(shipNameRegisInfoJson.getString("cbsyrmc"));
                shipNameRegisInfo.setYcssdqmc(shipNameRegisInfoJson.getString("ycssdqmc"));
                shipNameRegisInfo.setZxhzzdjzmsbh(shipNameRegisInfoJson.getString("zxhzzdjzmsbh"));
                shipNameRegisInfo.setCblx(shipNameRegisInfoJson.getString("cblx"));
                shipNameRegisInfo.setGxcz(shipNameRegisInfoJson.getString("gxcz"));
                shipNameRegisInfo.setCm(shipNameRegisInfoJson.getString("cm"));
                shipNameRegisInfo.setSqrxm(shipNameRegisInfoJson.getString("sqrxm"));
                shipNameRegisInfo.setCbsyrdh(shipNameRegisInfoJson.getString("cbsyrdh"));
                shipNameRegisInfo.setCtczyw(shipNameRegisInfoJson.getString("ctczyw"));
                shipNameRegisInfo.setYycbcmdjbahzsbh(shipNameRegisInfoJson.getString("yycbcmdjbahzsbh"));
                shipNameRegisInfo.setCz(shipNameRegisInfoJson.getString("cz"));
                shipNameRegisInfo.setCmdjxxwybs(shipNameRegisInfoJson.getString("cmdjxxwybs"));
                shipNameRegisInfo.setSqrq(shipNameRegisInfoJson.getString("sqrq"));
                shipNameRegisInfo.setCbsyrmcyw(shipNameRegisInfoJson.getString("cbsyrmcyw"));
                shipNameRegisInfo.setCtcz(shipNameRegisInfoJson.getString("ctcz"));
                shipNameRegisInfo.setCwgjzbpzsbh(shipNameRegisInfoJson.getString("cwgjzbpzsbh"));
                shipNameRegisInfo.setCmyw(shipNameRegisInfoJson.getString("cmyw"));
                //shipNameRegisInfo.setCzsm(shipNameRegisInfoJson.getString("czsm"));
                shipNameRegisInfo.setSqrdh(shipNameRegisInfoJson.getString("sqrdh"));
                shipNameRegisInfo.setSqrjmsfzhmhgszch(shipNameRegisInfoJson.getString("sqrjmsfzhmhgszch"));
                shipNameRegisInfo.setZdw(shipNameRegisInfoJson.getString("zdw"));
                shipNameRegisInfo.setCbsyrdzyw(shipNameRegisInfoJson.getString("cbsyrdzyw"));
                shipNameRegisInfo.setGxsj(shipNameRegisInfoJson.getString("gxsj"));
                shipNameRegisInfo.setYcxxwybs(shipNameRegisInfoJson.getString("ycxxwybs"));
                shipNameRegisInfo.setYcssdqdm(shipNameRegisInfoJson.getString("ycssdqdm"));
                shipNameRegisInfo.setEtl_dt(shipNameRegisInfoJson.getString("etl_dt"));

                shipNameRegisInfos.add(shipNameRegisInfo);
            }
            System.out.println("updateshipNameRegisInfo-pageNum:" + pageNum);
            //Thread.sleep(5000);
            pageNum ++;

            crewExamMapper.insertShipNameRegisInfo(shipNameRegisInfos);

        }
    }

    //@Scheduled(cron = "0 0 1 * * ?")
    public void run() throws Exception {
        crewExamMapper.deleteShipNameRegisInfo();
        ShipNameRegisInfo();
    }
}
