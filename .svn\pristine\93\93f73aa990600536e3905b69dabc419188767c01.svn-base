package com.bd.entity.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 船员信息查询封装对象
 */
@Data
public class PeopleQueryDto extends PageQueryDto {
    private String name;
    private String idcard;
    private String shipName;
    private String fzrq;
    /**
     * 证书类别
     */
    private String zslbmc;
    /**
     * 证书种类
     */
    private String zszlmc;
    private String qfjg;
    private String zszt;
    private String sqlb;
    private String zsdj;
    private String qfgy;
    private String zshm;
    private String dahm;
    private String qfgymc;
    private String startQfrq;
    private String endQfrq;
}
