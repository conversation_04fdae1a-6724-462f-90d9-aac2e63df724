<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bd.mapper.PeopleMapper">
    <insert id="InsertLawRecordInfo">
        INSERT INTO SHIP.LAWRECORDINFO(ALARMID, LAWPEOPLEID, NAME, COURSE, RESULT, LOADTIME, ALARMTYPE)
        VALUES (
                #{lawRecordInfo.ALARMID},
                #{lawRecordInfo.LAWPEOPLEID},
                #{lawRecordInfo.NAME},
                #{lawRecordInfo.COURSE},
                #{lawRecordInfo.RESULT},
                #{lawRecordInfo.LOADTIME},
                #{lawRecordInfo.ALARMTYPE}
               );
    </insert>
    <insert id="InsertBdMsg">
        INSERT INTO SHIP.BDSENDRECORD(STATICSHIPID, BDID, MSG, USERID, BSEND, LOADTIME, SENDTYPE)
        VALUES(
               #{bdMsg.staticShipId},
               #{bdMsg.bdId},
               #{bdMsg.msg},
               #{bdMsg.userId},
               0,
               #{bdMsg.loadTime},
               #{bdMsg.sendType}
            );

    </insert>

    <select id="GetBdMsg" resultType="com.bd.entity.BdMsg">
        select u.name, b.*, s.SHIPNAME as shipName, s.BDID as bdId, s.OWNER as owner,
               s.LXDH as lxdh, s.LXDZ as lxdz
        from SHIP.BDSENDRECORD b left join SHIP.SHIP_STATICINFO s
        on b.STATICSHIPID = s.ID
        left join SHIP.USERINFO u on b.userId = u.id
            where (s.shipName like concat ('%',#{shipName},'%')
                or IFNULL(b.bdId,'-') like concat ('%',#{bdId},'%'))
                and b.LOADTIME > #{startTime}
                and b.LOADTIME &lt; #{endTime}
                order by b.LOADTIME desc
            limit #{pageNum}, 10;
    </select>

    <select id="GetBdMsgCount" resultType="java.lang.Integer">
        select count (*)
        from SHIP.BDSENDRECORD b left join SHIP.SHIP_STATICINFO s
        on b.STATICSHIPID = s.ID
        where (s.shipName like concat ('%',#{shipName},'%')
          or IFNULL(b.bdId,'-') like concat ('%',#{bdId},'%'))
          and b.LOADTIME > #{startTime}
          and b.LOADTIME &lt; #{endTime};
    </select>

    <select id="GetAllBdMsg" resultType="com.bd.entity.BdMsg">
        select u.name, b.*, s.SHIPNAME as shipName, s.BDID as bdId, s.OWNER as owner,
               s.LXDH as lxdh, s.LXDZ as lxdz
        from SHIP.BDSENDRECORD b left join SHIP.SHIP_STATICINFO s
                                           on b.STATICSHIPID = s.ID
                                 left join SHIP.USERINFO u on b.userId = u.id
        where s.shipName like concat ('%',#{shipName},'%')
          and IFNULL(b.bdId,'-') like concat ('%',#{bdId},'%')
          and b.LOADTIME > #{startTime}
          and b.LOADTIME &lt; #{endTime}
        order by b.LOADTIME desc;
    </select>

    <select id="GetAllBdMsgByUserId" resultType="com.bd.entity.BdMsg">
        select u.name, b.*, s.SHIPNAME as shipName, s.BDID as bdId, s.OWNER as owner,
               s.LXDH as lxdh, s.LXDZ as lxdz
        from SHIP.BDSENDRECORD b
        left join SHIP.SHIP_STATICINFO s on b.STATICSHIPID = s.ID
        left join SHIP.USERINFO u on b.userId = u.id
        where (b.USERID = #{userId} or b.USERID = 0)
        <if test="shipId != -1">
          and b.STATICSHIPID  = #{shipId}
        </if>
        order by b.loadtime asc
    </select>

    <delete id="DeleteBdMsg">
        delete
        from SHIP.BDSENDRECORD
        where ID = #{id};
    </delete>

    <insert id="InsertCrewInfo" parameterType="list">
        INSERT INTO SHIP.PEOPLE ("NAME", "IDCARD", "SEX", "CERTIFICATETYPE",
                                 "CERTIFICATENAME", "CERTIFICATETIME", "CERTIFICATESTATE",
                                 "ORGANIZATION", "ZHIWU", "ZSDJMC", "SYCB", "ZSHM", "QFRQ", "QFGYXM", "DAHM","IDCARDSIGNCODE") values
        <foreach collection="list" item="it" separator=",">
            (#{it.NAME},#{it.IDCARD},#{it.SEX},#{it.CERTIFICATETYPE},
             #{it.CERTIFICATENAME},#{it.CERTIFICATETIME},#{it.CERTIFICATESTATE},
             #{it.ORGANIZATION},#{it.ZHIWU},#{it.ZSDJMC},#{it.SYCB},#{it.ZSHM},
             #{it.QFRQ}, #{it.QFGYXM}, #{it.DAHM}, #{it.IDCARDSIGNCODE})
        </foreach>
    </insert>

    <insert id="InsertBdMsgExample">
        INSERT INTO SHIP.BDMSG_EXAMPLE (content, userId) values (#{content}, #{userId})
    </insert>

    <select id="GetAllPeopleCount" resultType="java.lang.Integer">
        select count(*) from (
            select *, row_number() over (partition by p1.name order by p1.CERTIFICATETIME desc) as num
            from ship.people p1
            where p1.CERTIFICATETIME = (SELECT MAX(CERTIFICATETIME) FROM ship.people WHERE p1.name = name)
            <if test="zszt != null and zszt != '' and zszt == 1">
                and CERTIFICATESTATE = '1'
            </if>
            <if test="zszt != null and zszt != '' and zszt != 1">
                and CERTIFICATESTATE != '1'
            </if>
        order by p1.NAME desc
        )A where
                A.num = 1
            and
                A.NAME like concat('%',#{name},'%')
            and
                A.IDCARD like concat('%',#{idcard},'%')
            and
                A.QFRQ like concat('%',#{fzrq},'%')
             and
                A.CERTIFICATETYPE like concat('%',#{zslbmc},'%')
             and
                A.CERTIFICATENAME like concat('%',#{zszlmc},'%')
             and A.ORGANIZATION like concat('%',#{qfjg},'%')
             and A.CERTIFICATETYPE like concat('%',#{sqlb},'%')
             and A.ZSHM like  concat('%',#{zshm},'%')
             and A.ZSDJMC like  concat('%',#{zsdj},'%')
            and A.DAHM like concat('%',#{dahm},'%')
            and A.QFGYXM like concat('%',#{qfgy},'%')
            <if test="startQfrq != null and startQfrq != ''">
                and A.QFRQ &gt;= #{startQfrq}
            </if>
            <if test="endQfrq != null and endQfrq != ''">
                and A.QFRQ &lt;= #{endQfrq}
            </if>
    </select>

    <select id="GetAllPeople" resultType="com.bd.entity.People">
        select * from (
            select *, row_number() over (partition by p1.name order by p1.CERTIFICATETIME desc) as num  from ship.people p1
            where p1.CERTIFICATETIME = (SELECT MAX(CERTIFICATETIME) FROM ship.people WHERE p1.name = name)
            <if test="zszt != null and zszt != '' and zszt == 1">
                and CERTIFICATESTATE = '1'
            </if>
            <if test="zszt != null and zszt != '' and zszt != 1">
                and CERTIFICATESTATE != '1'
            </if>
            order by p1.NAME desc
        )A where
            A.num = 1
             and
            A.NAME like concat('%',#{name},'%')
             and
            A.IDCARD like concat('%',#{idcard},'%')
             and
            A.QFRQ like concat('%',#{fzrq},'%')
            and
            A.CERTIFICATETYPE like concat('%',#{zslbmc},'%')
             and
            A.CERTIFICATENAME like concat('%',#{zszlmc},'%')
            and A.ORGANIZATION like concat('%',#{qfjg},'%')
            and A.CERTIFICATETYPE like concat('%',#{sqlb},'%')
            and A.ZSHM like concat('%',#{zshm},'%')
            and A.ZSDJMC like concat('%',#{zsdj},'%')
            and A.DAHM like concat('%',#{dahm},'%')
            and A.QFGYXM like concat('%',#{qfgy},'%')
            <if test="startQfrq != null and startQfrq != ''">
                and A.QFRQ &gt;= #{startQfrq}
            </if>
            <if test="endQfrq != null and endQfrq != ''">
                and A.QFRQ &lt;= #{endQfrq}
            </if>
        limit #{pageNum}, 10;
    </select>
    <select id="GetAllPeople_Export" resultType="com.bd.entity.People">
        select * from (
        select *, row_number() over (partition by p1.name order by p1.CERTIFICATETIME desc) as num  from ship.people p1
        where p1.CERTIFICATETIME = (SELECT MAX(CERTIFICATETIME) FROM ship.people WHERE p1.name = name)
        <if test="zszt != null and zszt != '' and zszt == 1">
            and CERTIFICATESTATE = '1'
        </if>
        <if test="zszt != null and zszt != '' and zszt != 1">
            and CERTIFICATESTATE != '1'
        </if>
        order by p1.NAME desc
        )A where
        A.num = 1
        and
        A.NAME like concat('%',#{name},'%')
        and
        A.IDCARD like concat('%',#{idcard},'%')
        and
        A.QFRQ like concat('%',#{fzrq},'%')
        and
        A.CERTIFICATETYPE like concat('%',#{zslbmc},'%')
        and
        A.CERTIFICATENAME like concat('%',#{zszlmc},'%')
        and A.ORGANIZATION like concat('%',#{qfjg},'%')
        and A.CERTIFICATETYPE like concat('%',#{sqlb},'%')
        and A.ZSHM like concat('%',#{zshm},'%')
        and A.ZSDJMC like concat('%',#{zsdj},'%')
        and A.DAHM like concat('%',#{dahm},'%')
        and A.QFGYXM like concat('%',#{qfgy},'%')
        <if test="startQfrq != null and startQfrq != ''">
            and A.QFRQ &gt;= #{startQfrq}
        </if>
        <if test="endQfrq != null and endQfrq != ''">
            and A.QFRQ &lt;= #{endQfrq}
        </if>
    </select>

    <select id="GetLawRecordTodayWarning" resultType="com.bd.entity.LawRecordInfo">
        select l.*, a.shipname,a.lon,a.lat,a.content from SHIP.LAWRECORDINFO l
        INNER JOIN SHIP.ALARMRECORDINFO a on l.alarmid = a.id
        where l.loadTime > #{time}
        <if test="model == 0">
            and a.MODEL = #{model}
        </if>
        <if test="model == 1">
            and a.MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and a.MODEL in (#{model}, 0)
        </if>
        ORDER BY l.loadTime DESC
    </select>
    <select id="GetLawRecordInfoById" resultType="com.bd.entity.LawRecordInfo">
        select l.*, a.shipname,a.lon,a.lat,a.content from SHIP.LAWRECORDINFO l
        INNER JOIN SHIP.ALARMRECORDINFO a on l.alarmid = a.id
        where l.id = #{id}
        <if test="model == 0">
            and a.MODEL = #{model}
        </if>
        <if test="model == 1">
            and a.MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and a.MODEL in (#{model}, 0)
        </if>
    </select>
    <select id="GetAllLawRecordInfo" resultType="com.bd.entity.LawRecordInfo">
        SELECT * FROM SHIP.LAWRECORDINFO
        WHERE NAME like concat('%',#{name},'%')
        and LOADTIME &gt; #{startTime}
        and LOADTIME &lt; #{endTime}
        ORDER BY loadTime DESC
        limit #{pageNum}, 10;
    </select>

    <select id="GetLawRecordInfo" resultType="com.bd.entity.LawRecordInfo">
        SELECT * FROM SHIP.LAWRECORDINFO
        WHERE NAME like concat('%',#{name},'%')
          and LOADTIME &gt; #{startTime}
          and LOADTIME &lt; #{endTime}
        ORDER BY loadTime DESC
    </select>

    <delete id="DeleteLawRecordInfo">
        DELETE FROM SHIP.LAWRECORDINFO WHERE ID = ${id};
    </delete>
    <delete id="DeleteBdMsgExample">
        DELETE FROM SHIP.BDMSG_EXAMPLE WHERE ID = #{id}
    </delete>
    <delete id="ClearCrewInfo">
        DELETE FROM SHIP.PEOPLE;
    </delete>

    <select id="GetAllLawRecordInfoCount" resultType="java.lang.Integer">
        SELECT count(*) FROM SHIP.LAWRECORDINFO
        WHERE NAME like concat('%',#{name},'%')
          and LOADTIME &gt; #{startTime}
          and LOADTIME &lt; #{endTime}
    </select>
    <select id="GetPeopleByShipId" resultType="com.bd.entity.People">
        SELECT * from ship.people where staticshipid = #{shipId} limit #{pageNum}, 10;
    </select>
    <select id="GetPeopleByShipIdCount" resultType="java.lang.Integer">
        SELECT count(id) from ship.people where staticshipid = #{shipId}
    </select>
    <select id="GetLawPeopleInfo" resultType="com.bd.entity.LawPeople">
        SELECT * from ship.lawpeople
    </select>
    <select id="GetBdMsgExample" resultType="com.bd.entity.BDMsgExample">
        SELECT * FROM SHIP.BDMSG_EXAMPLE WHERE USERID = #{userId}
    </select>
    <select id="GetPeopleByShipName" resultType="java.lang.String">
        select insuredidentifynumber as name from ship.crew_bx
        where shipcname like concat('%',#{shipName},'%')
    </select>
    <select id="GetPeopleByShipNameCount" resultType="java.lang.Integer">
        select count(id) from ship.people
        where
        IDCARD in (
        <foreach collection="idCardList" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>
    <select id="GetPeopleByIdCard" resultType="com.bd.entity.People">
        select * from ship.people
        where
        CERTIFICATESTATE = '1'
        and
        IDCARD in (
        <foreach collection="idCardList" item="item" separator=",">
            #{item}
        </foreach>
        )

    </select>
    <select id="GetPortNodeByShipId" resultType="com.bd.entity.FisheryBoatInOutReport">
        SELECT *
        FROM SHIP.PORTNODE
        WHERE shipname IN (
            SELECT shipname
            FROM ship.ship_staticinfo
            WHERE id = #{shipId}
        )
          and length(CREWSTR) > 0
        <if test="state != null">
            and INOROUT = #{state}
        </if>
        order by TIME desc
        limit 1
    </select>

    <select id="GetCrewPeopleByShipId" resultType="com.bd.entity.People">

    </select>
    <select id="GetCrewBxByShipName" resultType="com.bd.entity.CrewBx">
        select c.*, p.CERTIFICATENAME, p.CERTIFICATETIME,p.ZSHM  from ship.crew_bx c
        left join (select * from ship.people where CERTIFICATESTATE = '1' and certificatetime > sysdate) p on p.idcard = c.insuredidentifynumber
        where c.shipcname like concat('%',#{shipName},'%')
        and c.policyno is null
    </select>

    <select id="GetCrewBxByShipNames" resultType="com.bd.entity.CrewBx">
            select c.*, p.CERTIFICATENAME, p.CERTIFICATETIME, p.ZSHM
            from ship.crew_bx c
            left join (select * from ship.people where CERTIFICATESTATE = '1' and certificatetime > sysdate) p
            on p.idcard = c.insuredidentifynumber
            where
            <foreach collection="shipNames" item="shipName" open="(" close=")" separator="or">
                c.shipcname like concat('%', #{shipName}, '%')
            </foreach>
            and c.policyno is null order by c.shipcname asc
    </select>

    <select id="GetlawCaseByShipId" resultType="com.bd.entity.LawCase">
        select * from ship.law_Case
        where
        IDCARD in (
        <foreach collection="idcards" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>

    <select id="GetShipNameByPeople" resultType="java.lang.String">
        SELECT APPLINAME from ship.crew_bx where insuredName = #{keyword}
    </select>
    <select id="GetPersonCount" resultType="java.lang.Integer">
        SELECT nullif(personcount,0)  from ship.crew_bx where shipcname like concat('%',#{shipName},'%') and policyno is not null
        </select>
    <select id="GetApplinameByShipName" resultType="java.lang.String">
        SELECT appliname from ship.crew_bx where shipcname like concat('%',#{shipName},'%') and policyno is not null
    </select>
    <select id="GetPeopleByIdcards" resultType="com.bd.entity.CrewBx">
        select c.*, p.*
        from SHIP.CREW_BX c
        right join (select * from ship.people where CERTIFICATESTATE = '1' and certificatetime > sysdate) p on p.idcard
        = c.insuredidentifynumber
        where c.policyno is null and p.IDCARD in
        <foreach item="item" collection="idcards" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="GetZshmByIdCard" resultType="java.lang.String">
        SELECT ZSHM from ship.people where idcard = #{idcard} order by CERTIFICATETIME desc limit 1
    </select>
    <select id="GetEndTimeByIdCard" resultType="java.lang.String">
        SELECT CERTIFICATETIME from ship.people where idcard = #{idcard} order by CERTIFICATETIME desc limit 1
    </select>
    <select id="GetPortNodeByShipIds" resultType="com.bd.entity.FisheryBoatInOutReport">
        SELECT *
        FROM (
        SELECT *,
        ROW_NUMBER() OVER (PARTITION BY shipname ORDER BY TIME DESC) AS row_num
        FROM SHIP.PORTNODE
        WHERE shipname IN (
        SELECT shipname
        FROM ship.ship_staticinfo
        WHERE id IN
          <foreach item="id" collection="list" open="(" separator="," close=")">
             #{id}
          </foreach>
        )
        AND LENGTH(CREWSTR) > 0
        AND TIME > #{beforeTime}
        ) AS latest_records
        WHERE row_num = 1
    </select>

    <select id="GetCrewCertificateStatistics" resultType="com.bd.entity.People">
        SELECT ID, NAME, IDCARD, SEX, ORGANIZATION, CERTIFICATETYPE, CERTIFICATENAME, ZHIWU, ZSDJMC
        FROM (
                 SELECT ID, NAME, IDCARD, SEX, ORGANIZATION, CERTIFICATETYPE, CERTIFICATENAME, ZHIWU, ZSDJMC,
                        ROW_NUMBER() OVER (PARTITION BY IDCARD ORDER BY QFRQ) as rn
                 FROM SHIP.PEOPLE
                 WHERE char_length(IDCARD) = 18
             ) AS RankedRecords
        WHERE rn = 1;
    </select>

    <update id="UpdateBdMsgExample">
        update SHIP.BDMSG_EXAMPLE set content = #{content} where ID =  #{id} and USERID = #{userId}
    </update>
    <update id="UpdateCrewTimeState">
        update ship.people set certificatestate = '2' where certificatetime &lt; #{time} and certificatestate = '1';
    </update>
    <select id="GetIdcardSignCodeByIdcard" resultType="java.lang.String">
            select IDCARDSIGNCODE from ship.people where idcard = #{idcard}
    </select>



</mapper>