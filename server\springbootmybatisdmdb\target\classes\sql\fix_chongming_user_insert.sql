-- 修复崇明用户插入问题的SQL脚本
-- 执行日期: 2025-07-29
-- 说明: 解决自增列赋值错误，提供两种插入方式

-- ========================================
-- 方案1：自动生成ID（推荐使用）
-- ========================================
-- 不指定ID列，让数据库自动生成自增ID
INSERT INTO SHIP.USERINFO (USERNAME, PASSWORD, NAME, LXDH, LEVEL, TOKEN, BM, ERRORCOUNT, USER_AREA) 
VALUES ('chongming01', '123456', '崇明', null, 1, null, null, 0, null);

-- 查看插入结果
SELECT * FROM SHIP.USERINFO WHERE USERNAME = 'chongming01';

-- ========================================
-- 方案2：指定ID值（如果需要特定ID）
-- ========================================
-- 如果您需要指定特定的ID值，请使用以下方式：

-- 开启IDENTITY_INSERT，允许为自增列赋值
-- SET IDENTITY_INSERT SHIP.USERINFO ON;

-- 插入指定ID的用户数据
-- INSERT INTO SHIP.USERINFO (ID, USERNAME, PASSWORD, NAME, LXDH, LEVEL, TOKEN, BM, ERRORCOUNT, USER_AREA) 
-- VALUES (100, 'chongming01', '123456', '崇明', null, 1, null, null, 0, null);

-- 关闭IDENTITY_INSERT
-- SET IDENTITY_INSERT SHIP.USERINFO OFF;

-- ========================================
-- 错误的插入方式（不要使用）
-- ========================================
-- 以下是导致错误的原因，请避免：
-- INSERT INTO SHIP.USERINFO (ID, USERNAME, PASSWORD, NAME, LXDH, LEVEL, TOKEN, BM, ERRORCOUNT, USER_AREA) 
-- VALUES (null, 'chongming01', '123456', '崇明', null, 1, null, null, 0, null);
-- 错误原因：在未开启IDENTITY_INSERT的情况下为自增列ID指定了值（即使是null）

-- ========================================
-- 验证和清理
-- ========================================
-- 如果需要删除重复或错误的记录：
-- DELETE FROM SHIP.USERINFO WHERE USERNAME = 'chongming01';

-- 查看所有用户记录：
-- SELECT ID, USERNAME, NAME, LEVEL FROM SHIP.USERINFO ORDER BY ID;
