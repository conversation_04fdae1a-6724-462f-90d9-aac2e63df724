package com.bd.mapper;

import com.bd.entity.*;
import com.bd.entity.BusinessManagement.AlarmRecord;
import com.bd.util.Utils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface PortMapper {

    List<PortInfo> GetAllPortInfo();
    List<PortInfo> GetPortInfo(@Param("pageNum")int pageNum);
    int GetPortInfoCount();

    List<PortInfo> GetOnePortInfo(@Param("portId")int portId);
    void AddPort(@Param("name")String name,
                 @Param("pointCount")int pointCount,
                 @Param("pointStr")String pointStr,
                 @Param("content")String content,
                 @Param("maxshapcount")int maxshapcount,
                 @Param("windlevel")int windlevel,
                 @Param("navmarkmmsi")String navmarkmmsi);

    void EditPort(@Param("portId")int portId,
                  @Param("newName")String newName,
                 @Param("pointCount")int pointCount,
                 @Param("pointStr")String pointStr,
                  @Param("content")String content,
                  @Param("maxshapcount")int maxshapcount,
                  @Param("windlevel")int windlevel,
                  @Param("navmarkmmsi")String navmarkmmsi);

    void DeletePort(@Param("portId")int portId);

    List<FisheryBoatInOutReport> selectShipInoutReport(@Param("shipName")String name,
                                           @Param("portName")String portName,
                                           @Param("state")int state,
                                           @Param("startTime")String startTime,
                                           @Param("endTime")String endTime
                                           );

    List<FisheryBoatInOutReport> select7DayRecord();

    List<OutInPortRecord> GetOutInPortRecord(@Param("time") long time);
    List<PortShipCount> GetPortShipCount(@Param("time") String time, @Param("time2") String time2);

    int queryOnePortCountInfo(@Param("info") Port_InPortShipCount info);
    void insertOnePortCountInfo(@Param("info") Port_InPortShipCount info);
    void updateOnePortCountInfo(@Param("info") Port_InPortShipCount info);
    List<OutInPortRecord> GetShip_inOrOutPortInfoDetail(@Param("shipName")String name,
                                                        @Param("portName")String portName,
                                                        @Param("state")int state,
                                                        @Param("startTime")String startTime,
                                                        @Param("endTime")String endTime,
                                                        @Param("pageNum")int pageNum);
    List<OutInPortRecord> GetShip_inOrOutPortInfoDetailCount(@Param("shipName")String name,
                                           @Param("portName")String portName,
                                           @Param("state")int state,
                                           @Param("startTime")String startTime,
                                           @Param("endTime")String endTime);



    List<OutInPortRecord> GetAllShip_inOrOutPortInfoDetail(@Param("shipName")String name,
                                                           @Param("portName")String portName,
                                                           @Param("state")int state,
                                                           @Param("startTime")String startTime,
                                                           @Param("endTime")String endTime);

    //预警
    void InsertOneAlarmInfo(@Param("shipDynamicInfo")ShipDynamicInfo shipDynamicInfo, @Param("type")int type, @Param("msg")String msg, @Param("userId")int userId, @Param("areaName")String areaName);

    // 查询一段时间内未进行进出港报告的预警
    List<AlarmRecord> getUnReportByTimePeriod(@Param("time1") String time1, @Param("time2") String time2);

    List<AlarmRecord> GetTodayWarningInfo(@Param("userId")int userId, @Param("time")String time, @Param("updateTime")String updateTime, @Param("model")int model);
    List<AlarmRecord> GetTodayWarningAllInfo(@Param("userId")int userId, @Param("time")String time, @Param("model")int model);
    List<AlarmRecord> GetTodayWarningInfo_noResolve(@Param("time")String time);
    void UpdateAlarmState(@Param("id")int id, @Param("updateTime")String updateTime);
    AlarmRecord GetAlarmDetialById(@Param("id")int id, @Param("model")int model);

    List<AlarmRecord> GetAllAlarmRecordInfo(@Param("shipName")String name,
                                            @Param("startTime")String startTime,
                                            @Param("endTime")String endTime,
                                            @Param("type")int type,
                                            @Param("pageNum")int pageNum,
                                            @Param("model")int model);

    List<AlarmRecord> GetAlarmRecordInfo(@Param("shipName")String name,
                                            @Param("startTime")String startTime,
                                            @Param("endTime")String endTime,
                                            @Param("type")int type,
                                            @Param("model")int model);

    void DeleteAlarmRecordInfo(@Param("alarmRecordId")int alarmRecordId);

    int GetAllAlarmRecordInfoCount(@Param("shipName")String name,
                                            @Param("startTime")String startTime,
                                            @Param("endTime")String endTime,
                                            @Param("type")int type,
                                            @Param("model")int model);


    int GetTodayWarningAllStatistics(@Param("userId")int userId, @Param("time")String time, @Param("model")int model);

    int GetTodayWarningAllStatisticsByUpdateTime(@Param("userId")int userId, @Param("time")String time, @Param("updateTime")String updateTime, @Param("model")int model);

    List<WarningInfo> GetTodayWarningNewUpdateTimeByZero(@Param("userId")int userId, @Param("time")String time, @Param("model")int model);

    List<WarningInfo> GetTodayWarningNewUpdateTime(@Param("userId")int userId, @Param("time")String time, @Param("updateTime")String updateTime, @Param("model")int model);

    int GetTodayWarningPortAllInfo(@Param("userId")int userId, @Param("time")String time, @Param("model")int model);
    int GetTodayWarningPortUntreatedCountInfo(@Param("userId")int userId, @Param("time")String time, @Param("model")int model);
    int GetTodayWarningFishCountInfo(@Param("userId")int userId, @Param("time")String time, @Param("model")int model);
    int GetTodayWarningFishUntreatedCountInfo(@Param("userId")int userId, @Param("time")String time, @Param("model")int model);
    int GetTodayWarningCrewCountInfo(@Param("userId")int userId, @Param("time")String time, @Param("model")int model);
    int GetTodayWarningCrewUntreatedCountInfo(@Param("userId")int userId, @Param("time")String time, @Param("model")int model);
    int GetTodayWarningFishCountInfo2(@Param("userId")int userId, @Param("time")String time, @Param("model")int model);
    int GetTodayWarningFishUntreatedCountInfo2(@Param("userId")int userId, @Param("time")String time, @Param("model")int model);

    TongjiModel GetAllAlarmCount_year(@Param("model")int model);

    TongjiModel GetAllAlarmCount_month(@Param("year")int year, @Param("month")int month, @Param("model")int model);

    TongjiModel GetFXAlarmCount_month(@Param("year")int year, @Param("month")int month, @Param("model")int model);

    TongjiModel GetFXAlarmCount_year(@Param("model")int model);

    TongjiModel GetJJAlarmCount_month(@Param("year")int year, @Param("month")int month, @Param("model")int model);

    TongjiModel GetJJAlarmCount_year(@Param("model")int model);

    TongjiModel GetZYAlarmCount_month(@Param("year")int year, @Param("month")int month, @Param("model")int model);

    TongjiModel GetZYAlarmCount_year(@Param("model")int model);

    TongjiModel GetTXAlarmCount_month(@Param("year")int year, @Param("month")int month);

    TongjiModel GetTXAlarmCount_year();

    TongjiModel GetZFAlarmCount_month(@Param("year")int year, @Param("month")int month);

    TongjiModel GetZFAlarmCount_year();


    // 视频
    String GetHlsImg(@Param("id")int id, @Param("portId")int portId);

    List<CameraInfo> GetCameraInfo();

    int GetCameraInfoPortCount();

    List<String> GetCameraIndexByPortId(@Param("portId") int portId);

    String GetHlsById(@Param("id")int id);

    List<CameraInfo> GetCameraInfoBySearch(@Param("searchName")String searchName);

    List<AlarmRecord> GetHistoryWarningInfo(@Param("type")int type, @Param("startTime")String startTime, @Param("endTime")String endTime, @Param("model")int model);

    AlarmRecord GetWarningShipInfoByShipId(@Param("id")int id, @Param("userId")int userId, @Param("time")String time, @Param("model")int model);

    List<PortShipInfo> GetShipInfoByPort(@Param("portId")int portId, @Param("time")String time);

    List<PortShipInfo> GetAllShipInfo();

    List<PortShipInfo> GetOnLineShipInfo(@Param("time")String time);

    List<PortShipInfo> GetOutLineShipInfo(@Param("type")int type, @Param("time1")String time1, @Param("time2")String time2);

    List<PortShipInfo> GetOutPortShipInfo(@Param("time")String time);

    List<PortShipInfo> GetInPortShipInfo(@Param("time")String time, @Param("portId")int portId);

    List<PortShipInfo> GetWSInShangHaiShipInfo(@Param("time")String time);

    List<PortShipInfo> GetWSInJinBuShipInfo(@Param("time")String time);

    List<PortShipInfo> GetWSInPortShipInfo(@Param("time")String time, @Param("portId")int portId);

    List<PortShipInfo> GetWSIn168169ShipInfo(@Param("time")String time);

    int GetWarningStatistics (@Param("userId")int userId, @Param("type")int type, @Param("time")String time, @Param("model")int model);

    int GetInPortShipCount(@Param("portId")int portId, @Param("time")String time);

    List<ShipDynamicInfo> GetInportShip();

    void updateInportState(@Param("staticShipId")int staticShipId);

    void updateInportState2(@Param("staticShipId")int staticShipId, @Param("portId")int id);

    List<ShipDynamicInfo> GetUpdateShip();

    List<PortShipInfo> GetFishAreaShipInfo(@Param("areaName")int areaName);

    int GetOutPortCount(@Param("time")long time);

    int GetInPortCount(@Param("time")long time);

    int getTyphoonAlarm(@Param("shipDynamicInfo")ShipDynamicInfo shipDynamicInfo, @Param("time")String time);

    void UpdateOneAlarmInfo(@Param("shipDynamicInfo")ShipDynamicInfo shipDynamicInfo, @Param("time")String time);

    int GetTodayFuxiuAlarmByShipName(@Param("shipname")String shipname);

    List<OutInPortRecord> GetOutInPortRecordByShipId(@Param("shipId") String shipId, @Param("state") int state);

    int updateOutInPortRecordReport();

    void UpdateOutlineReason(@Param("id") int id, @Param("newOutLineReason") String newOutLineReason);

    List<PortShipInfo> GetAllShanghaiShipsInfo();
}
