<template>
    <div class="container">
        <!-- 顶部搜索栏 -->
        <div class="top-bar">
            <!-- 用户信息区域 -->
            <div v-if="showUserInfo" class="user-info-container">
                <div class="user-info-header">
                    <span class="user-info-title">欢迎，{{ currentUser.name || currentUser.username }}</span>
                    <button @click="toggleUserInfo" class="close-user-btn">×</button>
                </div>
                <div class="user-info-content">
                    <div class="user-row" v-if="currentUser.userArea">
                        <span class="user-area">区域：{{ formatUserArea(currentUser.userArea) }}</span>
                    </div>
                    <button @click="handleLogout" class="logout-btn">退出登录</button>
                </div>
            </div>

            <div class="search-container">
                <input type="text" id="searchInput" v-model="searchQuery" placeholder="请输入船名、呼号、IMO或MMSI"
                    @keyup.enter="handleSearch" class="search-input">
                <button @click="handleSearch" class="search-button">
                    <i class="icon-search">搜索</i>
                </button>
                <div v-if="showSearchResults" class="search-results-dropdown">
                    <div class="search-results-header">
                        <span>搜索结果</span>
                        <button @click="closeSearchResults" class="close-btn">×</button>
                    </div>
                    <div class="search-results-content">
                        <table>
                            <thead>
                            </thead>
                            <tbody>
                                <tr v-for="(ship, index) in searchResults" :key="index" @click="handleShipClick(ship)">
                                    <td>{{ ship.shipName }}&nbsp;&nbsp;&nbsp;&nbsp;九位码：{{ ship.shipMmsi }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主地图区域 -->
        <div class="map-bar" @click="handleMapClick">
            <!-- 地图 -->
            <div id="map" ref="myDiv" @contextmenu.prevent="handleContextMenu" class="map"></div>
            <!-- 侧边工具栏 -->
            <!-- <div class="sidebar-container">
                <div class="sidebar-up-content">
                    <button class="sidebar-btn" @click="ranging">
                        <i class="icon iconfont icon-map-ruler"></i>
                    </button>
                    <button class="sidebar-btn" @click="openLocationDialog">
                        <i class="icon iconfont icon-dingwei"></i>
                    </button>
                    <button class="sidebar-btn">
                        <i class="icon iconfont icon-sousuo"></i>
                    </button>
                    <button class="sidebar-btn">
                        <i class="icon iconfont icon-chakan"></i>
                    </button>
                    <button class="sidebar-btn">
                        <i class="icon iconfont icon-xinfeng"></i>
                    </button>
                    <button class="sidebar-btn">
                        <i class="icon iconfont icon-dianziweilan"></i>
                    </button>
                    <button class="sidebar-btn">
                        <i class="icon iconfont icon-gongjuxiang"></i>
                    </button>
                </div>
                <div class="sidebar-down-content">
                    <button class="sidebar-btn">
                        <i class="icon iconfont icon-03"></i>
                    </button>
                    <button class="sidebar-btn">
                        <i class="icon iconfont icon-jiahao"></i>
                    </button>
                    <button class="sidebar-btn">
                        <i class="icon iconfont icon-jianhao"></i>
                    </button>
                </div>
            </div> -->
            <div class="sidebar-container">
                <div class="sidebar-up-content">
                    <button class="sidebar-btn" @click="toggleUserInfo">
                        <i class="icon iconfont icon-yonghu"></i>
                        <div class="btn-text"></div>
                    </button>
                    <button class="sidebar-btn" @click="StartSelectShipByCircleTest">
                        <i class="icon iconfont icon-sousuo"></i>
                        <div class="btn-text">圆搜</div>
                    </button>
                    <!-- 方搜按钮 - 点击启动矩形框选船舶功能 -->
                    <button class="sidebar-btn" @click="StartSelectShipByRectTest">
                        <i class="icon iconfont icon-chakan"></i>
                        <div class="btn-text">方搜</div>
                    </button>
                    <!--电子围栏相关的别删！！！-->
                    <!-- <button class="sidebar-btn" @click="openWeilanDialog">
                        <i class="icon iconfont icon-dianziweilan"></i>
                        <div class="btn-text">电子围栏</div>
                    </button> -->
                    <button class="sidebar-btn" @click="goToUnicast">
                        <i class="icon iconfont icon-xinfeng"></i>
                        <div class="btn-text">短信</div>
                    </button>
                    <button class="sidebar-btn" @click="toggleMenuDialog">
                        <i class="icon iconfont icon-caidan"></i>
                        <div class="btn-text">菜单</div>
                    </button>

                </div>
                <div class="sidebar-down-content">
                    <!-- <button class="sidebar-btn" @click="showTyphoonListCard = true">
                        <i class="icon iconfont icon-taifeng"></i>
                    </button> -->
                    <button class="sidebar-btn" @click="showQiehuanDialog = true">
                        <i class="icon iconfont icon-03"></i>
                    </button>
                    <!-- 地图放大按钮 - 点击调用zoomMap方法进行放大 -->
                    <button class="sidebar-btn" @click="zoomMap(1)">
                        <i class="icon iconfont icon-jiahao"></i>
                    </button>
                    <!-- 地图缩小按钮 - 点击调用zoomMap方法进行缩小 -->
                    <button class="sidebar-btn" @click="zoomMap(0)">
                        <i class="icon iconfont icon-jianhao"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 成功提示浮窗 -->
        <div v-if="showSuccessAlert" class="success-alert">
            <div class="success-alert-content">
                <span>{{ successMessage }}</span>
            </div>
        </div>

        <!-- 组件 -->
        <div class="component">
            <SimpleDialog v-if="showSimpleDialog&&selectedShip" :ship="selectedShip" @close="showSimpleDialog = false" />
            <ShipTrackCard v-if="showShipTrackCard&&selectedShip" :ship="selectedShip" :bOutSide="bOutSide"
                @close="showShipTrackCard = false"/>
            <dinwei-dialog ref="dinweiDialog" @on-locate="onLocate" @on-close="onDinweiDialogClose"></dinwei-dialog>
            <shipsearch-dialog v-if="showShipSearchDialog" :ships="selectShipInfoList" @close="showShipSearchDialog = false"></shipsearch-dialog>
            <WeilanDialog v-if="showWeilanDialog" @close="closeWeilanDialog" @confirm="confirmWeilanDialog" />
            <TyphoonListCard v-if="showTyphoonListCard" @close="showTyphoonListCard = false" />
            <QiehuanDialog v-if="showQiehuanDialog" @close="closeQiehuanDialog" @map-type-change="onMapTypeChange" @time-period-change="onTimePeriodChange" @map-mode-change="onMapModeChange" />
            <FujinDialog v-if="showFujinDialog" @close="closeFujinDialog" @location-obtained="onLocationObtained" @ship-selected="onShipSelected" />
            <BiaoZhuDialog
                v-if="showBiaoZhuDialog"
                :markData="{
                    type: curMarkTypeIndex,
                    pointCoords: { x: radiusCenterX, y: radiusCenterY },
                    lineCoords: showCurMarkInfo,
                    circleCoords: { x: radiusCenterX, y: radiusCenterY, radius: radiusLength },
                    polygonCoords: showCurMarkInfo
                }"
                @close="closeBiaoZhuDialog"
                @confirm="confirmBiaoZhuDialog"
                @start-map-interaction="startMapInteraction"
                @save-annotation="saveAnnotation" />
             <VideoPlayerCard v-if="showVideoPlayerCard && selectedCamera" :Camera="selectedCamera" @close="showVideoPlayerCard = false" />
            <FishingVesselStatisticsCard v-if="showFishingVesselStatisticsCard" @close="showFishingVesselStatisticsCard = false" @show-online-ship-list="handleShowOnlineShipList" />
            <OnlineShipListCard v-if="showOnlineShipListCard" @close="showOnlineShipListCard = false" />
            <MenuDialog v-if="showMenuDialog" :isRanging="isRanging" @close="closeMenuDialog" @menu-action="handleMenuAction" />

            <!-- 测距模式退出按钮 -->
            <div v-if="isRanging" class="ranging-exit-btn" @click="exitRanging">
                <span>×</span>
            </div>
        </div>

        <!-- 标注绘制控制框 -->
        <div id="drawSomethingBox" style="display: none; position: absolute; top: 50px; left: 50px; z-index: 1000; background: rgba(0, 20, 63, 0.9); border: 1px solid #ccc; border-radius: 5px; padding: 15px; color: white; min-width: 200px;">
            <div class="draw-box-header" style="margin-bottom: 10px; font-weight: bold; text-align: center;">
                {{ drawSomethingBoxTitle }}
            </div>
            <div class="draw-box-content">
                <!-- 点标注和线标注的坐标显示 -->
                <div class="coord-section">
                    <div v-if="pointOrLineShow && curMarkTypeIndex === 1" class="point-coords">
                        <p>点坐标：</p>
                        <p>经度：{{ radiusCenterX }}</p>
                        <p>纬度：{{ radiusCenterY }}</p>
                    </div>
                    <div v-if="pointOrLineShow && curMarkTypeIndex === 2" class="line-coords">
                        <p>线坐标：(共{{ showCurMarkInfo.length }}个点)</p>
                        <div v-for="(coord, index) in showCurMarkInfo" :key="index">
                            <p>点{{ index + 1 }}：经度{{ coord.x }}，纬度{{ coord.y }}</p>
                        </div>
                    </div>
                    <div v-if="pointOrLineShow && curMarkTypeIndex === 3" class="polygon-coords">
                        <p>多边形面坐标：(共{{ showCurMarkInfo.length }}个点)</p>
                        <div v-for="(coord, index) in showCurMarkInfo" :key="index">
                            <p>点{{ index + 1 }}：经度{{ coord.x }}，纬度{{ coord.y }}</p>
                        </div>
                    </div>
                </div>
                <!-- 圆标注的半径设置 -->
                <div v-if="radiusShow" class="radius-section">
                    <p>圆心坐标：{{ radiusCenter }}</p>
                    <div style="margin: 10px 0;">
                        <label>半径(米)：</label>
                        <input v-model="radiusLength" type="number" style="width: 80px; padding: 2px; margin-left: 5px;" />
                    </div>
                </div>

                <!-- 标注名称输入 -->
                <div class="name-section" style="margin: 10px 0;">
                    <label>标注名称：</label>
                    <input v-model="markShowContent" type="text" style="width: 120px; padding: 2px; margin-left: 5px;" />
                </div>

                <!-- 是否显示给其他人 -->
                <div class="share-section" style="margin: 10px 0;">
                    <label>
                        <input v-model="isShowToOther" type="checkbox" style="margin-right: 5px;" />
                        显示给其他人
                    </label>
                </div>

                <!-- 操作按钮 -->
                <div class="button-section" style="text-align: center; margin-top: 15px;">
                    <button @click="saveMaekInfoToSdkOT" style="background: #007bff; color: white; border: none; padding: 5px 15px; margin: 0 5px; border-radius: 3px; cursor: pointer;">
                        保存
                    </button>
                    <button @click="cancelDrawMarkInfo" style="background: #6c757d; color: white; border: none; padding: 5px 15px; margin: 0 5px; border-radius: 3px; cursor: pointer;">
                        取消
                    </button>
                </div>
            </div>
        </div>
        <!-- <div id="line"
            style="position: absolute; width: 550px; height: 50px; background-color: rgb(0,20,63); top: 100px; left: 20px;">
            <input id="historyTrackProgressBar" type="range" name="points" step="1" min="1" max="100" value="0"
                @change="clickRange()" style="position: absolute; top: 15px; background-color: #FF0000;" />
        </div> -->
    </div>


</template>

<script>
import global from './Global.vue';
import setAlertWindowShow from '../../static/js/windowAlert.js'
import getCurTime from '../../static/js/getCurTime.js'
import SimpleDialog from './SimpleDialog.vue';
import ShipTrackCard from './ShipTrackCard.vue';
import DinweiDialog from './DinweiDialog.vue';
import ShipsearchDialog from './ShipsearchDialog.vue';
import WeilanDialog from './WeilanDialog.vue';
import BiaoZhuDialog from './BiaoZhuDialog.vue';
import TyphoonListCard from './TyphoonListCard.vue';
import QiehuanDialog from './QiehuanDialog.vue';
import FujinDialog from './FujinDialog.vue';
import VideoPlayerCard from './VideoPlayerCard.vue';
import FishingVesselStatisticsCard from './FishingVesselStatisticsCard.vue';
import OnlineShipListCard from './OnlineShipListCard.vue';
import MenuDialog from './MenuDialog.vue';
var _this;
export default {
    name: "Index",
    components: { SimpleDialog, ShipTrackCard, DinweiDialog, ShipsearchDialog, WeilanDialog, BiaoZhuDialog, TyphoonListCard, VideoPlayerCard, FishingVesselStatisticsCard, OnlineShipListCard, QiehuanDialog, FujinDialog, MenuDialog },
    data() {
        return {
            // 当前登录用户信息
            currentUser: {
                username: '',
                name: '',
                userArea: ''
            },
            selectedCamera: '', // 选中的摄像头信息
            showVideoPlayerCard: false, // 视频播放器卡片显示
            showFishingVesselStatisticsCard: false, // 渔船分布统计卡片显示
            showOnlineShipListCard: false, // 在线船舶列表卡片显示
            showTyphoonListCard: false, // 台风列表卡片显示
            showQiehuanDialog: false, // 地图图层切换对话框显示
            showFujinDialog: false, // 附近船舶对话框显示
            showMenuDialog: false, // 菜单弹窗显示
            showUserInfo: false, // 用户信息窗口显示
            isMobileDevice: false, // 移动设备
            searchQuery: '', // 查询
            showSearchResults: false, // 是否查到搜索结果
            searchResults: [], // 存储搜索结果
            shipInfoImg: '', //船舶图标?
            shipInfoList: [], //船舶图标?
            shipInfoList1: [], //船舶图标?
            fouInfo: {}, //船舶图标?
            wakeIsShowOrNot: false,
            isRanging: false, // 是否正在测距
            isCircleSearching: false, // 是否正在圆搜模式
            isRectSearching: false, // 是否正在方搜模式，用于跟踪方框选择船舶的状态

            selectedShip: '', // 选中的船舶信息
            showSimpleDialog: false, // 渔船简略信息显示
            showShipTrackCard: false, // 渔船轨道卡片显示
            bOutSide: 0, // 是否为外省渔船
            
            showShipSearchDialog: false, // 是否显示圆搜对话框
            selectShipInfoList: [], // 存储圆选船舶列表
            showWeilanDialog: false, // 是否显示围栏对话框
            showBiaoZhuDialog: false, // 是否显示标注对话框
            ShipSearch1Name: "", // 搜索标题
            ShipSearch1IsSow: false, // 是否已显示
            mapShipShowOrNot: false, // 地图船舶显示
            wsShipInShanghaiCheck: false,
            showOutLineShipStart: false,
            areaSearchShow: false,
            showOutLineShip: false,
            ifFishArea: false,
            curSelecctShipCount: 0, // 当前选中船舶数量

            locationLonD: "",
            locationLonF: "",
            locationLonM: "",
            locationLatD: "",
            locationLatF: "",
            locationLatM: "",

            // 标注功能相关变量
            markIsShow: false, // 标注是否显示
            leftShow: false, // 左侧菜单是否显示
            rightShow: false, // 右侧菜单是否显示
            confirmDlg: false, // 确认对话框
            drawSomethingBoxTitle: "", // 绘制框标题
            pointOrLineShow: true, // 点或线显示
            radiusShow: false, // 半径显示
            curMarkTypeIndex: 0, // 当前标注类型索引 (0=无, 1=点, 2=线, 5=圆)
            curMarkInfo: [], // 当前标注信息
            showCurMarkInfo: [], // 显示当前标注信息
            radiusCenterX: "", // 圆心X坐标
            radiusCenterY: "", // 圆心Y坐标
            radiusCenter: '', // 圆心坐标
            radiusLength: 0, // 圆半径
            markShowContent: '标注', // 标注显示内容
            isShowToOther: false, // 是否显示给其他人
            geoPoAll: [], // 地理坐标数组
            aLinkDlg: true, // 链接对话框
            editDlg: false, // 编辑对话框
            markwindow: true, // 标注窗口
            editmarkwindow: false, // 编辑标注窗口
            editpointOrLineShow: false, // 编辑点或线显示
            editradiusShow: false, // 编辑半径显示

            // 成功提示相关
            showSuccessAlert: false, // 是否显示成功提示
            successMessage: '', // 成功提示消息

            // 地图缩放相关变量
            meteorologicalMonitoringList: [], // 气象监测列表，用于存储地图中心点和缩放级别
            curLevel: 10, // 当前地图缩放级别
        };
    },
    beforeCreate() {
        _this = this;
    },
    mounted() {
        // 初始化当前用户信息
        this.initCurrentUser();

        // 检测设备类型
        this.isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        // 如果是移动设备，添加触摸事件监听器
        if (this.isMobileDevice) {
            const mapEl = document.getElementById('map');
            if (mapEl) {
                mapEl.addEventListener('touchstart', this.handleTouchStart);
            }
        }

        //---------------------------sdkreturn
        window.ReturnSelectObjByMouseMoveTest = this.ReturnSelectObjByMouseMoveTest.bind(this);
        window.ReturnSelectObjByMouseLeftDownTest = this.ReturnSelectObjByMouseLeftDownTest.bind(this);
        window.ReturnOnMouseRightDownTest = this.ReturnOnMouseRightDownTest.bind(this);
        window.ReturnOnMouseLeftDownTest = this.ReturnOnMouseLeftDownTest.bind(this);
        window.ReturnOnMouseUpTest = this.ReturnOnMouseUpTest.bind(this);
        window.ReturnDrawDynamicObjNewInfoTest = this.ReturnDrawDynamicObjNewInfoTest.bind(this);
        window.GetCurMeasureDist = this.GetCurMeasureDist.bind(this);
        window.ReturnCurMeasurePoInfoByMouseDownTest = this.ReturnCurMeasurePoInfoByMouseDownTest.bind(this);
        window.ReturnDragMapTest = this.ReturnDragMapTest.bind(this);
        window.ReturnZoomMapForPcTest = this.ReturnZoomMapForPcTest.bind(this);
        window.ReturnOnMouseMoveTest = this.ReturnOnMouseMoveTest.bind(this);
        window.ReturnCurMeasureAreaSizeTest = this.ReturnCurMeasureAreaSizeTest.bind(this);
        window.ReturnTouchmoveByDragTest = this.ReturnTouchmoveByDragTest.bind(this);
        window.ReturnMapViewAfterDragOrZoomTest = this.ReturnMapViewAfterDragOrZoomTest.bind(this);
        window.ReturnCurPlayTrackTimeInfoTest = this.ReturnCurPlayTrackTimeInfoTest.bind(this);

        window.ReturnEditObjByMouseRightDownCallBack = this.ReturnEditObjByMouseRightDownCallBack.bind(this);

        window.ReturnCancelObjByMouseLeftDownCallBack = this.ReturnCancelObjByMouseLeftDownCallBack.bind(this);

        window.showLocation = this.showLocation.bind(this);

        _this.timer2 = setInterval(this.getShipInfo, 50000);

        setTimeout(function () {
            //_this.GetShipRule();
            //_this.getAllBDShipPosition();
            getMapToken();
            _this.getShipInfo(50000);//获取最近50000秒内的船舶数据
        }, 10)

        setTimeout(() => {
            //_this.getShipInfoByTime(2);
        }, 10);
        // 延迟调用addCameraInfo，确保地图初始化完成
        setTimeout(() => {
            this.addCameraInfo();
        }, 1000);


        function regist() {
            console.log('=== regist()函数被调用 ===');
            console.log('开始初始化图层，包括标注图层');
            // 初始化图层，包括标注图层
            Test_AddLayer();
            console.log('Test_AddLayer()调用完成');
        }
        function getMapToken() {
        }
        function Test_AddLayer() {
        }
        function Test_AddShipStyle() {
        }
        function init() {
            var objMapInfo = [];
            //   var mapObj = document.getElementById("map");
            API_SetMapImgMode(1);
            API_SetShipInObj(true);
            API_SetMapMinMaxLevel(1, 18);

            objMapInfo.div = "map"; //海图容器div的id

            // 详细的浏览器检测和日志
            console.log('=== 浏览器检测信息 ===');
            //console.log('User Agent:', navigator.userAgent);
            //console.log('Platform:', navigator.platform);
            //console.log('App Name:', navigator.appName);
            //console.log('App Version:', navigator.appVersion);

            // 自动判断设备类型
            const isAndroid = /Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent);
            console.log('是否检测为Android设备:', isAndroid);

            if (isAndroid) {
                objMapInfo.model = "android";
                console.log('设置地图模式为: android');
            } else {
                objMapInfo.model = "pc";
                console.log('设置地图模式为: pc');
            }

            console.log('地图初始化参数:', objMapInfo);
            API_InitYimaMap(objMapInfo);
            console.log('调用regist()函数');
            regist();
            API_HiddenYimaEncText();
            getMapToken();
            API_SetMapImagesUrl("http://**************:8089/dianxin_chart/");
            // API_SetMapImagesUrl("http://***********:11080/api/map/cuttingMap/");
            // API_SetMapImagesUrl("http://***********:11080/api/map/cuttingMap/");
            // $.get(global.IP + "/web/GetMapToken", function (data, status) {
            //   var wmsToken = '?graphNode=' + global.mapModel + '&token=' + data;
            //   API_RefreshMapImg();
            // }).fail(function (msg) {
            //   // 服务器地址
            //   // API_SetMapImagesUrl("http://**************:8089/YimaMap1/");
            //   API_SetMapImagesUrl("http://***********:7001/map/");
            //   API_RefreshMapImg();
            // });

            API_SetSaveTrackPoCount(1000);
            API_SetDrawCenterPoToFixedLen(6);
            API_SetMousePoInfoDivPosition(false, 500, 50); //显示鼠标位置
            API_SetScaleInfoDivPosition(false, 20, 50); //显示比例尺位置
            API_SetScaleLenInfoPosition(false, 20, 60); //显示比例尺长度
            API_SetShowToolBarOrNot(false);

            global.g_showSimpleInfoDiv = document.getElementById("ShowSimpleInfoByMouseMoveDiv");
            global.g_showDrawObjInfoByMouseMoveDiv = document.getElementById("ShowDrawObjInfoByMouseMoveDiv");
            global.g_showDetailShipInfoDiv = document.getElementById("ShowDetailShipInfoDiv");

            Test_AddLayer(); //添加图层
            Test_AddShipStyle(); //添加船舶样式
            API_SetFocusShipShowStyleByMinScale(10240000); //设置选中船舶至少显示的最小比例尺样式

            API_SetMapLevel(10, { x: 121.83219484335935, y: 31.417383188620214 });
            // API_SetMapLevel(7, { x: 131.83219484335935, y: 39.417383188620214});

            API_SetSelectAllObjectByMouseMove(50000000, true);
            API_SetShowShipHistroyTrackScale(5000000);
            API_SetIsShowShipHistroyTrackInfoBox(false);

            // setTimeout(API_ReDrawLayer, 500);

            API_SetIsShowShipHistroyTrackInfoBox(true);

            API_SteIsShowHistoryTrackDirectionSign(true);

            API_SetSelectShipByScrnPoStartScale(500000000);
            API_SetShowShipHistroyTrackScale(500000000);
            API_SetStartShowShipScale(500000000);

            API_SetWarnShipCircleStyle(2, "#FFFF00", 100);

            var optionClustererStyle = [];
            optionClustererStyle.push({ src: "../../static/img/clusterers1.png", w: 53, h: 52 });//个位
            optionClustererStyle.push({ src: "../../static/img/clusterers2.png", w: 56, h: 55 });//十位
            optionClustererStyle.push({ src: "../../static/img/clusterers3.png", w: 66, h: 65 });//百位
            optionClustererStyle.push({ src: "../../static/img/clusterers4.png", w: 78, h: 77 });//千位
            optionClustererStyle.push({ src: "../../static/img/clusterers5.png", w: 90, h: 89 });//万位
            var bInitClustererStyle = API_InitClustererStyle(optionClustererStyle);//设置聚合底图
            API_SetClustererShowOption(true, 1, 7, true);//显示聚合0～13级
            API_SetSelectRectByMouseMoveNotShow(true);
            API_DBClickByRigth(true);
            var CancelButtonStyle = [];
            CancelButtonStyle.lineWidth = 1;
            CancelButtonStyle.lineColor = "#FF0000";
            CancelButtonStyle.lineOpacity = 90;
            CancelButtonStyle.setFill = true;
            CancelButtonStyle.fillColor = "#FFFFFF";
            CancelButtonStyle.fillOpacity = 100;
            CancelButtonStyle.width = 10;
            CancelButtonStyle.height = 10;
            API_SetCancelButtonStyle(CancelButtonStyle);

            // 为兼容安卓端，使用Arial字体，避免黑体在部分安卓设备不显示
            API_SetShowShipInfoStyle("20px Arial", "#0000FF", 90);
            API_SetFishAreaName(true);
            //船舶选中
            API_SetShowSelectShipInfoStyle(true, "12px Arial", "#FF00FF", 80);
            API_ShipTrackTimeMarkShowOrNot(false);
            API_SetShipShowNameFrameStyle(1, 10, 20, 1, "#FF0000", 100);
            API_SetShipSimpleDetailsShowOrNot(true);
            // 强制显示船舶名称（如有此API，部分亿海图版本支持）
            if (typeof API_SetShowShipNameOrNot === 'function') {
                API_SetShowShipNameOrNot(true);
            }
            // 地图初始化后再次调用，确保生效
            setTimeout(function () {
                API_SetShowShipInfoStyle("20px Arial", "#0000FF", 90);
                API_SetShowSelectShipInfoStyle(true, "12px Arial", "#FF00FF", 80);
                API_SetShipSimpleDetailsShowOrNot(true);
                if (typeof API_SetShowShipNameOrNot === 'function') {
                    API_SetShowShipNameOrNot(true);
                }
            }, 1000);

            API_SetShipImgRotate(true);
            API_SteIsShowHistoryTrackDirectionSign(false);
            API_SetShowAllTyphoonPredictTrack(true);
            global.isCheckInit = true;

            Test_AddLayer(); //添加图层
            Test_AddShipStyle(); //添加船舶样式

            // 只保留window.OnShipClick注册，延迟2秒
            setTimeout(() => {
                window.OnShipClick = function (shipInfo) {
                    // 调用locationShip方法
                    let id = shipInfo.staticShipId || shipInfo.shipId || shipInfo.bdid || shipInfo.bdId;
                    if (!id) return;
                    _this.$options.methods.locationShip.bind(_this)(id);
                    // 弹出简单信息弹窗
                    global.curSelectShipId = id;
                    document.getElementById('ShipSimpleInformationView').style.display = 'block';
                    _this.$options.methods.changeCurIndex.bind(_this)("ShipSimpleInformationView");
                };

                // 地图初始化完成后，获取并显示已有的标注数据
                setTimeout(() => {
                    console.log('=== 准备调用getAllMarkInfo ===');
                    //console.log('当前时间:', new Date().toLocaleString());
                    //console.log('_this对象:', _this);
                    _this.getAllMarkInfo();
                }, 1000);
            }, 2000);
        }

        // 添加图层
        function Test_AddLayer() {
            //这里演示添加3个图层，分别是点标注图层、线标注图层、面标注图层、气象图层

            //--------------------------添加点标注图层-----------------------------------
            var pointLayerInfo = [];
            pointLayerInfo.id = global.g_iPointLayerId;
            pointLayerInfo.type = 1;//类型：1=点图层，2=线图层，3=面图层
            pointLayerInfo.name = "点图层";//图层名称
            pointLayerInfo.bShow = true; //显示

            pointLayerInfo.minShowScale = 1;//最大比例尺
            pointLayerInfo.maxShowScale = 2000000000;//最小比例尺
            pointLayerInfo.bShowTextOrNot = true;//是否显示名称
            pointLayerInfo.iStartShowTextScale = 5000000;//开始显示名称的最小比例尺

            var pointLayerPos = API_AddNewLayer(pointLayerInfo, null); //添加图层，得到图层的pos
            if (pointLayerPos > -1) {
                var pointStyle = [];

                //点图片样式
                pointStyle.strImgSrc = "/static/yimaencsdk/YimaEncSDK/img/light.png"; //图片地址
                pointStyle.iImgWidth = 20; //图片的宽度
                pointStyle.iImgHeight = 30; //图片的高度
                pointStyle.offsetScrnPo = { x: 0, y: -15 };//显示的偏移量，(0,0)为图片中心

                pointStyle.bShowImg = true;
                pointStyle.bShowText = true; //是否显示名称
                pointStyle.textColor = "#FF0000"; //名称颜色
                pointStyle.fontSize = "12px"; //名称字体大小
                pointStyle.iOpacity = 100;
                pointStyle.iTextOpacity = 10; //透明度
                pointStyle.bFilled = true; //是否填充颜色
                pointStyle.fillColor = "#ee5d72"; //填充的颜色
                global.g_iPointStylePos = API_AddPointLayerStyleByPos(pointLayerPos, pointStyle);

                API_SetLayerTextBackGroundColorByPos(pointLayerPos, true, "#FF0000", 50);//设置文字背景颜色
            }

            //---------------------------------添加线标注图层----------------------------

            var lintLayerInfo = [];
            lintLayerInfo.id = global.g_iLineLayerId;
            lintLayerInfo.type = 2; //类型：1=点图层，2=线图层，3=面图层
            lintLayerInfo.name = "线图层"; //图层名称
            lintLayerInfo.bShow = true; //显示
            var lineLayerPos = API_AddNewLayer(lintLayerInfo, null); //添加图层，得到图层的pos

            if (lineLayerPos > -1) {
                var lineStyle = [];
                lineStyle.borderWith = 3; //线的粗细
                lineStyle.borderColor = "#FFB90F"; //线的颜色
                lineStyle.iOpacity = 80; //透明度
                lineStyle.bShowText = true; //是否显示名称
                lineStyle.textColor = "#000000"; //名称颜色
                lineStyle.fontSize = "12px"; //名称字体大小
                lineStyle.iTextOpacity = 80; //透明度
                lineStyle.lineType = 1;//绘制实线、1=虚线
                lineStyle.lineLen = 5;
                lineStyle.dashLen = 2;
                global.g_iLineStylePos = API_AddLineLayerStyleByPos(lineLayerPos, lineStyle);
                API_SetLayerTextBackGroundColorByPos(lineLayerPos, true, "#00FF00", 50); //设置文字背景颜色
            }

            //-------------------------------------添加面标注图层---------------------
            var faceLayerInfo = [];
            faceLayerInfo.id = global.g_iFaceLayerId;
            faceLayerInfo.type = 3; //类型：1=点图层，2=线图层，3=面图层
            faceLayerInfo.name = "面图层"; //图层名称
            faceLayerInfo.bShow = true; //显示
            var faceLayerPos = API_AddNewLayer(faceLayerInfo, null); //添加图层，得到图层的pos
            if (faceLayerPos > -1) {
                var faceStyle = [];
                faceStyle.borderWith = 1; //线的粗细
                faceStyle.borderColor = "#092ee8"; //线的颜色
                faceStyle.bFilled = true; //是否填充颜色
                faceStyle.fillColor = "#FFFFFF"; //填充的颜色
                faceStyle.iOpacity = 50; //透明度
                faceStyle.bShowText = true; //是否显示名称
                faceStyle.textColor = "#000000"; //名称颜色
                faceStyle.fontSize = "12px"; //名称字体大小
                faceStyle.iTextOpacity = 80; //透明度
                faceStyle.iLineOpacity = 100;

                global.g_iFaceStylePos = API_AddFaceLayerStyleByPos(faceLayerPos, faceStyle);
                API_SetLayerTextBackGroundColorByPos(faceLayerPos, true, "#0000FF", 50); //设置文字背景颜色
            }

            //--------------------------------------添加气象图层(也是点标注一种)--------------
            var weatherLayerInfo = [];
            weatherLayerInfo.id = global.g_iWeatherLayerId;
            weatherLayerInfo.type = 1; //类型：1=点图层，2=线图层，3=面图层
            weatherLayerInfo.name = "气象图层"; //图层名称
            weatherLayerInfo.bShow = true; //显示
            weatherLayerInfo.bShowImg = true;//显示图片
            weatherLayerInfo.minShowScale = 20000;//最小显示比例尺
            weatherLayerInfo.maxShowScale = 1000000; //最大显示比例尺
            var weatherLayerPos = API_AddNewLayer(weatherLayerInfo, null); //添加图层，得到图层的pos
            if (weatherLayerPos > -1) {
                //这里添加两种气象样式
                var weatherStyle1 = [];
                weatherStyle1.bShowImg = true;
                weatherStyle1.strImgSrc = "/static/yimaencsdk/YimaEncSDK/img/sunshine1.png"; //图片地址（晴天图片）
                weatherStyle1.iImgWidth = 30; //图片的宽度
                weatherStyle1.iImgHeight = 26; //图片的高度
                weatherStyle1.bShowText = false; //是否显示名称

                var pos1 = API_AddPointLayerStyleByPos(weatherLayerPos, weatherStyle1);//添加第一种气象样式,这里的pos1应该是0

                var weatherStyle2 = [];
                weatherStyle2.bShowImg = true;//显示图片
                weatherStyle2.strImgSrc = "/static/yimaencsdk/YimaEncSDK/img/raining1.png"; //图片地址（阴天图片）
                weatherStyle2.iImgWidth = 30; //图片的宽度
                weatherStyle2.iImgHeight = 26; //图片的高度
                weatherStyle2.bShowText = false; //是否显示名称
                var pos2 = API_AddPointLayerStyleByPos(weatherLayerPos, weatherStyle2); //添加第一种气象样式,这里的pos1应该是1
            }

            //--------------------------------------添加港口图层(也是点标注一种)--------------
            var portLayerInfo = [];
            portLayerInfo.id = global.g_iPortLayerId;
            portLayerInfo.type = 1; //类型：1=点图层，2=线图层，3=面图层
            portLayerInfo.name = "港口图层"; //图层名称
            portLayerInfo.bShow = true; //显示
            portLayerInfo.bShowImg = true;
            var portLayerPos = API_AddNewLayer(portLayerInfo, null); //添加图层，得到图层的pos
            if (portLayerPos > -1) {
                //这里一种样式
                var portStyle = [];
                portStyle.bShowImg = true; //显示图片
                portStyle.strImgSrc = "../..//static/img/port.png"; //图片地址（晴天图片）
                portStyle.iImgWidth = 10; //图片的宽度
                portStyle.iImgHeight = 10; //图片的高度
                portStyle.bShowText = false; //是否显示名称
                portStyle.iOpacity = 50; //透明度
                var pos = API_AddPointLayerStyleByPos(portLayerPos, portStyle); //添加第一种港口样式,这里的pos应该是0
            }

            //---------------------------------------添加洋流图层(也是点标注，只是使用矢量符号来显示(箭头))-----------------------------------
            var ocLayerInfo = [];
            ocLayerInfo.id = global.g_iOceanCirculationLayerId; //洋流图层Id
            ocLayerInfo.type = 1; //类型：1=点图层，2=线图层，3=面图层
            ocLayerInfo.name = "洋流图层"; //图层名称
            ocLayerInfo.bShow = true; //显示
            var ocLayerPos = API_AddNewLayer(ocLayerInfo, null); //添加图层，得到图层的pos
            if (ocLayerPos > -1) {
                //这里一种样式
                var arrSymbolPo = [];//箭头符号
                arrSymbolPo.push({ x: -1, y: 10 });
                arrSymbolPo.push({ x: 1, y: 10 });
                arrSymbolPo.push({ x: 1, y: -3 });
                arrSymbolPo.push({ x: 3, y: -3 });
                arrSymbolPo.push({ x: 0, y: -10 });
                arrSymbolPo.push({ x: -3, y: -3 });
                arrSymbolPo.push({ x: -1, y: -3 });

                var ocStyle = [];
                ocStyle.bShowImg = false; //不使用图片，使用矢量符号
                ocStyle.arrSymbolPo = arrSymbolPo; //矢量符号顶点
                ocStyle.iImgWidth = 20; //符号的宽度
                ocStyle.iImgHeight = 50; //符号的高度
                ocStyle.bShowText = false; //是否显示名称
                ocStyle.borderWith = 1; //线的粗细
                ocStyle.borderColor = "#FF0000"; //线的颜色
                ocStyle.bFilled = false; //是否填充颜色
                ocStyle.fillColor = "#FF0000"; //填充的颜色
                ocStyle.iOpacity = 50; //透明度
                ocStyle.iCheckDrawMinNearOtherLen = 30;//该图层直接的标注间隙

                var pos = API_AddPointLayerStyleByPos(ocLayerPos, ocStyle); //添加第一种港口样式,这里的pos应该是0
            }


            // ---------------------------------------添加渔港图层-----------------------------------------
            var compositeLayerInfo = [];
            compositeLayerInfo.id = global.g_iFishingPortLayerId;
            compositeLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层 composite = 100 = 综合图层
            compositeLayerInfo.name = "渔港图层"; //图层名称
            compositeLayerInfo.bShow = true; //显示
            compositeLayerInfo.minShowScale = 1;//最大比例尺
            compositeLayerInfo.maxShowScale = 500000;//最小比例尺
            compositeLayerInfo.bShowTextOrNot = true;//是否显示名称
            compositeLayerInfo.iStartShowTextScale = 50000;//开始显示名称的最小比例尺
            var compositeLayerPos = API_AddNewLayer(compositeLayerInfo, null); //添加图层，得到图层的pos
            if (compositeLayerPos > -1) {
                var compositeStyle = [];
                compositeStyle.borderWith = 2; //线的粗细
                compositeStyle.borderColor = "#00143F"; //线的颜色
                compositeStyle.bFilled = true; //是否填充颜色
                compositeStyle.fillColor = "#246fa8"; //填充的颜色
                compositeStyle.iOpacity = 20; //透明度
                compositeStyle.bShowImg = false;
                compositeStyle.strImgSrc = '../../static/img/point.png'; //图片地址
                compositeStyle.iImgWidth = 10; //图片的宽度
                compositeStyle.iImgHeight = 10; //图片的高度
                compositeStyle.bShowText = true; //是否显示名称
                compositeStyle.textColor = "#000000"; //名称颜色
                compositeStyle.fontSize = "12px"; //名称字体大小
                compositeStyle.iTextOpacity = 100; //透明度
                //compositeStyle.iCheckDrawMinNearOtherLen = null;
                compositeStyle.iLineOpacity = 50;
                compositeStyle.offsetScrnPo = { x: -10, y: 15 };
                // compositeStyle.bDrawPointCircle = null;//是否绘制小圆点
                compositeStyle.lineType = 1;//绘制实线、1=虚线
                compositeStyle.lineLen = 5;
                compositeStyle.dashLen = 2;

                global.g_iFishingPortStylePos = API_AddCompositeLayerStyleByPos(compositeLayerPos, compositeStyle);
            }

            // ---------------------------------------添加伏休图层-----------------------------------------
            var FuxiuLayerInfo = [];
            FuxiuLayerInfo.id = global.g_iAreaFuxiuLayerId;
            FuxiuLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层 composite = 100 = 综合图层
            FuxiuLayerInfo.name = "伏休图层"; //图层名称
            FuxiuLayerInfo.bShow = true; //显示
            FuxiuLayerInfo.minShowScale = 1;//最大比例尺
            FuxiuLayerInfo.maxShowScale = 50000000;//最小比例尺
            FuxiuLayerInfo.bShowTextOrNot = true;//是否显示名称
            FuxiuLayerInfo.iStartShowTextScale = 500000;//开始显示名称的最小比例尺
            var FuxiuLayerPos = API_AddNewLayer(FuxiuLayerInfo, null); //添加图层，得到图层的pos
            if (FuxiuLayerPos > -1) {
                var FuxiuStyle = [];
                FuxiuStyle.borderWith = 3; //线的粗细
                FuxiuStyle.borderColor = "#FFFF00"; //线的颜色
                FuxiuStyle.bFilled = true; //是否填充颜色
                FuxiuStyle.fillColor = "#FFB90F"; //填充的颜色
                FuxiuStyle.iOpacity = 5; //透明度
                FuxiuStyle.bShowImg = false;
                FuxiuStyle.bShowText = false; //是否显示名称
                FuxiuStyle.textColor = "#000000"; //名称颜色
                FuxiuStyle.fontSize = "12px"; //名称字体大小
                FuxiuStyle.iTextOpacity = 100; //透明度
                FuxiuStyle.iLineOpacity = 50;
                FuxiuStyle.offsetScrnPo = { x: -10, y: 15 };
                FuxiuStyle.lineType = 1;//绘制实线、1=虚线
                FuxiuStyle.lineLen = 10;
                FuxiuStyle.dashLen = 2;

                global.g_iAreaFuxiuStylePos = API_AddCompositeLayerStyleByPos(FuxiuLayerPos, FuxiuStyle);
            }

            // ---------------------------------------添加高危图层-----------------------------------------
            var GaoweiLayerInfo = [];
            GaoweiLayerInfo.id = global.g_iAreaGaoweiLayerId;
            GaoweiLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层 composite = 100 = 综合图层
            GaoweiLayerInfo.name = "高危图层"; //图层名称
            GaoweiLayerInfo.bShow = true; //显示
            GaoweiLayerInfo.minShowScale = 1;//最大比例尺
            GaoweiLayerInfo.maxShowScale = 5000000;//最小比例尺
            GaoweiLayerInfo.bShowTextOrNot = true;//是否显示名称
            GaoweiLayerInfo.iStartShowTextScale = 500000;//开始显示名称的最小比例尺
            var GaoweiLayerPos = API_AddNewLayer(GaoweiLayerInfo, null); //添加图层，得到图层的pos
            if (GaoweiLayerPos > -1) {
                var GaoweiStyle = [];
                GaoweiStyle.borderWith = 2; //线的粗细
                GaoweiStyle.borderColor = "#FF0000"; //线的颜色
                GaoweiStyle.bFilled = true; //是否填充颜色
                GaoweiStyle.fillColor = "#FF0000"; //填充的颜色
                GaoweiStyle.iOpacity = 10; //透明度
                GaoweiStyle.bShowImg = false;
                GaoweiStyle.bShowText = true; //是否显示名称
                GaoweiStyle.textColor = "#000000"; //名称颜色
                GaoweiStyle.fontSize = "12px"; //名称字体大小
                GaoweiStyle.iTextOpacity = 100; //透明度
                GaoweiStyle.iLineOpacity = 50;
                GaoweiStyle.offsetScrnPo = { x: -10, y: 15 };
                GaoweiStyle.lineType = 1;//绘制实线、1=虚线
                GaoweiStyle.lineLen = 5;
                GaoweiStyle.dashLen = 2;

                global.g_iAreaGaoweiStylePos = API_AddCompositeLayerStyleByPos(GaoweiLayerPos, GaoweiStyle);
            }

            // ---------------------------------------添加特殊图层-----------------------------------------
            var TeshuLayerInfo = [];
            TeshuLayerInfo.id = global.g_iAreaTeshuLayerId;
            TeshuLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层 composite = 100 = 综合图层
            TeshuLayerInfo.name = "特殊图层"; //图层名称
            TeshuLayerInfo.bShow = true; //显示
            TeshuLayerInfo.minShowScale = 1;//最大比例尺
            TeshuLayerInfo.maxShowScale = 50000000;//最小比例尺
            TeshuLayerInfo.bShowTextOrNot = true;//是否显示名称
            TeshuLayerInfo.iStartShowTextScale = 500000;//开始显示名称的最小比例尺
            var TeshuLayerPos = API_AddNewLayer(TeshuLayerInfo, null); //添加图层，得到图层的pos
            if (TeshuLayerPos > -1) {
                var TeshuStyle = [];
                TeshuStyle.borderWith = 2; //线的粗细
                TeshuStyle.borderColor = "#000000"; //线的颜色
                TeshuStyle.bFilled = true; //是否填充颜色
                TeshuStyle.fillColor = "#00FA9A"; //填充的颜色
                TeshuStyle.iOpacity = 10; //透明度
                TeshuStyle.bShowImg = false;
                TeshuStyle.bShowText = true; //是否显示名称
                TeshuStyle.textColor = "#000000"; //名称颜色
                TeshuStyle.fontSize = "12px"; //名称字体大小
                TeshuStyle.iTextOpacity = 100; //透明度
                TeshuStyle.iLineOpacity = 50;
                TeshuStyle.offsetScrnPo = { x: -10, y: 15 };
                TeshuStyle.lineType = 1;//绘制实线、1=虚线
                TeshuStyle.lineLen = 10;
                TeshuStyle.dashLen = 2;

                global.g_iAreaTeshuStylePos = API_AddCompositeLayerStyleByPos(TeshuLayerPos, TeshuStyle);
            }

            // ---------------------------------------添加自定义图层-----------------------------------------
            var SelfLayerInfo = [];
            SelfLayerInfo.id = global.g_iAreaSelfLayerId;
            SelfLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层 composite = 100 = 综合图层
            SelfLayerInfo.name = "自定义图层"; //图层名称
            SelfLayerInfo.bShow = true; //显示
            SelfLayerInfo.minShowScale = 1;//最大比例尺
            SelfLayerInfo.maxShowScale = 5000000;//最小比例尺
            SelfLayerInfo.bShowTextOrNot = true;//是否显示名称
            SelfLayerInfo.iStartShowTextScale = 500000;//开始显示名称的最小比例尺
            var SelfLayerPos = API_AddNewLayer(SelfLayerInfo, null); //添加图层，得到图层的pos
            if (SelfLayerPos > -1) {
                var SelfStyle = [];
                SelfStyle.borderWith = 1; //线的粗细
                SelfStyle.borderColor = "#00143F"; //线的颜色
                SelfStyle.bFilled = true; //是否填充颜色
                SelfStyle.fillColor = "#246fa8"; //填充的颜色
                SelfStyle.iOpacity = 10; //透明度
                SelfStyle.bShowImg = false;
                SelfStyle.bShowText = true; //是否显示名称
                SelfStyle.textColor = "#000000"; //名称颜色
                SelfStyle.fontSize = "12px"; //名称字体大小
                SelfStyle.iTextOpacity = 100; //透明度
                SelfStyle.iLineOpacity = 50;
                SelfStyle.offsetScrnPo = { x: -10, y: 15 };
                SelfStyle.lineType = 1;//绘制实线、1=虚线
                SelfStyle.lineLen = 5;
                SelfStyle.dashLen = 2;

                global.g_iAreaSelfStylePos = API_AddCompositeLayerStyleByPos(SelfLayerPos, SelfStyle);
            }

            // ---------------------------------------添加综合图层-线标注-----------------------------------------
            var ComprehensiveLayerInfo = [];
            ComprehensiveLayerInfo.id = global.g_iComprehensiveLayerId;
            ComprehensiveLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层 composite = 100 = 综合图层
            ComprehensiveLayerInfo.name = "综合图层"; //图层名称
            ComprehensiveLayerInfo.bShow = true; //显示
            ComprehensiveLayerInfo.minShowScale = 1;//最大比例尺
            ComprehensiveLayerInfo.maxShowScale = 50000000;//最小比例尺
            ComprehensiveLayerInfo.bShowTextOrNot = true;//是否显示名称
            ComprehensiveLayerInfo.iStartShowTextScale = 500000;//开始显示名称的最小比例尺
            var ComprehensiveLayerPos = API_AddNewLayer(ComprehensiveLayerInfo, null); //添加图层，得到图层的pos
            if (ComprehensiveLayerPos > -1) {
                var ComprehensiveStyle = [];
                ComprehensiveStyle.borderWith = 2; //线的粗细
                ComprehensiveStyle.borderColor = "#FFB90F"; //线的颜色
                ComprehensiveStyle.bFilled = true; //是否填充颜色
                ComprehensiveStyle.fillColor = "#246fa8"; //填充的颜色
                ComprehensiveStyle.iOpacity = 100; //透明度
                ComprehensiveStyle.bShowImg = false;
                ComprehensiveStyle.bShowText = true; //是否显示名称
                ComprehensiveStyle.textColor = "#000000"; //名称颜色
                ComprehensiveStyle.fontSize = "12px"; //名称字体大小
                ComprehensiveStyle.iTextOpacity = 100; //透明度
                ComprehensiveStyle.iLineOpacity = 50;
                ComprehensiveStyle.offsetScrnPo = { x: -10, y: 15 };
                ComprehensiveStyle.lineType = 1;//绘制实线、1=虚线
                ComprehensiveStyle.lineLen = 10;
                ComprehensiveStyle.dashLen = 2;

                global.g_iComprehensiveStylePos = API_AddCompositeLayerStyleByPos(ComprehensiveLayerPos, ComprehensiveStyle);
            }

            //--------------------------------------添加摄像头图层(也是点标注一种)--------------
            var cameraLayerInfo = [];
            cameraLayerInfo.id = global.g_iCameraLayerId;
            cameraLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层
            cameraLayerInfo.name = "气象图层"; //图层名称
            cameraLayerInfo.bShow = true; //显示
            cameraLayerInfo.bShowImg = true;//显示图片
            cameraLayerInfo.minShowScale = 1;//最小显示比例尺
            cameraLayerInfo.maxShowScale = 500000; //最大显示比例尺
            var cameraLayerPos = API_AddNewLayer(cameraLayerInfo, null); //添加图层，得到图层的pos
            if (cameraLayerPos > -1) {
                var cameraStyle = [];
                cameraStyle.borderWith = 3; //线的粗细
                cameraStyle.borderColor = "#FF0000"; //线的颜色
                cameraStyle.bFilled = true; //是否填充颜色
                cameraStyle.fillColor = "#FFFF00"; //填充的颜色
                cameraStyle.iOpacity = 90; //透明度
                cameraStyle.bShowImg = true;
                cameraStyle.strImgSrc = '/static/img/camera-urgent.png'; //图片地址
                cameraStyle.iImgWidth = 16; //图片的宽度
                cameraStyle.iImgHeight = 16; //图片的高度
                cameraStyle.bShowText = false; //是否显示名称
                cameraStyle.textColor = "#000000"; //名称颜色
                cameraStyle.fontSize = "12px"; //名称字体大小
                cameraStyle.iTextOpacity = 60; //透明度
                cameraStyle.iLineOpacity = 50;
                cameraStyle.offsetScrnPo = { x: 0, y: 0 };
                cameraStyle.lineType = 0;//绘制实线、1=虚线
                cameraStyle.lineLen = 6;
                cameraStyle.dashLen = 4;

                global.g_iCameraStylePos = API_AddCompositeLayerStyleByPos(cameraLayerPos, cameraStyle);
            }

            //--------------------------------------添加标注图层--------------
            //console.log('=== 开始创建标注图层 ===');
            var markLayerInfo = [];
            markLayerInfo.id = global.g_iMarkLayerId;
            markLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层
            markLayerInfo.name = "标注图层"; //图层名称
            markLayerInfo.bShow = true; //显示
            markLayerInfo.bShowImg = true;//显示图片
            markLayerInfo.minShowScale = 1;//最小显示比例尺
            markLayerInfo.maxShowScale = 5000000; //最大显示比例尺
            //console.log('标注图层配置:', markLayerInfo);
            var markLayerPos = API_AddNewLayer(markLayerInfo, null); //添加图层，得到图层的pos
            //console.log('标注图层创建结果 - markLayerPos:', markLayerPos);
            if (markLayerPos > -1) {
                var markStyle = [];
                markStyle.borderWith = 3; //线的粗细
                markStyle.borderColor = "#FF0000"; //线的颜色
                markStyle.bFilled = true; //是否填充颜色
                markStyle.fillColor = "#FFFF00"; //填充的颜色
                markStyle.iOpacity = 10; //透明度
                markStyle.bShowImg = true;
                markStyle.strImgSrc = '/static/img/point.png'; //图片地址
                markStyle.iImgWidth = 16; //图片的宽度
                markStyle.iImgHeight = 16; //图片的高度
                markStyle.bShowText = false; //是否显示名称
                markStyle.textColor = "#000000"; //名称颜色
                markStyle.fontSize = "12px"; //名称字体大小
                markStyle.iTextOpacity = 60; //透明度
                markStyle.iLineOpacity = 50;
                markStyle.offsetScrnPo = { x: 0, y: 0 };
                markStyle.lineType = 0;//绘制实线、1=虚线
                markStyle.lineLen = 6;
                markStyle.dashLen = 4;

                global.g_iMarkStylePos = API_AddCompositeLayerStyleByPos(markLayerPos, markStyle);
                //console.log('标注图层样式创建结果 - g_iMarkStylePos:', global.g_iMarkStylePos);
            } else {
                console.error('标注图层创建失败，markLayerPos:', markLayerPos);
            }

            //--------------------------------------添加定位点物标图层--------------
            var locationLayerInfo = [];
            locationLayerInfo.id = global.g_iLocationLayerId;
            locationLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层
            locationLayerInfo.name = "定位图层"; //图层名称
            locationLayerInfo.bShow = true; //显示
            locationLayerInfo.bShowImg = true;//显示图片
            locationLayerInfo.minShowScale = 1;//最小显示比例尺
            locationLayerInfo.maxShowScale = 5000000; //最大显示比例尺
            var locationLayerPos = API_AddNewLayer(locationLayerInfo, null); //添加图层，得到图层的pos
            if (locationLayerPos > -1) {
                var locationStyle = [];
                locationStyle.borderWith = 3; //线的粗细
                locationStyle.borderColor = "#FF0000"; //线的颜色
                locationStyle.bFilled = true; //是否填充颜色
                locationStyle.fillColor = "#FFFF00"; //填充的颜色
                locationStyle.iOpacity = 10; //透明度
                locationStyle.bShowImg = true;
                locationStyle.strImgSrc = '../../static/ico/location.png'; //图片地址
                locationStyle.iImgWidth = 32; //图片的宽度
                locationStyle.iImgHeight = 32; //图片的高度
                locationStyle.bShowText = false; //是否显示名称
                locationStyle.textColor = "#000000"; //名称颜色
                locationStyle.fontSize = "12px"; //名称字体大小
                locationStyle.iTextOpacity = 60; //透明度
                locationStyle.iLineOpacity = 50;
                locationStyle.offsetScrnPo = { x: 0, y: 0 };
                locationStyle.lineType = 0;//绘制实线、1=虚线
                locationStyle.lineLen = 6;
                locationStyle.dashLen = 4;

                global.g_iLocationStylePos = API_AddCompositeLayerStyleByPos(locationLayerPos, locationStyle);
            }

            //--------------------------------------添加监测船舶的监测区域--------------
            var jianCeLayerInfo = [];
            jianCeLayerInfo.id = global.g_iJianCeLayerId;
            jianCeLayerInfo.type = 100; //类型：1=点图层，2=线图层，3=面图层
            jianCeLayerInfo.name = "定位图层"; //图层名称
            jianCeLayerInfo.bShow = true; //显示
            jianCeLayerInfo.bShowImg = true;//显示图片
            jianCeLayerInfo.minShowScale = 1;//最小显示比例尺
            jianCeLayerInfo.maxShowScale = 5000000; //最大显示比例尺
            var jianCeLayerPos = API_AddNewLayer(jianCeLayerInfo, null); //添加图层，得到图层的pos
            if (jianCeLayerPos > -1) {
                var jianCeStyle = [];
                jianCeStyle.borderWith = 3; //线的粗细
                jianCeStyle.borderColor = "#FF0000"; //线的颜色
                jianCeStyle.bFilled = true; //是否填充颜色
                jianCeStyle.fillColor = "#FFFF00"; //填充的颜色
                jianCeStyle.iOpacity = 10; //透明度
                jianCeStyle.bShowImg = true;
                jianCeStyle.strImgSrc = '../../static/ico/location.png'; //图片地址
                jianCeStyle.iImgWidth = 32; //图片的宽度
                jianCeStyle.iImgHeight = 32; //图片的高度
                jianCeStyle.bShowText = false; //是否显示名称
                jianCeStyle.textColor = "#000000"; //名称颜色
                jianCeStyle.fontSize = "12px"; //名称字体大小
                jianCeStyle.iTextOpacity = 60; //透明度
                jianCeStyle.iLineOpacity = 50;
                jianCeStyle.offsetScrnPo = { x: 0, y: 0 };
                jianCeStyle.lineType = 0;//绘制实线、1=虚线
                jianCeStyle.lineLen = 6;
                jianCeStyle.dashLen = 4;

                global.g_iJianCeStylePos = API_AddCompositeLayerStyleByPos(jianCeLayerPos, jianCeStyle);
            }
        }

        // 添加船舶样式
        function Test_AddShipStyle() {
            // 渔船
            var fishStyle = [];
            fishStyle.bImgSymbol = true;
            fishStyle.strImgSrc = './img/ship1.png';
            fishStyle.iImgWidth = 14;
            fishStyle.iImgHeight = 13;
            fishStyle.minScale = 1;                       //最小显示比例尺
            fishStyle.maxScale = 1000000000;              //最大显示比例尺
            fishStyle.iOpacity = 100;                      //透明度
            fishStyle.fillColor = "#FF0000";              //填充颜色

            var iShipStatePos1 = API_AddNewShipState(0);        //添加第一种船舶状态，状态值0
            var bResult1 = API_AddShipStateStyleByPos(iShipStatePos1, fishStyle); //船舶的状态0

            // 执法船
            var lawStyle = [];
            lawStyle.bImgSymbol = true;
            lawStyle.strImgSrc = './img/ship2.png';
            lawStyle.iImgWidth = 14;
            lawStyle.iImgHeight = 13;
            lawStyle.minScale = 1;                       //最小显示比例尺
            lawStyle.maxScale = 1000000000;              //最大显示比例尺
            lawStyle.iOpacity = 100;                      //透明度
            lawStyle.fillColor = "#FF0000";              //填充颜色

            var iShipStatePos1 = API_AddNewShipState(1);        //添加第一种船舶状态，状态值0
            var bResult1 = API_AddShipStateStyleByPos(iShipStatePos1, lawStyle); //船舶的状态0

            // 监测船
            var monitoringStyle = [];
            monitoringStyle.bImgSymbol = true;
            monitoringStyle.strImgSrc = './img/ship3.png';
            monitoringStyle.iImgWidth = 14;
            monitoringStyle.iImgHeight = 13;
            monitoringStyle.minScale = 1;                       //最小显示比例尺
            monitoringStyle.maxScale = 1000000000;              //最大显示比例尺
            monitoringStyle.iOpacity = 100;                      //透明度
            monitoringStyle.fillColor = "#FF0000";              //填充颜色

            var iShipStatePos1 = API_AddNewShipState(2);        //添加第一种船舶状态，状态值0
            var bResult1 = API_AddShipStateStyleByPos(iShipStatePos1, monitoringStyle); //船舶的状态0

            // 外省渔船
            var provincesStyle = [];
            provincesStyle.bImgSymbol = true;
            provincesStyle.strImgSrc = './img/ship4.png';
            provincesStyle.iImgWidth = 14;
            provincesStyle.iImgHeight = 13;
            provincesStyle.minScale = 1;                       //最小显示比例尺
            provincesStyle.maxScale = 1000000000;              //最大显示比例尺
            provincesStyle.iOpacity = 100;                      //透明度
            provincesStyle.fillColor = "#FF0000";              //填充颜色

            var iShipStatePos1 = API_AddNewShipState(3);        //添加第一种船舶状态，状态值0
            var bResult1 = API_AddShipStateStyleByPos(iShipStatePos1, provincesStyle); //船舶的状态0

            // 重点跟踪渔船
            var importStyle = [];
            importStyle.bImgSymbol = true;
            importStyle.strImgSrc = './img/ship5.png';
            importStyle.iImgWidth = 14;
            importStyle.iImgHeight = 13;
            importStyle.minScale = 1;                       //最小显示比例尺
            importStyle.maxScale = 1000000000;              //最大显示比例尺
            importStyle.iOpacity = 100;                      //透明度
            importStyle.fillColor = "#FF0000";              //填充颜色

            var iShipStatePos1 = API_AddNewShipState(4);        //添加第一种船舶状态，状态值0
            var bResult1 = API_AddShipStateStyleByPos(iShipStatePos1, importStyle); //船舶的状态0

            // 预警渔船
            var warningStyle = [];
            warningStyle.bImgSymbol = true;
            warningStyle.strImgSrc = './img/ship6.png';
            warningStyle.iImgWidth = 14;
            warningStyle.iImgHeight = 13;
            warningStyle.minScale = 1;                       //最小显示比例尺
            warningStyle.maxScale = 1000000000;              //最大显示比例尺
            warningStyle.iOpacity = 100;                      //透明度
            warningStyle.fillColor = "#FF0000";              //填充颜色

            var iShipStatePos1 = API_AddNewShipState(5);        //添加第一种船舶状态，状态值0
            var bResult1 = API_AddShipStateStyleByPos(iShipStatePos1, warningStyle); //船舶的状态0

            // 雷达目标
            var radarStyle = [];
            radarStyle.bImgSymbol = true;
            radarStyle.strImgSrc = './img/ship7.png';
            radarStyle.iImgWidth = 14;
            radarStyle.iImgHeight = 13;
            radarStyle.minScale = 1;                       //最小显示比例尺
            radarStyle.maxScale = 1000000000;              //最大显示比例尺
            radarStyle.iOpacity = 100;                      //透明度
            radarStyle.fillColor = "#FF0000";              //填充颜色

            var iShipStatePos1 = API_AddNewShipState(6);        //添加第一种船舶状态，状态值0
            var bResult1 = API_AddShipStateStyleByPos(iShipStatePos1, radarStyle); //船舶的状态0

            // 商船
            var merchantStyle = [];
            merchantStyle.bImgSymbol = true;
            merchantStyle.strImgSrc = './img/ship8.png';
            merchantStyle.iImgWidth = 14;
            merchantStyle.iImgHeight = 13;
            merchantStyle.minScale = 1;                       //最小显示比例尺
            merchantStyle.maxScale = 1000000000;              //最大显示比例尺
            merchantStyle.iOpacity = 100;                      //透明度
            merchantStyle.fillColor = "#FF0000";              //填充颜色

            var iShipStatePos1 = API_AddNewShipState(7);        //添加第一种船舶状态，状态值0
            var bResult1 = API_AddShipStateStyleByPos(iShipStatePos1, merchantStyle); //船舶的状态0

            API_SetOffLineShipStyle(false, { strFillColor: "#FF0000", iBorderSize: 2, strBorderColor: "#FFFFFF" });
        }

        // 初始化
        $(function () {
            init();
        })
    },

    methods: {
        /**
         * 初始化当前用户信息
         */
        initCurrentUser() {
            // 从sessionStorage获取用户信息
            this.currentUser.username = sessionStorage.getItem('username') || '';
            this.currentUser.userArea = sessionStorage.getItem('userArea') || '';
            this.currentUser.shipQueryAreas = sessionStorage.getItem('shipQueryAreas') || ''; // 获取用户可查询的船舶区域信息

            // 如果有用户ID，从后端获取完整的用户信息
            const userId = sessionStorage.getItem('userId');
            if (userId) {
                this.fetchUserInfo(userId);
            }
        },

        /**
         * 从后端获取用户详细信息
         */
        fetchUserInfo(userId) {
            $.get(global.IP + "/web/GetUserInfoById?userId=" + userId, (userInfo) => {
                if (userInfo) {
                    this.currentUser.name = userInfo.name || userInfo.username;
                    this.currentUser.username = userInfo.username;
                    this.currentUser.userArea = userInfo.userArea || '';
                    this.currentUser.shipQueryAreas = userInfo.shipQueryAreas || ''; // 获取用户可查询的船舶区域信息
                    console.log('用户信息加载完成:', this.currentUser);
                }
            }).fail((error) => {
                console.error('获取用户信息失败:', error);
            });
        },

        /**
         * 处理退出登录
         */
        handleLogout() {
            // 确认退出
            if (confirm('确定要退出登录吗？')) {
                // 清除sessionStorage中的用户信息
                sessionStorage.removeItem('isLogin');
                sessionStorage.removeItem('userId');
                sessionStorage.removeItem('username');
                sessionStorage.removeItem('userArea');
                sessionStorage.removeItem('shipQueryAreas'); // 清除用户可查询的船舶区域信息
                sessionStorage.removeItem('jurisdiction');
                sessionStorage.removeItem('systemPage');

                // 清除当前用户信息
                this.currentUser = {
                    username: '',
                    name: '',
                    userArea: '',
                    shipQueryAreas: '' // 清除船舶查询区域信息
                };

                // 跳转到登录页面
                this.$router.push('/login');

                console.log('用户已退出登录');
            }
        },

        /**
         * 获取用户可查询的船舶区域列表
         * @returns {Array} 区域名称数组
         */
        getUserShipQueryAreas() {
            const shipQueryAreas = sessionStorage.getItem('shipQueryAreas') || '';
            if (!shipQueryAreas.trim()) {
                return []; // 如果没有权限，返回空数组
            }
            // 将逗号分隔的字符串转换为数组，并去除空白字符
            return shipQueryAreas.split(',').map(area => area.trim()).filter(area => area);
        },

        /**
         * 检查用户是否有权限查询指定区域的船舶
         * @param {string} areaName 区域名称
         * @returns {boolean} 是否有权限
         */
        canQueryShipInArea(areaName) {
            const allowedAreas = this.getUserShipQueryAreas();
            // 如果没有设置权限，默认不允许查询
            if (allowedAreas.length === 0) {
                return false;
            }
            // 检查指定区域是否在允许的区域列表中
            return allowedAreas.includes(areaName);
        },

        // 添加摄像头
        addCameraInfo() {
            // 获取当前登录用户的区域信息（支持多区域，逗号分隔）
            let userArea = sessionStorage.getItem('userArea');
            console.log('原始用户区域数据:', userArea);

            // 处理JSON格式的userArea（兼容旧格式和新格式）
            let actualUserArea = '';
            if (userArea && userArea.trim() !== '') {
                try {
                    // 尝试解析JSON格式
                    const userAreaObj = JSON.parse(userArea);
                    if (userAreaObj.userArea) {
                        actualUserArea = userAreaObj.userArea;
                        console.log('解析JSON格式的用户区域:', actualUserArea);
                    } else {
                        actualUserArea = userArea; // 直接使用原始值
                    }
                } catch (e) {
                    // 如果不是JSON格式，直接使用原始值
                    actualUserArea = userArea;
                    console.log('使用原始格式的用户区域:', actualUserArea);
                }
            }

            console.log('最终用户区域:', actualUserArea);
            if (actualUserArea && actualUserArea.includes(',')) {
                console.log('检测到多区域权限:', actualUserArea.split(',').map(area => area.trim()));
            }

            // 构建请求URL，如果有用户区域则传递给后端进行筛选
            let requestUrl = global.IP + "/web/GetCameraInfo";
            if (actualUserArea && actualUserArea.trim() !== '') {
                requestUrl += "?userArea=" + encodeURIComponent(actualUserArea.trim());
            }

            $.get(requestUrl, function (cameraInfo, status) {
                console.log('从后端获取到的摄像头数据:', cameraInfo);
                console.log('后端筛选后的摄像头总数:', cameraInfo.length);

                // 统计变量
                let displayedCameraCount = 0; // 实际显示的摄像头数量

                for (var i = 0; i < cameraInfo.length; i++) {
                    // 为摄像头生成唯一名称，避免地图API因重复名称而添加失败
                    var objName = cameraInfo[i].name + '_' + cameraInfo[i].id;
                    var arrObjPo = [];

                    // 后端已经进行了筛选，这里只需要处理显示逻辑
                    // 注意：后端已经过滤了无效坐标和区域权限，这里直接使用数据

                    console.log('处理摄像头:', cameraInfo[i].name, '区域:', cameraInfo[i].areaName, 'ID:', cameraInfo[i].id, '坐标:', cameraInfo[i].lon, cameraInfo[i].lat);

                    // 特别关注金汇港和奚家港的摄像头
                    if (cameraInfo[i].areaName === '金汇港' || cameraInfo[i].areaName === '奚家港') {
                        console.warn('🔍 重点关注摄像头:', cameraInfo[i].name, 'ID:', cameraInfo[i].id);
                    }

                    arrObjPo.push({ x: cameraInfo[i].lon, y: cameraInfo[i].lat });

                    var layerPos = API_GetLayerPosById(global.g_iCameraLayerId); //获取点图层的pos  **
                    var layerStylePos = global.g_iCameraStylePos;
                    console.log('摄像头图层信息 - layerPos:', layerPos, 'layerStylePos:', layerStylePos);

                    if (layerPos > -1) {
                        var objInfo = [];
                        var arrExpAttrValue = []; //扩展字段，假如没有可以传入null

                        objInfo.objType = 1;
                        objInfo.layerPos = layerPos; //图层索引
                        objInfo.objId = cameraInfo[i].id; //标注id **
                        objInfo.name = objName; //标注名称
                        objInfo.showText = objName; //显示内容
                        objInfo.layerStylePos = layerStylePos; //使用样式索引
                        arrExpAttrValue.push("来一个扩展字段"); //扩展字段信息
                        var objPos = API_AddNewObject(objInfo, arrObjPo, arrExpAttrValue);

                        // 检查是否成功添加到地图
                        if (objPos > -1) {
                            // 统计实际显示的摄像头
                            displayedCameraCount++;
                            console.log('成功添加摄像头到地图:', cameraInfo[i].name, 'objPos:', objPos);

                            // 设置权限
                            var setCurEditObjInfo = [];
                            var lastObjInfo = API_GetNotEditObjInfo();
                            for (var iii = 0; iii < lastObjInfo.length; iii++) {
                                setCurEditObjInfo.push({ layerId: lastObjInfo[iii].layerId, objId: lastObjInfo[iii].objId });
                            }
                            setCurEditObjInfo.push({ layerId: global.g_iCameraLayerId, objId: cameraInfo[i].id });
                            API_SetNotEditObjInfo(setCurEditObjInfo);
                        } else {
                            console.error('添加摄像头到地图失败:', cameraInfo[i].name, 'ID:', cameraInfo[i].id, 'objPos:', objPos);
                            console.error('失败摄像头详细信息:', {
                                id: cameraInfo[i].id,
                                name: cameraInfo[i].name,
                                areaName: cameraInfo[i].areaName,
                                coordinates: { x: cameraInfo[i].lon, y: cameraInfo[i].lat },
                                objInfo: objInfo,
                                layerPos: layerPos,
                                layerStylePos: layerStylePos
                            });
                        }
                    } else {
                        console.error('摄像头图层未找到，无法添加摄像头:', cameraInfo[i].name, 'layerPos:', layerPos);
                    }
                }

                // 输出统计信息
                console.log('=== 摄像头统计信息 ===');
                console.log('后端返回的摄像头总数:', cameraInfo.length);
                console.log('实际显示的摄像头数量:', displayedCameraCount);
                if (userArea && userArea.trim() !== '') {
                    console.log('当前用户所在区域 "' + userArea + '" 的摄像头已由后端筛选完成');
                } else {
                    console.log('用户无区域限制，后端返回所有有效摄像头');
                }
                console.log('========================');

                API_ReDrawLayer();
            })
        },

        /**
         * 处理摄像头点击事件
         * 当用户点击地图上的摄像头图标时调用此方法
         * 支持PC端鼠标点击和移动端触摸点击
         * @param {Object} objInfo - 摄像头对象信息
         * @param {number} objInfo.objId - 摄像头ID
         * @param {number} objInfo.layerId - 摄像头所在图层ID
         */
        handleCameraClick: function (objInfo) {
            console.log('================= 摄像头点击处理开始 =================');

            // 提取摄像头ID
            const cameraId = objInfo.objId;

            // 根据摄像头ID获取详细信息并显示视频播放器
            this.getCameraInfoById(cameraId);
        },

        /**
         * 根据摄像头ID获取摄像头详细信息
         * 从服务器获取摄像头列表，找到对应ID的摄像头，并显示视频播放器
         * @param {number} cameraId - 摄像头ID
         */
        getCameraInfoById: function (cameraId) {
            // 获取当前登录用户的区域信息（支持多区域，逗号分隔）
            const userArea = sessionStorage.getItem('userArea');

            // 构建请求URL，如果有用户区域则传递给后端进行筛选
            let requestUrl = global.IP + "/web/GetCameraInfo";
            if (userArea && userArea.trim() !== '') {
                requestUrl += "?userArea=" + encodeURIComponent(userArea.trim());
            }

            // 向服务器请求摄像头信息列表
            $.get(requestUrl, (cameraList) => {
                // 检查返回的数据格式是否正确
                if (cameraList && Array.isArray(cameraList)) {
                    // 在摄像头列表中查找指定ID的摄像头
                    const camera = cameraList.find(cam => cam.id == cameraId);

                    if (camera) {
                        // 找到摄像头，保存选中的摄像头信息
                        this.selectedCamera = {
                            id: camera.id,
                            name: camera.name,
                            location: camera.location,
                            lon: camera.lon,
                            lat: camera.lat,
                            ...camera  // 展开其他摄像头属性（如hls视频流地址等）
                        };

                        // 显示视频播放器卡片
                        this.showVideoPlayerCard = true;
                    } else {
                        console.error('未找到ID为', cameraId, '的摄像头');
                    }
                } else {
                    console.error('摄像头列表格式错误:', cameraList);
                }
            }).fail((error) => {
                // 请求失败时的错误处理
                console.error('获取摄像头信息失败:', error);
            });
        },
        
        // 地图缩放方法 - 处理+/-按钮点击事件
        zoomMap: function (index) {
            // index为1表示放大(+按钮)，其他值表示缩小(-按钮)
            if(index == 1) {
                // 调用API放大地图，参数1表示放大
                API_ZoomMap(1, null);
            }
            else {
                // 调用API缩小地图，参数-1表示缩小
                API_ZoomMap(-1, null);
            }
        },

        //圆搜船舶
        StartSelectShipByCircleTest: function () {
            // 如果已经在圆搜模式，点击则取消
            if (this.isCircleSearching) {
                this.isCircleSearching = false;
                global.g_bSelectShipByCircleModel = false;
                API_SetCurDrawDynamicUseType(0); // 取消绘制模式
                API_AddCancelButtonIsShow(false);
                return;
            }
            
            // 开启圆搜模式
            this.isCircleSearching = true;
            var objStyle = []; //标注样式结构体，格式如下：{borderWith:1, borderColor:"#FF0000", fillColor : "#FF0000", textColor: "#FF0000", fontSize:"12px 宋体", iOpacity:80}
            objStyle.borderWith = "2"; //画笔粗细
            objStyle.borderColor = "#000000"; //画笔颜色
            objStyle.fillColor = "#FF0000"; //填充颜色
            objStyle.textColor = "#000000"; //字体颜色(主要是测距、测面积使用)
            objStyle.fontSize = "12px 宋体"; //字体大小(主要是测距、测面积使用)
            objStyle.iOpacity = 50; //透明度

            API_SetDynamicObjStyle(objStyle);
            API_SetCurDrawDynamicUseType(global.drawCircle); // 设置绘制模式为圆形
            global.g_bSelectShipByCircleModel = true; // 设置全局圆选模式标志
            _this.$options.methods.changeCurIndex.bind(this)("ShipSearch1");
            API_AddCancelButtonIsShow(true); // 显示取消按钮
        },

        //显示圆选船舶
        SelectShipsByGeoCircleTest: function (iCircleGeoX, iCircleGeoY, iCircleRadiusMeter, iGetMaxShipCount) {
            global.g_arrSelectShipInfoObj = null;
            // 调用API获取圆形区域内的船舶
            global.g_arrSelectShipInfoObj = API_SelectShipsByGeoCircle(iCircleGeoX, iCircleGeoY, iCircleRadiusMeter, iGetMaxShipCount);
            
            // 设置搜索相关状态
            _this.ShipSearch1Name = "搜索船舶详情(圆选)";
            _this.wsShipInShanghaiCheck = false;
            _this.showOutLineShipStart = false;
            _this.areaSearchShow = false;
            _this.showOutLineShip = false;
            _this.ifFishArea = false;
            
            // 处理船舶数据并设置表格
            _this.setShipInfoInTable();
            
            // 确认是否有船舶数据
            if (_this.selectShipInfoList && _this.selectShipInfoList.length > 0) {
                console.log("找到船舶数据：", _this.selectShipInfoList.length, "条");
                // 显示ShipsearchDialog对话框
                _this.showShipSearchDialog = true;
            } else {
                console.log("未找到船舶数据");
                alert('该区域未圈选到船舶');
            }
            
            // 重置状态
            global.g_bSelectShipByRectModel = false;
            global.g_bSelectShipByCircleModel = false;
            _this.isCircleSearching = false;
            API_SetCurDrawDynamicUseType(0); // 取消绘制模式
            API_AddCancelButtonIsShow(false); // 隐藏取消按钮
        },

        //方搜船舶 
        StartSelectShipByRectTest: function () {
            // 如果已经在方搜模式，点击则取消 - 实现切换功能
            if (this.isRectSearching) {
                this.isRectSearching = false; // 更新状态变量
                global.g_bSelectShipByRectModel = false; // 更新全局状态
                API_SetCurDrawDynamicUseType(0); // 取消绘制模式 - 停止海图绘制操作
                API_AddCancelButtonIsShow(false); // 隐藏取消按钮
                return;
            }
            
            // 开启方搜模式 - 允许用户在地图上绘制矩形区域
            this.isRectSearching = true; // 更新状态变量
            var objStyle = []; // 创建标注样式结构体，用于设置绘制矩形的样式
            objStyle.borderWith = "2"; // 画笔粗细 - 矩形边框宽度
            objStyle.borderColor = "#000000"; // 画笔颜色 - 矩形边框颜色为黑色
            objStyle.fillColor = "#FF0000"; // 填充颜色 - 矩形内部填充为红色
            objStyle.textColor = "#000000"; // 字体颜色 - 用于标注文字
            objStyle.fontSize = "12px 宋体"; // 字体大小 - 用于标注文字
            objStyle.iOpacity = 10; // 透明度 - 设置较低的透明度，方便用户看到区域内的船舶

            API_SetDynamicObjStyle(objStyle); // 设置绘制样式
            API_SetCurDrawDynamicUseType(global.drawRect); // 设置当前绘制模式为矩形
            global.g_bSelectShipByRectModel = true; // 设置全局状态，表示开始方搜模式
            _this.$options.methods.changeCurIndex.bind(this)("ShipSearch1"); // 更新UI层级
            API_AddCancelButtonIsShow(true); // 显示取消按钮，允许用户随时取消操作
        },

        //显示方搜船舶 - 处理用户绘制的矩形区域内的船舶信息
        SelectShipByRectTest: function (minGeoX, maxGeoX, minGeoY, maxGeoY, iGetShipCount) {
            global.g_arrSelectShipInfoObj = null; // 清空之前的船舶数据
            // 调用API查询指定矩形区域内的船舶，参数为矩形的左上角和右下角经纬度坐标
            // iGetShipCount为0表示不限制返回的船舶数量，返回区域内的所有船舶
            global.g_arrSelectShipInfoObj = API_SelectShipsByGeoRect(minGeoX, maxGeoX, minGeoY, maxGeoY, iGetShipCount);

            // 设置搜索相关状态 - 准备显示搜索结果
            _this.ShipSearch1Name = "搜索船舶详情(框选)"; // 设置对话框标题，指明是框选结果
            _this.wsShipInShanghaiCheck = false; // 重置各种过滤状态
            _this.showOutLineShipStart = false;
            _this.areaSearchShow = false;
            _this.showOutLineShip = false;
            _this.ifFishArea = false;
            
            // 处理船舶数据并设置表格 - 调用公共方法处理API返回的船舶数据
            _this.setShipInfoInTable(); // 此方法会解析船舶数据并填充到selectShipInfoList中
            
            // 确认是否有船舶数据 - 根据结果决定显示对话框还是提示信息
            if (_this.selectShipInfoList && _this.selectShipInfoList.length > 0) {
                console.log("找到船舶数据：", _this.selectShipInfoList.length, "条");
                // 显示ShipsearchDialog对话框 - 显示框选结果列表
                _this.showShipSearchDialog = true;
            } else {
                console.log("未找到船舶数据");
                alert('该区域未框选到船舶'); // 如果区域内没有船舶，弹出提示
            }
            
            // 重置状态 - 完成框选后恢复正常模式
            global.g_bSelectShipByRectModel = false; // 重置全局框选模式标志
            this.isRectSearching = false; // 重置本地状态变量
            API_SetCurDrawDynamicUseType(0); // 取消绘制模式，恢复地图正常操作
            API_AddCancelButtonIsShow(false); // 隐藏取消按钮，操作已完成
        },

        // 处理船舶数据并设置--表格内显示船舶数据
        setShipInfoInTable: function () {
            console.log("开始处理船舶数据");
            if(global.g_arrSelectShipInfoObj != null) {
                console.log("global.g_arrSelectShipInfoObj 存在");
                if(_this.ShipSearch1IsSow == false) {
                    _this.ShipSearch1IsSow = true;
                }
                _this.mapShipShowOrNot = true;
                _this.selectShipInfoList = []; // 清空之前的船舶列表
                
                // 确保arrShipInfo存在 - 处理第一种数据格式
                if (global.g_arrSelectShipInfoObj.arrShipInfo && global.g_arrSelectShipInfoObj.arrShipInfo.length > 0) {
                    console.log("处理arrShipInfo格式数据，数量:", global.g_arrSelectShipInfoObj.arrShipInfo.length);
                    for(var i = 0; i < global.g_arrSelectShipInfoObj.arrShipInfo.length; i++) {
                        // 将API返回的船舶数据转换为界面显示格式
                        _this.selectShipInfoList.push({
                            shipId: global.g_arrSelectShipInfoObj.arrShipInfo[i].shipId,
                            shipName: global.g_arrSelectShipInfoObj.arrShipInfo[i].shipName,
                            lon: this.coordinateTransformation(global.g_arrSelectShipInfoObj.arrShipInfo[i].shipGeoPoX / 10000000),
                            lat: this.coordinateTransformation(global.g_arrSelectShipInfoObj.arrShipInfo[i].shipGeoPoY / 10000000),
                            mmsi: global.g_arrSelectShipInfoObj.arrShipInfo[i].shipMMSI,
                            owner: global.g_arrSelectShipInfoObj.arrShipInfo[i].customFields && global.g_arrSelectShipInfoObj.arrShipInfo[i].customFields.split(",")[0] == undefined ? "-" : 
                                  global.g_arrSelectShipInfoObj.arrShipInfo[i].customFields ? global.g_arrSelectShipInfoObj.arrShipInfo[i].customFields.split(",")[0] : "-",
                            phone: global.g_arrSelectShipInfoObj.arrShipInfo[i].customFields && global.g_arrSelectShipInfoObj.arrShipInfo[i].customFields.split(",")[1] == undefined ? "-" : 
                                  global.g_arrSelectShipInfoObj.arrShipInfo[i].customFields ? global.g_arrSelectShipInfoObj.arrShipInfo[i].customFields.split(",")[1] : "-",
                            time: global.g_arrSelectShipInfoObj.arrShipInfo[i].shipTime,
                            speed: global.g_arrSelectShipInfoObj.arrShipInfo[i].shipSpeed,
                            course: global.g_arrSelectShipInfoObj.arrShipInfo[i].shipCourse,
                        });
                    }
    
                    // 对船舶列表按名称排序
                    _this.selectShipInfoList.sort((a, b) => {
                        let reg = /^[A-z]/;
                        if (reg.test(a.shipName) || reg.test(b.shipName)) {
                            if (a.shipName > b.shipName) {
                                return 1;
                            } else if (a.shipName < b.shipName) {
                                return -1;
                            } else {
                                return 0;
                            }
                        } else {
                            return a.shipName.localeCompare(b.shipName);
                        }
                    });
                } else if (global.g_arrSelectShipInfoObj.length > 0) {
                    // 处理第二种数据格式 - 数组格式
                    console.log("处理数组格式数据，数量:", global.g_arrSelectShipInfoObj.length);
                    for(var i = 0; i < global.g_arrSelectShipInfoObj.length; i++) {
                        const shipId = global.g_arrSelectShipInfoObj[i].shipId;
                        const shipPos = API_GetShipPosById(shipId);
                        if (shipPos > -1) {
                            const info = API_GetShipInfoByPos(shipPos);
                            if (info) {
                                // 通过API获取船舶详细信息并转换为界面显示格式
                                _this.selectShipInfoList.push({
                                    shipId: info.shipId,
                                    shipName: info.shipName || "未知船名",
                                    lon: this.coordinateTransformation(info.shipGeoPoX / 10000000),
                                    lat: this.coordinateTransformation(info.shipGeoPoY / 10000000),
                                    mmsi: info.shipMMSI || "未知MMSI",
                                    owner: info.customFields && info.customFields.split(",")[0] == undefined ? "-" : 
                                          info.customFields ? info.customFields.split(",")[0] : "-",
                                    phone: info.customFields && info.customFields.split(",")[1] == undefined ? "-" : 
                                          info.customFields ? info.customFields.split(",")[1] : "-",
                                    time: info.shipTime || "未知时间",
                                    speed: info.shipSpeed || 0,
                                    course: info.shipCourse || 0,
                                });
                            }
                        }
                    }
                } else {
                    // 处理其他可能的数据格式
                    console.log("无法识别的数据格式:", global.g_arrSelectShipInfoObj);
                    // 尝试直接处理对象
                    if (typeof global.g_arrSelectShipInfoObj === 'object') {
                        const keys = Object.keys(global.g_arrSelectShipInfoObj);
                        console.log("对象属性:", keys);
                        
                        // 如果有iShipCount属性，可能是另一种格式
                        if (global.g_arrSelectShipInfoObj.iShipCount > 0) {
                            console.log("处理iShipCount格式数据，数量:", global.g_arrSelectShipInfoObj.iShipCount);
                            for (let i = 0; i < global.g_arrSelectShipInfoObj.iShipCount; i++) {
                                const shipInfo = global.g_arrSelectShipInfoObj[i];
                                if (shipInfo) {
                                    _this.selectShipInfoList.push({
                                        shipId: shipInfo.shipId || "未知ID",
                                        shipName: shipInfo.shipName || "未知船名",
                                        lon: shipInfo.shipGeoPoX ? this.coordinateTransformation(shipInfo.shipGeoPoX / 10000000) : "未知经度",
                                        lat: shipInfo.shipGeoPoY ? this.coordinateTransformation(shipInfo.shipGeoPoY / 10000000) : "未知纬度",
                                        mmsi: shipInfo.shipMMSI || "未知MMSI",
                                        owner: "-",
                                        phone: "-",
                                        time: shipInfo.shipTime || "未知时间",
                                        speed: shipInfo.shipSpeed || 0,
                                        course: shipInfo.shipCourse || 0,
                                    });
                                }
                            }
                        }
                    }
                }
                
                console.log("处理完成，船舶数量:", _this.selectShipInfoList.length);
                _this.curSelecctShipCount = _this.selectShipInfoList.length; // 更新船舶计数
            } else {
                console.log("global.g_arrSelectShipInfoObj 为空");
            }
        },
        
        // 坐标转换辅助函数 - 将原始坐标转换为显示格式
        coordinateTransformation: function(coordinate) {
            // 如果项目中已有此函数，请删除此函数并使用现有的
            // 这里提供一个简单实现
            return parseFloat(coordinate).toFixed(6);
        },

        // 跳转至单播页面
        goToUnicast() {
            this.$router.push({ name: 'Unicast' });
        },
        //测距--绘制测距线
        ranging: function () {
            this.isRanging = !this.isRanging;
            if (this.isRanging) {
                var objStyle = []; //标注样式结构体，格式如下：{borderWith:1, borderColor:"#FF0000", fillColor : "#FF0000", textColor: "#FF0000", fontSize:"12px 宋体", iOpacity:80}
                objStyle.borderWith = "2"; //画笔粗细
                objStyle.borderColor = "#FF0000"; //画笔颜色
                objStyle.fillColor = "#FF0000"; //填充颜色
                objStyle.textColor = "#FFFFFF"; //字体颜色(主要是测距、测面积使用)
                objStyle.fontSize = "12px 宋体"; //字体大小(主要是测距、测面积使用)
                objStyle.iOpacity = 100; //透明度

                API_SetDynamicObjStyle(objStyle);

                API_SetDynamicObjTextBoxStyle({lineColor:"#FF0000", bFill:true, fillColor:"#FF0000",iOpacity:80});
                API_SetCurDrawDynamicUseType(6);
                API_AddCancelButtonIsShow(true);
            }
            else {
                API_SetCurDrawDynamicUseType(0);
            }
        },

        // 退出测距模式
        exitRanging: function () {
            this.isRanging = false;
            API_SetCurDrawDynamicUseType(0);
            API_AddCancelButtonIsShow(false);
        },

        //显示测距信息
        GetCurMeasureDist: function (CurDis, allMeasureDist, CurDegrees) {
            var allMile = parseInt(allMeasureDist * 1000); //转换成米
            var curMile = parseInt(CurDis * 1000);
            var curHaiLi = curMile / 1852;
            var curAllHaiLi = allMile / 1852;

            var strAllMile = "";
            var strCurMile = "";
            if (allMile > 1000) {
                strAllMile = (allMile / 1000).toFixed(2) + "千米（" + curAllHaiLi.toFixed(2) + "海里）";
            }
            else {
                strAllMile = allMile + "米（" + curHaiLi.toFixed(2) + "海里）";
            }

            if (curMile > 1000) {
                strCurMile = (curMile / 1000).toFixed(2) + "千米（" + curHaiLi.toFixed(2) + "海里）";
            }
            else {
                strCurMile = curMile + "米（" + curHaiLi.toFixed(2) + "海里）";
            }

            // document.getElementById("allMeasureDist").innerHTML = strAllMile;

            // document.getElementById("curDis").innerHTML = strCurMile;
            // var curFangWei = Math.round(CurDegrees * 1000) / 1000;
            // document.getElementById("curDegrees").innerHTML = curFangWei.toFixed(2) + "度";
        },

        // 打开定位弹窗
        openLocationDialog() {
            this.$refs.dinweiDialog.open();
        },

        //处理传进来的经纬度，然后调用locationOnMap方法在海图上定位
        onLocate: function(location) {
            this.locationLonD = location.longitude.deg;
            this.locationLonF = location.longitude.min;
            this.locationLonM = location.longitude.sec;
            this.locationLatD = location.latitude.deg;
            this.locationLatF = location.latitude.min;
            this.locationLatM = location.latitude.sec;
            this.locationOnMap();
        },

        // 定位弹窗关闭后，找到海图上的定位点，然后删除
        onDinweiDialogClose: function() {
            var layerPos = API_GetLayerPosById(global.g_iLocationLayerId);
            if (layerPos > -1) {
                var obj = API_GetObjectPosById(1, layerPos);
                if (obj) {
                    API_DelObjectByPos(obj.iLayerPos, obj.iObjPos);
                    API_ReDrawLayer();
                }
            }
        },

        //定位按钮--在海图上定位
        locationOnMap: function (location) {
            var objName = "定位";
            var arrObjPo = [];
            _this.locationLonD = _this.locationLonD == "" ? 0 : Number(_this.locationLonD);
            _this.locationLonF = _this.locationLonF == "" ? 0 : Number(_this.locationLonF);
            _this.locationLonM = _this.locationLonM == "" ? 0 : Number(_this.locationLonM);

            _this.locationLatD = _this.locationLatD == "" ? 0 : Number(_this.locationLatD);
            _this.locationLatF = _this.locationLatF == "" ? 0 : Number(_this.locationLatF);
            _this.locationLatM = _this.locationLatM == "" ? 0 : Number(_this.locationLatM);

            var lon =_this.locationLonD + (_this.locationLonF / 60) + (_this.locationLonM / 3600);
            var lat = _this.locationLatD + (_this.locationLatF / 60) + (_this.locationLatM / 3600);

            arrObjPo.push({x: lon * 10000000, y: lat * 10000000});

            var layerPos = API_GetLayerPosById(global.g_iLocationLayerId);
            var layerStylePos = global.g_iLocationStylePos;
            var bAddResult = false;
            if (layerPos > -1) {
                var objInfo = [];
                var arrExpAttrValue = []; //扩展字段，假如没有可以传入null

                objInfo.objType = 1;
                objInfo.layerPos = layerPos; //图层索引
                objInfo.objId = 1; //标注id **
                objInfo.name = objName; //标注名称
                objInfo.showText = objName; //显示内容
                objInfo.layerStylePos = layerStylePos; //使用样式索引
                arrExpAttrValue.push("来一个扩展字段"); //扩展字段信息
                var objPos = API_AddNewObject(objInfo, arrObjPo, arrExpAttrValue);

                if(objPos > -1) {
                    var objStyleInfo = [];

                    objStyleInfo.bShowImg = true;      //是否用图片显示
                    objStyleInfo.strImgSrc = "../../static/ico/location.png";
                    objStyleInfo.iImgWidth = 32; //图片的宽度
                    objStyleInfo.iImgHeight = 32; //图片的高度
                    objStyleInfo.iOpacity = 90;
                    objStyleInfo.textColor = "#000000";     //标注名称字体颜色(字符串)，例如"#000000"
                    objStyleInfo.fontSize = "16px 宋体";      //标注名称字体(字符串)，格式例如"12px 宋体"
                    objStyleInfo.bShowText = true;     //是否显示信息(布尔)，true=显示，false=不显示
                    objStyleInfo.iTextOpacity = 90;  //文本透明度(数字)，0~100，100为不透明

                    API_SetCompositeObjStyleByPos(layerPos, objPos, true, objStyleInfo);
                }
                // 设置权限
                var setCurEditObjInfo = [];
                var lastObjInfo = API_GetNotEditObjInfo();
                for(var iii = 0; iii < lastObjInfo.length; iii++) {
                    setCurEditObjInfo.push({layerId: lastObjInfo[iii].layerId, objId: lastObjInfo[iii].objId});
                }
                setCurEditObjInfo.push({layerId: global.g_iLocationLayerId, objId: 1});
                API_SetNotEditObjInfo(setCurEditObjInfo);
                API_ReDrawLayer();
            }
            var obj = API_GetObjectPosById(1, layerPos);
            API_SetMapLevel(8, null);
            _this.curLevel = 8;
            API_SetObjToMapViewCenterByPos(obj.iLayerPos, obj.iObjPos);
            API_ReDrawLayer();
        },


        // 新增：处理移动端触摸事件
        handleTouchStart(event) {
            event.preventDefault(); // 阻止默认行为

            if (event.touches && event.touches.length > 0) {
                const touch = event.touches[0];

                // 获取触摸点在屏幕上的位置
                const scrnPo = {
                    x: touch.clientX,
                    y: touch.clientY
                };
 
                // 检查是否点击了摄像头对象
                var selObjInfo = API_SelectCurScrnShowObjectInfoByScrnPo(scrnPo, true);
                if (selObjInfo && selObjInfo.length > 0) {
                    for (var i = 0; i < selObjInfo.length; i++) {
                        if (selObjInfo[i].layerId === global.g_iCameraLayerId) {
                            console.log('移动端：摄像头被点击了！');

                            // 处理摄像头点击
                            this.handleCameraClick(selObjInfo[i]);
                            return; // 找到摄像头后直接返回
                        }
                    }
                }
                
                // 使用更大的检测范围(150而不是默认的50)来检测船舶
                const shipInfo = API_GetShipInfoByScrnPo(scrnPo, 10);

                if (shipInfo != null) {
                    if (shipInfo.bClusterers == true) {
                        // 处理聚合点的逻辑
                    } else {
                        // 获取船舶ID
                        const shipId = shipInfo.shipId || shipInfo.bdid || shipInfo.bdId || shipInfo.staticShipId;
                        if (shipId) {
                            // 打开SimpleDialog
                            this.getSelectedShipInfo(shipId);

                            // 如果需要可以模拟点击效果
                            const feedback = document.createElement('div');
                            feedback.style.position = 'absolute';
                            feedback.style.left = (touch.clientX - 15) + 'px';
                            feedback.style.top = (touch.clientY - 15) + 'px';
                            feedback.style.width = '30px';
                            feedback.style.height = '30px';
                            feedback.style.borderRadius = '50%';
                            feedback.style.backgroundColor = 'rgba(255, 255, 255, 0.3)';
                            feedback.style.zIndex = 10000;
                            document.body.appendChild(feedback);

                            setTimeout(() => {
                                document.body.removeChild(feedback);
                            }, 300);
                        }
                    }
                }
            }
        },

        // 获取船舶信息
        getShipInfo: function (sec) {
            if (sec == null) {
                sec = 20;
            }
            $.ajaxSettings.async = true;
            $.get(global.IP + "/web/GetBDShipPosition?sec=" + sec, function (data, status) {
                // 先清空地图上已有的船舶（如有API可用，可加上清空逻辑）
                // API_ClearAllShips && API_ClearAllShips(); // 如有此API可用

                // 先清空自定义按钮
                var mapDiv = document.getElementById('map');
                var oldBtns = mapDiv.querySelectorAll('.ship-info-btn');
                oldBtns.forEach(function (btn) { btn.remove(); });

                for (var i = 0; i < data.length; i++) {
                    var bOnlineOrNot = true;
                    var bShowTrack = false;
                    var curShipInfo = [];
                    curShipInfo.shipId = data[i].staticShipId;
                    curShipInfo.shipMMSI = data[i].bdid;
                    curShipInfo.shipName = data[i].shipname;
                    var ow = 'null';
                    var lx = 'null';
                    if (data[i].owner != null) ow = data[i].owner;
                    if (data[i].lxdh != null) lx = data[i].lxdh;
                    curShipInfo.customFields = ow + ',' + lx;
                    curShipInfo.shipGeoPoX = data[i].lon;
                    curShipInfo.shipGeoPoY = data[i].lat;
                    curShipInfo.shipWidth = data[i].width || 0;
                    curShipInfo.shipLength = data[i].length || 0;
                    curShipInfo.shipSpeed = data[i].speed;
                    curShipInfo.shipCourse = data[i].cog;
                    curShipInfo.shipTime = data[i].loadTime;
                    // 设置船舶状态，决定显示什么图标
                    if (data[i].shiptype == 2) {
                        curShipInfo.iShipState = 0;   //渔船
                    } else if (data[i].shiptype == 3 || data[i].shiptype == 65 || data[i].shiptype == 75) {
                        curShipInfo.iShipState = 1;   //执法船
                    } else if (data[i].shiptype == 4) {
                        curShipInfo.iShipState = 0;   //渔船
                    } else if (data[i].shiptype == 6 || data[i].shiptype == 69 ||
                        data[i].shiptype == 62 || data[i].shiptype == 64 || data[i].shiptype == 63 ||
                        data[i].shiptype == 66 || data[i].shiptype == 67 || data[i].shiptype == 68 || data[i].shiptype == 610) {
                        curShipInfo.iShipState = 7;   //商船
                    } else if (data[i].shiptype == 61) {
                        curShipInfo.iShipState = 3;   //外省渔船
                    } else if (data[i].shiptype == 7 || data[i].shiptype == 71 ||
                        data[i].shiptype == 72 || data[i].shiptype == 74 || data[i].shiptype == 73 ||
                        data[i].shiptype == 76 || data[i].shiptype == 77 || data[i].shiptype == 78 || data[i].shiptype == 79 || data[i].shiptype == 710) {
                        curShipInfo.iShipState = 7;   //商船
                    } else {
                        curShipInfo.iShipState = 7;
                    }
                    if (data[i].boutside == 1) {
                        curShipInfo.iShipState = 3;
                    }
                    curShipInfo.bOnlineOrNot = bOnlineOrNot;
                    curShipInfo.bShowTrack = bShowTrack;
                    curShipInfo.arrExpAttrValue = [];
                    // 补充必要字段
                    curShipInfo.staticShipId = data[i].staticShipId || data[i].bdid || data[i].bdId;
                    curShipInfo.shipName = data[i].shipname || data[i].shipName;
                    curShipInfo.lon = data[i].lon;
                    curShipInfo.lat = data[i].lat;
                    // 打印添加船舶curShipInfo到控制台
                    //console.log('添加船舶', curShipInfo);
                    // 直接添加到地图（如需去重可加判断）
                    API_AddOneShip(curShipInfo);

                    // 添加自定义按钮
                    if (typeof API_LonLatToScrnPo === 'function') {
                        var scrnPo = API_LonLatToScrnPo({ x: data[i].lon, y: data[i].lat });
                        if (scrnPo) {
                            var btn = document.createElement('button');
                            btn.innerText = '详情';
                            btn.className = 'ship-info-btn';
                            btn.style.position = 'absolute';
                            btn.style.left = (scrnPo.x - 20) + 'px';
                            btn.style.top = (scrnPo.y - 20) + 'px';
                            btn.style.zIndex = 9999;
                            btn.onclick = (function (id, name) {
                                return function () {
                                    alert('staticShipId: ' + id + '\nshipname: ' + name);
                                }
                            })(data[i].staticShipId, data[i].shipname);
                            mapDiv.appendChild(btn);
                        }
                    }
                }
                API_SetShipSpeedLineSize(0, false);
                API_SetShipHeadLineSize(0, false);
            }).fail(function (msg) {
                console.log("error：" + JSON.stringify(msg))
            });
            $.ajaxSettings.async = true;
        },

        //更新渔船状态
        updateShipState: function () {
            for (var k = 0; k < global.jianCeUpdateShipInfo.length; k++) {
                var shipId = global.jianCeUpdateShipInfo[k].shipId;
                var shipStste = 0;
                if (global.jianCeUpdateShipInfo[k].isCheck == true) {
                    shipStste = 2;
                }
                else {
                    if (global.jianCeUpdateShipInfo[k].shiptype == 2) {
                        shipStste = 0;   //状态
                    }
                    else if (global.jianCeUpdateShipInfo[k].shiptype == 3) {
                        shipStste = 1;   //状态
                    }
                    else if (global.jianCeUpdateShipInfo[k].shiptype == 4) {
                        shipStste = 0;   //状态
                    }
                    else if (global.jianCeUpdateShipInfo[k].shiptype == 6 || global.jianCeUpdateShipInfo[k].shiptype == 61 ||
                        global.jianCeUpdateShipInfo[k].shiptype == 62 || global.jianCeUpdateShipInfo[k].shiptype == 63 ||
                        global.jianCeUpdateShipInfo[k].shiptype == 64 || global.jianCeUpdateShipInfo[k].shiptype == 65 ||
                        global.jianCeUpdateShipInfo[k].shiptype == 66 || global.jianCeUpdateShipInfo[k].shiptype == 67 ||
                        global.jianCeUpdateShipInfo[k].shiptype == 68 || global.jianCeUpdateShipInfo[k].shiptype == 69 ||
                        global.jianCeUpdateShipInfo[k].shiptype == 610) {
                        shipStste = 3;   //状态
                    }
                    else if (global.jianCeUpdateShipInfo[k].shiptype == 7 || global.jianCeUpdateShipInfo[k].shiptype == 71 ||
                        global.jianCeUpdateShipInfo[k].shiptype == 72 || global.jianCeUpdateShipInfo[k].shiptype == 73 ||
                        global.jianCeUpdateShipInfo[k].shiptype == 74 || global.jianCeUpdateShipInfo[k].shiptype == 75 ||
                        global.jianCeUpdateShipInfo[k].shiptype == 76 || global.jianCeUpdateShipInfo[k].shiptype == 77 ||
                        global.jianCeUpdateShipInfo[k].shiptype == 78 || global.jianCeUpdateShipInfo[k].shiptype == 79 ||
                        global.jianCeUpdateShipInfo[k].shiptype == 710) {
                        shipStste = 7;   //状态
                    }
                    else {
                        shipStste = 7;
                    }

                    if (global.jianCeUpdateShipInfo[k].bOutSide == 1) {
                        shipStste = 3;
                    }
                    global.jianCeUpdateShipInfo.splice(k, 1);
                }

                var pos = API_GetShipPosById(shipId);
                if (pos != -1) {
                    var info = API_GetShipInfoByPos(pos);
                    if (info != null) {
                        var curShipDynamicInfo = [];//更新的时候，只要把这些值设置好即可
                        curShipDynamicInfo.shipGeoPoX = info.shipGeoPoX;
                        curShipDynamicInfo.shipGeoPoY = info.shipGeoPoY;
                        curShipDynamicInfo.shipSpeed = info.shipSpeed;
                        curShipDynamicInfo.shipCourse = info.shipCourse;
                        curShipDynamicInfo.shipTime = info.shipTime;
                        curShipDynamicInfo.iShipState = shipStste;
                        curShipDynamicInfo.bOnlineOrNot = info.bOnlineOrNot;

                        API_UpdateOneShipDynamicInfoByPos(pos, curShipDynamicInfo); //更新一艘船舶动态信息
                    }
                }
            }

            for (var k = 0; k < global.focusUpdateShipInfo.length; k++) {
                var shipId = global.focusUpdateShipInfo[k].shipId;
                var shipStste = 0;
                if (global.focusUpdateShipInfo[k].isCheck == true) {
                    shipStste = 4;
                }
                else {
                    if (global.focusUpdateShipInfo[k].shiptype == 2) {
                        shipStste = 0;   //状态
                    }
                    else if (global.focusUpdateShipInfo[k].shiptype == 3 || global.focusUpdateShipInfo[k].shiptype == 63 ||
                        global.focusUpdateShipInfo[k].shiptype == 73) {
                        shipStste = 1;   //状态
                    }
                    else if (global.focusUpdateShipInfo[k].shiptype == 4) {
                        shipStste = 0;   //状态
                    }
                    else if (global.focusUpdateShipInfo[k].shiptype == 6 || global.focusUpdateShipInfo[k].shiptype == 61 ||
                        global.focusUpdateShipInfo[k].shiptype == 62 ||
                        global.focusUpdateShipInfo[k].shiptype == 64 || global.focusUpdateShipInfo[k].shiptype == 65 ||
                        global.focusUpdateShipInfo[k].shiptype == 66 || global.focusUpdateShipInfo[k].shiptype == 67 ||
                        global.focusUpdateShipInfo[k].shiptype == 68 || global.focusUpdateShipInfo[k].shiptype == 69 ||
                        global.focusUpdateShipInfo[k].shiptype == 610) {
                        shipStste = 3;   //状态
                    }
                    else if (global.focusUpdateShipInfo[k].shiptype == 7 || global.focusUpdateShipInfo[k].shiptype == 71 ||
                        global.focusUpdateShipInfo[k].shiptype == 72 ||
                        global.focusUpdateShipInfo[k].shiptype == 74 || global.focusUpdateShipInfo[k].shiptype == 75 ||
                        global.focusUpdateShipInfo[k].shiptype == 76 || global.focusUpdateShipInfo[k].shiptype == 77 ||
                        global.focusUpdateShipInfo[k].shiptype == 78 || global.focusUpdateShipInfo[k].shiptype == 79 ||
                        global.focusUpdateShipInfo[k].shiptype == 710) {
                        shipStste = 7;   //状态
                    }
                    else {
                        shipStste = 7;
                    }

                    if (global.focusUpdateShipInfo[k].bOutSide == 1) {
                        shipStste = 3;
                    }
                    global.focusUpdateShipInfo.splice(k, 1);
                }

                var pos = API_GetShipPosById(shipId);
                if (pos != -1) {
                    if (shipStste == 4) {
                        API_SetShipAllFollow(pos, true);
                    }
                    else {
                        API_SetShipAllFollow(pos, false);
                    }

                    var info = API_GetShipInfoByPos(pos);
                    if (info != null) {
                        var curShipDynamicInfo = [];//更新的时候，只要把这些值设置好即可
                        curShipDynamicInfo.shipGeoPoX = info.shipGeoPoX;
                        curShipDynamicInfo.shipGeoPoY = info.shipGeoPoY;
                        curShipDynamicInfo.shipSpeed = info.shipSpeed;
                        curShipDynamicInfo.shipCourse = info.shipCourse;
                        curShipDynamicInfo.shipTime = info.shipTime;
                        curShipDynamicInfo.iShipState = shipStste;
                        curShipDynamicInfo.bOnlineOrNot = info.bOnlineOrNot;

                        API_UpdateOneShipDynamicInfoByPos(pos, curShipDynamicInfo); //更新一艘船舶动态信息
                    }
                }
            }

            for (var k = 0; k < global.warningUpdateShipInfo.length; k++) {
                var shipId = global.warningUpdateShipInfo[k].shipId;
                var shipStste = 0;
                if (global.warningUpdateShipInfo[k].isCheck == true && _this.legengShow[8] == true) {
                    // 该船是预警船
                    shipStste = 5;
                }
                else {
                    if (global.warningUpdateShipInfo[k].shiptype == 2) {
                        shipStste = 0;   //状态
                    }
                    else if (global.warningUpdateShipInfo[k].shiptype == 3 || global.warningUpdateShipInfo[k].shiptype == 65 || global.warningUpdateShipInfo[k].shiptype == 75) {
                        shipStste = 1;   //状态
                    }
                    else if (global.warningUpdateShipInfo[k].shiptype == 4) {
                        shipStste = 0;   //状态
                    }
                    else if (global.warningUpdateShipInfo[k].shiptype == 6 || global.warningUpdateShipInfo[k].shiptype == 61 ||
                        global.warningUpdateShipInfo[k].shiptype == 62 || global.warningUpdateShipInfo[k].shiptype == 63 ||
                        global.warningUpdateShipInfo[k].shiptype == 64 ||
                        global.warningUpdateShipInfo[k].shiptype == 66 || global.warningUpdateShipInfo[k].shiptype == 67 ||
                        global.warningUpdateShipInfo[k].shiptype == 68 || global.warningUpdateShipInfo[k].shiptype == 69 ||
                        global.warningUpdateShipInfo[k].shiptype == 610) {
                        shipStste = 3;   //状态
                    }
                    else if (global.warningUpdateShipInfo[k].shiptype == 7 || global.warningUpdateShipInfo[k].shiptype == 71 ||
                        global.warningUpdateShipInfo[k].shiptype == 72 || global.warningUpdateShipInfo[k].shiptype == 73 ||
                        global.warningUpdateShipInfo[k].shiptype == 74 ||
                        global.warningUpdateShipInfo[k].shiptype == 76 || global.warningUpdateShipInfo[k].shiptype == 77 ||
                        global.warningUpdateShipInfo[k].shiptype == 78 || global.warningUpdateShipInfo[k].shiptype == 79 ||
                        global.warningUpdateShipInfo[k].shiptype == 710) {
                        shipStste = 7;   //状态
                    }
                    else {
                        shipStste = 7;
                    }

                    if (global.warningUpdateShipInfo[k].bOutSide == 1) {
                        shipStste = 3;
                    }
                    global.warningUpdateShipInfo.splice(k, 1);
                }

                var pos = API_GetShipPosById(shipId);
                if (pos != -1) {
                    var info = API_GetShipInfoByPos(pos);
                    if (info != null) {
                        var curShipDynamicInfo = [];//更新的时候，只要把这些值设置好即可
                        curShipDynamicInfo.shipGeoPoX = info.shipGeoPoX;
                        curShipDynamicInfo.shipGeoPoY = info.shipGeoPoY;
                        curShipDynamicInfo.shipSpeed = info.shipSpeed;
                        curShipDynamicInfo.shipCourse = info.shipCourse;
                        curShipDynamicInfo.shipTime = info.shipTime;
                        curShipDynamicInfo.iShipState = shipStste;
                        curShipDynamicInfo.bOnlineOrNot = info.bOnlineOrNot;

                        API_UpdateOneShipDynamicInfoByPos(pos, curShipDynamicInfo); //更新一艘船舶动态信息
                    }
                }
            }
        },

        //根据船名和终端号搜索
        handleSearch() {
            // console.log('执行搜索' + this.searchQuery + global.IP + "/web/QueryShip?keyword=" + this.searchQuery);
            var searchVal = this.searchQuery;
            if (searchVal == null || searchVal == undefined || searchVal == '') {
                setAlertWindowShow('search-container', '未输入数据', '', 2);
                return;
            }

            $.get(global.IP + "/web/QueryShip?keyword=" + searchVal, function (data, status) {
                if (data.length == 0) {
                    _this.showSearchResults = false;
                    setAlertWindowShow('search-container', '未找到匹配结果', '', 2);
                    return;
                }
                // console.log('搜索结果：', data[0].staticShipId);
                _this.searchResults = data.slice(0, 10).map(item => ({
                    shipMmsi: item.mmsi,
                    shipName: item.shipName,
                    staticShipId: item.staticShipId,
                }));
                _this.showSearchResults = true;
            }).fail(function (msg) {
                console.log("error：" + JSON.stringify(msg))
            });
        },

        // 获取选中船舶信息
        getSelectedShipInfo(shipId, showTrackCard = false) {
            $.get(global.IP + '/web/GetOneShipInfoById?id=' + shipId, (data, status) => {
                if (data && data.length > 0) {
                    this.selectedShip = data[0];
                    this.showSimpleDialog = true;
                    this.showShipTrackCard = showTrackCard;
                } else {
                    this.error = '未找到该船舶信息';
                }
            }).fail((msg) => {
                this.error = '获取船舶信息失败';
            });
        },

        // 点击船舶
        handleShipClick(ship) {
            // 确保船舶ID存在
            if (!ship || !ship.staticShipId) {
                console.log('船舶信息不完整');
                this.closeSearchResults();
                return;
            }
            // console.log('点击选项：', ship.staticShipId);
            // 船舶居中
            var shipPos = API_GetShipPosById(ship.staticShipId);
            if (shipPos > -1) {
                if (API_GetMapLevel() < 8) {
                    API_SetMapLevel(10, null);
                }
                API_SetShipToMapViewCenterByPos(shipPos);
                API_ReDrawShips();

                //获取被选择船舶的详细数据
                this.getSelectedShipInfo(ship.staticShipId, true);
            } else {
                console.log('未找到该船舶位置信息');
            }

            // 关闭搜索结果
            this.closeSearchResults();
        },

        // 关闭搜索结果
        closeSearchResults() {
            this.showSearchResults = false;
        },



        showLocation: function (shipPos) {
            setTimeout(function () {
                // API_SetMapLevel(10, null);
                API_SetShipToMapViewCenterByPos(shipPos);
                API_ReDrawShips();
            }, 500);
        },

        //鼠标事件
        // =====================return sdk
        ReturnSelectObjByMouseMoveTest: function (objInfo) {

            var lonEl = document.getElementById('lonText');
            if (lonEl) {
                lonEl.innerHTML = parseInt(API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).x) + '°' +
                    + parseInt((API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).x - parseInt(API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).x)) * 60) + "'" +
                    parseInt(((API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).x - parseInt(API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).x)) * 60 - parseInt((API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).x - parseInt(API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).x)) * 60)) * 60) + '"' + 'E';
            }
            var latEl = document.getElementById('latText');
            if (latEl) {
                latEl.innerHTML = parseInt(API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).y) + '°' +
                    + parseInt((API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).y - parseInt(API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).y)) * 60) + "'" +
                    parseInt(((API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).y - parseInt(API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).y)) * 60 - parseInt((API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).y - parseInt(API_GetLonLatPoByScrnPo(API_GetCurMouseScrnPo().x, API_GetCurMouseScrnPo().y, null).y)) * 60)) * 60) + '"' + 'N';
            }
            //API_SetCurHighLightObjectById(-1, -1);
            if (objInfo) {
                var scrnPo = objInfo.po;
                switch (objInfo.objType) {//{objType,id,po}
                    case 1: //选中了船舶，得到船舶的id,pos
                        if (objInfo.bClusterers == true) {

                        }
                        else {
                            var iShipId = objInfo.id;
                            var bSelPlayTrackShip = objInfo.bSelPlayTrackShip;//是否选中了轨迹回放的船舶
                            var iTrackPos = objInfo.iTrackPos; //假如是轨迹回放，则是选中轨迹点pos
                        }
                        break;
                    case 2: //选中了标注，得到标注所属的图层layerId以及标注objId
                        var layerId = objInfo.layerId;//图层的id
                        var objId = objInfo.objId;//标注的id

                        if (layerId == global.g_iWeatherLayerId) {
                            _this.$options.methods.ShowObjSimpleInfo.bind(this)(layerId, objId, scrnPo);
                        }
                        else if (layerId == global.g_iCameraLayerId) {
                            _this.$options.methods.showCameraInfo.bind(this)(layerId, objId, scrnPo);
                        }
                        else if (layerId == global.g_iMarkLayerId) {
                            _this.$options.methods.showMarkInfo.bind(this)(layerId, objId, scrnPo);
                        }
                        else if (layerId == global.g_iJianCeLayerId) {
                            _this.$options.methods.showJianCeInfo.bind(this)(layerId, objId, scrnPo);
                        }
                        else {
                            if (global.g_showSimpleInfoDiv) {
                                global.g_showSimpleInfoDiv.style.display = "none";
                            }
                        }

                        break;
                    case 3: //选中了台风轨迹信息
                        var typhoonId = objInfo.typhoonId; //台风id
                        var iTruePos = objInfo.iTruePos; //台风真实轨迹点pos
                        var iPredictPos = objInfo.iPredictPos; //真实轨迹点的预测轨迹点pos
                        var iPredictLinePos = objInfo.iPredictLinePos; //真实轨迹点的预测轨迹点pos
                        _this.$options.methods.ShowTyphoonTrackSimpleInfo.bind(this)(typhoonId, iTruePos, iPredictLinePos, iPredictPos, scrnPo);
                        break;
                    case 5: //选中了船舶，得到船舶的id,pos
                        if (objInfo.bClusterers == true) {

                        }
                        else {
                            _this.$options.methods.ShowShipSimpleInfo.bind(this)(objInfo);
                        }
                        break;
                    case 6: //选中了船舶，得到船舶的id,pos
                        if (objInfo.bClusterers == true) {

                        }
                        else {
                            _this.$options.methods.ShowShipSimpleInfo.bind(this)(objInfo);
                        }
                        break;
                }
            }
            else {
                if (global.g_showSimpleInfoDiv) {
                    global.g_showSimpleInfoDiv.style.display = "none";
                }
            }
        },

        /**
         * PC端鼠标左击时查询到的对象信息处理函数
         * 该函数是地图SDK的回调函数，当用户在PC端用鼠标左键点击地图上的对象时会被调用
         * @param {Object} objInfo - 点击对象的信息
         * @param {number} objInfo.objType - 对象类型：1=船舶，2=标注，3=台风等
         * @param {number} objInfo.layerId - 图层ID
         * @param {number} objInfo.objId - 对象ID
         */
        ReturnSelectObjByMouseLeftDownTest: function (objInfo) {

            // 如果没有点击到任何对象，直接返回
            if (objInfo == null) {
                return;
            }

            if (objInfo) {
                console.log(API_GetShipInfoByPos(API_GetShipPosById(500)).arrShipTrackPoints[API_GetShipInfoByPos(API_GetShipPosById(500)).arrShipTrackPoints.length - 1]);

                // 根据对象类型进行不同的处理
                switch (objInfo.objType) {
                    case 1: //选中了船舶，得到船舶的id,pos
                        break;
                    case 2: //选中了标注，得到标注所属的图层id以及标注id
                        var layerId = objInfo.layerId; //图层的id
                        var objId = objInfo.objId; //标注的id

                        // 检查是否点击了摄像头图层的对象
                        if (layerId == global.g_iCameraLayerId) {
                            // PC端摄像头点击处理
                            console.log('PC端：摄像头被点击了！');
                            console.log('摄像头ID:', objId);
                            console.log('摄像头图层ID:', layerId);

                            // 调用摄像头点击处理方法
                            // 该方法会获取摄像头详细信息并显示视频播放器
                            _this.handleCameraClick({objId: objId, layerId: layerId});
                        }

                        break;
                    case 3:
                        _this.curTyphoonRadiusInfo = [];
                        var typhoonPos = API_GetTyphoonPosById(objInfo.typhoonId);
                        var typhoonInfoBysdk = API_GetTyphoonInfoByPos(typhoonPos);
                        var typhoonBysdk = API_GetTyphoonTrackInfoByPos(typhoonPos, objInfo.iTruePos, -1, null);

                        var radius7 = 0;
                        var radius10 = 0;
                        if (typhoonBysdk.sevenRadius != undefined) {
                            radius7 = typhoonBysdk.sevenRadius;
                        }
                        if (typhoonBysdk.tenRadius != undefined) {
                            radius10 = typhoonBysdk.tenRadius;
                        }
                        _this.curTyphoonRadiusInfo.push({ po: typhoonBysdk.po, radius7: radius7, radius10: radius10 });
                        break;
                    case 5: //选中了船舶，得到船舶的id,pos
                        if (objInfo.bClusterers == true) {
                            var clustererShipLength = API_GetClustererShipIdInfoById(objInfo.data.id).length;
                            global.g_arrSelectShipInfoObj = { iShipCount: 0, arrShipInfo: [] };
                            global.g_arrSelectShipInfoObj.arrShipInfo = [];
                            for (var i = 0; i < clustererShipLength; i++) {
                                var pos = API_GetShipPosById(API_GetClustererShipIdInfoById(objInfo.data.id)[i]);
                                var info = API_GetShipInfoByPos(pos);
                                global.g_arrSelectShipInfoObj.arrShipInfo.push(info);
                            }
                            _this.ShipSearch1Name = "船舶详情(船舶聚合)";
                            _this.wsShipInShanghaiCheck = false;
                            _this.showOutLineShipStart = false;
                            _this.areaSearchShow = false;
                            _this.showOutLineShip = false;
                            _this.ifFishArea = false;
                            _this.$options.methods.changeCurIndex.bind(this)("ShipSearch1");
                            _this.$options.methods.setShipInfoInTable.bind(this)();

                        }
                        else {
                            var shipPos = API_GetShipPosById(objInfo.id);
                            sessionStorage.setItem("SHIPID", objInfo.id);
                            if (shipPos != -1) {
                                API_SelectShipShow(true);
                                API_SetSelectShipByPos(shipPos);
                                API_SetCurShipShowHistory(true);
                                _this.$options.methods.getDetailsAndSurroundingFishInfo.bind(this)(objInfo.id);
                                _this.$options.methods.setAllShipHistory.bind(this)();
                            }
                        }
                        break;
                    case 6: //选中了船舶，得到船舶的id,pos
                        break;
                }
            }
            else {
                if (global.g_showSimpleInfoDiv) {
                    global.g_showSimpleInfoDiv.style.display = "none";
                }
            }
        },

        // 设置当前点击船舶-船舶详细信息、周围渔船
        getDetailsAndSurroundingFishInfo: function (id) {
            var shipPos = API_GetShipPosById(id);
            if (shipPos == -1) {
                _this.$options.methods.centerToShip.bind(this)(id);
            }
            shipPos = API_GetShipPosById(id);
            global.focusOnTrackingShips = [];
            global.focusOnTrackingShips.push({ shipId: id });
            var shipInfo = null;
            if (shipPos > -1) {
                API_SetSelectShipByPos(shipPos);
                var info = API_GetShipInfoByPos(shipPos);
                $.get(global.IP + '/web/GetOneShipInfoById?id=' + id, function (data, status) {

                    _this.shipInfoList = [];
                    var shipInfo11 = [];
                    shipInfo11 = data;
                    switch (shipInfo11[0].shiptype) {
                        case 2:
                        case 61:
                            shipInfo11[0].shiptype = "渔船";
                            break;
                        case 3:
                            shipInfo11[0].shiptype = "渔政船";
                            break;
                        case 4:
                            shipInfo11[0].shiptype = "救援船";
                            break;
                        case 6:
                            shipInfo11[0].shiptype = "货船";
                            break;
                        case 7:
                            shipInfo11[0].shiptype = "货船";
                            break;
                        case 62:
                        case 72:
                            shipInfo11[0].shiptype = "货船";
                            break;
                        case 63:
                        case 73:
                            shipInfo11[0].shiptype = "客船";
                            break;
                        case 65:
                        case 75:
                        case 13:
                            shipInfo11[0].shiptype = "执法船";
                            break;
                        default:
                            shipInfo11[0].shiptype = "其他船";
                            break;
                    }
                    _this.shipInfoList = shipInfo11;
                    var info = API_GetShipInfoByPos(shipPos);
                    var lon = _this.$options.methods.ToDegrees.bind(this)(info.shipGeoPoX / 10000000 + "");
                    var lat = _this.$options.methods.ToDegrees.bind(this)(info.shipGeoPoY / 10000000 + "");
                    var point = lon + ' - ' + lat;


                    var lengthCount = 0;
                    if (data[0].length >= 12 && data[0].length < 24) lengthCount = 2;
                    else if (data[0].length >= 24 && data[0].length < 36) lengthCount = 2;
                    else if (data[0].length >= 36 && data[0].length < 45) lengthCount = 2;
                    else if (data[0].length >= 45) lengthCount = 3;
                    var glCount = 0;
                    if (data[0].zjglqw >= 50 && data[0].zjglqw < 250) glCount = 1;
                    else if (data[0].zjglqw >= 250 && data[0].zjglqw < 450) glCount = 2;
                    else if (data[0].zjglqw >= 450 && data[0].zjglqw < 750) glCount = 2;
                    else if (data[0].zjglqw >= 750 && data[0].zjglqw < 3000) glCount = 2;
                    else if (data[0].zjglqw >= 3000) glCount = 3;
                    if (data[0].zjglqw > 800) glCount++;

                    _this.shipInfoList1 = [];
                    console.log('7.' + info.bdTime + '-- --' + info.aisTime);
                    _this.shipInfoList1.push({ "point": point, 'speed': info.shipSpeed, 'time': info.shipTime, 'bdTime': data[0].bdTime, 'aisTime': data[0].aisTime, 'course': info.shipCourse, "littleCount": 0 + lengthCount + glCount });
                    _this.ocusOnTrackingTheDetailsOfFishingVesselsList = data;
                    _this.bdMsgShipInfo = [];
                    _this.bdMsgShipInfo.push({ shipId: id, name: data[0].shipname, bdId: data[0].bdid, lastPosTermNo: data[0].lastPosTermNo });
                });
                $.get(global.IP + '/web/GetShipImgUrl?shipId=' + id, function (data, status) {
                    _this.shipInfoImg = data.replace(/\s*/g, "");
                });
                API_ReDrawShips();
                global.curSelectShipId = id;
                document.getElementById('ShipSimpleInformationView').style.display = 'block';
                _this.$options.methods.changeCurIndex.bind(this)("ShipSimpleInformationView");
                _this.surroundingFishingBoatsList = [];
                if (info != null) {
                    _this.surroundingFishingBoatsList.push({ shipId: id, x: info.shipGeoPoX, y: info.shipGeoPoY });
                }

                _this.$options.methods.areaLocation.bind(this)(id);
            }
        },
        //鼠标右键事件
        //scrnPo:鼠标在海图上的位置
        ReturnOnMouseRightDownTest: function (scrnPo) {
            var selObjInfo = API_SelectCurScrnShowObjectInfoByScrnPo(scrnPo, true);
            if (selObjInfo) {
                // console.log(selObjInfo);
                // console.log(API_GetCurMapMouseState());
                if (API_GetCurMapMouseState() != 0) {
                    return
                }
                // 选中算法
                var objInfo = _this.$options.methods.selectObjInfoByRight.bind(this)(selObjInfo);

                if (objInfo.layerId == -1 || objInfo.objId == -1) {
                    return
                }

                // 显示港口信息
                if (objInfo.layerId == global.g_iFishingPortLayerId) {
                    for (var i = 0; i < global.allPortInfo.length; i++) {
                        if (global.allPortInfo[i].id == objInfo.objId) {
                            _this.portProfileInfo = objInfo.objId;
                            _this.$options.methods.changeCurIndex.bind(this)("portProfileView");
                            document.getElementById('portProfileView').style.display = 'block';
                        }
                    }
                }

                if (objInfo.layerId == global.g_iAreaFuxiuLayerId || objInfo.layerId == global.g_iAreaGaoweiLayerId || objInfo.layerId == global.g_iAreaTeshuLayerId || objInfo.layerId == global.g_iAreaSelfLayerId || objInfo.layerId == global.g_iComprehensiveLayerId) {
                    _this.$options.methods.showEditFenceInfo.bind(this)(objInfo.objId);
                }

                if (objInfo.layerId == global.g_iMarkLayerId) {
                    var count = 1;
                    var layerPos = API_GetLayerPosById(objInfo.layerId);
                    var obj = API_GetObjectPosById(objInfo.objId, layerPos);
                    _this.marklayerPos = layerPos;
                    _this.markobjPos = obj;
                    _this.markLayerId = objInfo.layerId;
                    _this.markObjId = objInfo.objId;
                    var markInfo = {};
                    $.ajaxSettings.async = false;
                    $.get(global.IP + "/web/GetMarkInfoById?id=" + objInfo.objId, function (data, status) {
                        markInfo["type"] = data.type;
                        markInfo["poscount"] = data.posCount;
                        markInfo["content"] = data.content;
                        markInfo["loadTime"] = data.loadTime;
                        markInfo["userId"] = data.userId;
                        markInfo["ownShow"] = data.ownShow;
                        markInfo["radius"] = data.radius;
                        markInfo["pointStr"] = data.pointStr;
                        markInfo["id"] = data.id;
                        markInfo["lon"] = "";
                        markInfo["lat"] = "";

                    });
                    $.ajaxSettings.async = true;
                    _this.markStr = markInfo;

                    if (_this.markStr["type"] == 2) {
                        document.getElementById("markName").innerHTML = _this.markStr.content;
                        _this.editradiusShow = false;
                        _this.editpointOrLineShow = true;
                        var table = document.getElementById("EditSomethingInfoTable");
                        var trs = table.getElementsByTagName("tr");
                        for (var i = trs.length - 1; i > 0; i--) {
                            trs[i].remove();
                        }
                        var pointStrLength = _this.markStr["pointStr"].split('#').length;
                        for (var j = 0; j < pointStrLength; j++) {
                            var geoPo = { x: coordinateTransformation(parseInt(_this.markStr["pointStr"].split('#')[j].split('@')[0]) / 10000000), y: coordinateTransformation(parseInt(_this.markStr["pointStr"].split('#')[j].split('@')[1]) / 10000000) };
                            _this.geoPoAll.push(geoPo);
                            _this.markStr["lon"] += _this.markStr["lon"] + coordinateTransformation(parseInt(_this.markStr["pointStr"].split('#')[j].split('@')[0]) / 10000000) + ",";
                            _this.markStr["lat"] += _this.markStr["lat"] + coordinateTransformation(parseInt(_this.markStr["pointStr"].split('#')[j].split('@')[1]) / 10000000) + ",";
                            var str =
                                "<tr style='height: 40px;'>" +
                                "<td data-size='padding-top,padding-left' style='border: 0px; padding-top: 10px; text-align:center;'>" +
                                (j + 1) +
                                "</td>" +
                                "<td data-size='padding-top' style='border: 0px; padding-top: 10px; text-align:center;'>" +
                                _this.geoPoAll[j].x +
                                "</td>" +
                                "<td data-size='padding-top' style='border: 0px; padding-top: 10px; text-align:center;'>" +
                                _this.geoPoAll[j].y +
                                "</td></tr>";
                            $("#EditSomethingInfoTable").append(str);
                        }
                    }
                    else if (_this.markStr["type"] == 5) {
                        document.getElementById("markName").innerHTML = _this.markStr.content;
                        _this.pointOrLineShow = true;
                        _this.editpointOrLineShow = false;
                        _this.curMarkTypeIndex = 5;
                        _this.editradiusShow = true;
                        _this.markStr["lon"] = coordinateTransformation(parseInt(_this.markStr["pointStr"].split('@')[0]) / 10000000);
                        _this.markStr["lat"] = coordinateTransformation(parseInt(_this.markStr["pointStr"].split('@')[1]) / 10000000);
                        document.getElementById("markName").innerHTML = _this.markStr.content;
                        document.getElementById("markLon").innerHTML = _this.markStr.lon;
                        document.getElementById("markLat").innerHTML = _this.markStr.lat;
                        document.getElementById("markRadius").innerHTML = _this.markStr.radius;
                    }
                    else if (_this.markStr["type"] == 1) {
                        document.getElementById("markName").innerHTML = _this.markStr.content;

                        _this.editradiusShow = false;
                        _this.editPointShow = true;
                        _this.markStr["lon"] = coordinateTransformation(parseInt(_this.markStr["pointStr"].split('@')[0]) / 10000000);
                        _this.markStr["lat"] = coordinateTransformation(parseInt(_this.markStr["pointStr"].split('@')[1]) / 10000000);
                        _this.radiusCenterX = _this.markStr["lon"];
                        _this.radiusCenterY = _this.markStr["lat"];
                        var table = document.getElementById("EditSomethingInfoTable");
                        var trs = table.getElementsByTagName("tr");
                        for (var i = trs.length - 1; i > 0; i--) {
                            trs[i].remove();
                        }
                        var str =
                            "<tr style='height: 40px;'>" +
                            "<td data-size='padding-top,padding-left' style='border: 0px; padding-top: 10px; text-align:center;'>" +
                            count +
                            "</td>" +
                            "<td data-size='padding-top' style='border: 0px; padding-top: 10px; text-align:center;'>" +
                            _this.markStr["lon"] +
                            "</td>" +
                            "<td data-size='padding-top' style='border: 0px; padding-top: 10px; text-align:center;'>" +
                            _this.markStr["lat"] +
                            "</td></tr>";
                        $("#EditSomethingInfoTable").append(str);
                    }

                    _this.aLinkDlg = false;
                    _this.editDlg = true;
                    _this.confirmDlg = false;
                    _this.drawSomethingBoxTitle = "编辑标注";
                    if (_this.leftShow == true) {
                        _this.$options.methods.ShowWindow.bind(this)('left');
                    }

                    if (_this.rightShow == true) {
                        _this.$options.methods.ShowWindow.bind(this)('right');
                    }
                    // _this.ShowWindow('left');
                    // _this.ShowWindow('right');
                    document.getElementById("drawSomethingBox").style.display = "block";
                    _this.markwindow = false;
                    _this.editmarkwindow = true;
                }
            }
            else {
                API_SetCurHighLightObjectById(-1, -1);//取消高亮
            }

            if (global.g_bCurDrawFaceForSelectShip == true) {
                global.g_bCurDrawFaceForSelectShip = false;
                //多边形查询船舶
                var arrGeoPo = API_GetCurDrawDynamicObjGeoPo();//绘制的多边形坐标
                var retShipInfo = API_SelectShipsByGeoPolygon(arrGeoPo, -1); //查询多边形内的船舶
                if (retShipInfo) {
                    _this.$options.methods.selectShipByPolygon.bind(this)(retShipInfo);
                }
            }
        },

        //鼠标左键事件
        //scrnPo:鼠标在海图上的位置
        ReturnOnMouseLeftDownTest: function (scrnPo) {
            console.log(scrnPo);
            var selObjInfo = API_SelectCurScrnShowObjectInfoByScrnPo(scrnPo, true);
            if (selObjInfo) {
                //alert(selObjInfo.length);
            }

            // PC端使用默认的检测范围
            var shipInfo = API_GetShipInfoByScrnPo(scrnPo, 10);
            if (shipInfo != null) {
                if (shipInfo.bClusterers == true)//选择聚合
                {
                    // 可选：处理聚合点击
                }
                else {
                    // 跳转到SimpleDialog页面，传递shipId参数
                    const shipId = shipInfo.shipId || shipInfo.bdid || shipInfo.bdId || shipInfo.staticShipId;
                    if (shipId) {
                        this.getSelectedShipInfo(shipId);
                    }
                }
            }
        },

        //显示简单的船舶信息（鼠标移动到船舶显示）
        ShowShipSimpleInfo: function (objInfo) {
            //选中的是轨迹回放的船舶
            if (objInfo.bSelPlayTrackShip == true) {
                var iShipPos = API_GetPlayShipPosById(objInfo.id);
                if (iShipPos > -1) {

                    var iMsgBoxHeight = 20;
                    var iMsgBoxWidth = 210;
                    iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;
                    var shipName, shipMmsi, shipGeoPoX, shipGeoPoY, shipSpeed, shipCourse, shipTime;
                    var strTitle;
                    var shipInfoObj = API_GetPlayShipInfoByPos(iShipPos);

                    var curShipPos = API_GetShipPosById(objInfo.id);
                    var shipInfoObj1 = API_GetShipAllExpAttrByPos(curShipPos);

                    if (shipInfoObj) {
                        shipName = shipInfoObj.shipName;
                        shipMmsi = (shipInfoObj.shipMMSI == null || shipInfoObj.shipMMSI == undefined) ? "" : shipInfoObj.shipMMSI;
                        shipGeoPoX = shipInfoObj.shipGeoPoX;
                        shipGeoPoY = shipInfoObj.shipGeoPoY;
                        shipSpeed = shipInfoObj.shipSpeed;
                        shipCourse = shipInfoObj.shipCourse;
                        shipTime = shipInfoObj.shipTime;
                        strTitle = "船舶信息:" + shipName;
                    }
                    if (objInfo.iTrackPos != null) {//选中的是轨迹点
                        var shipInfoObj = API_GetPlayHistroyTrackInfoByPos(iShipPos, objInfo.iTrackPos);
                        if (shipInfoObj) {
                            strTitle = "历史轨迹点信息";
                            shipGeoPoX = shipInfoObj.trackGeoPoX;
                            shipGeoPoY = shipInfoObj.trackGeoPoY;
                            shipSpeed = shipInfoObj.trackSpeed;
                            shipCourse = shipInfoObj.trackCourse;
                            shipTime = shipInfoObj.trackTime;
                        }
                        if (shipSpeed) {
                            shipSpeed = shipSpeed.toFixed(2);
                        }

                        var strInnerHTML = "<span style='float: center; text-align: left; white-space: nowrap'>" + strTitle.big().bold().fontcolor("#f2fa03") + "</span><br>";

                        strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>船名：" + shipName.fontcolor("#f2fa03") + "</span><br>";
                        iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                        strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>终端号：" + shipMmsi.fontcolor("#f2fa03") + "</span><br>";
                        iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                        var strLon = API_LonLatToString(shipGeoPoX / 10000000, true);
                        strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>经度：" + strLon.fontcolor("#f2fa03") + "</span><br>";
                        iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                        var strLat = API_LonLatToString(shipGeoPoY / 10000000, false);
                        strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>纬度：" + strLat.fontcolor("#f2fa03") + "</span><br>";
                        iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                        shipSpeed = shipSpeed + "&nbsp;" + "(节)";
                        strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航速：" + shipSpeed.fontcolor("#f2fa03") + "</span><br>";
                        iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                        shipCourse = shipCourse + "&nbsp;" + "(度)";
                        strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航向：" + shipCourse.fontcolor("#f2fa03") + "</span><br>";
                        iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                        strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>报位时间：" + shipTime.fontcolor("#f2fa03") + "</span><br>";
                        iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;


                        global.g_showSimpleInfoDiv.style.height = iMsgBoxHeight + "px";
                        global.g_showSimpleInfoDiv.style.width = iMsgBoxWidth + "px";
                        global.g_showSimpleInfoDiv.innerHTML = strInnerHTML;

                        this.$options.methods.ShowInfoDivBox.bind(this)(global.g_showSimpleInfoDiv, objInfo.po);
                    }
                    else {
                        $.get(global.IP + '/web/GetOneShipInfoById?id=' + objInfo.id, function (data, status) {
                            if (shipSpeed) {
                                shipSpeed = shipSpeed.toFixed(2);
                            }

                            var strInnerHTML = "<span style='float: center; text-align: left; white-space: nowrap'>" + strTitle.big().bold().fontcolor("#f2fa03") + "</span><br>";

                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>船名：" + shipName.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>终端号：" + shipMmsi.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            var strLon = API_LonLatToString(shipGeoPoX / 10000000, true);
                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>经度：" + strLon.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            var strLat = API_LonLatToString(shipGeoPoY / 10000000, false);
                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>纬度：" + strLat.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            shipSpeed = shipSpeed + "&nbsp;" + "(节)";
                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航速：" + shipSpeed.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            shipCourse = shipCourse + "&nbsp;" + "(度)";
                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航向：" + shipCourse.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>北斗报位时间：" + data[0].bdTime.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>AIS报位时间：" + data[0].aisTime.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;


                            global.g_showSimpleInfoDiv.style.height = iMsgBoxHeight + "px";
                            global.g_showSimpleInfoDiv.style.width = iMsgBoxWidth + "px";
                            global.g_showSimpleInfoDiv.innerHTML = strInnerHTML;

                            _this.$options.methods.ShowInfoDivBox.bind(this)(global.g_showSimpleInfoDiv, objInfo.po);
                        });
                    }

                }
            }
            else {//选中的是当前船舶
                var iShipPos = API_GetShipPosById(objInfo.id);
                if (iShipPos > -1) {
                    var shipInfoObj = API_GetShipInfoByPos(iShipPos);
                    if (shipInfoObj) {

                        var iMsgBoxHeight = 20;
                        var iMsgBoxWidth = 210;

                        var shipName, shipMmsi, shipGeoPoX, shipGeoPoY, shipSpeed, shipCourse, shipTime;

                        var strTitle = "船舶信息:" + shipInfoObj.shipName;
                        shipGeoPoX = shipInfoObj.shipGeoPoX;
                        shipGeoPoY = shipInfoObj.shipGeoPoY;
                        shipName = shipInfoObj.shipName;
                        shipMmsi = (shipInfoObj.shipMMSI == null || shipInfoObj.shipMMSI == undefined) ? "" : shipInfoObj.shipMMSI;
                        shipSpeed = shipInfoObj.shipSpeed;
                        shipCourse = shipInfoObj.shipCourse;
                        shipTime = shipInfoObj.shipTime;

                        if (objInfo.iTrackPos != null) {//选中的是轨迹点
                            var shipInfoObj = API_GetHistroyTrackInfoByPos(iShipPos, objInfo.iTrackPos);
                            if (shipInfoObj) {
                                strTitle = "当前船舶轨迹点信息:";
                                shipGeoPoX = shipInfoObj.trackGeoPoX;
                                shipGeoPoY = shipInfoObj.trackGeoPoY;
                                shipSpeed = shipInfoObj.trackSpeed;
                                shipCourse = shipInfoObj.trackCourse;
                                shipTime = shipInfoObj.trackTime;
                            }
                            var strTitle = "" + shipName + "<br>";
                            var strInnerHTML = "<span style='float: center; text-align: left; white-space: nowrap'>" + strTitle.big().bold().fontcolor("#f2fa03") + "</span>";
                            // strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>船名：" + shipName.fontcolor("#f2fa03") + "</span><br>";
                            // iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>终端号：" + shipMmsi.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            var strLon = API_LonLatToString(shipGeoPoX / 10000000, true);
                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>经度：" + strLon.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            var strLat = API_LonLatToString(shipGeoPoY / 10000000, false);
                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>纬度：" + strLat.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            shipSpeed = shipSpeed + "&nbsp;" + "(节)";
                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航速：" + shipSpeed.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            shipCourse = shipCourse + "&nbsp;" + "(度)";
                            strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航向：" + shipCourse.fontcolor("#f2fa03") + "</span><br>";
                            iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                            if (shipTime != null && shipTime != undefined) {
                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>报位时间：" + shipTime.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;
                            }

                            global.g_showSimpleInfoDiv.style.height = iMsgBoxHeight + "px";
                            global.g_showSimpleInfoDiv.style.width = iMsgBoxWidth + "px";
                            global.g_showSimpleInfoDiv.innerHTML = strInnerHTML;

                            this.$options.methods.ShowInfoDivBox.bind(this)(global.g_showSimpleInfoDiv, objInfo.po);
                        }
                        else {
                            if (_this.mouseMoveSelectShipData[0] != null && _this.mouseMoveSelectShipData[0].id == objInfo.id) {
                                var strTitle = "" + shipName + "<br>";
                                var strInnerHTML = "<span style='float: center; text-align: left; white-space: nowrap'>" + strTitle.big().bold().fontcolor("#f2fa03") + "</span>";
                                // strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>船名：" + shipName.fontcolor("#f2fa03") + "</span><br>";
                                // iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>终端号：" + shipMmsi.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                var strLon = API_LonLatToString(shipGeoPoX / 10000000, true);
                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>经度：" + strLon.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                var strLat = API_LonLatToString(shipGeoPoY / 10000000, false);
                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>纬度：" + strLat.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                shipSpeed = shipSpeed + "&nbsp;" + "(节)";
                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航速：" + shipSpeed.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                shipCourse = shipCourse + "&nbsp;" + "(度)";
                                strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航向：" + shipCourse.fontcolor("#f2fa03") + "</span><br>";
                                iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                if (_this.mouseMoveSelectShipData[0].bdTime != null && _this.mouseMoveSelectShipData[0].bdTime != undefined) {
                                    strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>北斗报位时间：" + _this.mouseMoveSelectShipData[0].bdTime.fontcolor("#f2fa03") + "</span><br>";
                                    iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;
                                }

                                if (_this.mouseMoveSelectShipData[0].aisTime != null && _this.mouseMoveSelectShipData[0].aisTime != undefined) {
                                    strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>AIS报位时间：" + _this.mouseMoveSelectShipData[0].aisTime.fontcolor("#f2fa03") + "</span><br>";
                                    iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;
                                }

                                global.g_showSimpleInfoDiv.style.height = iMsgBoxHeight + "px";
                                global.g_showSimpleInfoDiv.style.width = iMsgBoxWidth + "px";
                                global.g_showSimpleInfoDiv.innerHTML = strInnerHTML;

                                _this.$options.methods.ShowInfoDivBox.bind(this)(global.g_showSimpleInfoDiv, objInfo.po);
                            }
                            else {
                                $.ajaxSettings.async = false;
                                $.get(global.IP + '/web/GetOneShipInfoById?id=' + objInfo.id, function (data, status) {
                                    _this.mouseMoveSelectShipData = data;
                                    var strTitle = "" + shipName + "<br>";
                                    var strInnerHTML = "<span style='float: center; text-align: left; white-space: nowrap'>" + strTitle.big().bold().fontcolor("#f2fa03") + "</span>";
                                    // strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>船名：" + shipName.fontcolor("#f2fa03") + "</span><br>";
                                    // iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                    strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>终端号：" + shipMmsi.fontcolor("#f2fa03") + "</span><br>";
                                    iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                    var strLon = API_LonLatToString(shipGeoPoX / 10000000, true);
                                    strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>经度：" + strLon.fontcolor("#f2fa03") + "</span><br>";
                                    iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                    var strLat = API_LonLatToString(shipGeoPoY / 10000000, false);
                                    strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>纬度：" + strLat.fontcolor("#f2fa03") + "</span><br>";
                                    iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                    shipSpeed = shipSpeed + "&nbsp;" + "(节)";
                                    strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航速：" + shipSpeed.fontcolor("#f2fa03") + "</span><br>";
                                    iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                    shipCourse = shipCourse + "&nbsp;" + "(度)";
                                    strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>航向：" + shipCourse.fontcolor("#f2fa03") + "</span><br>";
                                    iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;

                                    if (data[0].bdTime != null && data[0].bdTime != undefined) {
                                        strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>北斗报位时间：" + data[0].bdTime.fontcolor("#f2fa03") + "</span><br>";
                                        iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;
                                    }

                                    if (data[0].aisTime != null && data[0].aisTime != undefined) {
                                        strInnerHTML += "<span style='float: left; text-align: left; white-space: nowrap'>AIS报位时间：" + data[0].aisTime.fontcolor("#f2fa03") + "</span><br>";
                                        iMsgBoxHeight += global.g_iSimpleBoxOneLineSize;
                                    }

                                    global.g_showSimpleInfoDiv.style.height = iMsgBoxHeight + "px";
                                    global.g_showSimpleInfoDiv.style.width = iMsgBoxWidth + "px";
                                    global.g_showSimpleInfoDiv.innerHTML = strInnerHTML;

                                    _this.$options.methods.ShowInfoDivBox.bind(this)(global.g_showSimpleInfoDiv, objInfo.po);
                                })
                                $.ajaxSettings.async = true;
                            }
                        }
                    }
                }
            }
        },

        //鼠标松开事件
        //scrnPo:鼠标在海图上的位置
        ReturnOnMouseUpTest: function (scrnPo) {
            if (_this.isShowShipSimpleInfo == true) {
                var shipInfo = API_GetShipInfoByScrnPo(scrnPo, 50);
                if (shipInfo.bShowTrack == false) {
                    _this.wakeIsShowOrNot = false;
                }
                else {
                    _this.wakeIsShowOrNot = true;
                }

                global.curSelectShipId = shipInfo.shipId;
                $.get(global.IP + '/web/GetShipImgUrl?shipId=' + shipInfo.shipId, function (data, status) {
                    _this.shipInfoImg = data.replace(/\s*/g, "");
                });
                document.getElementById('ShipSimpleInformationView').style.display = 'block';
                _this.isShowShipSimpleInfo = false;
            }
            var arrObjPo1 = API_GetCurDrawDynamicObjGeoPo();
            if (global.g_bSelectShipByRectModel) {
                var arrObjPo = API_GetCurDrawDynamicObjGeoPo();
                if (arrObjPo.length > 1) {
                    // 处理方搜(矩形框选)操作 - 鼠标松开时，如果处于方搜模式且已绘制矩形
                    // 获取矩形的对角坐标，并确保传入正确的最小/最大经纬度值
                    _this.SelectShipByRectTest(
                        Math.min(arrObjPo[0].x, arrObjPo[1].x),  // minGeoX - 矩形左边界经度
                        Math.max(arrObjPo[0].x, arrObjPo[1].x),  // maxGeoX - 矩形右边界经度
                        Math.min(arrObjPo[0].y, arrObjPo[1].y),  // minGeoY - 矩形下边界纬度
                        Math.max(arrObjPo[0].y, arrObjPo[1].y),  // maxGeoY - 矩形上边界纬度
                        0  // iGetShipCount=0表示不限制返回的船舶数量
                    );
                }
            }
            else if (global.g_bSelectShipByCircleModel) {
                var arrObjPo = API_GetCurDrawDynamicObjGeoPo();
                if (arrObjPo.length > 1) {
                    // 计算圆的半径
                    var disKm = API_GetDistBetwTwoPoint(arrObjPo[0].x, arrObjPo[0].y, arrObjPo[1].x, arrObjPo[1].y);
                    // 调用圆选船舶方法，传入圆心坐标和半径
                    _this.SelectShipsByGeoCircleTest(arrObjPo[0].x, arrObjPo[0].y, disKm, 0);
                }
            }
        },

        //动态绘制标注时，选中点之后返回的坐标
        ReturnDrawDynamicObjNewInfoTest: function (objDynamicInfo, curGeoPoInfo) {

            if (objDynamicInfo) {
                switch (objDynamicInfo.type) {
                    case global.drawPoint:
                        // 标注类型为点时：
                        if (_this.curMarkTypeIndex == 1) {
                            _this.$options.methods.GetDynamicObjInfoOfMark.bind(this)(objDynamicInfo);
                        }
                        // 点标注坐标获取
                        // _this.$options.methods.saveMaekInfoToSdkOT.bind(this)(type,);
                        // _this.$options.methods.GetCurDrawObjCurPo.bind(this)(objDynamicInfo.po.x, objDynamicInfo.po.y);
                        // _this.$options.methods.saveMaekInfoToSdkOT.bind(this)(objDynamicInfo.type, objDynamicInfo.po.x, objDynamicInfo.po.y);


                        break;
                    case global.drawLine: //绘制线
                        if (_this.curMarkTypeIndex == 2) {
                            _this.$options.methods.GetDynamicObjInfoOfMark.bind(this)(objDynamicInfo);
                        }
                        // this.$options.methods.GetCurDrawObjCurPo.bind(this)(objDynamicInfo.po.x, objDynamicInfo.po.y);
                        break;
                    case global.drawFace: //绘制面
                        if (_this.getElectronicFence == false) {
                            this.$options.methods.ShowCurDrawObjCurPo.bind(this)(objDynamicInfo.po.x, objDynamicInfo.po.y);
                        }
                        // 【修复】多边形面标注处理 - 修复多边形面标注坐标收集问题
                        // 问题：多边形面标注时，每次点击地图只显示坐标但不保存到showCurMarkInfo数组中
                        // 原因：缺少对curMarkTypeIndex==3（多边形面标注）的GetDynamicObjInfoOfMark方法调用
                        // 解决：添加对多边形面标注的坐标收集处理，确保每次点击都能正确收集坐标点
                        if (_this.curMarkTypeIndex == 3) {
                            _this.$options.methods.GetDynamicObjInfoOfMark.bind(this)(objDynamicInfo);
                        }
                        break;
                    case global.drawRect: //绘制矩形
                        if (_this.getAreaPlayBack == false) {
                            this.$options.methods.areaPlayBackArrPointsShow.bind(this)(objDynamicInfo);
                        }
                        // this.$options.methods.GetCurDrawRectInfo.bind(this)(objDynamicInfo.po.x, objDynamicInfo.po.y, objDynamicInfo.w, objDynamicInfo.h, objDynamicInfo.curPo);
                        break;
                    case global.drawCircle: //绘制圆
                        if (_this.curMarkTypeIndex == 5) {
                            _this.$options.methods.GetDynamicObjInfoOfMark.bind(this)(objDynamicInfo);
                        }
                        // this.$options.methods.GetCurDrawCircleInfo.bind(this)(objDynamicInfo.po.x, objDynamicInfo.po.y, objDynamicInfo.r, objDynamicInfo.curPo);
                        break;
                    case global.drawPoint: //测距
                        API_SetCurDrawDynamicUseType(global.drawPoint);
                        break;
                    case global.drawPoint: //测面积
                        API_SetCurDrawDynamicUseType(global.drawPoint);
                        break;
                    case global.drawPoint: //电子方位线
                        API_SetCurDrawDynamicUseType(global.drawPoint);
                        break;
                    case global.drawLineArea: //绘制线区域
                        this.$options.methods.GetCurDrawObjCurPo.bind(this)(objDynamicInfo.po.x, objDynamicInfo.po.y);
                        break;
                }
            }
            else if (curGeoPoInfo) {//动态绘制信息框(线标注和面标注)
                switch (curGeoPoInfo.type) {
                    case global.drawLine: //绘制线
                        // this.$options.methods.ShowCurDrawLineObjInfoBox.bind(this)(curGeoPoInfo.po, curGeoPoInfo.bEndDraw);
                        break;
                    case global.drawFace: //绘制面
                        // this.$options.methods.ShowCurDrawFaceObjInfoBox.bind(this)(curGeoPoInfo.po, curGeoPoInfo.bEndDraw);
                        break;
                }
            }
        },

        //测距时候，鼠标点击激发该方法
        //curGeoPo:鼠标当前点击的经纬度坐标，格式{1210000000,310000000}
        //curDis:当前点击点与上一个点的距离（km)
        //allMeasureDist:累加的距离（km）
        //CurDegrees:当前点与上一个点的角度（度）
        ReturnCurMeasurePoInfoByMouseDownTest: function (curGeoPo, curDis, allMeasureDist, CurDegrees) {
            if (curGeoPo) {
                this.$options.methods.GetCurMeasureDist.bind(this)(curDis, allMeasureDist, CurDegrees);
                //alert("测距点信息："+curGeoPo.x + "," + curGeoPo.y + "_" + curDis + "_" + allMeasureDist + "_" + CurDegrees);
            }
        },

        //拽动海图时候激发
        ReturnDragMapTest: function () {
            var lonLat11 = API_GetCurMapCenterLonLatPo();
            var scale11 = API_GetMapLevel();
            _this.meteorologicalMonitoringList = [];
            _this.meteorologicalMonitoringList.push({ lon: lonLat11.x, lat: lonLat11.y, level: scale11 });
            //this.$options.methods.ShowCurDrawObjInfoBox.bind(this)();//拽动时候要设置信息框移动
        },

        //缩放之后激发
        ReturnZoomMapForPcTest: function () {
            var lonLat11 = API_GetCurMapCenterLonLatPo();
            var scale11 = API_GetMapLevel();
            _this.meteorologicalMonitoringList = [];
            _this.meteorologicalMonitoringList.push({ lon: lonLat11.x, lat: lonLat11.y, level: scale11 });
            _this.curLevel = scale11;
            //this.$options.methods.ShowCurDrawObjInfoBox.bind(this)(); //缩放之后要设置信息框移动
        },

        //鼠标移动事件
        ReturnOnMouseMoveTest: function () {

        },

        //测面积时候激发
        //areaSize:当前测量面积(平方米)
        ReturnCurMeasureAreaSizeTest: function (areaSize) {
            // document.getElementById("msg").value = areaSize;;
        },

        //移动端单手势拖动触发该事件
        ReturnTouchmoveByDragTest: function () {
            API_ClearDrawMouseMoveSelObjCanvas();
        },

        //海图视图窗口或者比例级别改变时候触发，返回海图视图的经纬度范围大小信息
        ReturnMapViewAfterDragOrZoomTest: function (mapInfo) {
            if (mapInfo) {
                var type = mapInfo.type;            //1=拽动触发，2=缩放触发
                var level = mapInfo.level;      //比例级数
                var scale = mapInfo.scale;      //比例尺
                var lon = mapInfo.lon;              //中心点经度
                var lat = mapInfo.lat;              //中心点纬度
                var minLon = mapInfo.minLon;        //海图窗口最小经度
                var maxLon = mapInfo.maxLon;        //海图窗口最大经度
                var minLat = mapInfo.minLat;        //海图窗口最小纬度
                var maxLat = mapInfo.maxLat;        //海图窗口最大纬度
                //document.getElementById("msg").value = type + "," + level + "," + lon;
                console.log(mapInfo.level);
            }
        },

        // 编辑
        ReturnEditObjByMouseRightDownCallBack: function (info) {
            _this.$options.methods.saveEditObjInfoToDatabase.bind(this)(info);
        },

        // 动态绘制-取消按钮
        ReturnCancelObjByMouseLeftDownCallBack: function (isCheck) {
            if (isCheck == true) {
                API_AddCancelButtonIsShow(false);
                if (_this.curMarkTypeIndex != 0) {
                    _this.$options.methods.cancelDrawMarkInfo.bind(this)();
                }

                if (_this.getElectronicFence == false) {
                    _this.$options.methods.electronicFence_cancel.bind(this)();
                }

                if (_this.getAreaPlayBack == false) {
                    _this.$options.methods.areaPlayBack.bind(this)();
                }

            }
        },


        ReturnCurPlayTrackTimeInfoTest: function (time, isShow) {
            // if (_this.isHisEnd == true) {
            //     _this.historyTimeNumber = document.getElementById('historyTrackProgressBar').max;
            //     document.getElementById('historyTrackProgressBar').value = document.getElementById('historyTrackProgressBar').max;
            //     if (isShow == true) {
            //         _this.isHisEnd = false;
            //     }
            // }
            // else {
            //     if (time != null && _this.historyTimeNumber == 0) {
            //         _this.historyTimeNumber = _this.historyTimeNumber + 1;
            //         document.getElementById('historyTrackProgressBar').value = _this.historyTimeNumber;
            //         _this.curHistoryTime = time;
            //     }
            //     else if (_this.curHistoryTime != time && isShow == false) {
            //         _this.historyTimeNumber = _this.historyTimeNumber + 1;
            //         document.getElementById('historyTrackProgressBar').value = _this.historyTimeNumber;
            //     }
            //     else if (isShow == true) {
            //         _this.historyTimeNumber = document.getElementById('historyTrackProgressBar').max;
            //         document.getElementById('historyTrackProgressBar').value = document.getElementById('historyTrackProgressBar').max;
            //     }
            // }
        },

        // 格式化经纬度
        formatLonLat(lon, lat) {
            var lonD = parseInt(lon);
            var lonM = parseInt((lon - lonD) * 60);
            var lonS = parseInt((((lon - lonD) * 60) - lonM) * 60);
            var latD = parseInt(lat);
            var latM = parseInt((lat - latD) * 60);
            var latS = parseInt((((lat - latD) * 60) - latM) * 60);
            return {
                lonText: lonD + '°' + lonM + '′' + lonS + '″E',
                latText: latD + '°' + latM + '′' + latS + '″N'
            };
        },
        // 定位船舶
        locationShip: function (shipId) {
            if (_this.leftShow == true) {
                _this.$options.methods.ShowWindow.bind(this)('left');
                $("#ShipSearch1").animate({ left: _this.position6 + 'px' });
            }

            if (_this.rightShow == true) {
                _this.$options.methods.ShowWindow.bind(this)('right');
                $("#ShipSimpleInformationView").animate({ left: ((document.querySelector("#bt3").getBoundingClientRect().left) + (document.querySelector("#ShipSimpleInformationView").getBoundingClientRect().width) / 2) + 'px' });
                $("#ShipSimpleInformationView").animate({ top: ((document.querySelector("#toolCabinet").getBoundingClientRect().top) + (document.querySelector("#toolCabinet").getBoundingClientRect().height)) + 'px' });
            }

            API_SetCurDrawDynamicUseType(0);

            _this.$options.methods.getDetailsAndSurroundingFishInfo.bind(this)(shipId);

            var shipPos = API_GetShipPosById(shipId);

            if (API_GetMapLevel() < 8) {
                API_SetMapLevel(10, null);
            }

            setTimeout(function () {
                API_SetShipToMapViewCenterByPos(shipPos);
                API_ReDrawShips();
            }, 50)
        },

        // 显示船舶详情
        shipDetailTwoView: function (params, id, boxNum) {
            if (params === true) {
                _this.whiteIsShow = true;
                document.getElementById('shipDetailsTwoID').style.display = 'block';
                _this.$refs.shipDetails.shipInfos(id);
                _this.$refs.shipDetails.shipCardInfo(id);
                _this.$refs.shipDetails.shipMonitorInfo(id);
                // _this.$refs.shipDetails.lawCaseInfo(id);
                if (boxNum) {
                    _this.$refs.shipDetails.boxNum = boxNum;
                } else {
                    _this.$refs.shipDetails.boxNum = 1;
                }
            }
        },
        // 显示终端信息
        showClientInfo(params) {
            if (params == true) {
                _this.$refs['showClientInfo'].style.display = 'block';
            }
        },
        //船舶详情界面中的重点跟踪
        changeComprehensiveSituationDataByshipSimpleInformation: function (params, id) {
            switch (params) {
                case 0:
                    break;
                case 1:
                    var isAdd = false;
                    var index = -1;
                    // 判断是不是在重点跟踪队列里面
                    if (global.importShipInfo.length > 0) {
                        for (var i = 0; i < global.importShipInfo.length; i++) {
                            if (global.importShipInfo[i].staticShipId == id) {
                                isAdd = true;
                                index = i;
                                break;
                            }
                        }
                    }
                    // 如果在重点跟踪队列则.......
                    $.ajaxSettings.async = false;
                    if (isAdd == true) {
                        $.get(global.IP + "/web/DeleteImportanceShip?id=" + id + '&userId=' + sessionStorage.getItem("userId"), function (data, status) {

                        }).fail(function (msg) {

                        });

                        $.get(global.IP + '/web/GetOneShipInfoById?id=' + id, function (data, status) {
                            var pos = API_GetShipPosById(id);
                            API_SetShipAllFollow(pos, false);
                            var info = API_GetShipInfoByPos(pos);
                            var curShipDynamicInfo = [];//更新的时候，只要把这些值设置好即可
                            curShipDynamicInfo.shipGeoPoX = info.shipGeoPoX;
                            curShipDynamicInfo.shipGeoPoY = info.shipGeoPoY;
                            curShipDynamicInfo.shipSpeed = info.shipSpeed;
                            curShipDynamicInfo.shipCourse = info.shipCourse;
                            curShipDynamicInfo.shipTime = info.shipTime;

                            if (data[0].shiptype == 2) {
                                curShipDynamicInfo.iShipState = 0;   //状态
                            }
                            else if (data[0].shiptype == 3 || data[0].shiptype == 13 || data[0].shiptype == 75 || data[0].shiptype == 65) {
                                curShipDynamicInfo.iShipState = 1;   //状态
                            }
                            else if (data[0].shiptype == 4) {
                                curShipDynamicInfo.iShipState = 0;   //状态
                            }
                            else if (data[0].shiptype == 6 || data[0].shiptype == 61 ||
                                data[0].shiptype == 62 ||
                                data[0].shiptype == 64 || data[0].shiptype == 63 ||
                                data[0].shiptype == 66 || data[0].shiptype == 67 ||
                                data[0].shiptype == 68 || data[0].shiptype == 69 ||
                                data[0].shiptype == 610) {
                                curShipDynamicInfo.iShipState = 3;   //状态
                            }
                            else if (data[0].shiptype == 7 || data[0].shiptype == 71 ||
                                data[0].shiptype == 72 ||
                                data[0].shiptype == 74 || data[0].shiptype == 73 ||
                                data[0].shiptype == 76 || data[0].shiptype == 77 ||
                                data[0].shiptype == 78 || data[0].shiptype == 79 ||
                                data[0].shiptype == 710) {
                                curShipDynamicInfo.iShipState = 7;   //状态
                            }
                            else {
                                curShipDynamicInfo.iShipState = 7;
                            }

                            if (data[0].boutside == 1) {
                                curShipDynamicInfo.iShipState = 3;
                            }

                            curShipDynamicInfo.bOnlineOrNot = info.bOnlineOrNot;
                            API_UpdateOneShipDynamicInfoByPos(pos, curShipDynamicInfo); //更新一艘船舶动态信息
                            API_ReDrawShips();
                        });

                        _this.fouInfo = '0';
                    }
                    // 如果不在重点跟踪队列则......
                    else {
                        $.ajaxSettings.async = false;
                        $.get(global.IP + '/web/SetImportanceShip?id=' + id + '&userId=' + sessionStorage.getItem("userId"), function (data, status) {

                        });
                        _this.showSuccessMessage('已添加到重点跟踪队列');

                        var pos = API_GetShipPosById(id);
                        var info = API_GetShipInfoByPos(pos);
                        API_SetShipAllFollow(pos, true);
                        var curShipDynamicInfo = [];    //更新的时候，只要把这些值设置好即可
                        curShipDynamicInfo.shipGeoPoX = info.shipGeoPoX;
                        curShipDynamicInfo.shipGeoPoY = info.shipGeoPoY;
                        curShipDynamicInfo.shipSpeed = info.shipSpeed;
                        curShipDynamicInfo.shipCourse = info.shipCourse;
                        curShipDynamicInfo.shipTime = info.shipTime;
                        curShipDynamicInfo.iShipState = 4;
                        curShipDynamicInfo.bOnlineOrNot = info.bOnlineOrNot;

                        API_UpdateOneShipDynamicInfoByPos(pos, curShipDynamicInfo); //更新一艘船舶动态信息
                        API_ReDrawShips();
                        _this.fouInfo = '1';
                    }
                    $.get(global.IP + "/web/GetImportanceShip?userId=" + sessionStorage.getItem("userId"), function (data, status) {
                        // 这里global.importShipInfo不更新就不会显示到左边的重点关注渔船列表中

                        global.importShipInfo = [];
                        _this.focusOnTrackingFishingBoatsList = [];
                        global.importShipInfo = data;
                        _this.focusOnTrackingFishingBoatsList = global.importShipInfo;
                    }).fail(function (msg) {
                        console.log("error：" + JSON.stringify(msg))
                    });

                    $.ajaxSettings.async = true;
                    break;
                case 2:
                    break;
                case 3:
                    break;
                case 4:
                    break;
                case 5:
                    document.getElementById('ShipSimpleInformationView').style.display = 'none';
                    _this.$options.methods.isTrackShow.bind(this)(true);
                    document.getElementById('replay').style.top = '56px';
                    document.getElementById('replay').style.left = '20px';
                    if (_this.leftShow == true) {
                        _this.$options.methods.ShowWindow.bind(this)('left');
                    }
                    var startDate = (new Date().getTime()) / 1000 - parseFloat(_this.getHistoryTime) * 60 * 60; //得到秒数
                    var endDate = (new Date().getTime()) / 1000; //得到秒数

                    var cueStartDate = _this.$options.methods.changeTime.bind(this)(startDate * 1000);
                    var cueEndDate = _this.$options.methods.changeTime.bind(this)(endDate * 1000);
                    document.getElementById('portTableInputStartTime').value = cueStartDate;
                    document.getElementById('portTableInputEndTime').value = cueEndDate;
                    document.getElementById('replay').style.display = 'block';
                    break;
                case 6:
                    _this.$options.methods.setAllShipHistory.bind(this)();
                    break;
                case 7:
                    // $.get(function() {})
                    _this.shipWithAreaTitle = API_GetCurSelectShipInfo().shipName;
                    _this.shipWithAreaShipId = API_GetCurSelectShipInfo().shipId;
                    _this.shipWithAreaAreaId = _this.areaInfos[0].id;
                    document.getElementById("shipWithArea").style.display = "block";
                    break;
            }
        },
        //船舶详情中船员详情的显示
        showCrewInfo: function (params, id) {
            if (params === true) {
                document.getElementById('crewDetailView').style.display = "block";
                _this.$refs.crewDetails.saveShipId(id);
                // _this.$refs.crewDetails.switchMess(3);
                _this.$refs.crewDetails.getData();
            }
        },
        // 取消船舶跟踪
        cancelFloowInCom: function (params) {
            if (params == true && API_GetCurFollowShipInfo() != null) {
                API_FollowShipByPos(-1);
                API_ReDrawShips();//重绘
            }
        },

        isTrackShow: function (params) {
            var count = API_GetShipsCount();
            for (var i = 0; i < count; i++) {
                API_SetShowShipTrackOrNotByPos(i, false);
            }
            API_ReDrawShips();
        },
        //改变控件Index使后出现的在前出现的上层
        changeCurIndex: function (id) {
            global.curIndex += 1;
            document.getElementById(id).style.zIndex = global.curIndex;
        },
        // 更新实时信息
        GetBdRealTimeMsgInfo: function (params) {
            _this.realTimeMsgInfo = params;
        },
        // 船舶简介-打开北斗通信
        openBeiDouShip: function (params) {
            document.getElementById('ShipSimpleInformationView').style.display = 'none';
            document.getElementById('beidouCommunicationView').style.display = 'block';
            _this.beiDouShipId = [];
            _this.beiDouShipId.push(params);
        },
        //船舶详情窗口关闭
        closeShipDiv: function (params) {
            if (params === true) {
                document.getElementById('shipDetailsTwoID').style.display = "none";
                document.getElementById('earlyWarningMonitoringView').style.filter = 'none';
                document.getElementById('videoSurveillanceView').style.filter = 'none';
                document.getElementById('meteorologicalMonitoringView').style.filter = 'none';
                document.getElementById('Legeng').style.filter = 'none';
                document.getElementById('longitudeAndLatitudeDisplay').style.filter = 'none';
                document.getElementById('leftImg').style.filter = 'none';
                document.getElementById('rightImg').style.filter = 'none';
                document.getElementById('searchDiv').style.filter = 'none';
                document.getElementById('toolCabinet').style.filter = 'none';
                document.getElementById('bt2').style.filter = 'none';
                document.getElementById('bt3').style.filter = 'none';
                document.getElementById('bt4').style.filter = 'none';
                document.getElementById('bt5').style.filter = 'none';
                document.getElementById('bt6').style.filter = 'none';
                document.getElementById('bt7').style.filter = 'none';
                document.getElementById('bt8').style.filter = 'none';
                document.getElementById('bt9').style.filter = 'none';
                document.getElementById('bt10').style.filter = 'none';
                document.getElementById('bt11').style.filter = 'none';
                document.getElementById('bt12').style.filter = 'none';
                document.getElementById('bt13').style.filter = 'none';
                document.getElementById('bt14').style.filter = 'none';
                document.getElementById('bt15').style.filter = 'none';
                document.getElementById('bt16').style.filter = 'none';
                document.getElementById('bt17').style.filter = 'none';
                document.getElementById('bt18').style.filter = 'none';
                document.getElementById('bt19').style.filter = 'none';
                document.getElementById('bt20').style.filter = 'none';
                document.getElementById('topTitle').style.filter = 'none';
                document.getElementById('fishingVesselStatisticsView').style.filter = 'none';
                document.getElementById('map').style.filter = 'none';
                document.getElementById('shipPortSelect').style.filter = 'none';
                document.getElementById('fishingVesselDwellTime').style.filter = 'none';
                document.getElementById('fishingVesselMonitoringView').style.filter = 'none';
                document.getElementById('bdMsgSendView').style.filter = 'none';
            }
        },
        getifadd: function (val) {
            _this.ifadd = val;
        },

        // 打开电子围栏对话框
        openWeilanDialog() {
            this.showWeilanDialog = true;
        },

        // 关闭电子围栏对话框
        closeWeilanDialog() {
            this.showWeilanDialog = false;
        },

        // 确认围栏设置
        confirmWeilanDialog(data) {
            console.log('围栏设置数据:', data);
            // 这里可以添加保存围栏数据的逻辑
            this.showWeilanDialog = false;
        },

        // 关闭地图图层切换对话框
        closeQiehuanDialog() {
            this.showQiehuanDialog = false;
        },

        // 打开附近船舶对话框
        openFujinDialog() {
            this.showFujinDialog = true;
        },

        // 关闭附近船舶对话框
        closeFujinDialog() {
            this.showFujinDialog = false;
        },

        // 位置获取成功回调
        onLocationObtained(location) {
            //console.log('获取到位置:', location);
            // 在地图上标记当前位置
            this.markCurrentLocation(location.longitude, location.latitude);
        },

        // 船舶选择回调
        onShipSelected(ship) {
            console.log('选择了船舶:', ship);
            // 关闭附近船舶对话框
            this.showFujinDialog = false;
            // 显示船舶详情
            this.selectedShip = ship;
            this.showSimpleDialog = true;
            // 在地图上定位到该船舶
            if (ship.staticShipId) {
                this.getDetailsAndSurroundingFishInfo(ship.staticShipId);
            }
        },

        // 地图类型改变事件
        onMapTypeChange(mapType) {
            console.log('地图类型改变:', mapType);
            // 这里可以添加切换地图类型的逻辑
            // 例如调用相应的API来切换地图底图
        },

        // 时段改变事件
        onTimePeriodChange(timePeriod) {
            console.log('时段改变:', timePeriod);
            // 这里可以添加时段切换的逻辑
        },

        // 地图模式改变事件
        onMapModeChange(mapMode) {
            console.log('地图模式改变:', mapMode);
            // 这里可以添加地图模式切换的逻辑
            // 例如切换白天/黄昏/夜晚模式
        },


        //添加标注相关------------start------------

        // 打开添加标注对话框
        addAnnotation() {
            console.log('打开添加标注对话框');
            this.showBiaoZhuDialog = true;
        },

        // 【修复】关闭标注对话框 - 修复取消按钮不退出绘制模式问题
        // 问题：点击取消按钮后，对话框关闭但地图仍处于绘制模式
        // 原因：只关闭了对话框，没有调用退出绘制模式的相关方法
        // 解决：在关闭对话框的同时，调用cancelDrawMarkInfo方法退出绘制模式并清理状态

        // 关闭标注对话框
        closeBiaoZhuDialog() {
            console.log('关闭标注对话框，同时退出绘制模式');
            this.showBiaoZhuDialog = false;

            // 如果当前处于标注绘制模式，需要退出绘制模式
            if (this.curMarkTypeIndex != 0) {
                console.log('当前处于标注模式，curMarkTypeIndex:', this.curMarkTypeIndex, '执行取消绘制操作');
                this.cancelDrawMarkInfo();
            }
        },

        // 确认标注设置
        // confirmBiaoZhuDialog(data) {
        //     console.log('confirmBiaoZhuDialog called with data:', data);

        //     // 先关闭选择对话框
        //     this.showBiaoZhuDialog = false;

        //     // 根据标注类型调用相应的添加标注方法，开始地图交互
        //     switch(data.type) {
        //         case 'point':
        //             console.log('开始点标注');
        //             this.addMarkInfo(1); // 点标注
        //             break;
        //         case 'line':
        //             console.log('开始线标注');
        //             this.addMarkInfo(2); // 线标注
        //             break;
        //         case 'circle':
        //             console.log('开始圆标注');
        //             this.addMarkInfo(5); // 圆标注
        //             break;
        //         case 'polygon':
        //             console.log('开始多边形面标注');
        //             this.addMarkInfo(3); // 多边形面标注
        //             break;
        //         default:
        //             console.log('未知的标注类型:', data.type);
        //     }
        // },

        // 开始地图交互--根据标注类型调用相应的addMarkInfo方法
        startMapInteraction(data) {
            console.log('开始地图交互:', data);
            // 根据标注类型调用相应的地图交互方法
            switch(data.type) {
                case 'point':
                    console.log('开始点标注地图交互');
                    this.addMarkInfo(1); // 点标注
                    break;
                case 'line':
                    console.log('开始线标注地图交互');
                    this.addMarkInfo(2); // 线标注
                    break;
                case 'circle':
                    console.log('开始圆标注地图交互');
                    this.addMarkInfo(5); // 圆标注
                    break;
                case 'polygon':
                    console.log('开始多边形面标注地图交互');
                    this.addMarkInfo(3); // 多边形面标注
                    break;
                default:
                    console.log('未知的标注类型:', data.type);
            }
        },

        // 保存标注
        saveAnnotation(data) {
            console.log('保存标注:', data);
            console.log('当前标注类型:', this.curMarkTypeIndex);
            console.log('当前showCurMarkInfo:', this.showCurMarkInfo);
            console.log('showCurMarkInfo长度:', this.showCurMarkInfo.length);

            // 设置标注名称
            this.markShowContent = data.name || '标注';
            // 调用保存方法
            this.saveMaekInfoToSdkOT();
            // 关闭对话框
            this.showBiaoZhuDialog = false;
        },

        // 显示成功提示
        showSuccessMessage(message) {
            this.successMessage = message;
            this.showSuccessAlert = true;

            // 2秒后自动关闭
            setTimeout(() => {
                this.showSuccessAlert = false;
            }, 2000);
        },

        // 获取当前用户ID（预留逻辑，目前默认返回0）
        getCurrentUserId: function() {
            // TODO: 这里预留获取用户ID的逻辑
            // 可以从以下几种方式获取：
            // 1. sessionStorage.getItem("userId")
            // 2. localStorage.getItem("userId")
            // 3. 从URL参数获取
            // 4. 从全局状态管理获取
            // 5. 从后端API获取当前登录用户信息

            // 方式1：从sessionStorage获取（如果有登录功能）
            const sessionUserId = sessionStorage.getItem("userId");
            if (sessionUserId) {
                console.log('从sessionStorage获取到userId:', sessionUserId);
                return parseInt(sessionUserId);
            }

            // 方式2：从URL参数获取（如果通过URL传递）
            const urlParams = new URLSearchParams(window.location.search);
            const urlUserId = urlParams.get('userId');
            if (urlUserId) {
                console.log('从URL参数获取到userId:', urlUserId);
                return parseInt(urlUserId);
            }

            // 方式3：默认值（当前设置为0，可根据需要修改）
            console.log('使用默认userId: 0');
            return 0;
        },

        // 从后端查询获取已有标注并在地图上显示
        getAllMarkInfo: function() {
            console.log('=== 开始获取所有标注数据 ===');
            //console.log('当前时间:', new Date().toLocaleString());

            // 获取用户ID的逻辑（预留）
            let userId = this.getCurrentUserId();
            console.log('获取到的userId:', userId);


            if (!userId && userId !== 0) {
                console.log('无法获取用户ID，跳过获取标注数据');
                return;
            }

            // 请求URL
            const requestUrl = global.IP + "/web/GetAllMarkInfo?userId=" + userId;
            console.log('请求URL:', requestUrl);
            //console.log('global.IP:', global.IP);

            $.get(requestUrl, (data, status) => {
                console.log('=== 获取标注数据成功 ===');
                console.log('响应状态:', status);
                //console.log('响应数据:', data);
                console.log('数据类型:', typeof data);

                // 处理不同的数据格式
                let markList = null;

                // 如果data是字符串，尝试解析JSON
                if (typeof data === 'string') {
                    try {
                        const parsedData = JSON.parse(data);
                        console.log('解析后的数据:', parsedData);
                        markList = parsedData.list || parsedData;
                    } catch (e) {
                        console.error('JSON解析失败:', e);
                        return;
                    }
                } else if (data && typeof data === 'object') {
                    // 如果data是对象，直接获取list
                    markList = data.list || data;
                }

                console.log('处理后的markList:', markList);
                console.log('markList类型:', typeof markList);
                console.log('markList是否为数组:', Array.isArray(markList));

                // 如果markList存在，则显示标注
                if (markList && Array.isArray(markList) && markList.length > 0) {
                    //console.log('找到', markList.length, '个标注，开始在地图上显示');
                    this.displayMarkInfoOnMap(markList);
                } 
                else {
                    console.log('没有找到标注数据');
                    console.log('原始data:', data);
                    console.log('处理后markList:', markList);
                }
            }).fail((xhr, status, error) => {
                console.error('=== 获取标注数据失败 ===');
                console.error('错误信息:', error);
                console.error('状态:', status);
                console.error('xhr状态:', xhr.status);
                console.error('xhr响应文本:', xhr.responseText);
            });
        },

        // 在地图上显示标注数据
        displayMarkInfoOnMap: function(markList) {
            //console.log('开始在地图上显示标注数据，共', markList.length, '个标注');

            // 遍历markList，在地图上显示标注
            markList.forEach((markInfo, index) => {
                //console.log('处理第', index + 1, '个标注:', markInfo);

                try {
                    // 获取标注图层索引
                    const layerPos = API_GetLayerPosById(global.g_iMarkLayerId);
                    // 获取标注样式索引
                    const layerStylePos = global.g_iMarkStylePos;

                    if (layerPos < 0) {
                        console.error('标注图层未找到，layerPos:', layerPos);
                        return;
                    }

                    //console.log('图层信息 - layerPos:', layerPos, 'layerStylePos:', layerStylePos);

                    let arrObjPo = [];
                    let objInfo = {};

                    // 根据标注类型处理坐标数据
                    if (markInfo.type == 2) {
                        // 线标注
                        const arrPo = markInfo.pointStr.split("#");
                        for (let j = 0; j < arrPo.length; j++) {
                            if (arrPo[j] == "") continue;
                            const arrOnePo = arrPo[j].split("@");
                            arrObjPo.push({x: Number(arrOnePo[0]), y: Number(arrOnePo[1])});
                        }
                        objInfo.objType = 2; // 线
                    } else if (markInfo.type == 3) {
                        // 多边形面标注
                        const arrPo = markInfo.pointStr.split("#");
                        for (let j = 0; j < arrPo.length; j++) {
                            if (arrPo[j] == "") continue;
                            const arrOnePo = arrPo[j].split("@");
                            arrObjPo.push({x: Number(arrOnePo[0]), y: Number(arrOnePo[1])});
                        }
                        objInfo.objType = 3; // 多边形面
                    } else if (markInfo.type == 5) {
                        // 圆标注
                        const arrOnePo = markInfo.pointStr.split("@");
                        arrObjPo.push({x: Number(arrOnePo[0]), y: Number(arrOnePo[1])});
                        objInfo.objType = 5; // 圆
                        objInfo.r = markInfo.radius;
                    } else {
                        // 点标注 (type == 1)
                        const arrOnePo = markInfo.pointStr.split("@");
                        arrObjPo.push({x: Number(arrOnePo[0]), y: Number(arrOnePo[1])});
                        objInfo.objType = 1; // 点
                    }

                    // 设置对象信息，参考现有代码的格式
                    objInfo.layerPos = layerPos; // 图层索引
                    objInfo.objId = markInfo.id; // 标注id
                    objInfo.name = markInfo.content; // 标注名称
                    objInfo.showText = markInfo.content; // 显示内容
                    objInfo.layerStylePos = layerStylePos; // 使用样式索引

                    //console.log('准备添加标注到地图:', objInfo, '坐标:', arrObjPo);

                    // 扩展字段
                    let arrExpAttrValue = [];
                    arrExpAttrValue.push("标注_" + markInfo.id);

                    // 使用正确的API函数添加到地图
                    const objPos = API_AddNewObject(objInfo, arrObjPo, arrExpAttrValue);
                    //console.log('标注添加到地图结果 objPos:', objPos, '标注信息:', objInfo);

                    if (objPos > -1) {
                        console.log('标注添加成功，objPos:', objPos);
                    } else {
                        console.error('标注添加失败，objPos:', objPos);
                    }

                } catch (error) {
                    console.error('处理标注时出错:', error, '标注数据:', markInfo);
                }
            });

            // 重绘地图
            API_ReDrawLayer();
            console.log('所有标注处理完成，地图已重绘');
        },

        // 添加标注方法-点、线、圆面，多边形面
        addMarkInfo: function (markIndex) {
            console.log('addMarkInfo called with markIndex:', markIndex);
            console.log('当前curMarkTypeIndex:', this.curMarkTypeIndex);
            console.log('当前showCurMarkInfo:', this.showCurMarkInfo);
            this.markIsShow = false;

            // 左右两侧菜单栏回收
            if(this.leftShow == true) {
                this.ShowWindow('left');
            }
            if(this.rightShow == true) {
                this.ShowWindow('right');
            }

            var objStyle = []; //标注样式结构体
            objStyle.borderWith = "2"; //画笔粗细
            objStyle.borderColor = "#FF0000"; //画笔颜色
            objStyle.fillColor = "#FF0000"; //填充颜色
            objStyle.textColor = "#000000"; //字体颜色
            objStyle.fontSize = "12px 宋体"; //字体大小
            objStyle.iOpacity = 50; //透明度
            objStyle.strImgSrc = '../../static/img/mark.png';

            API_SetDynamicObjStyle(objStyle);
            API_AddCancelButtonIsShow(true);
            this.confirmDlg = false;

            // 根据标注类型设置不同的标题
            switch(markIndex) {
                case 1:
                    this.drawSomethingBoxTitle = "添加点标注";
                    break;
                case 2:
                    this.drawSomethingBoxTitle = "添加线标注";
                    break;
                case 3:
                    this.drawSomethingBoxTitle = "添加多边形面标注";
                    break;
                case 5:
                    this.drawSomethingBoxTitle = "添加圆标注";
                    break;
                default:
                    this.drawSomethingBoxTitle = "添加标注";
                    break;
            }

            this.changeCurIndex("drawSomethingBox");

            // 如果当前已经有标注类型在进行，且不是同一类型，先清理状态
            if(this.curMarkTypeIndex != 0 && this.curMarkTypeIndex != markIndex) {
                console.log('切换标注类型，从', this.curMarkTypeIndex, '到', markIndex);
                // 清理之前的状态
                this.showCurMarkInfo = [];
                this.radiusCenterX = "";
                this.radiusCenterY = "";
                if(this.curMarkTypeIndex == 5){
                    this.radiusCenter = '';
                    this.radiusLength = 0;
                }
                this.curMarkInfo = [];
            }

            switch(markIndex) {
                case 1:
                    // 点标注逻辑
                    if(this.pointOrLineShow == false) {
                        this.pointOrLineShow = true;
                    }
                    if(this.radiusShow == true) {
                        this.radiusShow = false;
                    }

                    document.getElementById('drawSomethingBox').style.display = 'block';
                    API_SetCurDrawDynamicUseType(1);
                    this.curMarkTypeIndex = 1;
                    this.curMarkInfo = [];
                    this.showCurMarkInfo = []; // 清空显示的坐标信息
                    break;
                case 2:
                    // 线标注逻辑
                    if(this.pointOrLineShow == false) {
                        this.pointOrLineShow = true;
                    }
                    if(this.radiusShow == true) {
                        this.radiusShow = false;
                    }
                    document.getElementById('drawSomethingBox').style.display = 'block';
                    API_SetCurDrawDynamicUseType(2);
                    this.curMarkTypeIndex = 2;
                    this.curMarkInfo = [];
                    this.showCurMarkInfo = []; // 清空显示的坐标信息
                    break;
                case 3:
                    // 多边形面标注逻辑
                    if(this.pointOrLineShow == false) {
                        this.pointOrLineShow = true;
                    }
                    if(this.radiusShow == true) {
                        this.radiusShow = false;
                    }
                    document.getElementById('drawSomethingBox').style.display = 'block';
                    API_SetCurDrawDynamicUseType(3);
                    this.curMarkTypeIndex = 3;
                    this.curMarkInfo = [];
                    this.showCurMarkInfo = []; // 清空显示的坐标信息
                    break;
                case 5:
                    // 圆标注逻辑
                    if(this.pointOrLineShow == true) {
                        this.pointOrLineShow = false;
                    }
                    if(this.radiusShow == false) {
                        this.radiusShow = true;
                    }
                    document.getElementById('drawSomethingBox').style.display = 'block';
                    API_SetCurDrawDynamicUseType(5);
                    this.curMarkTypeIndex = 5;
                    this.curMarkInfo = [];
                    this.showCurMarkInfo = []; // 清空显示的坐标信息
                    break;
            }
        },
        // 根据标注类型获取坐标
        GetDynamicObjInfoOfMark: function (DynamicInfo) {
            // 打印当前标注类型和动态信息
            console.log('GetDynamicObjInfoOfMark called with curMarkTypeIndex:', this.curMarkTypeIndex, 'DynamicInfo:', DynamicInfo);

            switch (this.curMarkTypeIndex) {
                case 1:
                    var DynamicInfoPoX = this.NumToMin(parseInt(DynamicInfo.po.x)/10000000,true);
                    var DynamicInfoPoY = this.NumToMin(parseInt(DynamicInfo.po.y)/10000000,false);
                    this.radiusCenterX = DynamicInfoPoX;
                    this.radiusCenterY = DynamicInfoPoY;

                    var geo = [];
                    geo.push({x: parseInt(DynamicInfo.po.x), y: parseInt(DynamicInfo.po.y)});
                    this.curMarkInfo = [];
                    this.curMarkInfo.push({type: 1, po: geo});
                    console.log('点标注坐标更新:', {x: DynamicInfoPoX, y: DynamicInfoPoY});
                    break;
                case 2:
                    console.log('线标注处理开始，当前curMarkInfo长度:', this.curMarkInfo.length);

                    if(this.curMarkInfo.length == 0) {
                        var geo = [];
                        geo.push({x: parseInt(DynamicInfo.po.x), y: parseInt(DynamicInfo.po.y)});
                        this.curMarkInfo.push({type: 2, po: geo});
                        console.log('创建新的线标注，第一个点');
                    }
                    else {
                        this.curMarkInfo[0].po.push({x: parseInt(DynamicInfo.po.x), y: parseInt(DynamicInfo.po.y)});
                        console.log('添加线标注点，当前总点数:', this.curMarkInfo[0].po.length);
                    }

                    var lon = this.NumToMin(this.curMarkInfo[0].po[this.curMarkInfo[0].po.length - 1].x/10000000,true);
                    var lat = this.NumToMin(this.curMarkInfo[0].po[this.curMarkInfo[0].po.length - 1].y/10000000,false);
                    // 去掉E，N,以及空格
                    lon = lon.substring(0, lon.length - 3);
                    lat = lat.substring(0, lat.length - 3);
                    // 把用户要看的存到showCurMarkInfo中
                    const newCoord = {x:lon, y:lat};
                    // 使用Vue.set确保响应式更新
                    this.$set(this.showCurMarkInfo, this.showCurMarkInfo.length, newCoord);
                    console.log('线标注坐标添加:', newCoord, '当前showCurMarkInfo:', this.showCurMarkInfo);
                    console.log('当前pointOrLineShow:', this.pointOrLineShow, 'curMarkTypeIndex:', this.curMarkTypeIndex);
                    break;
                case 3:
                    console.log('多边形面标注处理开始，当前curMarkInfo长度:', this.curMarkInfo.length);

                    if(this.curMarkInfo.length == 0) {
                        var geo = [];
                        geo.push({x: parseInt(DynamicInfo.po.x), y: parseInt(DynamicInfo.po.y)});
                        this.curMarkInfo.push({type: 3, po: geo});
                        console.log('创建新的多边形面标注，第一个点');
                    }
                    else {
                        this.curMarkInfo[0].po.push({x: parseInt(DynamicInfo.po.x), y: parseInt(DynamicInfo.po.y)});
                        console.log('添加多边形面标注点，当前总点数:', this.curMarkInfo[0].po.length);
                    }

                    var lon = this.NumToMin(this.curMarkInfo[0].po[this.curMarkInfo[0].po.length - 1].x/10000000,true);
                    var lat = this.NumToMin(this.curMarkInfo[0].po[this.curMarkInfo[0].po.length - 1].y/10000000,false);
                    // 去掉E，N,以及空格
                    lon = lon.substring(0, lon.length - 3);
                    lat = lat.substring(0, lat.length - 3);
                    // 把用户要看的存到showCurMarkInfo中
                    const newPolygonCoord = {x:lon, y:lat};
                    // 使用Vue.set确保响应式更新
                    this.$set(this.showCurMarkInfo, this.showCurMarkInfo.length, newPolygonCoord);
                    console.log('多边形面标注坐标添加:', newPolygonCoord, '当前showCurMarkInfo:', this.showCurMarkInfo);
                    console.log('当前pointOrLineShow:', this.pointOrLineShow, 'curMarkTypeIndex:', this.curMarkTypeIndex);
                    break;
                case 5:
                    this.radiusCenterX = this.NumToMin(parseInt(DynamicInfo.po.x)/10000000, true);
                    this.radiusCenterY = this.NumToMin(parseInt(DynamicInfo.po.y)/10000000, false);
                    this.radiusLength = DynamicInfo.r.toFixed(4);

                    var geo = [];
                    geo.push({x: parseInt(DynamicInfo.po.x), y: parseInt(DynamicInfo.po.y)});
                    this.curMarkInfo = [];
                    this.curMarkInfo.push({type: 5, po: geo});
                    break;
            }
        },

        // 取消绘制标注
        cancelDrawMarkInfo: function () {
            document.getElementById('drawSomethingBox').style.display = 'none';
            API_SetCurDrawDynamicUseType(0);
            this.aLinkDlg = true;
            this.editDlg = false;
            this.markwindow = true;
            this.editmarkwindow = false;
            this.editpointOrLineShow = false;
            this.editradiusShow = false;
            // 清空之前的线的点
            this.showCurMarkInfo = [];
            this.radiusCenterX = "";
            this.radiusCenterY = "";
            if(this.curMarkTypeIndex == 5){
                this.radiusCenter = '';
                this.radiusLength = 0;
            }
            this.curMarkTypeIndex = 0;
            this.pointOrLineShow = true;
            this.radiusShow = false;
            this.curMarkInfo = [];
            this.markShowContent = '标注';
            this.isShowToOther = false;
            this.geoPoAll = [];
        },

        // 显示/隐藏窗口方法
        ShowWindow: function(direction) {
            if (direction === 'left') {
                this.leftShow = !this.leftShow;
            } else if (direction === 'right') {
                this.rightShow = !this.rightShow;
            }
        },

        // 坐标转换方法
        NumToMin(lonLat, bIsLon, decimalCount) {
            return NumToMin(lonLat, bIsLon, decimalCount);
        },

        // 存储标注到sdk和数据库
        saveMaekInfoToSdkOT: function() {
            var _this = this; // 保存this引用，用于AJAX回调

            console.log('saveMaekInfoToSdkOT called');
            console.log('当前curMarkTypeIndex:', this.curMarkTypeIndex);
            console.log('当前markShowContent:', this.markShowContent);
            console.log('当前showCurMarkInfo:', this.showCurMarkInfo);

            // 检查是否填了标注名称
            if(this.markShowContent==''){
                setAlertWindowShow("situationView", "标注名称不能为空","rgb(13, 38, 92)", 1);
                return;
            }

            var timeElement = getCurTime();
            var loadTime = timeElement.year + "-" + timeElement.month + "-" + timeElement.date + " " + timeElement.hour + ":" + timeElement.minute + ":" + timeElement.second;

            // 插入数据库
            var markData = {};
            var ownShow = 1;
            if(sessionStorage.getItem("jurisdiction") == 0 && this.isShowToOther == true) {
                ownShow = 0;
            }
            markData["type"] = this.curMarkTypeIndex;
            markData["ownShow"] = ownShow;
            markData["loadTime"] = loadTime;
            markData["userId"] = this.getCurrentUserId();
            markData["content"] = this.markShowContent;

            switch(this.curMarkTypeIndex){
                case 1:
                    var coordinateString = '';
                    var radiusCenterX = this.radiusCenterX.substring(0, this.radiusCenterX.length-4);
                    var radiusCenterY = this.radiusCenterY.substring(0, this.radiusCenterY.length-4);

                    // 经度
                    let duX1 = parseFloat(radiusCenterX.split('°')[0]);
                    let fenX1 = parseFloat(radiusCenterX.split('°')[1].split('′')[0])/60;
                    let miaoX1 = parseFloat(radiusCenterX.split('°')[1].split('′')[1])/3600;

                    // 纬度
                    let duY1 = parseFloat(radiusCenterY.split('°')[0]);
                    let fenY1 = parseFloat(radiusCenterY.split('°')[1].split('′')[0])/60;
                    let miaoY1 = parseFloat(radiusCenterY.split('°')[1].split('′')[1])/3600;

                    coordinateString = "" + (duX1 + fenX1 + miaoX1) * 10000000 + "@" + (duY1 + fenY1 + miaoY1) * 10000000;

                    markData["posCount"] = 1;
                    markData["pointStr"] = coordinateString;
                    markData["radius"] = 0.0;
                    break;
                case 2:
                    console.log('处理线标注，showCurMarkInfo长度:', this.showCurMarkInfo.length);
                    console.log('showCurMarkInfo内容:', this.showCurMarkInfo);

                    // 检查是否有空坐标
                    for(let i = 0;i<this.showCurMarkInfo.length;i++){
                        if(this.showCurMarkInfo[i].x==''||this.showCurMarkInfo[i].y==''){
                            console.log('发现空坐标，索引:', i, '坐标:', this.showCurMarkInfo[i]);
                            setAlertWindowShow("situationView", "不能有空坐标","rgb(13, 38, 92)", 1);
                            return;
                        }
                    }
                    // 至少有两个点才能是线
                    if(this.showCurMarkInfo.length<2){
                        console.log('线标注点数不足，当前点数:', this.showCurMarkInfo.length);
                        setAlertWindowShow("situationView", "线标注至少两个点","rgb(13, 38, 92)", 1);
                        return;
                    }

                    var coordinateString = '';
                    // 将列表中的时分秒格式转换为普通格式
                    for(let i = 0; i < this.showCurMarkInfo.length; i++){
                        // 经度
                        var duX2 = parseFloat(this.showCurMarkInfo[i].x.split('°')[0]);
                        var fenX2 = parseFloat(this.showCurMarkInfo[i].x.split('°')[1].split('′')[0])/60;
                        var miaoX2 = parseFloat(this.showCurMarkInfo[i].x.split('°')[1].split('′')[1])/3600;

                        // 纬度
                        var duY2 = parseFloat(this.showCurMarkInfo[i].y.split('°')[0]);
                        var fenY2 = parseFloat(this.showCurMarkInfo[i].y.split('°')[1].split('′')[0])/60;
                        var miaoY2 = parseFloat(this.showCurMarkInfo[i].y.split('°')[1].split('′')[1])/3600;

                        coordinateString += "#" + (duX2 + fenX2 + miaoX2) * 10000000 + "@" + (duY2 + fenY2 + miaoY2) * 10000000;
                    }

                    markData["posCount"] = this.showCurMarkInfo.length;
                    markData["pointStr"] = coordinateString;
                    markData["radius"] = 0.0;
                    // 去掉第一个#
                    markData['pointStr'] = markData['pointStr'].substring(1,markData['pointStr'].length);
                    break;
                case 3:
                    console.log('处理多边形面标注，showCurMarkInfo长度:', this.showCurMarkInfo.length);
                    console.log('showCurMarkInfo内容:', this.showCurMarkInfo);

                    // 检查是否有空坐标
                    for(let i = 0;i<this.showCurMarkInfo.length;i++){
                        if(this.showCurMarkInfo[i].x==''||this.showCurMarkInfo[i].y==''){
                            console.log('发现空坐标，索引:', i, '坐标:', this.showCurMarkInfo[i]);
                            setAlertWindowShow("situationView", "不能有空坐标","rgb(13, 38, 92)", 1);
                            return;
                        }
                    }
                    // 至少有三个点才能是多边形面
                    if(this.showCurMarkInfo.length<3){
                        console.log('多边形面标注点数不足，当前点数:', this.showCurMarkInfo.length);
                        setAlertWindowShow("situationView", "多边形面标注至少三个点","rgb(13, 38, 92)", 1);
                        return;
                    }

                    var coordinateString = '';
                    // 将列表中的时分秒格式转换为普通格式
                    for(let i = 0; i < this.showCurMarkInfo.length; i++){
                        // 经度
                        var duX3 = parseFloat(this.showCurMarkInfo[i].x.split('°')[0]);
                        var fenX3 = parseFloat(this.showCurMarkInfo[i].x.split('°')[1].split('′')[0])/60;
                        var miaoX3 = parseFloat(this.showCurMarkInfo[i].x.split('°')[1].split('′')[1])/3600;

                        // 纬度
                        var duY3 = parseFloat(this.showCurMarkInfo[i].y.split('°')[0]);
                        var fenY3 = parseFloat(this.showCurMarkInfo[i].y.split('°')[1].split('′')[0])/60;
                        var miaoY3 = parseFloat(this.showCurMarkInfo[i].y.split('°')[1].split('′')[1])/3600;

                        coordinateString += "#" + (duX3 + fenX3 + miaoX3) * 10000000 + "@" + (duY3 + fenY3 + miaoY3) * 10000000;
                    }

                    markData["posCount"] = this.showCurMarkInfo.length;
                    markData["pointStr"] = coordinateString;
                    markData["radius"] = 0.0;
                    // 去掉第一个#
                    markData['pointStr'] = markData['pointStr'].substring(1,markData['pointStr'].length);
                    break;
                case 5:
                    var coordinateString = '';
                    var radiusCenterX = this.radiusCenterX.substring(0, this.radiusCenterX.length-4);
                    var radiusCenterY = this.radiusCenterY.substring(0, this.radiusCenterY.length-4);

                    // 经度
                    let duX5 = parseFloat(radiusCenterX.split('°')[0]);
                    let fenX5 = parseFloat(radiusCenterX.split('°')[1].split('′')[0])/60;
                    let miaoX5 = parseFloat(radiusCenterX.split('°')[1].split('′')[1])/3600;

                    // 纬度
                    let duY5 = parseFloat(radiusCenterY.split('°')[0]);
                    let fenY5 = parseFloat(radiusCenterY.split('°')[1].split('′')[0])/60;
                    let miaoY5 = parseFloat(radiusCenterY.split('°')[1].split('′')[1])/3600;

                    coordinateString = "" + (duX5 + fenX5 + miaoX5) * 10000000 + "@" + (duY5 + fenY5 + miaoY5) * 10000000;

                    markData["posCount"] = 1;
                    markData["pointStr"] = coordinateString;
                    markData["radius"] = this.radiusLength;
                    break;
            }

            console.log('准备发送AJAX请求');
            console.log('请求URL:', global.IP + "/web/InsertMarkInfo");
            console.log('请求数据:', markData);
            console.log('JSON数据:', JSON.stringify(markData));

            // AJAX保存到数据库
            $.ajaxSettings.async = false;
            $.ajax({
                url: global.IP + "/web/InsertMarkInfo",
                type: "POST",
                data: JSON.stringify(markData),
                dataType: "json",
                contentType: "application/json",
                dateString: true,

                beforeSend: function(xhr) {
                    console.log('AJAX请求即将发送');
                },

                success: function(data){
                    console.log('AJAX请求成功，返回数据:', data);
                    // 使用新的成功提示方法
                    _this.showSuccessMessage('添加成功');
                    _this.addMarkInfoByCurDraw(data.markId, coordinateString);
                    document.getElementById('drawSomethingBox').style.display = 'none';
                    API_SetCurDrawDynamicUseType(0);
                    if(_this.curMarkTypeIndex == 5){
                        _this.radiusCenter = '';
                        _this.radiusLength = 0;
                    }
                    _this.curMarkTypeIndex = 0;
                    _this.pointOrLineShow = true;
                    _this.radiusShow = false;
                    _this.curMarkInfo = [];
                    _this.markShowContent = '标注';
                    _this.isShowToOther = false;
                },

                error: function(xhr, status, error){
                    console.log('AJAX请求失败');
                    console.log('xhr:', xhr);
                    console.log('status:', status);
                    console.log('error:', error);
                    setAlertWindowShow("situationView", "标注失败","red", 1);
                },
            });
            $.ajaxSettings.async = true;
            // 清空之前的点
            this.showCurMarkInfo = [];
        },

        // 根据返回的id添加标注
        addMarkInfoByCurDraw: function (markId, pointStr) {
            var arrObjPo = [];
            var layerPos = API_GetLayerPosById(global.g_iMarkLayerId);
            var layerStylePos = global.g_iMarkStylePos;
            var objType = this.curMarkTypeIndex;

            var objInfo = []; //标注对象
            if(objType == 2 || objType == 3) {
                var arrPo = pointStr.split("#");
                for(var j = 0; j < arrPo.length; j++) {
                    if(arrPo[j] == "") {
                        continue
                    }
                    var arrOnePo = arrPo[j].split("@");
                    arrObjPo.push({x: Number(arrOnePo[0]), y: Number(arrOnePo[1])});
                }
            }
            else {
                arrObjPo.push({x: this.curMarkInfo[0].po[0].x, y: this.curMarkInfo[0].po[0].y});
            }

            if(objType == 5) {
                objInfo.r = this.radiusLength;
            }
            objInfo.objType = objType;
            objInfo.layerPos = layerPos; //图层索引
            objInfo.objId = markId; //标注id
            objInfo.name = this.markShowContent; //标注名称
            objInfo.showText = this.markShowContent; //显示内容
            objInfo.layerStylePos = layerStylePos; //使用样式索引
            var arrExpAttrValue = []; //扩展字段

            var objPos = API_AddNewObject(objInfo, arrObjPo, arrExpAttrValue);

            if(objPos > -1) {
                if(objType == 1) {
                    var objStyleInfo = [];
                    objStyleInfo.bShowImg = true;
                    objStyleInfo.strImgSrc = "../../static/img/mark.png";
                    objStyleInfo.iImgWidth = 20;
                    objStyleInfo.iImgHeight = 20;
                    objStyleInfo.iOpacity = 90;
                    objStyleInfo.textColor = "#000000";
                    objStyleInfo.fontSize = "16px 宋体";
                    objStyleInfo.bShowText = true;
                    objStyleInfo.iTextOpacity = 90;
                    API_SetCompositeObjStyleByPos(layerPos, objPos, true, objStyleInfo);
                }

                if(objType == 2) {
                    var objStyleInfo = [];
                    objStyleInfo.iOpacity = 100;
                    objStyleInfo.borderWith = 3;
                    objStyleInfo.borderColor = "#1afa29";
                    objStyleInfo.textColor = "#000000";
                    objStyleInfo.fontSize = "16px 宋体";
                    objStyleInfo.bShowText = true;
                    objStyleInfo.iTextOpacity = 90;
                    objStyleInfo.lineType = 0;
                    objStyleInfo.lineLen = 6;
                    objStyleInfo.dashLen = 4;
                    API_SetCompositeObjStyleByPos(layerPos, objPos, true, objStyleInfo);
                }

                if(objType == 3) {
                    var objStyleInfo = [];
                    objStyleInfo.bFilled = true;
                    objStyleInfo.fillColor = "#1afa29";
                    objStyleInfo.iOpacity = 30;
                    objStyleInfo.borderWith = 2;
                    objStyleInfo.iLineOpacity = 100;
                    objStyleInfo.borderColor = "#1afa29";
                    objStyleInfo.bShowText = true;
                    objStyleInfo.textColor = "#000000";
                    objStyleInfo.fontSize = "16px 宋体";
                    objStyleInfo.iTextOpacity = 90;
                    API_SetCompositeObjStyleByPos(layerPos, objPos, true, objStyleInfo);
                }

                if(objType == 5) {
                    var objStyleInfo = [];
                    objStyleInfo.bFilled = true;
                    objStyleInfo.fillColor = "#1afa29";
                    objStyleInfo.iOpacity = 50;
                    objStyleInfo.borderWith = 1;
                    objStyleInfo.iLineOpacity = 100;
                    objStyleInfo.borderColor = "#FF0000";
                    objStyleInfo.bShowText = true;
                    objStyleInfo.textColor = "#000000";
                    objStyleInfo.fontSize = "16px 宋体";
                    objStyleInfo.iTextOpacity = 90;
                    API_SetCompositeObjStyleByPos(layerPos, objPos, true, objStyleInfo)
                }
            }
            API_ReDrawLayer();
        },

        // 获取当前设备的地理位置
        getDeviceLocation: function() {
            // 显示加载提示
            this.successMessage = '正在获取位置...';
            this.showSuccessAlert = true;
            
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    // 成功回调
                    (position) => {
                        const longitude = position.coords.longitude; // 经度
                        const latitude = position.coords.latitude;   // 纬度
                        
                        // 在地图上标记当前位置
                        this.markCurrentLocation(longitude, latitude);
                        
                        // 显示成功提示
                        this.successMessage = '已获取当前位置: ' + 
                                             '经度 ' + longitude.toFixed(6) + 
                                             ', 纬度 ' + latitude.toFixed(6);
                        this.showSuccessAlert = true;
                        
                        // 3秒后自动隐藏提示
                        setTimeout(() => {
                            this.showSuccessAlert = false;
                        }, 3000);
                    },
                    // 错误回调
                    (error) => {
                        let errorMsg = '';
                        switch(error.code) {
                            case error.PERMISSION_DENIED:
                                errorMsg = '用户拒绝了位置请求权限';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMsg = '位置信息不可用';
                                break;
                            case error.TIMEOUT:
                                errorMsg = '获取位置超时';
                                break;
                            case error.UNKNOWN_ERROR:
                                errorMsg = '发生未知错误';
                                break;
                        }
                        this.successMessage = '获取位置失败: ' + errorMsg;
                        this.showSuccessAlert = true;
                        
                        // 3秒后自动隐藏提示
                        setTimeout(() => {
                            this.showSuccessAlert = false;
                        }, 3000);
                    },
                    // 选项
                    {
                        enableHighAccuracy: true, // 高精度
                        timeout: 5000,           // 超时时间，毫秒
                        maximumAge: 0            // 不使用缓存
                    }
                );
            } else {
                this.successMessage = '您的浏览器不支持地理定位';
                this.showSuccessAlert = true;
                
                // 3秒后自动隐藏提示
                setTimeout(() => {
                    this.showSuccessAlert = false;
                }, 3000);
            }
        },

        // 在地图上标记当前位置
        markCurrentLocation: function(longitude, latitude) {
            // 先删除之前的标记（如果有）
            var layerPos = API_GetLayerPosById(global.g_iLocationLayerId);
            if (layerPos > -1) {
                var obj = API_GetObjectPosById(1, layerPos);
                if (obj) {
                    API_DelObjectByPos(obj.iLayerPos, obj.iObjPos);
                }
            }
            
            // 添加新标记
            const objName = "当前位置";
            const arrObjPo = [];
            
            // 添加位置点
            arrObjPo.push({x: longitude * 10000000, y: latitude * 10000000});
            
            if (layerPos > -1) {
                var objInfo = [];
                var arrExpAttrValue = []; // 扩展字段

                objInfo.objType = 1;
                objInfo.layerPos = layerPos; 
                objInfo.objId = 1;
                objInfo.name = objName;
                objInfo.showText = objName;
                objInfo.layerStylePos = global.g_iLocationStylePos;
                arrExpAttrValue.push("当前设备位置");
                
                var objPos = API_AddNewObject(objInfo, arrObjPo, arrExpAttrValue);

                if(objPos > -1) {
                    var objStyleInfo = [];

                    objStyleInfo.bShowImg = true;
                    objStyleInfo.strImgSrc = "../../static/ico/location.png";
                    objStyleInfo.iImgWidth = 32;
                    objStyleInfo.iImgHeight = 32;
                    objStyleInfo.iOpacity = 90;
                    objStyleInfo.textColor = "#FF0000";
                    objStyleInfo.fontSize = "16px 宋体";
                    objStyleInfo.bShowText = true;
                    objStyleInfo.iTextOpacity = 90;

                    API_SetCompositeObjStyleByPos(layerPos, objPos, true, objStyleInfo);
                }
                
                // 设置地图级别和居中显示位置
                API_SetMapLevel(10, null);
                this.curLevel = 10;
                API_SetObjToMapViewCenterByPos(layerPos, objPos);
                API_ReDrawLayer();
            }
        },
        //添加标注相关------------end------------

        // 菜单弹窗相关方法
        toggleMenuDialog() {
            this.showMenuDialog = !this.showMenuDialog;
        },
        closeMenuDialog() {
            this.showMenuDialog = false;
        },
        handleMenuAction(action) {
            switch (action) {
                case 'ranging':
                    this.ranging();
                    break;
                case 'location':
                    this.openLocationDialog();
                    break;
                case 'nearby-ships':
                    this.openFujinDialog();
                    break;
                case 'add-annotation':
                    this.addAnnotation();
                    break;
                case 'ship-statistics':
                    this.showFishingVesselStatisticsCard = true;
                    break;
                case 'typhoon-list':
                    this.showTyphoonListCard = true;
                    break;
                default:
                    console.log('未知的菜单操作:', action);
            }
        },
        // 处理显示在线船舶列表事件
        handleShowOnlineShipList() {
            // 关闭渔船分布统计卡片
            this.showFishingVesselStatisticsCard = false;
            // 显示在线船舶列表卡片
            this.showOnlineShipListCard = true;
        },
        // 处理地图点击，关闭菜单
        handleMapClick(event) {
            // 如果点击的不是菜单按钮或菜单内容，则关闭菜单
            if (this.showMenuDialog) {
                const target = event.target;
                const isMenuButton = target.closest('.sidebar-btn');
                const isMenuDialog = target.closest('.menu-dialog');

                if (!isMenuButton && !isMenuDialog) {
                    this.showMenuDialog = false;
                }
            }
        },

        // 格式化用户区域信息
        formatUserArea(userArea) {
            if (!userArea) return '';
            
            try {
                // 尝试解析JSON格式
                const areaData = JSON.parse(userArea);
                if (areaData.userArea) {
                    return areaData.userArea;
                }
            } catch (e) {
                // 如果不是JSON格式，直接返回原值
                return userArea;
            }
            
            return userArea;
        },

        // 切换用户信息窗口显示/隐藏
        toggleUserInfo() {
            this.showUserInfo = !this.showUserInfo;
        },
    },
}
</script>

<style scoped>
.container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.top-bar {
    padding: 3.5px 5px 5px 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0);
    top: 0;
    width: 100%;
    z-index: 3;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 用户信息区域样式 */
.user-info-container {
    display: flex;
    flex-direction: column;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    margin: 0 auto;
    min-width: 200px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
}

.user-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #1890ff;
    padding: 10px 15px;
    color: #ffffff;
}

.user-info-title {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
}

.close-user-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #ffffff;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-user-btn:hover {
    color: #cccccc;
}

.user-info-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 15px;
    background: #ffffff;
}

.user-row {
    display: flex;
    align-items: center;
}

.user-welcome {
    color: #333333;
    font-size: 12px;
    font-weight: 500;
}

.user-area {
    color: #666666;
    font-size: 11px;
}

.logout-btn {
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 5px;
    align-self: center;
}

.logout-btn:hover {
    background: #007aff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
}

.logout-btn:active {
    transform: translateY(0);
}

.search-container {
    display: flex;
    margin: 0 auto;
    max-width: 600px;
    flex: 1;
}

.search-input {
    flex: 1;
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-size: 14px;
}

.search-input:focus {
    border-color: #1890ff;
    outline: none;
}

.search-button {
    padding: 0 15px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
}

.search-results-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    max-height: 500px;
    overflow-y: auto;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 3;
    margin-top: 5px;
}

.search-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

.search-results-content {
    padding: 10px;
}

.search-results-content table {
    width: 100%;
    border-collapse: collapse;
}

.search-results-content th,
.search-results-content td {
    padding: 8px 12px;
    border: 1px solid #ddd;
    text-align: left;
}

.search-results-content th {
    background-color: #f9f9f9;
    font-weight: bold;
}

.search-results-content tr:nth-child(even) {
    background-color: #f9f9f9;
}

.search-results-content tr:hover {
    background-color: #f0f0f0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #999;
}

.close-btn:hover {
    color: #666;
}

/* 为船舶图标添加移动端友好的CSS样式 */
@media (max-width: 600px) {

    /* 移动设备样式 */
    .ship-info-btn {
        width: 40px !important;
        height: 40px !important;
        font-size: 12px !important;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }
}

.search-container {
    position: relative;
}

.map-bar {
    flex: 1;
    width: 100%;
    min-height: 0;
    display: flex;
}

.map {
  pointer-events: auto;
  touch-action: none;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  height: 100%;
  flex: 1;
  z-index: 1;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
}

.sidebar-container {
  position: fixed;
  width: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  right: 15px;
  top: 30px;
  bottom: 2px;
  padding: 5px 0 0 0;
  justify-content: space-between;
  pointer-events: none;
  z-index: 2;
}

.sidebar-up-content {
    width: 100%;
    padding: 5px 0px;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0);
    flex-grow: 1;
    margin-top: 2.5px;
}

.sidebar-down-content {
    width: 100%;
    padding: 0px;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0);
    margin-top: auto;
    margin-bottom: 0px;
}

.sidebar-btn {
    width: 50px;
    height: 50px;
    padding: 0;
    margin: 2px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    background-color: rgba(249, 249, 249, 1);
    border: none;
    font-size: 12px;
    pointer-events: auto;
    line-height: 1.2;
}

.sidebar-btn .icon {
    margin-left: 0;
    margin-right: 0;
    font-size: 18px;
}

.btn-text {
    margin-top: 4px;
}

/* 成功提示浮窗样式 */
.success-alert {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    animation: slideDown 0.3s ease-out;
}

.success-alert-content {
    background-color: rgba(255, 255, 255, 0.9);
    color: #333;
    padding: 8px 16px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-size: 14px;
    min-width: 120px;
    justify-content: center;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes slideDownMobile {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 测距退出按钮样式 */
.ranging-exit-btn {
    position: fixed;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    color: #666;
    border: 1px solid #ddd;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
}

.ranging-exit-btn:hover {
    background: #f5f5f5;
    border-color: #bbb;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
    color: #333;
}

.ranging-exit-btn span {
    font-size: 24px;
    font-weight: bold;
    line-height: 1;
}

/* 移动设备适配 */
@media (max-width: 768px) {
    .success-alert {
        top: 15px;
        left: 50%;
        right: auto;
        width: 50%;
        transform: translateX(-50%);
        animation: slideDownMobile 0.3s ease-out;
    }

    .success-alert-content {
        font-size: 16px;
        padding: 10px 16px;
    }

    /* 移动设备上的测距退出按钮 */
    .ranging-exit-btn {
        bottom: 30px;
        width: 50px;
        height: 50px;
    }

    .ranging-exit-btn span {
        font-size: 22px;
    }
}
</style>