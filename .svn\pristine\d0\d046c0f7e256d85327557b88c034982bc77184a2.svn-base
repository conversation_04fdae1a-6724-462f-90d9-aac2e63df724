package com.bd.service.impl;
import com.bd.entity.*;
import com.bd.entity.BusinessManagement.AlarmRecord;
import com.bd.mapper.UserMapper;
import com.bd.service.UserService;
import com.bd.util.Utils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
@Service
public class UserImpl implements UserService {
    @Resource
    private UserMapper userMapper;
    @Override
    public UserInfo GetUser(UserInfo userInfo) {
        return userMapper.GetUser(userInfo);
    }
    @Override
    public int GetUserName(String username) {
        return userMapper.GetUsername(username);
    }
    @Override
    public UserInfo GetUserInfoByUsername(String username) {
        return userMapper.GetUserInfoByUsername(username);
    }
    @Override
    public UserInfo GetUserInfoById(int id) {
        return userMapper.GetUserInfoById(id);
    }
    @Override
    public List<UserInfo> GetAllUser(String userName, int userType, int pageNum) {
        if(userType == -1) {
            return userMapper.GetAllUser(userName, pageNum);
        }
        else {
            return userMapper.GetAllUserByType(userName, userType, pageNum);
        }
    }
    @Override
    public int GetAllUserCount(String userName, int userType) {
        if(userType == -1) {
            return userMapper.GetAllUserCount(userName);
        }
        else {
            return userMapper.GetAllUserCountByType(userName, userType);
        }
    }
    @Override
    public void InsertUser(UserInfo user) {
        userMapper.InsertUser(user);
    }
    @Override
    public List<UserInfo> GetUserInfo() {
        return userMapper.GetUserInfo();
    }
    @Override
    public void UpdateUser(int userId, UserInfo userInfo) {
        userMapper.UpdateUser(userId, userInfo);
    }
    @Override
    public void DeleteUser(int id) {
        userMapper.DeleteUser(id);
    }
    @Override
    public TongjiModel GetCPUState() {
        return null;
    }
    @Override
    public TongjiModel GetNeiCunState() {
        return null;
    }
    @Override
    public TongjiModel GetYingPanState() {
        return null;
    }
    @Override
    public TongjiModel GetNetState() {
        return null;
    }
    @Override
    public void addUserOperationInfo(UserOperation userOperation) {
        userMapper.addUserOperationInfo(userOperation);
    }
    @Override
    public List<UserOperation> GetUserOperation(String userName, String startTime, String endTime, int type, int pageNum){
        return userMapper.GetUserOperation(userName, startTime, endTime, type, pageNum);
    };
    @Override
    public int GetUserOperationCount(String userName, String startTime, String endTime, int type){
        return userMapper.GetUserOperationCount(userName, startTime, endTime, type);
    };
    @Override
    public int GetUserCount(int type){
        Date dNow = new Date(); //当前时间
        Date dBefore1 = new Date();
        Date dBefore3 = new Date();
        Calendar calendar1 = Calendar.getInstance(); //得到日历
        Calendar calendar3 = Calendar.getInstance(); //得到日历
        calendar1.setTime(dNow);//把当前时间赋给日历
        calendar3.setTime(dNow);//把当前时间赋给日历
        calendar1.add(calendar1.MONTH, -1); //设置为前3月
        calendar3.add(calendar3.MONTH, -3); //设置为前3月
        dBefore1 = calendar1.getTime(); //得到前3月的时间
        dBefore3 = calendar3.getTime(); //得到前3月的时间
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); //设置时间格式
        String defaultStartDate1 = sdf.format(dBefore1); //格式化前3月的时间
        String defaultStartDate3 = sdf.format(dBefore3); //格式化当前时间
        int count = 0;
        switch (type) {
            case 0:
                count = userMapper.GetUserCount(Utils.GetNowTimeString(7200));
                break;
            case 1:
                count = userMapper.GetUserCount(Utils.GetNowTimeString(7200));
                break;
            case 3:
                count = userMapper.GetUserCount(Utils.GetNowTimeString(3600 * 24 * 3));
                break;
            case 7:
                count = userMapper.GetUserCount(Utils.GetNowTimeString(3600 * 24 * 7));
                break;
            case 30:
                count = userMapper.GetUserCount(defaultStartDate1);
                break;
            case 90:
                count = userMapper.GetUserCount(defaultStartDate3);
                break;
        }
        return count;
    }
    @Override
    public int GetErrorCount(String name) {
        return userMapper.GetErrorCount(name);
    }
    @Override
    public void SetErrorCount(String name, int count) {
        userMapper.SetErrorCount(name, count);
    }
    @Override
    public void SetUserSetting(UserSettingInfo userSettingInfo) {
        userMapper.SetUserSetting(userSettingInfo);
    }
    @Override
    public void UpdateUserSetting(UserSettingInfo userSettingInfo) {
        userMapper.UpdateUserSetting(userSettingInfo);
    };
    @Override
    public void DeleteUserSetting(int userId, int setting) {
        userMapper.DeleteUserSetting(userId, setting);
    };
    @Override
    public List<UserSettingInfo> GetUserSetting(int userId) {
        return userMapper.GetUserSetting(userId);
    };
    @Override
    public void UpdateToken(String access_token) {
        userMapper.UpdateToken(access_token);
    }
    @Override
    public String GetMapToken() {
        return userMapper.GetMapToken();
    }
    @Override
    public UserInfo isCheckAccount(String sendName) {
        return userMapper.isCheckAccount(sendName);
    }
    @Override
    public void SendWarningToOtherAccount(int sendId, int receiveId, int warningId) {
        userMapper.SendWarningToOtherAccount(sendId, receiveId, warningId);
    }
    @Override
    public String GetOpenCenterToken() {
        return userMapper.GetOpenCenterToken();
    }
    @Override
    public String GetOpenCenterToken_HIK() {
        return userMapper.GetOpenCenterToken_HIK();
    }
    @Override
    public void UpdateOpenCenterToken(String token) {
        userMapper.UpdateOpenCenterToken(token);
    }
    @Override
    public void UpdateOpenCenterToken_HIK(String token) {
        userMapper.UpdateOpenCenterToken_HIK(token);
    }
    @Override
    public List<PushInformation> GetWarningToOtherAccount(int userId, int model) {
        return userMapper.GetWarningToOtherAccount(userId, model);
    };
    @Override
    public void SetWarningToOtherAccountIsRead(int userId) {
        userMapper.SetWarningToOtherAccountIsRead(userId);
    }
    @Override
    public void deletePortNode() {
        userMapper.deletePortNode();
    }
}
