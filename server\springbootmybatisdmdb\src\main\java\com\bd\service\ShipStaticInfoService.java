package com.bd.service;

import com.bd.entity.*;
import com.bd.entity.BusinessManagement.AlarmRecord;
import com.bd.entity.dto.ShipQueryDto;
import com.bd.entity.ship.*;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface ShipStaticInfoService {
    List<ShipSerch> GetShipInfoByNameOrTerminalNumber(String name);
    List<ShipStaticInfo_all> GetOneShipInfoByShipName(String name);
    List<ShipStaticInfo_all> GetOneShipInfoById(int id);
    List<ShipStaticInfo_all> GetOneWSShipInfoById(int id);

    PageInfo<ShipStaticInfo_all> GetAllFishShip(ShipQueryDto queryDto);
    PageInfo<ShipStaticInfo_all> getAllFishShip(ShipQueryDto queryDto);
    int GetAllFishShipCount(ShipQueryDto queryDto);

    List<ShipStaticInfo_all> GetAllFishShip_special(String shipName, String bdId, String mmsi, int shipType, int specialType, int pageNum);

    List<ShipStaticInfo_all> GetFishShip_special(String shipName, String bdId, String mmsi, int shipType, int specialType);

    int GetAllFishShipCount_special(String shipName, String bdId, String mmsi, int shipType, int specialType);

    void SetSpecialShip(int shipId, int shipType);

    void DeleteSpecialShip(int shipId, int specialType);

    int GetOnlineShipCount();

    int GetTotalShipCount();

    List<ShipStaticInfo_all> GetAllInPortShip(String portName, int pageNum);

    List<ShipStaticInfo_all> GetAllInPortShipCount(String portName);

    List<ShipStaticInfo_all> GetAllInPortShip_Export(String portName);

    List<AlarmRecord> GetAllShutDownInfo(String shipName, int pageNum, int model);

    int GetAllShutDownInfoCount(String shipName, int model);

    List<AlarmRecord> GetAllShutDownInfo_Export(String shipName, int model);

    List<ShipStaticInfo_all> GetAllFishShip_Export(String shipName, String bdId, String mmsi, int shipType, List<Long> managerIdList,
                                                   int shipLength, int shipSmallOrBig, int shipPerson, int personSmallOrBig);

    List<ShipStaticInfo_card> GetOneShipCardById(int id);

    PlayShipInfo GetPlayShipInfoById(int id);

    List<Ship_Voyage> GetAllVoyageInfo(String shipName, int pageNum);

    int GetAllVoyageInfoCount(String shipName);

    List<Ship_Voyage> GetAllVoyageInfo_Export(String shipName);

    List<Ship_WorkInfo> GetAllShipWorkInfo(String shipName,String startTime, String endTime,int bdOrAis, int pageNum);

    List<Ship_WorkInfo> GetAllShipWorkInfoCount(String shipName, String startTime, String endTime, int bdOrAis);

    List<Ship_WorkInfo> GetAllShipWorkInfo_Export(String shipName,String startTime, String endTime, int bdOrAis);

    List<AllShipType> GetFocusShipType();

    List<AllShipType> GetWarningShipType(int userId, int model);

    List<AllShipType> GetJianCeShipType();

    void InsertShipNameCard(List<Ship_NameCard> shipList);

    void InsertShipNation(List<Ship_Nation> shipList);

    void InsertShipNet(List<Ship_Net> shipList);

    void InsertShipPermit(List<Ship_Permit> shipList);

    void InsertShipCheck(List<Ship_Check> shipList);

    void InsertShipSave(List<Ship_Save> shipList);

    void ClearShipInfo();

    ShipStaticInfo_all GetShipIdByName(String shipName);

    void ClearOldInfo();

    int GetManagerCount(List<Long> managerIdList);

    List<ShipSerch> GetShipInfoByShipNameList(List<String> shipNameList);

    List<ShipStaticInfo_all> selectShipName();

    FisheryPermitInfo GetOneShipCardTexuById(int id);

    List<Ship> GetCurrentInPortShips();

    List<Port> GetCurrentAllPorts();

    List<Ship> GetCurrentShipsByPortName(String portName);

    List<Port> GetOutsideInPorts();

    List<Ship> GetOutsideShipsByPortName(String portName);
}
