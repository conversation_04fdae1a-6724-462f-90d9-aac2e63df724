<template>
  <div class="multicast-container">
    <div class="header-wrapper">
      <button class="back-btn" @click="goBack"></button>
      <div class="switch" @click="toggleMode" :class="{ 'multicast-active': !isUnicast }">
        <div class="switch-item unicast-item">单播</div>
        <div class="switch-item multicast-item">组播</div>
      </div>
    </div>
    <div class="group-list">
      <label
        v-for="group in groups"
        :key="group.id"
        class="group-btn"
        :class="{ selected: selectedGroupIds.includes(group.id) }"
      >
        <input
          type="checkbox"
          v-model="selectedGroupIds"
          :value="group.id"
          style="display:none"
        />
        {{ group.groupName }}
      </label>
      <button class="add-btn" @click="addGroup">+</button>
    </div>
    <div class="chat-area">
      <!-- 聊天内容可以用 v-for 渲染 -->
    </div>
    <div class="input-area">
      <input v-model="input" class="chat-input" />
      <button
        class="send-btn"
        @mousedown="sendBtnActive = true"
        @mouseup="sendBtnActive = false"
        @mouseleave="sendBtnActive = false"
        @touchstart="sendBtnActive = true"
        @touchend="sendBtnActive = false"
        @touchcancel="sendBtnActive = false"
        :class="{active: sendBtnActive}"
        @click="send"
      >发送</button>
    </div>
  </div>
</template>

<script>
import global from './Global.vue'
export default {
  data() {
    return {
      isUnicast: false,
      groups: [
        { id: 1, bdId: '上海', groupName: '@ 上海' },
        { id: 2, bdId: '崇明', groupName: '@ 崇明' },
        { id: 3, bdId: '浦东', groupName: '@ 浦东' }
      ],
      selectedGroupIds: [],
      input: '',
      sendBtnActive: false
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1);
      setTimeout(() => {
        window.location.reload();
      }, 100);
    },
    toggleMode() {
      this.isUnicast = !this.isUnicast;
      if (this.isUnicast) {
        this.$router.replace('/unicast');
      } else {
        this.$router.replace('/multicast');
      }
    },
    addGroup() {
      // 添加组逻辑
    },
    send() {
      if (this.selectedGroupIds.length === 0) {
        alert('请选择要发送的组');
        return;
      }
      const mess = this.input;
      const userId = sessionStorage.getItem('userId');
      this.selectedGroupIds.forEach(id => {
        const group = this.groups.find(g => g.id === id);
        if (!group) return;
        const data = {
          bdId: group.bdId,
          groupName: group.groupName,
          msg: mess,
          userId
        };
        $.ajax({
          url: global.IP + "/web/InsertBdMsg",
          type: "POST",
          data: JSON.stringify(data),
          dataType: "json",
          contentType: "application/json",
          success: function(data) {
            // ...你的成功逻辑
          },
          error: function() {
            // ...你的失败逻辑
          }
        });
      });
      this.input = '';
    }
  }
}
</script>

<style scoped>
.multicast-container {
  width: 100%;
  height: 100vh;
  background: #f3f3f3;
  display: flex;
  flex-direction: column;
}

.header-wrapper {
  position: relative;
  background-color: #1890ff;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0;
}

.switch {
  display: flex;
  position: relative;
  width: 140px;
  border-radius: 20px;
  background: #fff;
  padding: 2px;
  cursor: pointer;
  user-select: none;
}

.switch::before {
  content: '';
  position: absolute;
  width: calc(50% - 2px);
  height: calc(100% - 4px);
  top: 2px;
  left: 2px;
  background: #1890ff;
  border-radius: 18px;
  transition: left 0.25s ease-out;
  z-index: 1;
}

.switch.multicast-active::before {
  left: 50%;
}

.switch-item {
  flex: 1;
  padding: 6px;
  text-align: center;
  z-index: 2;
  font-weight: 500;
  font-size: 14px;
  transition: color 0.25s ease-out;
}

.switch .unicast-item {
  color: #333;
}

.switch .multicast-item {
  color: white;
}

.switch.multicast-active .unicast-item {
  color: #333;
}

.switch.multicast-active .multicast-item {
  color: white;
}

.back-btn {
  position: absolute;
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}
.back-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  border-top: 2px solid white;
  border-left: 2px solid white;
  transform: translate(-50%, -50%) rotate(-45deg);
}
.header {
  background: #e5e5e5;
  text-align: center;
  font-size: 32px;
  padding: 16px 0;
  font-weight: bold;
}
.group-list {
  display: inline-flex;
  align-items: center;
  padding: 8px 8px;
  margin: 3px 0;
  gap: 4px 6px;
  justify-content: flex-start;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  min-height: 44px;
  margin-bottom: 0;
  white-space: nowrap;
  overflow-x: auto;
  width: 100%;
  box-sizing: border-box;
}
.group-list > .group-btn {
  margin-bottom: 0;
}
.group-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  border: 2px solid #1890ff;
  border-radius: 4px;
  padding: 4px 16px;
  font-size: 14px;
  color: #1890ff;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(.4,0,.2,1), background 0.2s, color 0.2s, box-shadow 0.2s;
  font-weight: 500;
  letter-spacing: 0.2px;
  margin-right: 4px;
  min-width: 70px;
  max-width: 140px;
  white-space: nowrap;
  box-shadow: none;
  user-select: none;
  position: relative;
}
.group-btn.selected {
  background: #1890ff;
  color: #fff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.18);
  border: 2px solid #1890ff;
}
.add-btn {
  width: 28px;
  height: 28px;
  font-size: 18px;
  border: 1.5px dashed #1890ff;
  border-radius: 4px;
  background: #fff;
  color: #1890ff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
  vertical-align: middle;
  box-shadow: 0 1px 4px rgba(24, 144, 255, 0.13);
  margin-left: 0;
  margin-bottom: 0;
}
.add-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}
.add-btn:active {
  transform: scale(0.95);
  background-color: #fff;
  color: #1890ff;
  border-color: #1890ff;
  border-style: solid;
}
.chat-area {
  flex: 1;
  border: 2px solid #f3f3f3;
  background: #f3f3f3;
  margin: 0 0 0 0;
  padding: 0;
  overflow-y: auto;
}
.input-area {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #e5e5e5;
  border-top: 1px solid #ccc;
}
.chat-input {
  flex: 1;
  height: 40px;
  font-size: 18px;
  border: 2px solid #ccc;
  border-radius: 4px;
  padding: 0 8px;
  margin-right: 16px;
}
.chat-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}
.send-btn {
  position: relative;
  overflow: hidden;
  width: 80px;
  height: 40px;
  font-size: 20px;
  background: #fff;
  border: 2px solid #1890ff;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.15s cubic-bezier(.4,0,.2,1), background 0.15s, color 0.15s, box-shadow 0.15s, border-color 0.15s;
  box-shadow: 0 1px 4px rgba(24, 144, 255, 0.10);
  outline: none;
}
.send-btn.active {
  transform: scale(0.93);
  background: #1890ff;
  color: #fff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.18);
  border-color: #1890ff;
}
.send-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
  border-color: #1890ff;
}
</style>
