package com.bd.thread;

import com.alibaba.fastjson.JSONObject;
import com.bd.entity.People;
import com.bd.service.PeopleService;
import com.bd.service.UserService;
import com.bd.util.HttpTool;
import com.bd.util.Utils;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
@Controller
@EnableScheduling
public class UpdateCrewInfoThread{

    @Resource
    private PeopleService peopleService;

    @Resource
    private UserService userService;

    //@Scheduled(fixedRate = Integer.MAX_VALUE)
    //@Scheduled(cron = "0 0 1 * * ?")
    public void run() throws Exception {
        System.out.println("updateCrewInfo---begin!");
        DeleteCrewInfo();
        UpdateCrewInfo();
        UpdateCrewTimeState();
        System.out.println("updateCrewInfo---end!");
    }

    ///////////////船员数据更新///////////////
    public void UpdateCrewInfo() throws Exception {
        int pageNum = 1;
        List<String> crewList = new ArrayList<>();
        while (true) {
            try {
                crewList = GetCrewList(pageNum);
                //Thread.sleep(1000 * 10);
            } catch (Exception e) {
                continue;
            }
            List<People> peopleList = new ArrayList<>();
            if (crewList.size() < 2) break;
            for (String crewStr : crewList) {
                //System.out.println(crewList.size());
                JSONObject crewJson = JSONObject.parseObject(crewStr);
                People people = new People();
                if(crewJson.get("xm") == null)
                    people.setNAME("--");
                else
                    people.setNAME(crewJson.get("xm").toString());

                if(crewJson.get("sfzhm") == null)
                    people.setIDCARD("--");
                else
                {
                    people.setIDCARD(crewJson.get("sfzhm").toString());
                }
                if(crewJson.get("zszlmc") == null)
                    people.setCERTIFICATENAME("--");
                else
                    people.setCERTIFICATENAME(crewJson.get("zszlmc").toString());

                if(crewJson.get("zszt") == null)
                    people.setCERTIFICATESTATE("--");
                else
                    people.setCERTIFICATESTATE(crewJson.get("zszt").toString());

                if(crewJson.get("lbmc") == null)
                    people.setCERTIFICATETYPE("--");
                else
                    people.setCERTIFICATETYPE(crewJson.get("lbmc").toString());

                if(crewJson.get("jzrq") == null)
                    people.setCERTIFICATETIME("--");
                else
                    people.setCERTIFICATETIME(crewJson.get("jzrq").toString());

                if(crewJson.get("zszwmc") == null)
                    people.setZHIWU("--");
                else
                    people.setZHIWU(crewJson.get("zszwmc").toString());

                if(crewJson.get("qfjgmc") == null)
                    people.setORGANIZATION("--");
                else
                    people.setORGANIZATION(crewJson.get("qfjgmc").toString());

                if(crewJson.get("xb") == null)
                    people.setSEX("--");
                else
                    people.setSEX(crewJson.get("xb").toString());

                if(crewJson.get("zsdjmc") == null)
                    people.setZSDJMC("--");
                else
                    people.setZSDJMC(crewJson.get("zsdjmc").toString());
                if (crewJson.get("sycb") == null)
                    people.setSYCB("--");
                else
                    people.setSYCB(crewJson.get("sycb").toString());
                if (crewJson.get("zshm") == null)
                    people.setZSHM("--");
                else
                    people.setZSHM(crewJson.get("zshm").toString());
                if (crewJson.get("qfrq") == null)
                    people.setQFRQ("--");
                else
                    people.setQFRQ(crewJson.get("qfrq").toString());

                //签发官员姓名
                if (crewJson.get("qfgyxm") == null)
                    people.setQFGYXM("--");
                else
                    people.setQFGYXM(crewJson.get("qfgyxm").toString());

                if (crewJson.get("dahm") == null)
                    people.setDAHM("--");
                else
                    people.setDAHM(crewJson.get("dahm").toString());


                peopleList.add(people);


            }
            System.out.println("pageNum:" + pageNum);
            pageNum++;

            peopleService.InsertCrewInfo(peopleList);

        }
    }

    public List<String> GetCrewList(int pageNum) {
        String token = userService.GetOpenCenterToken();
        //System.out.println(token);
        String header = "Bearer " + token;
        String url = "http://10.90.7.5:18080/api/service/share/P330399853278400512?conditions=null&pageNum=" + pageNum + "&pageSize=" + 1000;
        String crewData = HttpTool.doGet(url, header);
        crewData = crewData.replace("[", "");
        crewData = crewData.replace("]", "");
        crewData = crewData.replace("},{", "}#{");
        List<String> crewJsonList = Arrays.asList(crewData.split("#"));
        return crewJsonList;
    }

    public void DeleteCrewInfo() {
        peopleService.ClearCrewInfo();
    }

    public void UpdateCrewTimeState(){
        peopleService.UpdateCrewTimeState(Utils.GetTodayTimeString());
    }
}
