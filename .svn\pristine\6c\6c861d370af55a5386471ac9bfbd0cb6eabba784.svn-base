package com.bd.entity.ship;

import lombok.Data;

@Data
public class Ship_Net {

    public String app_time;
    public String company_name;
    public String company_address;
    public String company_nature;
    public String company_postcode;
    public String company_phone;
    public String company_lawman;
    public String company_ship_num;
    public String company_all_power;
    public String company_apply_reason;
    public String person_name;
    public String person_identify;
    public String person_address;
    public String person_postcode;
    public String person_phone;
    public String person_job;
    public String person_ship_num;
    public String person_all_power;
    public String person_apply_reason;
    public String dict_app_pro_type;
    public String dict_app_ship_type;
    public String dict_app_job_type1;
    public String dict_app_job_type2;
    public String app_job_place;
    public String app_main_power;
    public String app_subship_power;
    public String app_ship_length;
    public String app_all_ton;
    public String dict_app_ship_material;
    public String app_power_norm;
    public String old_power_norm;
    public String norm_apply_number;
    public String norm_apply_district_id;
    public String norm_permit_number_old;
    public String norm_apply_reissue_count;
    public String user_operator;
    public String operate_time;
    public String norm_apply_print_count;
    public String norm_apply_rel_number;
    public String app_subship_num;
    public String company_reg_no;
    public String dist_norm_district_id;
    public String remark;
    public String dist_ship_district_id;
    public String app_job_way_fuzhu;
    public String signate_person;
    public String signate_time;
    public String norm_permit_number;
    public String norm_permit_print_count;
    public String norm_rel_permit_number;
    public String dept_signate_dept;
    public String log_norm;
    public String op_state;
    public String create_by;
    public String create_time;
    public String update_by;
    public String update_time;
    public String is_deleted;
}