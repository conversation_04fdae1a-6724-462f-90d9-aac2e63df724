<template>
  <div class="weilan-dialog" :style="{
      left: dialogLeft + 'px',
      top: dialogTop + 'px',
      position: 'fixed',
      zIndex: 9999,
      transform: isMobile ? 'scale(0.6)' : 'scale(0.8)',
      transformOrigin: 'top left'
  }" @mousedown="onDragStart" @touchstart="onTouchStart">
    <div class="dialog-header">
      <div class="dialog-title">绘制电子围栏</div>
      <span class="close-btn" @click.stop="closeDialog">×</span>
    </div>

    <!-- 表单区域 -->
    <div class="form-section" @mousedown.stop @touchstart.stop>
      <div class="form-row">
        <label class="form-label">围栏名称:</label>
        <input type="text" v-model="formData.fenceName" class="form-input" placeholder="区域">
      </div>

      <div class="form-row">
        <label class="form-label">区域类型:</label>
        <select v-model="formData.annotationType" class="form-select">
          <option value="fuxiu">伏休区</option>
          <option value="gaowei">高危区</option>
          <option value="teshu">特殊区</option>
          <option value="custom">自定义报警区</option>
        </select>
      </div>

      <div class="form-row">
        <label class="form-label">预警类型:</label>
        <select v-model="formData.warningType" class="form-select">
          <option value="enter">进区域报警</option>
          <option value="exit">出区域报警</option>
          <option value="both">进出区域报警</option>
        </select>
      </div>

      <div class="form-row">
        <label class="form-label">开始时间:</label>
        <input type="date" v-model="formData.startDate" class="form-input">
      </div>

      <div class="form-row">
        <label class="form-label">结束时间:</label>
        <input type="date" v-model="formData.endDate" class="form-input">
      </div>

      <div class="form-row">
        <label class="form-label">速度规则:</label>
        <select v-model="formData.speedRule" class="form-select">
          <option value="none">无速度限制</option>
          <option value="limit">限速</option>
          <option value="min">最低速度</option>
        </select>
      </div>

      <div class="form-row" v-if="formData.speedRule !== 'none'">
        <label class="form-label">速度阈值:</label>
        <input type="number" v-model="formData.speedValue" class="form-input" placeholder="请输入速度值">
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section" @mousedown.stop @touchstart.stop>
      <table class="data-table">
        <thead>
          <tr>
            <th>序号</th>
            <th>经度</th>
            <th>纬度</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(point, index) in tableData" :key="index">
            <td>{{ index + 1 }}</td>
            <td>{{ point.longitude }}</td>
            <td>{{ point.latitude }}</td>
            <td>
              <button class="action-btn delete-btn" @click="deletePoint(index)">删除</button>
            </td>
          </tr>
          <tr v-if="tableData.length === 0">
            <td colspan="4" class="empty-row">暂无数据</td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 按钮区域 -->
    <div class="button-section" @mousedown.stop @touchstart.stop>
      <button class="action-btn cancel-btn" @click="closeDialog">取消</button>
      <button class="action-btn confirm-btn" @click="confirmAction">确定</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WeilanDialog',
  data() {
    const isMobile = /Android|webOS|iPhone|iPod|BlackBerry|iPad|Mobile/i.test(navigator.userAgent);
    return {
      isMobile: isMobile,
      dialogLeft: isMobile ? 10 : 300,
      dialogTop: isMobile ? 60 : 100,
      dragging: false,
      dragOffsetX: 0,
      dragOffsetY: 0,
      formData: {
        fenceName: '',
        annotationType: 'fuxiu', // 默认选择伏休区
        warningType: 'enter',
        startDate: '',
        endDate: '',
        speedRule: 'none',
        speedValue: ''
      },
      tableData: [
        // 示例数据
        { longitude: '120.123456', latitude: '30.123456' },
        { longitude: '120.234567', latitude: '30.234567' },
        { longitude: '120.345678', latitude: '30.345678' }
      ]
    }
  },
  mounted() {
    document.addEventListener('mousemove', this.onDragMove);
    document.addEventListener('mouseup', this.onDragEnd);
    document.addEventListener('touchmove', this.onTouchMove, { passive: false });
    document.addEventListener('touchend', this.onTouchEnd);
  },
  beforeDestroy() {
    document.removeEventListener('mousemove', this.onDragMove);
    document.removeEventListener('mouseup', this.onDragEnd);
    document.removeEventListener('touchmove', this.onTouchMove);
    document.removeEventListener('touchend', this.onTouchEnd);
  },
  methods: {
    closeDialog() {
      this.$emit('close');
    },
    confirmAction() {
      // 处理确定按钮点击
      const data = {
        formData: this.formData,
        tableData: this.tableData
      };
      this.$emit('confirm', data);
    },
    deletePoint(index) {
      this.tableData.splice(index, 1);
    },
    // PC端拖拽
    onDragStart(e) {
      if (e.button !== 0) return;
      this.dragging = true;
      this.dragOffsetX = e.clientX - this.dialogLeft;
      this.dragOffsetY = e.clientY - this.dialogTop;
    },
    onDragMove(e) {
      if (!this.dragging) return;
      this.dialogLeft = e.clientX - this.dragOffsetX;
      this.dialogTop = e.clientY - this.dragOffsetY;
    },
    onDragEnd() {
      this.dragging = false;
    },
    // 移动端拖拽
    onTouchStart(e) {
      if (e.touches.length !== 1) return;
      this.dragging = true;
      const touch = e.touches[0];
      this.dragOffsetX = touch.clientX - this.dialogLeft;
      this.dragOffsetY = touch.clientY - this.dialogTop;
    },
    onTouchMove(e) {
      if (!this.dragging || e.touches.length !== 1) return;
      const touch = e.touches[0];
      this.dialogLeft = touch.clientX - this.dragOffsetX;
      this.dialogTop = touch.clientY - this.dragOffsetY;
      e.preventDefault();
    },
    onTouchEnd() {
      this.dragging = false;
    }
  }
}
</script>

<style scoped>
.weilan-dialog {
  background: white;
  border-radius: 8px;
  padding: 20px;
  width: 480px;
  max-height: 80vh;
  font-size: 16px;
  color: #333;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: fixed;
  z-index: 9999;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  cursor: move;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px 20px 10px 20px;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
}

.dialog-title {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
  line-height: 1.2;
  margin: 0;
}

.close-btn {
  position: absolute;
  right: 0;
  font-size: 28px;
  color: #999;
  cursor: pointer;
  font-weight: bold;
  transition: color 0.2s;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: transparent;
}

.close-btn:hover {
  color: #d32f2f;
  background: #f5f5f5;
}

.form-section {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.form-label {
  width: 80px;
  text-align: right;
  margin-right: 10px;
  color: #333;
  font-size: 16px;
  flex-shrink: 0;
}

.form-input, .form-select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 16px;
  line-height: 1.4;
  height: 38px;
  box-sizing: border-box;
  outline: none;
  transition: border-color 0.2s;
  background: white;
}

.form-input:focus, .form-select:focus {
  border-color: #1890ff;
}

.form-select {
  cursor: pointer;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='%23666' d='M8 11L3 6h10z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 12px;
  padding-right: 32px;
}

/* 修复移动端日期输入框样式 */
.form-input[type="date"] {
  background: white !important;
  color: #333 !important;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.form-input[type="date"]::-webkit-calendar-picker-indicator {
  background: transparent;
  bottom: 0;
  color: transparent;
  cursor: pointer;
  height: auto;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: auto;
}

.form-input[type="date"]::-webkit-inner-spin-button,
.form-input[type="date"]::-webkit-clear-button {
  display: none;
}

.form-input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield;
}

.form-input[type="number"]::-webkit-outer-spin-button,
.form-input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.table-section {
  margin-bottom: 20px;
  max-height: 250px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  flex: 1;
  min-height: 120px;
  position: relative;
}

/* 美化滚动条 */
.table-section::-webkit-scrollbar {
  width: 6px;
}

.table-section::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.table-section::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.table-section::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 16px;
}

.data-table th {
  background: #f5f5f5;
  padding: 8px;
  text-align: center;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 500;
  color: #333;
}

.data-table td {
  padding: 8px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.data-table tbody tr:hover {
  background: #f9f9f9;
}

.empty-row {
  color: #999;
  font-style: italic;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.delete-btn {
  background: #ff4d4f;
  color: white;
}

.delete-btn:hover {
  background: #ff7875;
}

.button-section {
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid #e0e0e0;
  flex-shrink: 0;
  margin-top: auto;
  background: white;
}

.cancel-btn {
  background: #f5f5f5;
  color: #333;
  margin-right: 10px;
  padding: 8px 20px;
}

.cancel-btn:hover {
  background: #e0e0e0;
}

.confirm-btn {
  background: #1890ff;
  color: white;
  padding: 8px 20px;
}

.confirm-btn:hover {
  background: #40a9ff;
}

/* 移动端适配 */
@media (max-width: 480px) {
  .weilan-dialog {
    width: 95vw;
    height: 90vh;
    max-height: 90vh;
    padding: 15px;
    font-size: 16px;
    top: 5vh;
    left: 2.5vw;
    transform: none;
    overflow: hidden;
  }

  .dialog-header {
    padding: 10px 25px 15px 25px;
  }

  .dialog-title {
    font-size: 26px;
  }

  .close-btn {
    font-size: 32px;
    width: 40px;
    height: 40px;
  }

  .form-label {
    width: 75px;
    font-size: 16px;
  }

  .form-input, .form-select {
    font-size: 16px;
    height: 40px;
    padding: 8px 12px;
  }

  .form-select {
    padding-right: 32px;
    background-size: 12px;
    background-position: right 8px center;
  }

  /* 移动端日期输入框特殊处理 */
  .form-input[type="date"] {
    background: white !important;
    color: #333 !important;
    font-size: 16px !important;
    height: 40px !important;
    line-height: 1.4 !important;
  }

  .form-input[type="date"]:focus {
    background: white !important;
    color: #333 !important;
  }

  .form-input[type="date"]::-webkit-datetime-edit {
    color: #333 !important;
  }

  .form-input[type="date"]::-webkit-datetime-edit-fields-wrapper {
    color: #333 !important;
  }

  .form-input[type="date"]::-webkit-datetime-edit-text {
    color: #333 !important;
  }

  .form-input[type="date"]::-webkit-datetime-edit-month-field,
  .form-input[type="date"]::-webkit-datetime-edit-day-field,
  .form-input[type="date"]::-webkit-datetime-edit-year-field {
    color: #333 !important;
  }

  .data-table {
    font-size: 15px;
  }

  .data-table th, .data-table td {
    padding: 8px 6px;
  }

  .action-btn {
    padding: 8px 12px;
    font-size: 15px;
  }

  .table-section {
    max-height: none;
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  .button-section {
    position: sticky;
    bottom: 0;
    background: white;
    z-index: 10;
    margin-top: 10px;
    padding-top: 15px;
    border-top: 1px solid #e0e0e0;
  }
}
</style>