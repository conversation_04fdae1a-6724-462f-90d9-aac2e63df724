package com.bd.entity;

import lombok.Data;


/**
 * 船员
 */
@Data
public class People {

    private int ID;
    private int STATICSHIPID;
    private String SHIPNAME;
    private String NAME;
    private String IDCARD;
    private String LXDH;
    private String LXDZ;
    private String SEX;
    private String BAOXIAN;
    private String LOADTIME;
    private String CERTIFICATETYPE;
    private String CERTIFICATENAME;
    private String CERTIFICATETIME;
    private String CERTIFICATESTATE;
    private String ORGANIZATION;
    private String ZHIWU;
    private String ZSDJMC;
    private String SYCB;
    private String ZSHM;
    private String QFRQ;

    private String QFGYXM;
    private String DAHM;
    private String IDCARDSIGNCODE;
    private String LXDHSIGNCODE;
}
