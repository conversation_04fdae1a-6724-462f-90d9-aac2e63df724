package com.bd.entity;

import lombok.Data;

/**
 * 渔船基本信息
 */
@Data
public class FishingBoatInfo {
    private String ycxxwybs;
    private String gxsj;
    private String gxcz;
    private String ycssdqdm;
    private String ycssdqmc;
    private String ycbm;
    private String cm;
    private String cmyw;
    private String cbzl;
    private String cz;
    private String xk;
    private String xs;
    private String zdw;
    private String jdw;
    private String zjzgl;
    private String zjxhy;
    private String zjxhe;
    private String zjxhs;
    private String zjgly;
    private String zjgle;
    private String zjgls;
    private String ctcz;
    private String ctczyw;
    private String jzwgrq;
    private String zjsl;
    private String cbhhsbm;
    private String zczjsl;
    private String zczjxh;
    private String zccm;
    private String zcsl;
    private String zczgl;
    private String hyyyblxkzlb;
    private String cbsyrmc;
    private String cbsyrmcyw;
    private String cbsyrdz;
    private String cbsyrdzyw;
    private String cbsyrdh;
    private String cblx;
    private String cblxyw;
    private String zjjhy;
    private String zjjhe;
    private String zjjhs;
    private String cwgjzbpzsbh;
    private String skgl;
    private String hzcz;
    private String hzzd;
    private String etl_time;
}
