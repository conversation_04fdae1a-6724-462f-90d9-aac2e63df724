-- 测试数据插入脚本
-- 执行日期: 2025-07-29
-- 说明: 插入测试用户和摄像头数据

-- ========================================
-- 插入测试用户数据
-- ========================================

-- 插入崇明区域用户
INSERT INTO SHIP.USERINFO (USERNAME, PASSWORD, NAME, LXDH, LEVEL, TOKEN, BM, ERRORCOUNT, USER_AREA) 
VALUES ('chongming01', '123456', '崇明用户', null, 1, null, null, 0, '崇明');

-- 插入浦东区域用户
INSERT INTO SHIP.USERINFO (USERNAME, PASSWORD, NAME, LXDH, LEVEL, TOKEN, BM, ERRORCOUNT, USER_AREA) 
VALUES ('pudong01', '123456', '浦东用户', null, 1, null, null, 0, '浦东');

-- 插入管理员用户（可以看到所有摄像头）
INSERT INTO SHIP.USERINFO (USERNAME, PASSWORD, NAME, LXDH, LEVEL, TOKEN, BM, ERRORCOUNT, USER_AREA) 
VALUES ('admin', '123456', '管理员', null, 0, null, null, 0, null);

-- ========================================
-- 插入测试摄像头数据
-- ========================================

-- 插入崇明区域摄像头
INSERT INTO SHIP.CAMERAINFO (LON, LAT, AREA, NAME, STATE, HLS, IMG, PORTID, AREANAME) 
VALUES (1214567890, 313456789, '崇明港区', '崇明港口摄像头1', 1, 'http://example.com/camera1.m3u8', null, 1, '崇明');

INSERT INTO SHIP.CAMERAINFO (LON, LAT, AREA, NAME, STATE, HLS, IMG, PORTID, AREANAME) 
VALUES (1214567900, 313456800, '崇明港区', '崇明港口摄像头2', 1, 'http://example.com/camera2.m3u8', null, 1, '崇明');

-- 插入浦东区域摄像头
INSERT INTO SHIP.CAMERAINFO (LON, LAT, AREA, NAME, STATE, HLS, IMG, PORTID, AREANAME) 
VALUES (1214567800, 313456700, '浦东港区', '浦东港口摄像头1', 1, 'http://example.com/camera3.m3u8', null, 2, '浦东');

INSERT INTO SHIP.CAMERAINFO (LON, LAT, AREA, NAME, STATE, HLS, IMG, PORTID, AREANAME) 
VALUES (1214567810, 313456710, '浦东港区', '浦东港口摄像头2', 1, 'http://example.com/camera4.m3u8', null, 2, '浦东');

-- 插入黄浦区域摄像头
INSERT INTO SHIP.CAMERAINFO (LON, LAT, AREA, NAME, STATE, HLS, IMG, PORTID, AREANAME) 
VALUES (1214567700, 313456600, '黄浦港区', '黄浦港口摄像头1', 1, 'http://example.com/camera5.m3u8', null, 3, '黄浦');

-- ========================================
-- 验证插入结果
-- ========================================

-- 查看用户数据
SELECT * FROM SHIP.USERINFO WHERE USERNAME IN ('chongming01', 'pudong01', 'admin');

-- 查看摄像头数据
SELECT * FROM SHIP.CAMERAINFO WHERE AREANAME IN ('崇明', '浦东', '黄浦');

-- 验证摄像头查询接口
SELECT c.*, p.PORTNAME as portName, c.AREANAME as areaName
FROM ship.camerainfo c
LEFT JOIN ship.portinfo p ON c.portId = p.id;
