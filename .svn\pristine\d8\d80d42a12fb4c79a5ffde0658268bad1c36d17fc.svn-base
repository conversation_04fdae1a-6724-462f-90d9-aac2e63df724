package com.bd.service.impl;
import com.bd.entity.*;
import com.bd.entity.dto.CrewCertificationQueryDto;
import com.bd.entity.dto.PeopleQueryDto;
import com.bd.mapper.PeopleMapper;
import com.bd.service.PeopleService;
import com.bd.util.Utils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
@Service
public class PeopleImpl implements PeopleService {
    @Resource
    private PeopleMapper peopleMapper;
    @Override
    public int GetAllPeopleCount(PeopleQueryDto queryDto) {
        return peopleMapper.GetAllPeopleCount(queryDto);
    }

    @Override
    public List<People> GetAllPeople_Export(PeopleQueryDto queryDto) {
        return peopleMapper.GetAllPeople_Export(queryDto);
    }

    @Override
    public List<People> GetAllPeople(PeopleQueryDto queryDto) {
        return peopleMapper.GetAllPeople(queryDto);
    }
    @Override
    public int InsertLawRecordInfo(LawRecordInfo lawRecordInfo) {
        return peopleMapper.InsertLawRecordInfo(lawRecordInfo);
    }
    @Override
    public int InsertBdMsg(BdMsg bdMsg) {
        return peopleMapper.InsertBdMsg(bdMsg);
    }
    @Override
    public List<BdMsg> GetBdMsg(String shipName, String bdId, String startTime, String endTime, int pageNum) {
        System.out.println(peopleMapper.GetBdMsg(shipName, bdId, startTime, endTime, pageNum));
        return peopleMapper.GetBdMsg(shipName, bdId, startTime, endTime, pageNum);
    };
    @Override
    public int GetBdMsgCount(String shipName, String bdId, String startTime, String endTime) {
        return peopleMapper.GetBdMsgCount(shipName, bdId, startTime, endTime);
    };
    @Override
    public List<BdMsg> GetAllBdMsg(String shipName, String bdId, String startTime, String endTime) {
        return peopleMapper.GetAllBdMsg(shipName, bdId, startTime, endTime);
    };
    @Override
    public List<BdMsg> GetAllBdMsgByUserId(int userId, int shipId) {
        return peopleMapper.GetAllBdMsgByUserId(userId, shipId);
    };
    @Override
    public void DeleteBdMsg(int id) {
        peopleMapper.DeleteBdMsg(id);
    };
    @Override
    public List<LawRecordInfo> GetLawRecordTodayWarning(int model) {
        return peopleMapper.GetLawRecordTodayWarning(Utils.GetTodayTimeString(), model);
    }
    @Override
    public LawRecordInfo GetLawRecordInfoById(int id, int model) {
        return peopleMapper.GetLawRecordInfoById(id, model);
    }
    @Override
    public List<LawRecordInfo> GetAllLawRecordInfo(String name, String startTime, String endTime, int pageNum) {
        return peopleMapper.GetAllLawRecordInfo(name, startTime, endTime, pageNum);
    }
    @Override
    public List<LawRecordInfo> GetLawRecordInfo(String name, String startTime, String endTime) {
        return peopleMapper.GetLawRecordInfo(name, startTime, endTime);
    }
    @Override
    public void DeleteLawRecordInfo(int id) {
        peopleMapper.DeleteLawRecordInfo(id);
    }
    @Override
    public int GetAllLawRecordInfoCount(String name, String startTime, String endTime) {
        return peopleMapper.GetAllLawRecordInfoCount(name, startTime, endTime);
    }
    @Override
    public List<People> GetPeopleByShipId(int shipId, int pageNum) {
        return peopleMapper.GetPeopleByShipId(shipId, pageNum);
    }
    @Override
    public int GetPeopleByShipIdCount(int shipId) {
        return peopleMapper.GetPeopleByShipIdCount(shipId);
    }
    @Override
    public List<String> GetPeopleByShipName(String shipName, int pageNum) {
        return peopleMapper.GetPeopleByShipName(shipName, pageNum);
    }
    @Override
    public int GetPeopleByShipNameCount(List<String> shipName) {
        return peopleMapper.GetPeopleByShipNameCount(shipName);
    }
    @Override
    public List<LawPeople> GetLawPeopleInfo() {
        return peopleMapper.GetLawPeopleInfo();
    }
    @Override
    public void InsertCrewInfo(List<People> crewList) {
        peopleMapper.InsertCrewInfo(crewList);
    }
    @Override
    public void InsertBdMsgExample(String content, int userId) {
        peopleMapper.InsertBdMsgExample(content, userId);
    }
    @Override
    public void DeleteBdMsgExample(int id) {
        peopleMapper.DeleteBdMsgExample(id);
    }
    @Override
    public void UpdateBdMsgExample(int id, int userId, String content) {
        peopleMapper.UpdateBdMsgExample(id, userId, content);
    }
    @Override
    public List<BDMsgExample> GetBdMsgExample(int userId) {
        return peopleMapper.GetBdMsgExample(userId);
    }
    @Override
    public void ClearCrewInfo() {
        peopleMapper.ClearCrewInfo();
    }
    @Override
    public List<People> GetPeopleByIdCard(List<String> idCardStr, int pageNum) {
        if (idCardStr.size()>0)
            return peopleMapper.GetPeopleByIdCard(idCardStr, pageNum);
        return new ArrayList<>();
    }
    @Override
    public List<People> GetCrewPeopleByShipId(int shipId) {
        return peopleMapper.GetCrewPeopleByShipId(shipId);
    }
    @Override
    public FisheryBoatInOutReport GetPortNodeByShipId(int shipId, Integer state) {
        return peopleMapper.GetPortNodeByShipId(shipId, state);
    }

    @Override
    public List<FisheryBoatInOutReport> GetPortNodeByShipIds(List<Integer> shipIds) {
        return peopleMapper.GetPortNodeByShipIds(shipIds,Utils.GetNowTimeString(180*24*3600));
    }

    @Override
    public List<CrewBx> GetCrewBxByShipName(String shipName) {
        return peopleMapper.GetCrewBxByShipName(shipName, Utils.GetNowTimeString());
    }

    @Override
    public List<CrewBx> GetCrewBxByShipNames(List<String> shipNames) {
        return peopleMapper.GetCrewBxByShipNames(shipNames,Utils.GetNowTimeString());
    }

    @Override
    public List<CrewBx> GetPeopleByIdcards(List<String> idcards) {
        return peopleMapper.GetPeopleByIdcards(idcards);
    }
    @Override
    public List<LawCase> GetlawCaseByShipId(List<String> idcards) {
        return peopleMapper.GetlawCaseByShipId(idcards);
    }
    @Override
    public List<String> GetShipNameByPeople(String keyword) {
        return peopleMapper.GetShipNameByPeople(keyword);
    }
    @Override
    public int GetPersonCount(String shipName) {
        Integer count = peopleMapper.GetPersonCount(shipName);
        return count == null ? 0: count;
    }
    @Override
    public String GetApplinameByShipName(String shipName) {
        return peopleMapper.GetApplinameByShipName(shipName);
    }

    @Override
    public String GetZshmByIdCard(String idcard) {
        return peopleMapper.GetZshmByIdCard(idcard);
    }

    @Override
    public String GetEndTimeByIdCard(String idcard) {
        return peopleMapper.GetEndTimeByIdCard(idcard);
    }

    @Override
    public void UpdateCrewTimeState(String time) {
        peopleMapper.UpdateCrewTimeState(time);
    }

    @Override
    public List<People> GetCrewCertificateStatistics(CrewCertificationQueryDto queryDto) {
        return peopleMapper.GetCrewCertificateStatistics(queryDto);
    }

    @Override
    public String GetIdcardSignCodeByIdcard(String idcard) {
        return peopleMapper.GetIdcardSignCodeByIdcard(idcard);
    }
}
