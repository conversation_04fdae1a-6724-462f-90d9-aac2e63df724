2025-01-02 14:58:41.162 ERROR [pool-2-thread-1]o.s.scheduling.support.TaskUtils$LoggingErrorHandler.handleError:96 -Unexpected error occurred in scheduled task.
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLException: 字符串转换出错
### The error may exist in file [G:\yimaWorking\bdxt_shipMonitor_web\Server\springbootmybatisdmdb\target\classes\mapper\PeopleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: update ship.people set certificatestate = 2 where certificatetime < ? and certificatestate = 1;
### Cause: java.sql.SQLException: 字符串转换出错
; ]; 字符串转换出错; nested exception is java.sql.SQLException: 字符串转换出错
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:74)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy73.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:287)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:67)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:57)
	at com.sun.proxy.$Proxy79.UpdateCrewTimeState(Unknown Source)
	at com.bd.service.impl.PeopleImpl.UpdateCrewTimeState(PeopleImpl.java:185)
	at com.bd.thread.UpdateCrewInfoThread.UpdateCrewTimeState(UpdateCrewInfoThread.java:166)
	at com.bd.thread.UpdateCrewInfoThread.run(UpdateCrewInfoThread.java:38)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:65)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.sql.SQLException: 字符串转换出错
	at dm.jdbc.dbaccess.DBError.throwSQLException(DBError.java:44)
	at dm.jdbc.dbaccess.Request_Response.resp_checkErr(Request_Response.java:2570)
	at dm.jdbc.dbaccess.Request_Response.resp_execute(Request_Response.java:1107)
	at dm.jdbc.dbaccess.DmdbCSI.getDescAndResultSet(DmdbCSI.java:903)
	at dm.jdbc.dbaccess.DmdbCSI.executePrepared(DmdbCSI.java:943)
	at dm.jdbc.dbaccess.DmdbCSI.executePrepared(DmdbCSI.java:332)
	at dm.jdbc.driver.DmdbPreparedStatement_bs.preparedExceute(DmdbPreparedStatement_bs.java:2243)
	at dm.jdbc.driver.DmdbPreparedStatement_bs.executeInner(DmdbPreparedStatement_bs.java:2345)
	at dm.jdbc.driver.DmdbPreparedStatement_bs.execute(DmdbPreparedStatement_bs.java:1506)
	at dm.jdbc.driver.DmdbPreparedStatement.do_execute(DmdbPreparedStatement.java:1904)
	at dm.jdbc.filter.FilterChain.PreparedStatement_execute(FilterChain.java:1471)
	at dm.jdbc.driver.DmdbPreparedStatement.execute(DmdbPreparedStatement.java:639)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor98.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy94.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.GeneratedMethodAccessor109.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy92.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 21 common frames omitted
2025-01-02 15:01:44.079 ERROR [pool-2-thread-1]o.s.scheduling.support.TaskUtils$LoggingErrorHandler.handleError:96 -Unexpected error occurred in scheduled task.
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLException: 字符串转换出错
### The error may exist in file [G:\yimaWorking\bdxt_shipMonitor_web\Server\springbootmybatisdmdb\target\classes\mapper\PeopleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: update ship.people set certificatestate = 2 where certificatetime < ? and certificatestate = 1;
### Cause: java.sql.SQLException: 字符串转换出错
; ]; 字符串转换出错; nested exception is java.sql.SQLException: 字符串转换出错
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:74)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy73.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:287)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:67)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:57)
	at com.sun.proxy.$Proxy79.UpdateCrewTimeState(Unknown Source)
	at com.bd.service.impl.PeopleImpl.UpdateCrewTimeState(PeopleImpl.java:185)
	at com.bd.thread.UpdateCrewInfoThread.UpdateCrewTimeState(UpdateCrewInfoThread.java:166)
	at com.bd.thread.UpdateCrewInfoThread.run(UpdateCrewInfoThread.java:38)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:65)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.sql.SQLException: 字符串转换出错
	at dm.jdbc.dbaccess.DBError.throwSQLException(DBError.java:44)
	at dm.jdbc.dbaccess.Request_Response.resp_checkErr(Request_Response.java:2570)
	at dm.jdbc.dbaccess.Request_Response.resp_execute(Request_Response.java:1107)
	at dm.jdbc.dbaccess.DmdbCSI.getDescAndResultSet(DmdbCSI.java:903)
	at dm.jdbc.dbaccess.DmdbCSI.executePrepared(DmdbCSI.java:943)
	at dm.jdbc.dbaccess.DmdbCSI.executePrepared(DmdbCSI.java:332)
	at dm.jdbc.driver.DmdbPreparedStatement_bs.preparedExceute(DmdbPreparedStatement_bs.java:2243)
	at dm.jdbc.driver.DmdbPreparedStatement_bs.executeInner(DmdbPreparedStatement_bs.java:2345)
	at dm.jdbc.driver.DmdbPreparedStatement_bs.execute(DmdbPreparedStatement_bs.java:1506)
	at dm.jdbc.driver.DmdbPreparedStatement.do_execute(DmdbPreparedStatement.java:1904)
	at dm.jdbc.filter.FilterChain.PreparedStatement_execute(FilterChain.java:1471)
	at dm.jdbc.driver.DmdbPreparedStatement.execute(DmdbPreparedStatement.java:639)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor93.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy94.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.GeneratedMethodAccessor104.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy92.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 21 common frames omitted
2025-01-02 15:02:29.322 ERROR [pool-2-thread-1]o.s.scheduling.support.TaskUtils$LoggingErrorHandler.handleError:96 -Unexpected error occurred in scheduled task.
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLException: 字符串转换出错
### The error may exist in file [G:\yimaWorking\bdxt_shipMonitor_web\Server\springbootmybatisdmdb\target\classes\mapper\PeopleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: update ship.people set certificatestate = 2 where certificatetime < ? and certificatestate = 1;
### Cause: java.sql.SQLException: 字符串转换出错
; ]; 字符串转换出错; nested exception is java.sql.SQLException: 字符串转换出错
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:74)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy73.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:287)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:67)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:57)
	at com.sun.proxy.$Proxy79.UpdateCrewTimeState(Unknown Source)
	at com.bd.service.impl.PeopleImpl.UpdateCrewTimeState(PeopleImpl.java:185)
	at com.bd.thread.UpdateCrewInfoThread.UpdateCrewTimeState(UpdateCrewInfoThread.java:166)
	at com.bd.thread.UpdateCrewInfoThread.run(UpdateCrewInfoThread.java:38)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:65)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.sql.SQLException: 字符串转换出错
	at dm.jdbc.dbaccess.DBError.throwSQLException(DBError.java:44)
	at dm.jdbc.dbaccess.Request_Response.resp_checkErr(Request_Response.java:2570)
	at dm.jdbc.dbaccess.Request_Response.resp_execute(Request_Response.java:1107)
	at dm.jdbc.dbaccess.DmdbCSI.getDescAndResultSet(DmdbCSI.java:903)
	at dm.jdbc.dbaccess.DmdbCSI.executePrepared(DmdbCSI.java:943)
	at dm.jdbc.dbaccess.DmdbCSI.executePrepared(DmdbCSI.java:332)
	at dm.jdbc.driver.DmdbPreparedStatement_bs.preparedExceute(DmdbPreparedStatement_bs.java:2243)
	at dm.jdbc.driver.DmdbPreparedStatement_bs.executeInner(DmdbPreparedStatement_bs.java:2345)
	at dm.jdbc.driver.DmdbPreparedStatement_bs.execute(DmdbPreparedStatement_bs.java:1506)
	at dm.jdbc.driver.DmdbPreparedStatement.do_execute(DmdbPreparedStatement.java:1904)
	at dm.jdbc.filter.FilterChain.PreparedStatement_execute(FilterChain.java:1471)
	at dm.jdbc.driver.DmdbPreparedStatement.execute(DmdbPreparedStatement.java:639)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy94.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy92.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 22 common frames omitted
