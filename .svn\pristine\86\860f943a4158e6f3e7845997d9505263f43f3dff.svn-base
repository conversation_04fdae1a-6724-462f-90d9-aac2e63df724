package com.bd.service.impl;
import com.bd.entity.*;
import com.bd.mapper.TyphoonMapper;
import com.bd.service.TyphoonService;
import com.bd.util.Utils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import com.bd.util.Utils;
@Service
public class TyphoonImpl implements TyphoonService {
    @Resource
    private TyphoonMapper typhoonMapper;
    @Override
    public List<TyphoonInfo> GetTyphoonInfo() {
        return typhoonMapper.GetTyphoonInfo(Utils.GetNowTimeString_year() + "-1-1");
    }
    @Override
    public List<Integer> GetTyphoonCountInfo() {
        return typhoonMapper.GetTyphoonCountInfo(Utils.GetNowTimeString_year() + "-1-1");
    }
    @Override
    public List<TyphoonInfo> GetTyphoonInfoByYear(int year) {
        return typhoonMapper.GetTyphoonInfoByYear(year);
    }
    @Override
    public List<Integer> GetTyphoonInfoByYearCount() {
        return typhoonMapper.GetTyphoonInfoByYearCount();
    }
    @Override
    public List<TyphoonInfo> GetTyphoonInfoById(int id) {
        return typhoonMapper.GetTyphoonInfoById(id);
    }
    @Override
    public List<TyphoonInfo> GetYBTyphoonInfoById(int id) {
        return typhoonMapper.GetYBTyphoonInfoById(id);
    }
    @Override
    public List<TyphoonInfo> GetTodayTyphoonInfo(String startTime, String endTime) {
        return typhoonMapper.GetTodayTyphoonInfo(startTime, endTime);
    }
    @Override
    public int GetTodayTyphoonInfoCount(String startTime, String endTime) {
        return typhoonMapper.GetTodayTyphoonInfoCount(startTime, endTime);
    }
    @Override
    public List<TyphoonInfo> GetYBQDById(int id) {
        return typhoonMapper.GetYBQDById(id);
    }
}
