<component name="libraryTable">
  <library name="Maven: org.junit.jupiter:junit-jupiter:5.10.1">
    <CLASSES>
      <root url="jar://D:/apache-maven-3.8.5/repository/org/junit/jupiter/junit-jupiter/5.10.1/junit-jupiter-5.10.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://D:/apache-maven-3.8.5/repository/org/junit/jupiter/junit-jupiter/5.10.1/junit-jupiter-5.10.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://D:/apache-maven-3.8.5/repository/org/junit/jupiter/junit-jupiter/5.10.1/junit-jupiter-5.10.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>