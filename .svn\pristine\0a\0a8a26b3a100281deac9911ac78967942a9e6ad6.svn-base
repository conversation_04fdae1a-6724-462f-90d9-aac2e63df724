
import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.Configurator;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.bd.entity.PortInfo;
import com.bd.mapper.CrewExamMapper;
import com.bd.mapper.Mapper1;
import com.bd.mapper.PortMapper;
import com.bd.mapper.ShipMapper;
import com.bd.service.PeopleService;
import com.bd.service.PortService;
import com.bd.service.ShipService;
import com.bd.service.ShipStaticInfoService;
import com.bd.thread.DailyTask;
import com.bd.thread.UdateCrewBx;
import org.apache.logging.log4j.LogManager;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ContextConfiguration(classes = MPTest.class)
@MapperScan("com.bd.mapper")
@ComponentScan({"com.bd.service","com.bd.thread"})
// @Rollback(value = false)
public class MPTest {
    @Resource
    Mapper1 mapper1;
    @Resource
    ApplicationContext context;
    @Resource
    PortService portService;
    @Resource
    PeopleService peopleService;
    @Resource
    ShipStaticInfoService shipStaticInfoService;
    @Resource
    ShipService shipService;
    @Resource
    PortMapper portMapper;
    @Resource
    UdateCrewBx udateCrewBx;
    @Resource
    CrewExamMapper crewExamMapper;
    @Resource
    DailyTask dailyTask;
    @Resource
    ShipMapper shipMapper;

    @Test
    public void test() {
        List<List<String>> data = new ArrayList<>();
        List<PortInfo> portInfos = portMapper.GetAllPortInfo();
        for (int i = 0; i < 5; i++) {
            DateTime date = DateUtil.date().offset(DateField.YEAR,-i);
            String startTime = DateUtil.format(DateUtil.beginOfYear(date), "yyyy-MM-dd HH:mm:ss");
            String endTime = DateUtil.format(DateUtil.endOfYear(date), "yyyy-MM-dd HH:mm:ss");
            // System.out.println(startTime);
            // System.out.println(endTime);
            for (PortInfo portInfo : portInfos) {
                int maxBerth = shipMapper.getMaxBerth(startTime, endTime, portInfo.getId());
                // Console.log(StrUtil.padAfter(portInfo.getPortName(),10," "),
                //         StrUtil.padAfter(String.valueOf(maxBerth),10," "),
                //         StrUtil.padAfter(String.valueOf(DateUtil.year(date)),10," "));
                data.add(Arrays.asList(portInfo.getPortName(),String.valueOf(maxBerth),String.valueOf(DateUtil.year(date))));
            }
            data.add(Collections.emptyList());
        }
        ExcelWriter writer = ExcelUtil.getWriter("C:\\Users\\<USER>\\Desktop\\dd.xlsx");
        writer.write(data,false);
        writer.close();
    }


}

