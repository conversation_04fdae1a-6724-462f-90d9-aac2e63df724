<template>
  <div class="fujin-dialog" :class="{ 'has-ships': nearbyShips.length > 0 }" :style="{
      left: dialogLeft + 'px',
      top: dialogTop + 'px',
      position: 'fixed',
      zIndex: 9999,
      transform: isMobile ? 'scale(0.6)' : 'scale(0.8)',
      transformOrigin: 'top left'
  }">
    <div class="dialog-header" @mousedown="onDragStart" @touchstart="onTouchStart">
      <div class="dialog-title">附近船舶</div>
      <span class="close-btn" @click.stop="closeDialog">×</span>
    </div>

    <div class="dialog-content" @wheel="preventContentScroll" @touchmove="preventContentScroll">
      <!-- 当前位置信息 -->
      <div class="location-section">
        <h3>当前位置</h3>
        <div class="location-info" v-if="currentLocation">
          <div class="location-item">
            <span class="label">经度：</span>
            <span class="value">{{ currentLocation.longitude.toFixed(6) }}°</span>
          </div>
          <div class="location-item">
            <span class="label">纬度：</span>
            <span class="value">{{ currentLocation.latitude.toFixed(6) }}°</span>
          </div>
          <div class="location-item">
            <span class="label">精度：</span>
            <span class="value">{{ currentLocation.accuracy ? currentLocation.accuracy.toFixed(0) + 'm' : '未知' }}</span>
          </div>
          <div class="location-item">
            <span class="label">获取时间：</span>
            <span class="value">{{ formatTime(currentLocation.timestamp) }}</span>
          </div>
        </div>
        <div class="location-placeholder" v-else>
          <p v-if="locationStatus === 'loading'">正在获取位置信息...</p>
          <p v-else-if="locationStatus === 'error'" class="error">{{ locationError }}</p>
          <p v-else>点击下方按钮获取当前位置</p>
        </div>
      </div>

      <!-- 搜索设置 -->
      <div class="search-settings">
        <div class="setting-group">
          <label>搜索半径：</label>
          <input
            type="number"
            v-model.number="searchRadius"
            min="1"
            max="100"
            class="radius-input"
          />
          <span class="unit-label">海里</span>
        </div>

      </div>

      <!-- 搜索状态提示 -->
      <div class="search-status" v-if="searchStatus === 'loading'">
        <p>正在搜索附近船舶...</p>
      </div>
      <div class="search-status" v-else-if="searchStatus === 'error'">
        <p class="error">搜索失败：{{ searchError }}</p>
      </div>
      <div class="search-status" v-else-if="searchStatus === 'empty'">
        <p>附近暂无船舶</p>
      </div>

      <!-- 附近船舶列表 -->
      <div class="nearby-ships-section" v-if="nearbyShips.length > 0" @wheel="onShipSectionWheel" @touchmove="onShipSectionTouchMove">
        <h3>附近船舶 ({{ nearbyShips.length }}艘)</h3>
        <div
          class="ships-list"
          @touchstart="onShipListTouchStart"
          @touchmove="onShipListTouchMove"
          @wheel="onShipListWheel"
        >
          <div
            class="ship-item"
            v-for="ship in nearbyShips"
            :key="ship.id"
            @click="selectShip(ship)"
          >
            <div class="ship-name">{{ ship.shipname || '未知船舶' }}</div>
            <div class="ship-info">
              <span class="ship-id">终端号：{{ ship.bdid || '-' }}</span>
              <span class="ship-distance">距离：{{ formatDistance(ship.distance) }}</span>
            </div>
            <div class="ship-position">
              位置：{{ ship.lon ? ship.lon.toFixed(4) : '-' }}°, {{ ship.lat ? ship.lat.toFixed(4) : '-' }}°
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 - 固定在底部 -->
    <div class="action-buttons-fixed">
      <button
        class="btn-primary"
        @click="getCurrentLocation"
        :disabled="locationStatus === 'loading'"
      >
        {{ locationStatus === 'loading' ? '获取中...' : '获取当前位置' }}
      </button>
      <button
        class="btn-secondary"
        @click="searchNearbyShips"
        :disabled="!currentLocation || searchStatus === 'loading'"
      >
        {{ searchStatus === 'loading' ? '搜索中...' : '搜索附近船舶' }}
      </button>
    </div>
  </div>
</template>

<script>
import global from './Global.vue'

export default {
  name: 'FujinDialog',
  data() {
    const isMobile = /Android|webOS|iPhone|iPod|BlackBerry|iPad|Mobile/i.test(navigator.userAgent);
    return {
      isMobile: isMobile,
      dialogLeft: isMobile ? 10 : 300,
      dialogTop: isMobile ? 60 : 100,
      dragging: false,
      dragOffsetX: 0,
      dragOffsetY: 0,
      touchStartY: 0, // 记录触摸开始位置

      // 位置相关
      currentLocation: null,
      locationStatus: 'idle', // idle, loading, success, error
      locationError: '',

      // 船舶搜索相关
      nearbyShips: [],
      searchStatus: 'idle', // idle, loading, success, error, empty
      searchError: '',
      searchRadius: 10, // 搜索半径
      distanceUnit: 'nm', // 距离单位：固定为海里
      showAllShips: false // 调试选项：显示所有船舶
    }
  },
  methods: {
    // 关闭对话框
    closeDialog() {
      this.$emit('close');
    },

    // 获取当前位置
    getCurrentLocation() {
      this.locationStatus = 'loading';
      this.locationError = '';

      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          // 成功回调
          (position) => {
            this.currentLocation = {
              longitude: position.coords.longitude,
              latitude: position.coords.latitude,
              accuracy: position.coords.accuracy,
              timestamp: position.timestamp
            };
            this.locationStatus = 'success';

            // 触发位置获取成功事件
            this.$emit('location-obtained', this.currentLocation);
          },
          // 错误回调
          (error) => {
            let errorMsg = '';
            switch(error.code) {
              case error.PERMISSION_DENIED:
                errorMsg = '用户拒绝了位置请求权限';
                break;
              case error.POSITION_UNAVAILABLE:
                errorMsg = '位置信息不可用';
                break;
              case error.TIMEOUT:
                errorMsg = '获取位置超时';
                break;
              default:
                errorMsg = '发生未知错误';
                break;
            }
            this.locationError = errorMsg;
            this.locationStatus = 'error';
          },
          // 选项
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000 // 1分钟内的缓存位置可用
          }
        );
      } else {
        this.locationError = '您的浏览器不支持地理定位';
        this.locationStatus = 'error';
      }
    },

    // 搜索附近船舶
    searchNearbyShips() {
      if (!this.currentLocation) {
        return;
      }

      this.searchStatus = 'loading';
      this.searchError = '';
      this.nearbyShips = [];

      // 调用API获取船舶位置数据 (扩大时间范围到6小时)
      $.get(global.IP + "/web/GetBDShipPosition?sec=21600", (data) => {
        if (data && data.length > 0) {
          // 计算距离并筛选附近船舶
          const nearbyShips = [];

          // 将搜索半径从海里转换为公里（用于统一计算）
          const radiusInKm = this.searchRadius * 1.852;

          // console.log('=== 附近船舶搜索调试信息 ===');
          // console.log('当前位置:', this.currentLocation);
          // console.log('当前位置详细:', `纬度: ${this.currentLocation.latitude}, 经度: ${this.currentLocation.longitude}`);
          // console.log('搜索半径(海里):', this.searchRadius);
          // console.log('搜索半径(公里):', radiusInKm);
          // console.log('总船舶数量:', data.length);

          // 显示前5艘船的位置信息
          // console.log('前5艘船舶位置:');
          data.slice(0, 5).forEach((ship, index) => {
            if (ship.lon && ship.lat) {
              // 转换坐标格式：从度*1000000格式转换为度格式
              let shipLat = ship.lat;
              let shipLon = ship.lon;

              // 确保是数字类型
              shipLat = Number(shipLat);
              shipLon = Number(shipLon);

              // 如果坐标值大于1000，说明是度*10000000的格式，需要转换
              if (Math.abs(shipLat) > 1000) {
                shipLat = shipLat / 10000000;
              }
              if (Math.abs(shipLon) > 1000) {
                shipLon = shipLon / 10000000;
              }

              const dist = this.calculateDistance(
                this.currentLocation.latitude,
                this.currentLocation.longitude,
                shipLat,
                shipLon
              );
              //console.log(`船舶${index + 1}: ${ship.shipname || '未知'} - 原始位置(${ship.lat}, ${ship.lon}) - 转换后位置(${shipLat.toFixed(6)}, ${shipLon.toFixed(6)}) - 距离: ${(dist/1.852).toFixed(2)}海里`);
            }
          });

          let validShipCount = 0;
          let allShipsMinDistance = Infinity;  // 所有船舶的最近距离
          let allShipsMaxDistance = 0;         // 所有船舶的最远距离
          let filteredMinDistance = Infinity;  // 筛选后船舶的最近距离
          let filteredMaxDistance = 0;         // 筛选后船舶的最远距离

          data.forEach(ship => {
            if (ship.lon && ship.lat) {
              validShipCount++;

              // 转换坐标格式：如果坐标值很大，说明是度*10000000的格式，需要除以10000000
              let shipLat = ship.lat;
              let shipLon = ship.lon;

              // 判断是否需要坐标转换（通常大于1000000的值需要除以10000000）
              if (Math.abs(shipLat) > 1000000) {
                shipLat = shipLat / 10000000;
              }
              if (Math.abs(shipLon) > 1000000) {
                shipLon = shipLon / 10000000;
              }

              const distanceInKm = this.calculateDistance(
                this.currentLocation.latitude,
                this.currentLocation.longitude,
                shipLat,
                shipLon
              );

              // 记录所有船舶的距离范围
              allShipsMinDistance = Math.min(allShipsMinDistance, distanceInKm);
              allShipsMaxDistance = Math.max(allShipsMaxDistance, distanceInKm);



              // 如果开启显示所有船舶，或者在搜索半径内
              if (this.showAllShips || distanceInKm <= radiusInKm) {
                // 转换为海里显示
                const displayDistance = distanceInKm / 1.852;

                // 记录筛选后船舶的距离范围
                filteredMinDistance = Math.min(filteredMinDistance, distanceInKm);
                filteredMaxDistance = Math.max(filteredMaxDistance, distanceInKm);

                nearbyShips.push({
                  ...ship,
                  lat: shipLat, // 使用转换后的坐标
                  lon: shipLon, // 使用转换后的坐标
                  distance: displayDistance.toFixed(2),
                  distanceInKm: distanceInKm // 保留公里数用于排序
                });
              }
            }
          });



          // 按实际距离排序（使用公里）
          nearbyShips.sort((a, b) => a.distanceInKm - b.distanceInKm);

          this.nearbyShips = nearbyShips;
          this.searchStatus = nearbyShips.length > 0 ? 'success' : 'empty';
        } else {
          this.searchStatus = 'empty';
        }
      }).fail((error) => {
        this.searchError = '获取船舶数据失败';
        this.searchStatus = 'error';
        //console.error('获取船舶数据失败:', error);
      });
    },

    // 计算两点间距离（单位：公里）
    calculateDistance(lat1, lon1, lat2, lon2) {
      const R = 6371; // 地球半径，单位：公里
      const dLat = this.deg2rad(lat2 - lat1);
      const dLon = this.deg2rad(lon2 - lon1);
      const a =
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
        Math.sin(dLon/2) * Math.sin(dLon/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      return R * c;
    },

    // 角度转弧度
    deg2rad(deg) {
      return deg * (Math.PI/180);
    },

    // 选择船舶
    selectShip(ship) {
      this.$emit('ship-selected', ship);
    },

    // 格式化时间
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleString('zh-CN');
    },

    // 格式化距离显示
    formatDistance(distance) {
      return `${distance}海里`;
    },

    // 拖拽相关方法
    onDragStart(event) {
      this.dragging = true;
      const clientX = event.type === 'mousedown' ? event.clientX : event.touches[0].clientX;
      const clientY = event.type === 'mousedown' ? event.clientY : event.touches[0].clientY;

      this.dragOffsetX = clientX - this.dialogLeft;
      this.dragOffsetY = clientY - this.dialogTop;

      if (event.type === 'mousedown') {
        document.addEventListener('mousemove', this.onDragMove);
        document.addEventListener('mouseup', this.onDragEnd);
      } else {
        document.addEventListener('touchmove', this.onDragMove);
        document.addEventListener('touchend', this.onDragEnd);
      }
    },

    onTouchStart(event) {
      this.onDragStart(event);
    },

    onDragMove(event) {
      if (!this.dragging) return;

      const clientX = event.type === 'mousemove' ? event.clientX : event.touches[0].clientX;
      const clientY = event.type === 'mousemove' ? event.clientY : event.touches[0].clientY;

      this.dialogLeft = clientX - this.dragOffsetX;
      this.dialogTop = clientY - this.dragOffsetY;
    },

    onDragEnd() {
      this.dragging = false;
      document.removeEventListener('mousemove', this.onDragMove);
      document.removeEventListener('mouseup', this.onDragEnd);
      document.removeEventListener('touchmove', this.onDragMove);
      document.removeEventListener('touchend', this.onDragEnd);
    },

    // 船舶列表滚动事件处理
    onShipListTouchStart(event) {
      // 记录触摸开始位置
      this.touchStartY = event.touches[0].clientY;
      // 阻止事件冒泡到父级的 preventContentScroll
      event.stopPropagation();
    },

    onShipListTouchMove(event) {
      const shipsList = event.currentTarget;
      const touchY = event.touches[0].clientY;
      const deltaY = this.touchStartY - touchY;

      // 检查是否可以滚动
      const canScrollUp = shipsList.scrollTop > 0;
      const canScrollDown = shipsList.scrollTop < (shipsList.scrollHeight - shipsList.clientHeight);

      // 始终阻止事件冒泡到父级的 preventContentScroll
      event.stopPropagation();

      // 如果列表不能滚动，阻止默认行为
      if ((deltaY > 0 && !canScrollDown) || (deltaY < 0 && !canScrollUp)) {
        event.preventDefault();
      }
    },

    onShipListWheel(event) {
      const shipsList = event.currentTarget;
      const deltaY = event.deltaY;

      // 检查是否可以滚动
      const canScrollUp = shipsList.scrollTop > 0;
      const canScrollDown = shipsList.scrollTop < (shipsList.scrollHeight - shipsList.clientHeight);

      // 始终阻止事件冒泡到父级的 preventContentScroll
      event.stopPropagation();

      // 如果列表不能滚动，阻止默认行为
      if ((deltaY > 0 && !canScrollDown) || (deltaY < 0 && !canScrollUp)) {
        event.preventDefault();
      }
    },

    // 防止对话框内容区域（除船舶列表外）的滚动事件
    preventContentScroll(event) {
      // 阻止滚动事件冒泡，防止影响页面滚动
      event.stopPropagation();
      event.preventDefault();
    },

    // 船舶列表区域（包括标题）的滚动事件处理
    onShipSectionWheel(event) {
      // 阻止事件冒泡到父级的 preventContentScroll
      event.stopPropagation();

      // 如果点击的是标题或其他非列表区域，阻止默认行为
      if (event.target.closest('.ships-list') === null) {
        event.preventDefault();
      }
    },

    onShipSectionTouchMove(event) {
      // 阻止事件冒泡到父级的 preventContentScroll
      event.stopPropagation();

      // 如果点击的是标题或其他非列表区域，阻止默认行为
      if (event.target.closest('.ships-list') === null) {
        event.preventDefault();
      }
    }
  }
}
</script>

<style scoped>
.fujin-dialog {
  background: white;
  border-radius: 8px;
  width: 420px;
  min-height: auto; /* 自动高度 */
  max-height: 90vh; /* 最大高度不超过视口高度的90% */
  font-size: 18px; /* 进一步增大基础字体 */
  color: #333;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: fixed;
  z-index: 9999;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  border: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内容溢出 */
}

/* 当有船舶列表时，使用固定高度 */
.fujin-dialog.has-ships {
  height: 700px;
}

.dialog-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between; /* 两端对齐 */
  padding: 15px 15px 15px 20px; /* 左侧内边距更大 */
  border-bottom: 1px solid #e0e0e0;
  background: #1890ff;
  border-radius: 8px 8px 0 0;
  cursor: move;
}

.dialog-title {
  font-size: 24px; /* 进一步增大字体 */
  font-weight: bold;
  color: white;
  margin: 0; /* 移除默认边距 */
}

.close-btn {
  background: none;
  border: none;
  font-size: 32px; /* 进一步增大字体 */
  color: white; /* 直接使用白色 */
  cursor: pointer;
  padding: 0;
  width: 36px; /* 进一步增大尺寸 */
  height: 36px; /* 进一步增大尺寸 */
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0; /* 防止被压缩 */
}

.close-btn:hover {
  color: rgba(255, 255, 255, 0.8); /* 悬停时稍微透明 */
}

.dialog-content {
  padding: 15px;
  overflow-y: auto;
  overflow-x: hidden;
  flex: 0 1 auto; /* 允许内容区域根据内容自动调整大小 */
  max-height: calc(90vh - 120px); /* 预留标题栏和按钮区域的空间 */
  -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
  overscroll-behavior: contain; /* 防止滚动链 */
  /* 防止内容区域的滚动事件冒泡到父级 */
  touch-action: pan-y;
}

/* 当有船舶列表时，内容区域使用flex布局 */
.fujin-dialog.has-ships .dialog-content {
  flex: 1;
  min-height: 0;
}

.location-section {
  margin-bottom: 20px;
}

.location-section h3 {
  margin: 0 0 10px 0;
  font-size: 20px; /* 进一步增大标题字体 */
  color: #333;
  font-weight: bold;
}

.location-info {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.location-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.location-item:last-child {
  margin-bottom: 0;
}

.location-item .label {
  color: #666;
  font-weight: 500;
}

.location-item .value {
  color: #333;
  font-weight: bold;
}

.location-placeholder {
  text-align: center;
  padding: 20px;
  color: #666;
}

.location-placeholder .error {
  color: #dc3545;
}

.search-settings {
  margin-bottom: 20px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.setting-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-group label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  font-size: 18px; /* 进一步增大字体 */
}

.radius-input {
  width: 80px;
  padding: 10px 12px; /* 进一步增大内边距 */
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 18px; /* 进一步增大字体 */
  text-align: center;
}

.radius-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.unit-label {
  padding: 10px 12px; /* 进一步增大内边距 */
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  font-size: 18px; /* 进一步增大字体 */
}

.debug-option {
  margin-top: 10px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 6px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.action-buttons-fixed {
  display: flex;
  gap: 10px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 0 0 8px 8px;
  flex-shrink: 0; /* 防止按钮区域被压缩 */
}

.btn-primary, .btn-secondary {
  flex: 1;
  padding: 14px 20px; /* 进一步增大内边距 */
  border: none;
  border-radius: 6px;
  font-size: 18px; /* 进一步增大字体 */
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background: #1890ff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #40a9ff;
}

.btn-primary:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
}

.btn-secondary {
  background: #52c41a;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #73d13d;
}

.btn-secondary:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
}

.nearby-ships-section h3 {
  margin: 0 0 12px 0;
  font-size: 20px; /* 进一步增大标题字体 */
  color: #333;
  font-weight: bold;
}

.ships-list {
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
  overscroll-behavior: contain; /* 防止滚动链 */
}

.ship-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.ship-item:hover {
  background: #e3f2fd;
  border-color: #1890ff;
}

.ship-item:last-child {
  margin-bottom: 0;
}

.ship-name {
  font-weight: bold;
  color: #333;
  margin-bottom: 6px;
  font-size: 18px; /* 进一步增大船舶名称字体 */
}

.ship-info {
  display: flex;
  justify-content: space-between;
  font-size: 16px; /* 进一步增大字体 */
  color: #666;
  margin-bottom: 4px;
}

.ship-position {
  font-size: 16px; /* 进一步增大字体 */
  color: #666;
}

.search-status {
  text-align: center;
  padding: 20px;
  color: #666;
  font-size: 18px; /* 进一步增大字体 */
}

.search-status .error {
  color: #dc3545;
}
</style>