package com.bd.thread;

import com.bd.entity.*;
import com.bd.service.AreaService;
import com.bd.service.PortService;
import com.bd.service.ShipService;
import com.bd.util.M_POINT;
import com.bd.util.Utils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static java.lang.Math.*;
import static java.lang.Math.sqrt;

public class DataReadThread implements Runnable{

    private ShipService shipService;

    private PortService portService;

    private AreaService areaService;

    private List<Ship> gShipList = new ArrayList<>();
    private List<ShipDynamicInfo> gShipDynamicInfoLisht = new ArrayList<>();
    private List<ShipDynamicInfo> gShipDynamicInfoLisht_ws = new ArrayList<>();
    private List<Port> portList = new ArrayList<>();
    private List<PortInfo> portInfoList = new ArrayList<>();
    private List<Port_InPortShipCount> inPortShipCountList = new ArrayList<>();
    private List<AreaInfo> areaInfos = new ArrayList<>();
    private List<FishArea> fishAreaInfos = new ArrayList<>();
    List<Ship_Voyage> ship_voyagesList = new ArrayList<>();

    List<ShipDynamicInfo> typhoonAlarmshipList = new ArrayList<>();

    AreaInfo shanghai;

    public DataReadThread(ShipService shipService, PortService portService, AreaService areaService){
        this.shipService = shipService;
        this.portService = portService;
        this.areaService = areaService;
    }

    public List<Ship> getShipList(){
        return shipService.GetBDShipPosition((int) Utils.GetNowTimelong() - 30);
    }

    public List<ShipDynamicInfo> getShipDynamicList(){
        return shipService.GetBDShipPosition_ronghe_strTime_thread(Utils.GetNowTimeString(50));
    }

    public List<ShipDynamicInfo> getShipDynamicList_ws(){
        return shipService.GetBDShipPosition_ronghe_strTime_thread_ws(Utils.GetNowTimeString(120));
    }

    public List<PortInfo> getPortList(){
        return portService.GetAllPortInfo();
    }

    public List<AreaInfo> GetAreaInfo(){
        return areaService.GetAreaInfo();
    }

    public List<FishArea> GetFishAreaInfo(){
        return areaService.GetFishAreaInfo();
    }

    @Override
    public void run() {
        initPortList();
        initAreaList();
        initFishAreaList();
        while (true){
            try {
                updateAreaList();
                updatePosition();
                updatePosition_ws();
                // inOrOutPortRecord();
                lonAlarmJudgment();
                inOrOutPortRecord_ws();
                wsShipFuxiuRecord();
                deleteShip();
                deleteShip_ws();
                checkAreaAlarm_gaowei();
                checkAreaAlarm_teshu();
                checkAreaAlarm_zidingyi();
                checkAreaAlarm_gaowei_w();
                checkAreaAlarm_teshu_w();
                checkAreaAlarm_zidingyi_w();
                areaAlarm_shewai_han();
                areaAlarm_shewai_ri();
                areaAlarm_shewai_yue();
                updateInportShip();
                updateFishAreaId();
                //typhoonAlarm();

                //inOrOutPortVoyage();
                Thread.sleep(30*1000);

            }catch (Exception e){
                System.out.println(e);
            }
        }
    }

//////////////进出港判断/////////////////

    public void updatePosition(){
        //List<Ship> shipList = getShipList();
        List<ShipDynamicInfo> shipList = getShipDynamicList();
        System.out.println("船舶数量：" + gShipDynamicInfoLisht.size());
        System.out.println("本次更新船舶数量：" + shipList.size());
        for(ShipDynamicInfo ship:shipList){
            if (gShipDynamicInfoLisht.size() < 1){
                gShipDynamicInfoLisht.add(ship);
                continue;
            }
            boolean bUpdateOrAdd = false;
            for (ShipDynamicInfo ships: gShipDynamicInfoLisht){
                if(ships.getStaticShipId() == ship.getStaticShipId()){
                    ships.setLON(ship.getLON());
                    ships.setLAT(ship.getLAT());
                    ships.setSPEED(ship.getSPEED());
                    ships.setReportTime(ship.getReportTime());
                    ships.setLoadTime(ship.getLoadTime());
                    ships.setINPORTID(ship.getINPORTID());
                    ships.setINPORTSTATE(ship.getINPORTSTATE());
                    ships.setBWhiteFuXiu(ship.getBWhiteFuXiu());
                    bUpdateOrAdd = true;
                    break;
                }
            }
            if (!bUpdateOrAdd){
                gShipDynamicInfoLisht.add(ship);
            }
        }
    }

    public void updatePosition_ws(){
        List<ShipDynamicInfo> shipList = getShipDynamicList_ws();
        System.out.println("外省船舶数量：" + gShipDynamicInfoLisht_ws.size());
        System.out.println("本次更新外省船舶数量：" + shipList.size());
        for(ShipDynamicInfo ship:shipList){
            if(!Utils.IsPointInPolygon(new M_POINT(ship.getLON(), ship.getLAT()), shanghai.getPOSCOUNT(), shanghai.getPoints())){
                shipService.updateShipInShanghaiState(ship, 0);
                continue;
            }
            if (gShipDynamicInfoLisht_ws.size() < 1){
                gShipDynamicInfoLisht_ws.add(ship);
                continue;
            }
            boolean bUpdateOrAdd = false;
            for (ShipDynamicInfo ships: gShipDynamicInfoLisht_ws){
                if(ships.getId() == ship.getId()){
                    ships.setLON(ship.getLON());
                    ships.setLAT(ship.getLAT());
                    ships.setReportTime(ship.getReportTime());
                    ships.setLoadTime(ship.getLoadTime());
                    ships.setINPORTID(ship.getINPORTID());
                    ships.setINPORTSTATE(ship.getINPORTSTATE());
                    ships.setBWhiteFuXiu(ship.getBWhiteFuXiu());
                    bUpdateOrAdd = true;
                    break;
                }
            }
            if (!bUpdateOrAdd){
                gShipDynamicInfoLisht_ws.add(ship);
            }
        }
    }

    public void initPortList(){
        portInfoList = portService.GetAllPortInfo();
        for (PortInfo port: portInfoList) {
            port.pointsByStr();
            port.pointsByStr_tmp();
/*            Port_InPortShipCount port_inPortShipCount = new Port_InPortShipCount();
            port_inPortShipCount.setPortID(port.getId());
            port_inPortShipCount.setPortName(port.getPortName());
            inPortShipCountList.add(port_inPortShipCount);*/
        }

    }

    public void initAreaList(){
        areaInfos = GetAreaInfo();
        for(AreaInfo areaInfo:areaInfos){
            areaInfo.pointsByStr();
        }
    }

    public void initFishAreaList(){
        fishAreaInfos = GetFishAreaInfo();
        for(FishArea areaInfo:fishAreaInfos){
            areaInfo.pointsByStr();
        }
    }

    // 这个目前是以进出港口为判断依据。修改为（新写方法）内容qq发你了（不要进行真实数据代码测试）
    /*
    *
    * */
    public void inOrOutPortRecord(){
        for (ShipDynamicInfo ship: gShipDynamicInfoLisht){
            M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());

            boolean bIn = false;
            boolean bIn_tmp = false;
            boolean isRecord = false;

            for (PortInfo port: portInfoList){
                isRecord = false;
                bIn_tmp = false;
                List<M_POINT> points = port.getPoints();
                List<M_POINT> points_tmp = port.getPoints_tmp();
                int poCount = port.getPointCount();
                bIn = Utils.IsPointInPolygon(shipPo, poCount, points);
                if(points_tmp.size() > 1){
                    isRecord = true;
                    bIn_tmp = Utils.IsPointInPolygon(shipPo, 4, points_tmp);
                }

                if(bIn){
                    //在港
                    ship.setINPORTID(port.getId());
                    if(ship.getINPORTSTATE() == 0){
                        //在港状态变化 不在港 > 在港
                        ship.setINPORTSTATE(1);
                        System.out.println("in:" + ship.getSHIPNAME() + "-" + ship.getINPORTSTATE() + "-" + port.getPortName());
                        shipService.UpdateShipInPortState(port.getId(), ship.getINPORTSTATE(), ship.getId());
                        if(isRecord)
                            shipService.InsertOutInPortRecord(ship);
                    }
                    break;
                }

                if (bIn)
                    ship.setBAlarm(0);

            }

            if(ship.getINPORTSTATE() == 1 && ship.getSPEED() > 2 && !bIn){
                //在港状态变化 在港 > 不在港
                for (PortInfo port: portInfoList)
                {
                    if(port.getId() == ship.getINPORTID())
                    {
                        List<M_POINT> points_tmp = port.getPoints_tmp();
                        if(points_tmp.size() > 1)
                        {
                            boolean bIn_tmp_out = Utils.IsPointInPolygon(shipPo, 4, points_tmp);
                            if(!bIn_tmp_out)
                            {
                                ship.setINPORTSTATE(0);
                                System.out.println("out:" + ship.getSHIPNAME() + "-" + ship.getINPORTSTATE() + "-" + ship.getINPORTID());
                                shipService.UpdateShipInPortState(0, ship.getINPORTSTATE(), ship.getId());
                                shipService.InsertOutInPortRecord(ship);
                            }
                        }
                        else
                        {
                            ship.setINPORTSTATE(0);
                            System.out.println("out:" + ship.getSHIPNAME() + "-" + ship.getINPORTSTATE() + "-" + ship.getINPORTID());
                            shipService.UpdateShipInPortState(0, ship.getINPORTSTATE(), ship.getId());
                        }
                    }

                }

                if(!bIn && ship.getBAlarm() == 0 && ship.getBWhiteFuXiu() == 0 && ship.getSPEED() > 0){
                    //本市渔船港外报警
                    ship.setBAlarm(1);
                    LocalDate today = LocalDate.now();
                    if (today.isAfter(LocalDate.of(today.getYear(), 5, 1)) && today.isBefore(LocalDate.of(today.getYear(), 8, 1))) {
                        portService.InsertOneAlarmInfo(ship, 301, "上海渔船伏休期港外报警", 0, "");
                    }
                }
            }


        }
    }
    public void lonAlarmJudgment(){
        for (ShipDynamicInfo ship: gShipDynamicInfoLisht){
            M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());

            boolean bIn = false;
            boolean bIn_tmp = false;
            boolean isRecord = false;
            boolean isCorrection = false;//是否整改默认未整改
            CheckRecord checkRecord = shipService.getCheckRecordByShipName(ship.getSHIPNAME());
            if(checkRecord != null && (checkRecord.getHANDL_OPINIONS() == null || "测试，请忽略".equals(checkRecord.getHANDL_OPINIONS()))){
                isCorrection = true;
            }
            for (PortInfo port: portInfoList){
                isRecord = false;
                bIn_tmp = false;
                List<M_POINT> points = port.getPoints();
                List<M_POINT> points_tmp = port.getPoints_tmp();
                int poCount = port.getPointCount();
                bIn = Utils.IsPointInPolygon(shipPo, poCount, points);
                if(points_tmp.size() > 1){
                    isRecord = true;
                    bIn_tmp = Utils.IsPointInPolygon(shipPo, 4, points_tmp);
                }

                if(bIn){
                    //在港
                    ship.setINPORTID(port.getId());
                    if(ship.getINPORTSTATE() == 0){
                        //在港状态变化 不在港 > 在港
                        ship.setINPORTSTATE(1);
                        System.out.println("in:" + ship.getSHIPNAME() + "-" + ship.getINPORTSTATE() + "-" + port.getPortName());
                        shipService.UpdateShipInPortState(port.getId(), ship.getINPORTSTATE(), ship.getId());
                        if(isRecord)
                            shipService.InsertOutInPortRecord(ship);
                    }
                    break;
                }

                if (bIn)
                    ship.setBAlarm(0);

            }

            if(ship.getINPORTSTATE() == 1 && ship.getSPEED() > 2 && !bIn){
                //在港状态变化 在港 > 不在港
                for (PortInfo port: portInfoList){
                    if(port.getId() == ship.getINPORTID()){
                        List<M_POINT> points_tmp = port.getPoints_tmp();
                        if(points_tmp.size() > 1){
                            boolean bIn_tmp_out = Utils.IsPointInPolygon(shipPo, 4, points_tmp);
                            if(!bIn_tmp_out){
                                ship.setINPORTSTATE(0);
                                System.out.println("out:" + ship.getSHIPNAME() + "-" + ship.getINPORTSTATE() + "-" + ship.getINPORTID());
                                shipService.UpdateShipInPortState(0, ship.getINPORTSTATE(), ship.getId());
                                shipService.InsertOutInPortRecord(ship);
                            }
                        }else{
                            //ship.setINPORTSTATE(0);
                            //System.out.println("out:" + ship.getSHIPNAME() + "-" + ship.getINPORTSTATE() + "-" + ship.getINPORTID());
                            //shipService.UpdateShipInPortState(0, ship.getINPORTSTATE(), ship.getId());
                        }
                    }

                }

                if((!bIn && ship.getBAlarm() == 0 && ship.getBWhiteFuXiu() == 0 && ship.getSPEED() > 0 && shipPo.x>1220000000)||isCorrection==false){
                    //本市渔船港外报警(已经改为超过东经122度且未整改的港外预警)
                    ship.setBAlarm(1);
                    LocalDate today = LocalDate.now();
                    if (today.isAfter(LocalDate.of(today.getYear(), 5, 1)) && today.isBefore(LocalDate.of(today.getYear(), 8, 1))) {
                        portService.InsertOneAlarmInfo(ship, 301, "上海渔船伏休期港外报警", 0, "");
                    }
                }
            }


        }
    }


    public void inOrOutPortRecord_ws(){
        for (ShipDynamicInfo ship: gShipDynamicInfoLisht_ws){
            M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());

            boolean bIn = false;

            for (PortInfo port: portInfoList){

                List<M_POINT> points = port.getPoints();
                int poCount = port.getPointCount();
                bIn = Utils.IsPointInPolygon(shipPo, poCount, points);
                if(bIn){
                    //在港
                    ship.setINPORTID(port.getId());
                    if(ship.getINPORTSTATE() == 0){
                        //在港状态变化 不在港 > 在港
                        ship.setINPORTSTATE(1);
                        System.out.println(ship.getSHIPNAME() + "-" + ship.getINPORTSTATE() + "-" + port.getPortName());
                        shipService.UpdateShipInPortState(port.getId(), ship.getINPORTSTATE(), ship.getId());
                        //shipService.InsertOutInPortRecord(ship);
                    }
                    break;
                }
            }

            if(ship.getINPORTSTATE() == 1 && !bIn){
                //在港状态变化 在港 > 不在港
                ship.setINPORTSTATE(0);
                System.out.println(ship.getSHIPNAME() + "-" + ship.getINPORTSTATE() + "-" + ship.getINPORTID());
                shipService.UpdateShipInPortState(0, ship.getINPORTSTATE(), ship.getId());
                //shipService.InsertOutInPortRecord(ship);
            }

        }

    }

    public void inOrOutPortVoyage(){

        for (ShipDynamicInfo ship: gShipDynamicInfoLisht){
            M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());

            boolean bIn = false;

            for (PortInfo port: portInfoList){

                List<M_POINT> points = port.getPoints();
                int poCount = port.getPointCount();
                bIn = Utils.IsPointInPolygon(shipPo, poCount, points);
                if(bIn){
                    //在港
                    ship.setINPORTID(port.getId());
                    if(ship.getINPORTSTATE() == 0){
                        //todo 进港
                        ship.setINPORTSTATE(1);
                        System.out.println(ship.getSHIPNAME() + "-" + ship.getINPORTSTATE() + "-" + port.getPortName());
                        //shipService.UpdateShipInPortState(port.getId(), ship.getINPORTSTATE(), ship.getId());
                        //shipService.InsertOutInPortRecord(ship);

                        //todo 记录航程-进港
                        for (Ship_Voyage voyage :ship_voyagesList){
                            if(voyage.getSTATICSHIPID() == ship.getStaticShipId()){
                                voyage.setENDPORT(port.getId());
                                voyage.setENDTIME(ship.getLoadTime());
                                List<ShipTrack> tracks = shipService.GetShipHistoryTrackById(ship.getStaticShipId(),
                                        Utils.GetLongTimeForString(voyage.getSTARTTIME()),
                                        Utils.GetLongTimeForString(voyage.getENDTIME()), 0,0,0);

                                voyage.setDIS(Utils.GetDisByTracks(tracks));
                                voyage.setWORKTIME(Utils.GetHourByTwoTime(voyage.getSTARTTIME(), voyage.getENDTIME()));
                                shipService.InsertShipVoyage(voyage);
                                ship_voyagesList.remove(voyage);
                                System.out.println("记录航程-进港 : " + ship.getSHIPNAME());
                            }
                        }
                    }
                    break;
                }
            }

            if(ship.getINPORTSTATE() == 1 && !bIn){
                //todo 出港
                ship.setINPORTSTATE(0);
                System.out.println(ship.getSHIPNAME() + "-" + ship.getINPORTSTATE() + "-" + ship.getINPORTID());
                //shipService.UpdateShipInPortState(0, ship.getINPORTSTATE(), ship.getId());
                //shipService.InsertOutInPortRecord(ship);

                //todo 记录航程-出港
                Ship_Voyage voyage = new Ship_Voyage();
                voyage.setSTATICSHIPID(ship.getStaticShipId());
                voyage.setSHIPNAME(ship.getSHIPNAME());
                voyage.setSTARTPORT(ship.getINPORTID());
                voyage.setSTARTTIME(ship.getLoadTime());
                voyage.setWORKDATE(ship.getLoadTime());
                ship_voyagesList.add(voyage);
                System.out.println("记录航程-出港 : " + ship.getSHIPNAME());

            }
        }

    }

    public void deleteShip(){
        for(int i = 0; i < gShipDynamicInfoLisht.size(); i++){
            if (Utils.GetLongTimeForString(gShipDynamicInfoLisht.get(i).getBdTime()) < (Utils.GetNowTimelong() - 24*60*60)){
                //todo 异常关机预警
                if(gShipDynamicInfoLisht.get(i).getINPORTSTATE() == 0){
                    portService.InsertOneAlarmInfo(gShipDynamicInfoLisht.get(i), 310, "出海船舶异常离线", 0, "");
                }
                gShipDynamicInfoLisht.remove(i);
                i --;
            }
        }
        //System.out.println("船舶信号消失 " + count + " 个");
    }

    public void deleteShip_ws(){
        for(int i = 0; i < gShipDynamicInfoLisht_ws.size(); i++){
            if (gShipDynamicInfoLisht_ws.get(i).getReportTime() < (Utils.GetNowTimelong() - 1*60*60)){
                if(gShipDynamicInfoLisht_ws.get(i).getINPORTSTATE() == 0){
                    shipService.updateShipInShanghaiState(gShipDynamicInfoLisht_ws.get(i), 0);
                }
                gShipDynamicInfoLisht_ws.remove(i);
            }
        }
    }

    public void wsShipFuxiuRecord(){
        for (ShipDynamicInfo ship: gShipDynamicInfoLisht_ws){
            M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());
            //int state_ws = 0;
            for (AreaInfo area:areaInfos){
                if(area.getNAME().equals("上海海域")){
                    List<M_POINT> points = area.getPoints();
                    int poCount = area.getPOSCOUNT();
                    boolean bInFuxiuArea = Utils.IsPointInPolygon(shipPo, poCount, points);
                    if (bInFuxiuArea){
                        //todo 不能每次循环都报警，进入区域的外省渔船
                        if (ship.getBAlarm() == 0) {
                            ship.setBAlarm(1);
                            LocalDate today = LocalDate.now();
                            if (today.isAfter(LocalDate.of(today.getYear(), 5, 1)) && today.isBefore(LocalDate.of(today.getYear(), 8, 1))) {
                                int alarmCount = portService.GetTodayFuxiuAlarmByShipName(ship.getSHIPNAME());
                                if(alarmCount < 1)
                                    portService.InsertOneAlarmInfo(ship, 301, "外省渔船进入上海海域预警", 0, "");
                            }
                            shipService.updateShipInShanghaiState(ship, 1);
                        }

                    }else{
                        ship.setBAlarm(0);
                    }
                }
                if(area.getNAME().equals("168169")){
                    List<M_POINT> points = area.getPoints();
                    int poCount = area.getPOSCOUNT();
                    boolean bInFuxiuArea = Utils.IsPointInPolygon(shipPo, poCount, points);
                    if (bInFuxiuArea){
                        //todo 不能每次循环都报警，进入区域的外省渔船
                        if (ship.getBAlarm3() == 0) {
                            ship.setBAlarm3(1);
                            shipService.updateShipInShanghaiState(ship, 3);
                        }

                    }else{
                        ship.setBAlarm3(0);
                    }
                }
                if(area.getNAME().equals("长江口禁捕管理区")){
                    //System.out.println(area.getNAME());
                    List<M_POINT> points = area.getPoints();
                    int poCount = area.getPOSCOUNT();
                    boolean bInFuxiuArea = Utils.IsPointInPolygon(shipPo, poCount, points);
                    if (bInFuxiuArea){
                        //todo 不能每次循环都报警，进入区域的外省渔船
                        if (ship.getBAlarm2() == 0) {
                            ship.setBAlarm2(1);
                            shipService.updateShipInShanghaiState(ship, 2);
                        }
                    }else{
                        ship.setBAlarm2(0);
                    }
                }

            }
        }
    }

    public void updateAreaList(){
        areaInfos = GetAreaInfo();
        for(AreaInfo areaInfo:areaInfos){
            areaInfo.pointsByStr();
            if(areaInfo.getNAME().equals("上海海域")){
                shanghai = areaInfo;
            }
        }
    }

    public void checkAreaAlarm_gaowei(){

        for (AreaInfo area:areaInfos){
            if(area.getSTARTTIME() == null || area.getENDTIME() == null) continue;
            //报警规则
            if(area.getROUTETYPE() == 0) continue;
            if(area.getTYPE() != 2) continue;
            //时间限制
            if(Utils.CompareTime(Utils.GetNowTimeString(), area.getSTARTTIME())
                    && !Utils.CompareTime(Utils.GetNowTimeString(), area.getENDTIME())){

                for (ShipDynamicInfo ship: gShipDynamicInfoLisht){
                    M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());

                    //速度限制
                    if(area.getBSPEED() == 1 && ship.getSPEED() < area.getSPEED()) {
                        continue;
                    }else if(area.getBSPEED() == 2 && ship.getSPEED() > area.getSPEED()){
                        continue;
                    }

                    //System.out.println("check:"+area.getNAME()+"---shipName:"+ship.getSHIPNAME());

                    boolean bInArea = false;
                    if(ship.getBOutside() == 0){
                        List<M_POINT> points = area.getPoints();
                        int poCount = area.getPOSCOUNT();
                        bInArea = Utils.IsPointInPolygon(shipPo, poCount, points);
                    }
                    if (bInArea) {
                        if(!ship.getAlarmList_gaowei().contains(area.getID())){
                            ship.getAlarmList_gaowei().add(area.getID());
                            if(area.getROUTETYPE() == 1 || area.getROUTETYPE() == 3){
                                portService.InsertOneAlarmInfo(ship, 302,
                                        "渔船驶入:" + area.getNAME() + ",区域类型:高危区,船速" + ship.getSPEED() + "节",
                                        area.getUSERID(), area.getNAME());
                            }
                        }
                    }
                    else {
                        for(int i = ship.getAlarmList_gaowei().size() - 1; i >= 0; i--){
                            if(ship.getAlarmList_gaowei().get(i) == area.getID()){
                                ship.getAlarmList_gaowei().remove(i);
                                if(area.getROUTETYPE() == 2 || area.getROUTETYPE() == 3){
                                    portService.InsertOneAlarmInfo(ship, 302,
                                            "渔船驶离:" + area.getNAME() + ",区域类型:高危区,船速" + ship.getSPEED() + "节",
                                            area.getUSERID(), area.getNAME());
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public void checkAreaAlarm_teshu(){

        for (AreaInfo area:areaInfos){
            if(area.getSTARTTIME() == null || area.getENDTIME() == null) continue;
            //报警规则
            if(area.getROUTETYPE() == 0) continue;
            if(area.getTYPE() != 3) continue;
            //时间限制
            if(Utils.CompareTime(Utils.GetNowTimeString(), area.getSTARTTIME())
                    && !Utils.CompareTime(Utils.GetNowTimeString(), area.getENDTIME())){

                for (ShipDynamicInfo ship: gShipDynamicInfoLisht){
                    M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());

                    //速度限制
                    if(area.getBSPEED() == 1 && ship.getSPEED() < area.getSPEED()) {
                        continue;
                    }else if(area.getBSPEED() == 2 && ship.getSPEED() > area.getSPEED()){
                        continue;
                    }

                    //System.out.println("check:"+area.getNAME()+"---shipName:"+ship.getSHIPNAME());

                    boolean bInArea = false;
                    if(ship.getBOutside() == 0){
                        List<M_POINT> points = area.getPoints();
                        int poCount = area.getPOSCOUNT();
                        bInArea = Utils.IsPointInPolygon(shipPo, poCount, points);
                    }
                    if (bInArea) {
                        if(!ship.getAlarmList_teshu().contains(area.getID())){
                            ship.getAlarmList_teshu().add(area.getID());
                            if(area.getROUTETYPE() == 1 || area.getROUTETYPE() == 3){
                                portService.InsertOneAlarmInfo(ship, 302,
                                        "渔船驶入:" + area.getNAME() + ",区域类型:特殊区,船速" + ship.getSPEED() + "节",
                                        area.getUSERID(), area.getNAME());
                            }
                        }
                    }
                    else {
                        for(int i = ship.getAlarmList_teshu().size() - 1; i >= 0; i--){
                            if(ship.getAlarmList_teshu().get(i) == area.getID()){
                                ship.getAlarmList_teshu().remove(i);
                                if(area.getROUTETYPE() == 2 || area.getROUTETYPE() == 3){
                                    portService.InsertOneAlarmInfo(ship, 302,
                                            "渔船驶离:" + area.getNAME() + ",区域类型:特殊区,船速" + ship.getSPEED() + "节",
                                            area.getUSERID(), area.getNAME());
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public void checkAreaAlarm_zidingyi(){

        for (AreaInfo area:areaInfos){
            if(area.getSTARTTIME() == null || area.getENDTIME() == null) continue;
            //报警规则
            if(area.getROUTETYPE() == 0) continue;
            if(area.getTYPE() != 4) continue;
            //时间限制
            if(Utils.CompareTime(Utils.GetNowTimeString(), area.getSTARTTIME())
                    && !Utils.CompareTime(Utils.GetNowTimeString(), area.getENDTIME())){

                for (ShipDynamicInfo ship: gShipDynamicInfoLisht){
                    M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());

                    //速度限制
                    if(area.getBSPEED() == 1 && ship.getSPEED() < area.getSPEED()) {
                        continue;
                    }else if(area.getBSPEED() == 2 && ship.getSPEED() > area.getSPEED()){
                        continue;
                    }

                    //System.out.println("check:"+area.getNAME()+"---shipName:"+ship.getSHIPNAME());

                    boolean bInArea = false;
                    if(ship.getBOutside() == 0){
                        List<M_POINT> points = area.getPoints();
                        int poCount = area.getPOSCOUNT();
                        bInArea = Utils.IsPointInPolygon(shipPo, poCount, points);
                    }
                    if (bInArea) {
                        if(!ship.getAlarmList_zidingyi().contains(area.getID())){
                            ship.getAlarmList_zidingyi().add(area.getID());
                            if(area.getROUTETYPE() == 1 || area.getROUTETYPE() == 3){
                                portService.InsertOneAlarmInfo(ship, 302,
                                        "渔船驶入:" + area.getNAME() + ",区域类型:自定义报警区,船速" + ship.getSPEED() + "节",
                                        area.getUSERID(), area.getNAME());
                            }
                        }
                    }
                    else {
                        for(int i = ship.getAlarmList_zidingyi().size() - 1; i >= 0; i--){
                            if(ship.getAlarmList_zidingyi().get(i) == area.getID()){
                                ship.getAlarmList_zidingyi().remove(i);
                                if(area.getROUTETYPE() == 2 || area.getROUTETYPE() == 3){
                                    portService.InsertOneAlarmInfo(ship, 302,
                                            "渔船驶离:" + area.getNAME() + ",区域类型:自定义报警区,船速" + ship.getSPEED() + "节",
                                            area.getUSERID(), area.getNAME());
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    //////////外省//////////

    public void checkAreaAlarm_gaowei_w(){

        for (AreaInfo area:areaInfos){
            if(area.getSTARTTIME() == null || area.getENDTIME() == null) continue;
            //报警规则
            if(area.getROUTETYPE() == 0) continue;
            if(area.getTYPE() != 2) continue;
            //时间限制
            if(Utils.CompareTime(Utils.GetNowTimeString(), area.getSTARTTIME())
                    && !Utils.CompareTime(Utils.GetNowTimeString(), area.getENDTIME())){

                for (ShipDynamicInfo ship: gShipDynamicInfoLisht_ws){
                    M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());

                    //速度限制
                    if(area.getBSPEED() == 1 && ship.getSPEED() < area.getSPEED()) {
                        continue;
                    }else if(area.getBSPEED() == 2 && ship.getSPEED() > area.getSPEED()){
                        continue;
                    }

                    //System.out.println("check:"+area.getNAME()+"---shipName:"+ship.getSHIPNAME());

                    boolean bInArea = false;
                    List<M_POINT> points = area.getPoints();
                    int poCount = area.getPOSCOUNT();
                    bInArea = Utils.IsPointInPolygon(shipPo, poCount, points);
                    if (bInArea) {
                        if(!ship.getAlarmList_gaowei().contains(area.getID())){
                            ship.getAlarmList_gaowei().add(area.getID());
                            if(area.getROUTETYPE() == 1 || area.getROUTETYPE() == 3){
                                portService.InsertOneAlarmInfo(ship, 302,
                                        "渔船驶入:" + area.getNAME() + ",区域类型:高危区,船速" + ship.getSPEED() + "节",
                                        area.getUSERID(), area.getNAME());
                            }
                        }
                    }
                    else {
                        for(int i = ship.getAlarmList_gaowei().size() - 1; i >= 0; i--){
                            if(ship.getAlarmList_gaowei().get(i) == area.getID()){
                                ship.getAlarmList_gaowei().remove(i);
                                if(area.getROUTETYPE() == 2 || area.getROUTETYPE() == 3){
                                    portService.InsertOneAlarmInfo(ship, 302,
                                            "渔船驶离:" + area.getNAME() + ",区域类型:高危区,船速" + ship.getSPEED() + "节",
                                            area.getUSERID(), area.getNAME());
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public void checkAreaAlarm_teshu_w(){

        for (AreaInfo area:areaInfos){
            if(area.getSTARTTIME() == null || area.getENDTIME() == null) continue;
            //报警规则
            if(area.getROUTETYPE() == 0) continue;
            if(area.getTYPE() != 3) continue;
            //时间限制
            if(Utils.CompareTime(Utils.GetNowTimeString(), area.getSTARTTIME())
                    && !Utils.CompareTime(Utils.GetNowTimeString(), area.getENDTIME())){

                for (ShipDynamicInfo ship: gShipDynamicInfoLisht_ws){
                    M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());

                    //速度限制
                    if(area.getBSPEED() == 1 && ship.getSPEED() < area.getSPEED()) {
                        continue;
                    }else if(area.getBSPEED() == 2 && ship.getSPEED() > area.getSPEED()){
                        continue;
                    }

                    //System.out.println("check:"+area.getNAME()+"---shipName:"+ship.getSHIPNAME());

                    boolean bInArea = false;
                    List<M_POINT> points = area.getPoints();
                    int poCount = area.getPOSCOUNT();
                    bInArea = Utils.IsPointInPolygon(shipPo, poCount, points);
                    if (bInArea) {
                        if(!ship.getAlarmList_teshu().contains(area.getID())){
                            ship.getAlarmList_teshu().add(area.getID());
                            if(area.getROUTETYPE() == 1 || area.getROUTETYPE() == 3){
                                portService.InsertOneAlarmInfo(ship, 302,
                                        "渔船驶入:" + area.getNAME() + ",区域类型:特殊区,船速" + ship.getSPEED() + "节",
                                        area.getUSERID(), area.getNAME());
                            }
                        }
                    }
                    else {
                        for(int i = ship.getAlarmList_teshu().size() - 1; i >= 0; i--){
                            if(ship.getAlarmList_teshu().get(i) == area.getID()){
                                ship.getAlarmList_teshu().remove(i);
                                if(area.getROUTETYPE() == 2 || area.getROUTETYPE() == 3){
                                    portService.InsertOneAlarmInfo(ship, 302,
                                            "渔船驶离:" + area.getNAME() + ",区域类型:特殊区,船速" + ship.getSPEED() + "节",
                                            area.getUSERID(), area.getNAME());
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public void checkAreaAlarm_zidingyi_w(){

        for (AreaInfo area:areaInfos){
            if(area.getSTARTTIME() == null || area.getENDTIME() == null) continue;
            //报警规则
            if(area.getROUTETYPE() == 0) continue;
            if(area.getTYPE() != 4) continue;
            //时间限制
            if(Utils.CompareTime(Utils.GetNowTimeString(), area.getSTARTTIME())
                    && !Utils.CompareTime(Utils.GetNowTimeString(), area.getENDTIME())){

                for (ShipDynamicInfo ship: gShipDynamicInfoLisht_ws){
                    M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());

                    //速度限制
                    if(area.getBSPEED() == 1 && ship.getSPEED() < area.getSPEED()) {
                        continue;
                    }else if(area.getBSPEED() == 2 && ship.getSPEED() > area.getSPEED()){
                        continue;
                    }

                    //System.out.println("check:"+area.getNAME()+"---shipName:"+ship.getSHIPNAME());

                    boolean bInArea = false;
                    List<M_POINT> points = area.getPoints();
                    int poCount = area.getPOSCOUNT();
                    bInArea = Utils.IsPointInPolygon(shipPo, poCount, points);

                    if (bInArea) {
                        if(!ship.getAlarmList_zidingyi().contains(area.getID())){
                            ship.getAlarmList_zidingyi().add(area.getID());
                            if(area.getROUTETYPE() == 1 || area.getROUTETYPE() == 3){
                                portService.InsertOneAlarmInfo(ship, 302,
                                        "渔船驶入:" + area.getNAME() + ",区域类型:自定义报警区,船速" + ship.getSPEED() + "节",
                                        area.getUSERID(), area.getNAME());
                            }
                        }
                    }
                    else {
                        for(int i = ship.getAlarmList_zidingyi().size() - 1; i >= 0; i--){
                            if(ship.getAlarmList_zidingyi().get(i) == area.getID()){
                                ship.getAlarmList_zidingyi().remove(i);
                                if(area.getROUTETYPE() == 2 || area.getROUTETYPE() == 3){
                                    portService.InsertOneAlarmInfo(ship, 302,
                                            "渔船驶离:" + area.getNAME() + ",区域类型:自定义报警区,船速" + ship.getSPEED() + "节",
                                            area.getUSERID(), area.getNAME());
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    //////////外省/////////

    public void areaAlarm_shewai_han(){
        for (ShipDynamicInfo ship: gShipDynamicInfoLisht){
            M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());
            boolean bInArea = false;
            for (AreaInfo area:areaInfos){

                if(area.getTYPE() == 5 && ship.getBOutside() == 0){
                    List<M_POINT> points = area.getPoints();
                    int poCount = area.getPOSCOUNT();
                    bInArea = Utils.IsPointInPolygon(shipPo, poCount, points);
                }
                if (bInArea) {
                    if (ship.getBAlarm_shewai_han() == 0) {
                        ship.setBAlarm_shewai_han(1);
                        portService.InsertOneAlarmInfo(ship, 303, "渔船进入"+ area.getNAME(), 0, area.getNAME());
                    }
                    break;
                }
            }
            if(!bInArea) ship.setBAlarm_shewai_han(0);
        }
    }

    public void areaAlarm_shewai_ri(){
        for (ShipDynamicInfo ship: gShipDynamicInfoLisht){
            M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());
            boolean bInArea = false;
            for (AreaInfo area:areaInfos){

                if(area.getTYPE() == 6 && ship.getBOutside() == 0){
                    List<M_POINT> points = area.getPoints();
                    int poCount = area.getPOSCOUNT();
                    bInArea = Utils.IsPointInPolygon(shipPo, poCount, points);
                }
                if (bInArea) {
                    if (ship.getBAlarm_shewai_ri() == 0) {
                        ship.setBAlarm_shewai_ri(1);
                        portService.InsertOneAlarmInfo(ship, 303, "渔船进入"+ area.getNAME(), 0, area.getNAME());
                    }
                    break;
                }
            }
            if(!bInArea) ship.setBAlarm_shewai_ri(0);
        }
    }

    public void areaAlarm_shewai_yue(){
        for (ShipDynamicInfo ship: gShipDynamicInfoLisht){
            M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());
            boolean bInArea = false;
            for (AreaInfo area:areaInfos){

                if(area.getTYPE() == 7 && ship.getBOutside() == 0){
                    List<M_POINT> points = area.getPoints();
                    int poCount = area.getPOSCOUNT();
                    bInArea = Utils.IsPointInPolygon(shipPo, poCount, points);
                }
                if (bInArea) {
                    if (ship.getBAlarm_shewai_yue() == 0) {
                        ship.setBAlarm_shewai_yue(1);
                        portService.InsertOneAlarmInfo(ship, 303, "渔船进入"+ area.getNAME(), 0, area.getNAME());
                    }
                    break;
                }
            }
            if(!bInArea) ship.setBAlarm_shewai_yue(0);
        }
    }

    //清理未在渔港的船
    public void updateInportShip(){
        List<ShipDynamicInfo> inportList = GetInportShip();
        for (ShipDynamicInfo ship: inportList) {
            M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());

            boolean bIn = false;
            for (PortInfo port : portInfoList) {
                if(ship.getINPORTID() == port.getId()){
                    List<M_POINT> points = port.getPoints();
                    int poCount = port.getPointCount();
                    bIn = Utils.IsPointInPolygon(shipPo, poCount, points);
                    if (!bIn) {
                        portService.updateInportState(ship.getStaticShipId());
                    }
                }
            }

        }
    }

    public List<ShipDynamicInfo> GetInportShip(){
        return portService.GetInportShip();
    }

    public void updateInportShip2(){
        List<ShipDynamicInfo> inportList = GetUpdateShip();
        for (ShipDynamicInfo ship: inportList) {
            M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());

            boolean bIn = false;
            for (PortInfo port : portInfoList) {

                List<M_POINT> points = port.getPoints();
                int poCount = port.getPointCount();
                bIn = Utils.IsPointInPolygon(shipPo, poCount, points);
                if (bIn) {
                    portService.updateInportState2(ship.getStaticShipId(), port.getId());
                }

            }
        }
    }

    public List<ShipDynamicInfo> GetUpdateShip(){
        return portService.GetUpdateShip();
    }

    //港池
    public void portInGC(){
        for (ShipDynamicInfo ship: gShipDynamicInfoLisht){
            M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());

            boolean bIn = false;

            for (PortInfo port: portInfoList){

                List<M_POINT> points = port.getPoints();
                int poCount = port.getPointCount();
                bIn = Utils.IsPointInPolygon(shipPo, poCount, points);
                if(bIn){
                    //在港
                    ship.setINPORTID(port.getId());
                    if(ship.getINPORTSTATE() == 0){
                        //在港状态变化 不在港 > 在港
                        ship.setINPORTSTATE(1);
                        System.out.println(ship.getSHIPNAME() + "-" + ship.getINPORTSTATE() + "-" + port.getPortName());
                        shipService.UpdateShipInPortState(port.getId(), ship.getINPORTSTATE(), ship.getId());
                        shipService.InsertOutInPortRecord(ship);
                    }
                    break;
                }
            }

            if(ship.getINPORTSTATE() == 1 && !bIn){
                //在港状态变化 在港 > 不在港
                ship.setINPORTSTATE(0);
                System.out.println(ship.getSHIPNAME() + "-" + ship.getINPORTSTATE() + "-" + ship.getINPORTID());
                shipService.UpdateShipInPortState(0, ship.getINPORTSTATE(), ship.getId());
                shipService.InsertOutInPortRecord(ship);
            }
        }
    }

    //更新渔区
    public void updateFishAreaId() throws Exception{
        for (ShipDynamicInfo ship : gShipDynamicInfoLisht) {
            M_POINT shipPo = new M_POINT(ship.getLON(), ship.getLAT());

            boolean bIn = false;

            for (FishArea fishArea : fishAreaInfos) {

                List<M_POINT> points = fishArea.getPoints();

                bIn = Utils.IsPointInPolygon(shipPo, 4, points);

                if (bIn) {
                    shipService.updateFishAreaId(ship, fishArea.getName());
                    //System.out.println(ship.getSHIPNAME()+"---"+fishArea.getName());
                }
            }

        }
    }

    //移位
    public void typhoonAlarm(){
        /*
        * 记录两次报位的差值，直接存报警
        * 客户端启动台风模式时，从数据库查询台风移位报警
        * */

        boolean bAddOrUpdate = true;
        List<ShipDynamicInfo> shipList = getShipDynamicList();
        for (ShipDynamicInfo shipDynamicInfo: shipList){
            for(int i = 0; i < typhoonAlarmshipList.size(); i++){
                ShipDynamicInfo ship = typhoonAlarmshipList.get(i);
                if(ship.getStaticShipId() == shipDynamicInfo.getStaticShipId()){
                    //1.判断距离,并报警
                    int dis = distByToPoint(ship.getLON(),ship.getLAT(), shipDynamicInfo.getLON(), shipDynamicInfo.getLAT());
                    if(dis >= 50){
                        System.out.println(dis);
                        //todo 台风报警是多少？？ 10
                        //查询今天有没有该船报警
                        int alarmCount = portService.getTyphoonAlarm(shipDynamicInfo, Utils.GetNowDateTimeString());
                        if(alarmCount < 1){
                            portService.InsertOneAlarmInfo(shipDynamicInfo, 102, "船舶移位报警",0,"");
                        }else{
                            portService.UpdateOneAlarmInfo(shipDynamicInfo, Utils.GetNowDateTimeString());
                        }

                    }
                    //2.替换
                    typhoonAlarmshipList.get(i).setLAT(shipDynamicInfo.getLAT());
                    typhoonAlarmshipList.get(i).setLON(shipDynamicInfo.getLON());
                    bAddOrUpdate = false;
                }
            }
            if(bAddOrUpdate){
                typhoonAlarmshipList.add(shipDynamicInfo);
            }
        }
    }

    //前端伏休模式报警
    public void checkFuXiuAlarm(){
        /*
        * 按照之前的伏休规则，存报警
        * 客户端启动伏休模式时，从数据库查询台风移位报警
        * */
    }

    public void sss(){
        List<String> bdidList = shipService.GetTrackBdidList();
        for (int i = 0; i < bdidList.size(); i ++){
            int count = shipService.GetstaticshipidCount(bdidList.get(i));
            if(count > 0){
                int staticshipid = shipService.GetStaticshipidByBdid(bdidList.get(i));
                shipService.InsertTrack(staticshipid, bdidList.get(i));
                System.out.println(staticshipid + "--" + bdidList.get(i));
            }
        }
    }

    //米
    public int distByToPoint(int Lon1, int Lat1, int Lon2, int Lat2){
        double aLon1 = (double) Lon1 / 180 / 10000000 * PI;
        double aLat1 = (double) Lat1 / 180 / 10000000 * PI;
        double aLon2 = (double) Lon2 / 180 / 10000000 * PI;
        double aLat2 = (double) Lat2 / 180 / 10000000 * PI;

        double dlon = aLon2 - aLon1;
        double dlat = aLat2 - aLat1;

        double a = pow(sin(dlat / 2), 2) + cos(aLat1) * cos(aLat2) * pow(sin(dlon/2), 2);
        double c = 2 * atan2(sqrt(a), sqrt(1-a) );

        double d = 6378137 * c;
        return (int) d;
    }

    //初始化渔区
    List<FishArea2> fishArea2List = new ArrayList<>();
    public void initFishArea2List()
    {
        fishArea2List.add(new FishArea2("1", new M_POINT(1205000000, 405000000)));
    }

    //判断 船在渔区？
    public boolean isPointInFishArea(M_POINT shipPoint, FishArea2 fishArea)
    {
        for(int i = 0; i < fishArea2List.size(); i++)
        {
            //给的M_POINT(1205000000, 405000000) 是渔区左下角，看了下数据，
            // 右上角是 +5000000

            if(shipPoint.x > fishArea.GetPoint().x &&
               shipPoint.x < fishArea.GetPoint().x + 5000000 &&
               shipPoint.y > fishArea.GetPoint().y &&
               shipPoint.y < fishArea.GetPoint().x + 5000000)
            {
                return true;
            }
        }
        return false;
    }
}


