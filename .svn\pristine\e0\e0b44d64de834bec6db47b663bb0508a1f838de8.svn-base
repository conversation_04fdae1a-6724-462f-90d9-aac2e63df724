package com.bd.entity;

import lombok.Data;

@Data
public class UserInfo {
    private int id;
    private String username;
    private String password;
    private String name;
    private String lxdh;
    private int level;
    private String state;
    private String token;
    private String bm;
    private String userArea; // 用户所在区域，用于摄像头过滤
    private String shipQueryAreas; // 用户可查询的船舶区域，多个区域用逗号分隔
}
