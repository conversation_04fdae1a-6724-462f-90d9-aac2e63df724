<template>
  <div class="simple-dialog" :style="{
      left: dialogLeft + 'px',
      top: dialogTop + 'px',
      position: 'fixed',
      zIndex: 9999,
      transform: isMobile ? 'scale(0.6)' : 'scale(0.8)',
      transformOrigin: 'top left'
  }" @mousedown="onDragStart" @touchstart="onTouchStart" @dblclick="viewMoreDetails">
    <span class="close-btn" @click.stop="closeDialog">×</span>
    <div class="ship-title">
      <span class="ship-name">{{ ship ? ship.shipname : '未知船舶' }}</span>
    </div>
    <div class="beidou-id">
      北斗终端号：{{ ship ? ship.bdid : '-' }}
    </div>
    <div class="header">
      <!-- 这里原本有船名和北斗终端号，现已删除，只保留后续内容 -->
    </div>
    <div class="divider"></div>
    <!-- 基本信息补全 -->
    <div class="section info-grid" v-if="ship">
      <div class="item"><span class="label">船舶类型</span><span class="value">{{ ship.cblx || '-' }}</span></div>
      <div class="item"><span class="label">MMSI</span><span class="value highlight">{{ ship.mmsi || '-' }}</span></div>
      <div class="item"><span class="label">核定人数</span><span class="value">{{ ship.maxPeopleCount || '-' }}</span></div>
      <div class="item"><span class="label">最低配员</span><span class="value">{{ minPersonNumCalc }}</span></div>
      <div class="item"><span class="label">功率(千瓦)</span><span class="value">{{ ship.zjzgl || '-' }}</span></div>
      <div class="item"><span class="label">船长(米)</span><span class="value">{{ ship.length || '-' }}</span></div>
    </div>
    <div class="divider"></div>
    <!-- 船东/联系人信息补全（表格布局） -->
    <table class="contact-table" v-if="ship">
      <tr>
        <td class="label">船舶所有人</td>
        <td class="value">{{ ship.cbsyrmc || '-' }}</td>
        <td class="label">联系方式</td>
        <td class="value">{{ ship.cbsyrdh || '-' }}</td>
      </tr>
      <tr>
        <td class="label">船长</td>
        <td class="value">{{ ship.captain || '-' }}</td>
        <td class="label">联系方式</td>
        <td class="value">{{ ship.captainlxdh || '-' }}</td>
      </tr>
      <tr>
        <td class="label">应急联系人</td>
        <td class="value">{{ ship.emergencyContact || '-' }}</td>
        <td class="label">联系方式</td>
        <td class="value">{{ ship.emergencyContactLxdh || '-' }}</td>
      </tr>
      <tr>
        <td class="label">实际操作员</td>
        <td class="value">{{ ship.sjOperator || '-' }}</td>
        <td class="label">联系方式</td>
        <td class="value">{{ ship.sjOperatorLxdh || '-' }}</td>
      </tr>
      <tr>
        <td class="label" colspan="2"></td>
        <td class="label">卫星电话</td>
        <td class="value">{{ ship.wxlxdh || '-' }}</td>
      </tr>
    </table>
    <div class="divider"></div>
    <!-- 速度、角度、报位终端号 -->
    <div class="section row speed-row" v-if="ship" style="margin-left: 4px;">
      <div><span class="label">速度</span><span class="value">{{ ship.speed ? ship.speed + '节' : '-' }}</span></div>
      <div><span class="label">角度</span><span class="value">{{ ship.cog ? ship.cog + '°' : '-' }}</span></div>
      <div><span class="label">报位终端号</span><span class="value">{{ ship.bdid || '-' }}</span></div>
    </div>
    <div class="divider"></div>
    <!-- 定位时间、最新位置 -->
    <div class="section time-section" v-if="ship">
      <div><span class="label">北斗报位时间</span><span class="value">{{ ship.bdTime || '-' }}</span></div>
      <div><span class="label">AIS报位时间</span><span class="value">{{ ship.aisTime || '-' }}</span></div>
      <div><span class="label">船舶最新位置</span><span class="value">{{ formatLonLat(ship.lon, ship.lat) }}</span></div>
    </div>
  </div>
</template>

<script>
import global from './Global.vue'
export default {
  name: 'SimpleDialog',
  props: {
    ship: {
      type: Object,
      required: true
    }
  },
  data() {
    const isMobile = /Android|webOS|iPhone|iPod|BlackBerry|iPad|Mobile/i.test(navigator.userAgent);
    return {
      isMobile: isMobile,
      dialogLeft: isMobile ? 10 : 500,
      dialogTop: isMobile ? 60 : 200,
      dragging: false,
      dragOffsetX: 0,
      dragOffsetY: 0
    }
  },
  computed: {
    minPersonNumCalc() {
      if (!this.ship) return '-';
      const len = Number(this.ship.length);
      const gl = Number(this.ship.zjglqw);
      let lengthCount = 0;
      if (len >= 12 && len < 24) lengthCount = 2;
      else if (len >= 24 && len < 36) lengthCount = 2;
      else if (len >= 36 && len < 45) lengthCount = 2;
      else if (len >= 45) lengthCount = 3;
      let glCount = 0;
      if (gl >= 50 && gl < 250) glCount = 1;
      else if (gl >= 250 && gl < 450) glCount = 2;
      else if (gl >= 450 && gl < 750) glCount = 2;
      else if (gl >= 750 && gl < 3000) glCount = 2;
      else if (gl >= 3000) glCount = 3;
      if (gl > 800) glCount++;
      // 取最大值作为最低配员
      const result = Math.max(lengthCount, glCount);
      return result > 0 ? result : '-';
    }
  },
  watch: {
    // 监视ship prop的变化，如果需要的话可以在这里做一些响应
    ship(newShip, oldShip) {
      if (newShip && (!oldShip || newShip.id !== oldShip.id)) {
        // 当船舶信息变化时，可以重置对话框位置或执行其他逻辑
        console.log('SimpleDialog received new ship data:', newShip);
    }
    }
  },
  mounted() {
    document.addEventListener('mousemove', this.onDragMove);
    document.addEventListener('mouseup', this.onDragEnd);
    document.addEventListener('touchmove', this.onTouchMove, { passive: false });
    document.addEventListener('touchend', this.onTouchEnd);
  },
  beforeDestroy() {
    document.removeEventListener('mousemove', this.onDragMove);
    document.removeEventListener('mouseup', this.onDragEnd);
    document.removeEventListener('touchmove', this.onTouchMove);
    document.removeEventListener('touchend', this.onTouchEnd);
  },
  methods: {
    closeDialog() {
      this.$emit('close');
    },
    viewMoreDetails() {
      // 跳转到详细页面，传递船舶ID
      const shipId = this.ship ? this.ship.id : null;
      if (!shipId) return;

      const shipName = this.ship ? (this.ship.shipname || this.ship.shipName || '-') : '-';
      this.$router.push({
        name: 'shipInfo',
        params: { shipId: shipId, shipName: shipName }
      });
    },
    formatLonLat(lon, lat) {
      if (!lon || !lat) return '-';
      const lonD = parseInt(lon);
      const lonM = parseInt((lon - lonD) * 60);
      const lonS = parseInt((((lon - lonD) * 60) - lonM) * 60);
      const latD = parseInt(lat);
      const latM = parseInt((lat - latD) * 60);
      const latS = parseInt((((lat - latD) * 60) - latM) * 60);
      return lonD + '°' + lonM + "'" + lonS + '" - ' + latD + '°' + latM + "'" + latS + '"';
    },
    getShipTypeName(type) {
      if (type === 2 || type === '2') return '渔船';
      // 可扩展其他类型映射
      return type || '-';
    },
    // PC端拖拽
    onDragStart(e) {
      if (e.button !== 0) return;
      this.dragging = true;
      this.dragOffsetX = e.clientX - this.dialogLeft;
      this.dragOffsetY = e.clientY - this.dialogTop;
      // e.preventDefault(); // 移除，保证双击事件可用
    },
    onDragMove(e) {
      if (!this.dragging) return;
      this.dialogLeft = e.clientX - this.dragOffsetX;
      this.dialogTop = e.clientY - this.dragOffsetY;
    },
    onDragEnd() {
      this.dragging = false;
    },
    // 移动端拖拽
    onTouchStart(e) {
      if (e.touches.length !== 1) return;
      this.dragging = true;
      const touch = e.touches[0];
      this.dragOffsetX = touch.clientX - this.dialogLeft;
      this.dragOffsetY = touch.clientY - this.dialogTop;
      // e.preventDefault(); // 移除，保证双击事件可用
    },
    onTouchMove(e) {
      if (!this.dragging || e.touches.length !== 1) return;
      const touch = e.touches[0];
      this.dialogLeft = touch.clientX - this.dragOffsetX;
      this.dialogTop = touch.clientY - this.dragOffsetY;
      e.preventDefault();
    },
    onTouchEnd() {
      this.dragging = false;
    }
  }
}
</script>

<style scoped>
.simple-dialog {
  background: white;
  border-radius: 14px;
  padding: 16px 22px 10px 22px;
  width: 550px;
  /* 增加宽度 */
  max-height: 85vh;
  /* 增加最大高度 */
  font-size: 16px;
  /* 恢复字体大小 */
  color: black;
  box-shadow: 0 6px 28px rgba(0, 0, 0, 0.15);
  position: fixed;
  left: 35vw;
  top: 10vh;
  /* 位置更靠上 */
  z-index: 9998;
  /* 低于ShipTrackCard的z-index */
  /* transform 在style绑定中动态设置，这里不写 */
  transform-origin: top left;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  overflow-y: auto;
  /* 允许内容滚动 */
  border: 1px solid rgba(24, 144, 255, 0.2);
  /* 添加一个轻微的边框 */
}

.close-btn {
  position: absolute;
  top: 12px;
  right: 16px;
  font-size: 24px;
  color: #1890ff;
  cursor: pointer;
  font-weight: bold;
  transition: color 0.2s;
}

.close-btn:hover {
  color: #d32f2f;
}

.header {
  display: flex;
  align-items: center;
  gap: 12px 18px;
  flex-wrap: wrap;
  margin-bottom: 6px;
}

.ship-name {
  font-size: 28px !important;
  font-weight: bold;
  color: #1890ff;
  margin-right: 10px;
}

.bd-label {
  color: #1890ff;
  font-weight: 500;
}

.bd-value {
  color: #333;
  font-weight: 500;
}

.divider {
  border-bottom: 1px dashed #b3c6d6;
  margin: 8px 0 10px 0;
}

.section {
  margin-bottom: 0px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px 12px;
}

.item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.label {
  color: #1890ff;
  font-weight: 500;
  min-width: 76px;
  text-align: right;
  margin-right: 4px;
  font-size: 16px;
}

.value {
  color: #222;
  margin-right: 15px;
  font-weight: 400;
  font-size: 16px;
}

.value.highlight {
  color: #d32f2f;
  font-weight: bold;
}

.contact-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contact-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
}

.contact-col {
  display: flex;
  align-items: center;
  min-width: 0;
}

.row.speed-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  margin-bottom: 0;
  box-sizing: border-box;
  width: 100%;
}

.time-section>div {
  margin-bottom: 2px;
}

.more-section {
  text-align: center;
  margin-top: 10px;
}

.more-btn {
  background: linear-gradient(135deg, #1890ff 0%, #1890ff 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 0 auto;
}

.more-btn:hover {
  background: linear-gradient(135deg, #1890ff 0%, #1890ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.more-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
}

.more-icon {
  font-size: 16px;
}

.contact-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 10px;
}

.contact-table td {
  padding: 2px 8px;
  vertical-align: top !important;
  line-height: 1.4;
}

.contact-table tr {
  height: 32px;
}

.contact-table .label {
  color: #1890ff;
  font-weight: 500;
  min-width: 76px;
  text-align: right;
  font-size: 16px;
}

.contact-table .value {
  color: #222;
  font-weight: 400;
  min-width: 55px;
  word-break: break-all;
  font-size: 16px;
}

.ship-title {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 3px;
  margin-top: 6px;
}

.beidou-id {
  text-align: center;
  font-size: 16px;
  color: #333;
  margin-bottom: 6px;
  word-break: break-all;
}

@media (max-width: 420px) {
  .simple-dialog {
    width: 95vw;
    padding: 10px 2vw 10px 2vw;
    font-size: 14px;
    max-height: 90vh;
    /* 在移动设备上增加最大高度 */
  }

  .ship-name {
    font-size: 24px !important;
  }

  .label {
    min-width: 65px;
    font-size: 14px;
  }

  .value {
    font-size: 14px;
  }

  .more-btn {
    padding: 10px 18px;
    font-size: 14px;
  }

  .contact-table tr {
    height: 28px;
  }

  .contact-table .label,
  .contact-table .value {
    font-size: 14px;
  }
}
</style>
