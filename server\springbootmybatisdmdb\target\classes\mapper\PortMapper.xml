<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bd.mapper.PortMapper">

    <resultMap id="BaseResultMap" type="com.bd.entity.Port">
        <result column="NAME" jdbcType="VARCHAR" property="portName" />
        <result column="POSCOUNT" jdbcType="INTEGER" property="pointCount" />
        <result column="POINTSTR" jdbcType="VARCHAR" property="pointStr" />
        <result column="PROVINCENAME" jdbcType="VARCHAR" property="provinceName" />
        <result column="ORGANIZATIONID" jdbcType="LONGVARCHAR" property="organizationID" />
        <result column="ISUSED" jdbcType="INTEGER" property="isUsed" />
    </resultMap>

    <resultMap id="OutInPortRecordResultMap" type="com.bd.entity.OutInPortRecord">
        <result column="ID" jdbcType="INTEGER" property="id" />
        <result column="PORTNAME" jdbcType="VARCHAR" property="portName" />
    </resultMap>

    <resultMap id="GetPortShipCountResultMap" type="com.bd.entity.PortShipCount">
        <result column="PORTNAME" jdbcType="VARCHAR" property="portName" />
        <result column="ALLSHIPCOUNT" jdbcType="INTEGER" property="allShipCount" />
    </resultMap>

    <resultMap id="portResultMap" type="com.bd.entity.PortInfo">
        <result column="ID" jdbcType="INTEGER" property="id" />
        <result column="POSCOUNT" jdbcType="VARCHAR" property="pointCount" />
    </resultMap>

    <select id="GetAllPortInfo" resultType="com.bd.entity.PortInfo" resultMap="portResultMap">

        SELECT * FROM SHIP.PORTINFO;

    </select>

    <select id="GetPortInfo" resultType="com.bd.entity.PortInfo" resultMap="portResultMap">

        SELECT * FROM SHIP.PORTINFO limit #{pageNum}, 10;

    </select>

    <select id="GetPortInfoCount" resultType="java.lang.Integer">

        SELECT COUNT(*) FROM SHIP.PORTINFO;

    </select>

    <select id="GetOnePortInfo" resultType="com.bd.entity.PortInfo" resultMap="portResultMap">

        SELECT * FROM SHIP.PORTINFO WHERE id = #{portId};

    </select>

    <insert id="AddPort">

        INSERT INTO SHIP.PORTINFO(PORTNAME, POSCOUNT, POINTSTR, CONTENT, MAXSHAPCOUNT, WINDLEVEL, NAVMARKMMSI)
        VALUES (
                #{name},
                #{pointCount},
                #{pointStr},
                #{content},
                #{maxshapcount},
                #{windlevel},
                #{navmarkmmsi}
               );

    </insert>

    <update id="EditPort">

        UPDATE SHIP.PORTINFO
        SET PORTNAME = #{newName},
            POSCOUNT = #{pointCount},
            POINTSTR = #{pointStr},
            CONTENT = #{content},
            MAXSHAPCOUNT = #{maxshapcount},
            WINDLEVEL = #{windlevel},
            NAVMARKMMSI = #{navmarkmmsi}
        WHERE ID = #{portId};

    </update>

    <delete id="DeletePort">

        DELETE FROM SHIP.PORTINFO WHERE ID = #{portId};

    </delete>

    <select id="GetOutInPortRecord" resultType="com.bd.entity.OutInPortRecord">
        SELECT
            s.staticShipId,
            s.portId,
            s.shipName,
            s.bdid,
            s.state,
            s.REPORTTIME,
            s.loadTime,
            p.portName
        from SHIP.OUTINPORTRECORD_2 s
        LEFT JOIN SHIP.PORTINFO p
        on s.portId = p.id
        LEFT JOIN  SHIP.SHIP_STATICINFO t
        on t.ID = s.STATICSHIPID
        where s.REPORTTIME > #{time} AND t.SHIPTYPE in (2, 61)
        order by s.REPORTTIME desc;
    </select>

    <select id="GetPortShipCount" resultType="com.bd.entity.PortShipCount" resultMap="GetPortShipCountResultMap">
        SELECT SUM(CASE WHEN d.loadTIme > #{time2} THEN 1 ELSE 0 END) as ALLSHIPCOUNT,
               SUM(CASE WHEN (s.BOUTSIDE = 1 or s.shiptype = 61) and d.loadTIme > #{time2} THEN 1 ELSE 0 END) AS wsShipCount,
               SUM(CASE WHEN s.BOUTSIDE = 0 and d.loadTIme > #{time2} THEN 1 ELSE 0 END) AS tongdaoShipCount,
               p.PORTNAME, p.id as portId
        from SHIP.PORTINFO p
        inner join SHIP.SHIP_DYNAMICTION d on p.id = d.INPORTID
        inner join SHIP.SHIP_STATICINFO s on s.id = d.staticshipid
        where d.inportstate = 1 and s.boutside in (0,1,2) GROUP BY p.PORTNAME, p.id
        order by ALLSHIPCOUNT desc
    </select>

    <select id="queryOnePortCountInfo" resultType="java.lang.Integer" parameterType="com.bd.entity.Port_InPortShipCount">
        SELECT COUNT(*) FROM SHIP.INPORTSHIPCOUNTINFO
        where PORTID = #{info.portID}
        and YEAR = #{info.year}
        and MONTH = #{info.month}
        and DAY = #{info.day}
    </select>

    <insert id="insertOnePortCountInfo">
        INSERT INTO SHIP.INPORTSHIPCOUNTINFO (PORTID, PORTNAME, SHIPCOUNT, WSSHIPCOUNT, YEAR, MONTH, DAY)
        values (
                #{info.portID},
                #{info.portName},
                #{info.shipCount},
                #{info.wsShipCount},
                #{info.year},
                #{info.month},
                #{info.day}
               );
    </insert>

    <update id="updateOnePortCountInfo">
        update SHIP.INPORTSHIPCOUNTINFO
        set SHIPCOUNT = #{info.shipCount},
            "WSSHIPCOUNT" = #{info.wsShipCount}
        where PORTID = #{info.portID}
        and YEAR = #{info.year}
        and MONTH = #{info.month}
        and DAY = #{info.day}
    </update>

    <update id="UpdateAlarmState">
        UPDATE SHIP.ALARMRECORDINFO SET STATE = 1, updateTime = #{updateTime} WHERE id = #{id}
    </update>
    <update id="updateInportState">
        UPDATE SHIP.SHIP_DYNAMICTION set inportId = 0 , inPortstate = 0 where staticshipid = #{staticShipId};
    </update>
    <update id="updateInportState2">
        UPDATE SHIP.SHIP_DYNAMICTION set inportId = #{portId} , inPortstate = 1 where staticshipid = #{staticShipId};
    </update>
    <update id="UpdateOneAlarmInfo">
        update SHIP.ALARMRECORDINFO
        set loadtime = sysdate
        where StaticShipId = #{shipDynamicInfo.staticShipId}
        and loadtime &gt;= #{time}
        and type = 102
    </update>
    <update id="updateOutInPortRecordReport">
        UPDATE SHIP.OUTINPORTRECORD_2 t1
        SET t1.ISREPORT = 1
        WHERE EXISTS(
                      SELECT 1
                      FROM SHIP.PORTNODE t2
                      WHERE t1.ISREPORT = 0
                        AND t2.SHIPNAME = t1.SHIPNAME
                        AND t2.INOROUT != t1.STATE
                        AND t2.TIME BETWEEN t1.LOADTIME - 1 AND t1.LOADTIME + 1
                  );
    </update>
    <update id="UpdateOutlineReason">
        UPDATE SHIP.SHIP_ENCRYPTION e SET e.OUTLINEREASON = #{newOutLineReason} WHERE e.SHIPID = #{id}
    </update>

    <select id="GetShip_inOrOutPortInfoDetail" resultType="com.bd.entity.OutInPortRecord">
        SELECT s.staticShipId,
            s.portId,
            s.shipName,
            s.bdid,
            s.state,
            s.REPORTTIME,
            s.loadTime,
            s.isReport,
            p.portName
        from SHIP.OUTINPORTRECORD_2 s
        LEFT JOIN SHIP.PORTINFO p on s.PORTID = p.id
        WHERE shipName like concat('%',#{shipName},'%')
        and LOADTIME &gt; #{startTime}
        and LOADTIME &lt; #{endTime}
        and p.portName like concat('%',#{portName},'%')
        <if test="state != 3">
            and state = #{state}
        </if>
        order by REPORTTIME desc
        limit #{pageNum}, 10;
    </select>
    <select id="GetShip_inOrOutPortInfoDetailCount" resultType="java.lang.Integer">
        SELECT s.staticShipId,
        s.portId,
        s.shipName,
        s.bdid,
        s.state,
        s.REPORTTIME,
        s.loadTime,
        p.portName
        from SHIP.OUTINPORTRECORD_2 s
        LEFT JOIN SHIP.PORTINFO p on s.PORTID = p.id
        WHERE shipName like concat('%',#{shipName},'%')
        and LOADTIME &gt; #{startTime}
        and LOADTIME &lt; #{endTime}
        and p.portName like concat('%',#{portName},'%')
        <if test="state != 3">
            and state = #{state}
        </if>
        order by REPORTTIME desc
    </select>

    <select id="GetAllShip_inOrOutPortInfoDetail" resultType="com.bd.entity.OutInPortRecord">
        SELECT s.staticShipId,
        s.portId,
        s.shipName,
        s.bdid,
        s.state,
        s.REPORTTIME,
        s.loadTime,
        p.portName
        from SHIP.OUTINPORTRECORD_2 s
        LEFT JOIN SHIP.PORTINFO p on s.PORTID = p.id
        WHERE shipName like concat('%',#{shipName},'%')
        and LOADTIME &gt; #{startTime}
        and LOADTIME &lt; #{endTime}
        and p.portName like concat('%',#{portName},'%')
        <if test="state != 3">
            and state = #{state}
        </if>
        order by REPORTTIME desc
    </select>

    <select id="GetTodayWarningAllInfo" resultType="com.bd.entity.BusinessManagement.AlarmRecord">
        SELECT * from SHIP.ALARMRECORDINFO where loadTime > #{time} AND USERID in (0, #{userId})
        <if test="model == 0">
            and (MODEL = #{model} or type = 101)
            and type not in (102, 310)
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
            and type not in (102, 310)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
        ORDER BY state ASC,loadTime DESC
    </select>
    <select id="GetTodayWarningInfo" resultType="com.bd.entity.BusinessManagement.AlarmRecord">
        SELECT * from SHIP.ALARMRECORDINFO where loadTime > #{time} and updateTime > #{updateTime} AND USERID in (0, #{userId})
        <if test="model == 0">
            and (MODEL = #{model} or type = 101)
            and type not in (102, 310)
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
            and type not in (102, 310)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
        ORDER BY state ASC,updateTime DESC
    </select>
    <select id="GetAlarmDetialById" resultType="com.bd.entity.BusinessManagement.AlarmRecord">
        SELECT a.* , coalesce(s.owner,s2.CBSYRMC) as owner , s.lxdh
        FROM SHIP.ALARMRECORDINFO a
        LEFT JOIN SHIP.SHIP_STATICINFO s
        ON a.staticshipid = s.id
        join SHIP.SHIP_NAME_REGIS_INFO s2 on a.SHIPNAME = s2.CM
        where a.ID = #{id}
        <if test="model == 0">
            and (MODEL = #{model} or type = 101)
        </if>
        <if test="model == 1">
            and a.MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and a.MODEL in (#{model}, 0)
        </if>
    </select>
    <select id="GetTodayWarningInfo_noResolve" resultType="com.bd.entity.BusinessManagement.AlarmRecord">
        SELECT * from SHIP.ALARMRECORDINFO where loadTime > #{time} and state = 0 ORDER BY loadTime DESC
    </select>
    <select id="GetAllAlarmRecordInfo" resultType="com.bd.entity.BusinessManagement.AlarmRecord">
        SELECT * from SHIP.ALARMRECORDINFO
        WHERE shipName like concat('%',#{shipName},'%')
        and LOADTIME &gt; #{startTime}
        and LOADTIME &lt; #{endTime}
        <if test="type != -1">
            and type = #{type}
        </if>
        <if test="model != -1">
            and MODEL = #{model}
        </if>
        ORDER BY loadTime DESC
        limit #{pageNum}, 10;
    </select>

    <select id="GetAlarmRecordInfo" resultType="com.bd.entity.BusinessManagement.AlarmRecord">
        SELECT * from SHIP.ALARMRECORDINFO
        WHERE shipName like concat('%',#{shipName},'%')
        and LOADTIME &gt; #{startTime}
        and LOADTIME &lt; #{endTime}
        <if test="type != -1">
            and type = #{type}
        </if>
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
        ORDER BY loadTime DESC
    </select>

    <select id="GetAllAlarmRecordInfoCount" resultType="java.lang.Integer">
        SELECT count(*) from SHIP.ALARMRECORDINFO
        WHERE shipName like concat('%',#{shipName},'%')
          and LOADTIME &gt; #{startTime}
          and LOADTIME &lt; #{endTime}
        <if test="type != -1">
            and type = #{type}
        </if>
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
        ORDER BY loadTime DESC
        limit 1;
    </select>

    <delete id="DeleteAlarmRecordInfo">
        DELETE FROM SHIP.ALARMRECORDINFO WHERE ID = #{alarmRecordId};
    </delete>

    <insert id="InsertOneAlarmInfo" >
        insert into SHIP.ALARMRECORDINFO(SHIPNAME, STATICSHIPID, PEOPLEID, BDID, PORTID, LON, LAT, TYPE, STATE, LOADTIME, CONTENT, UPDATETIME, USERID, AREANAME, model)
        VALUES(
            #{shipDynamicInfo.SHIPNAME},
            #{shipDynamicInfo.staticShipId},
            0,
            #{shipDynamicInfo.LastPosTermNo},
            #{shipDynamicInfo.INPORTID},
            #{shipDynamicInfo.LON},
            #{shipDynamicInfo.LAT},
            #{type},
            0,
            sysdate,
            #{msg},
            sysdate,
            #{userId},
            #{areaName},
            <if test="type == 301">
                1
            </if>
            <if test="type == 102">
                2
            </if>
            <if test="type != 301 and type != 102">
                0
            </if>
              );

    </insert>

    <select id="GetTodayWarningAllStatistics" resultType="java.lang.Integer">

        SELECT COUNT(*) FROM SHIP.ALARMRECORDINFO
                        WHERE loadtime > #{time}
                        AND (USERID = 0 or USERID = #{userId})
                        AND STATE = 0
                        AND TYPE != 310
        <if test="model == 0">
            and (MODEL = #{model} or TYPE = 101)
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
    </select>

    <select id="GetTodayWarningAllStatisticsByUpdateTime" resultType="java.lang.Integer">

        SELECT COUNT(*) FROM SHIP.ALARMRECORDINFO
                        WHERE loadTime > #{time}
                        AND updateTime > #{updateTime}
                        AND (USERID = 0 or USERID = #{userId})
                        AND STATE = 0
                        AND TYPE != 310
        <if test="model == 0">
            and (MODEL = #{model} or TYPE = 101)
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
    </select>

    <select id="GetTodayWarningNewUpdateTimeByZero" resultType="com.bd.entity.WarningInfo">

        SELECT UPDATETIME as updateTime, TYPE as warningType FROM SHIP.ALARMRECORDINFO
        WHERE loadTime > #{time}
        AND (USERID = 0 or USERID = #{userId})
        <if test="model == 0">
            and (MODEL = #{model} or TYPE = 101)
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
        order by UPDATETIME DESC

    </select>

    <select id="GetTodayWarningNewUpdateTime" resultType="com.bd.entity.WarningInfo">

        SELECT UPDATETIME as updateTime, TYPE as warningType FROM SHIP.ALARMRECORDINFO
        WHERE loadTime > #{time}
        AND updateTime > #{updateTime}
        AND (USERID = 0 or USERID = #{userId})
        <if test="model == 0">
            and (MODEL = #{model} or TYPE = 101)
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
        order by UPDATETIME DESC

    </select>

    <select id="GetTodayWarningPortAllInfo" resultType="java.lang.Integer">

        SELECT COUNT(*) FROM SHIP.ALARMRECORDINFO WHERE loadtime > #{time} AND USERID in (0, #{userId})
        <if test="model == 0">
            and ((MODEL = #{model} and TYPE in (101, 102)) or TYPE = 101)
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0) and TYPE in (101, 102)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0) and TYPE in (101, 102)
        </if>

    </select>

    <select id="GetTodayWarningPortUntreatedCountInfo" resultType="java.lang.Integer">

        SELECT COUNT(*) from SHIP.ALARMRECORDINFO where loadTime > #{time} AND STATE = 0 AND USERID in (0, #{userId})
        <if test="model == 0">
            and ((MODEL = #{model} and TYPE in (101, 102)) or TYPE = 101)
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0) and TYPE in (101, 102)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0) and TYPE in (101, 102)
        </if>

    </select>

    <select id="GetTodayWarningFishCountInfo" resultType="java.lang.Integer">

        SELECT COUNT(*) from SHIP.ALARMRECORDINFO where loadTime > #{time} AND TYPE in (201,202,203,204) AND USERID in (0, #{userId})
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>

    </select>

    <select id="GetTodayWarningFishUntreatedCountInfo" resultType="java.lang.Integer">

        SELECT COUNT(*) from SHIP.ALARMRECORDINFO where loadTime > #{time} AND TYPE in (201,202,203,204) AND STATE = 0 AND USERID in (0, #{userId})
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>

    </select>

    <select id="GetTodayWarningFishCountInfo2" resultType="java.lang.Integer">

        SELECT COUNT(*) from SHIP.ALARMRECORDINFO where loadTime > #{time} AND TYPE in (301,302,303,304,305,306,307,308,309) AND USERID in (0, #{userId})
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>

    </select>

    <select id="GetTodayWarningFishUntreatedCountInfo2" resultType="java.lang.Integer">

        SELECT COUNT(*) from SHIP.ALARMRECORDINFO where loadTime > #{time} AND TYPE in (301,302,303,304,305,306,307,308,309) AND STATE = 0 AND USERID in (0, #{userId})
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>

    </select>

    <select id="GetTodayWarningCrewCountInfo" resultType="java.lang.Integer">

        SELECT COUNT(*) from SHIP.ALARMRECORDINFO where loadTime > #{time} AND TYPE = 401 AND USERID in (0, #{userId})
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>

    </select>

    <select id="GetTodayWarningCrewUntreatedCountInfo" resultType="java.lang.Integer">

        SELECT COUNT(*) from SHIP.ALARMRECORDINFO where loadTime > #{time} AND TYPE = 401 AND STATE = 0 AND USERID in (0, #{userId})
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>

    </select>

    <select id="GetAllAlarmCount_year" resultType="com.bd.entity.TongjiModel">
        select
            SUM(case when MONTH(loadtime)=1 or MONTH(loadtime)=2 then 1 else 0 end ) as a,
            SUM(case when MONTH(loadtime)=3 or MONTH(loadtime)=4 then 1 else 0 end ) as b,
            SUM(case when MONTH(loadtime)=5 or MONTH(loadtime)=6 then 1 else 0 end ) as c,
            SUM(case when MONTH(loadtime)=7 or MONTH(loadtime)=8 then 1 else 0 end ) as d,
            SUM(case when MONTH(loadtime)=9 or MONTH(loadtime)=10 then 1 else 0 end ) as e,
            SUM(case when MONTH(loadtime)=11 or MONTH(loadtime)=12 then 1 else 0 end ) as f
        from ship.alarmrecordinfo
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
        group by year(loadtime) order by year(loadtime) desc limit 1
    </select>
    <select id="GetAllAlarmCount_month" resultType="com.bd.entity.TongjiModel">
        select
            SUM(case when day(loadtime)=1 or day(loadtime)=2 or day(loadtime)=3 or day(loadtime)=4 or day(loadtime)=5 then 1 else 0 end ) as a,
            SUM(case when day(loadtime)=6 or day(loadtime)=7 or day(loadtime)=8 or day(loadtime)=9 or day(loadtime)=10 then 1 else 0 end ) as b,
            SUM(case when day(loadtime)=11 or day(loadtime)=12 or day(loadtime)=13 or day(loadtime)=14 or day(loadtime)=15 then 1 else 0 end ) as c,
            SUM(case when day(loadtime)=16 or day(loadtime)=17 or day(loadtime)=18 or day(loadtime)=19 or day(loadtime)=20 then 1 else 0 end ) as d,
            SUM(case when day(loadtime)=21 or day(loadtime)=22 or day(loadtime)=23 or day(loadtime)=24 or day(loadtime)=25 then 1 else 0 end ) as e,
            SUM(case when day(loadtime)=26 or day(loadtime)=27 or day(loadtime)=28 or day(loadtime)=29 or day(loadtime)=30 or day(loadtime)=31 then 1 else 0 end ) as f
        from ship.alarmrecordinfo
        where year(loadtime) = #{year}
          and month(loadtime) = #{month}
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
        group by year(loadtime)
    </select>
    <select id="GetFXAlarmCount_month" resultType="com.bd.entity.TongjiModel">
        select
            SUM(case when day(loadtime)=1 or day(loadtime)=2 or day(loadtime)=3 or day(loadtime)=4 or day(loadtime)=5 then 1 else 0 end ) as a,
            SUM(case when day(loadtime)=6 or day(loadtime)=7 or day(loadtime)=8 or day(loadtime)=9 or day(loadtime)=10 then 1 else 0 end ) as b,
            SUM(case when day(loadtime)=11 or day(loadtime)=12 or day(loadtime)=13 or day(loadtime)=14 or day(loadtime)=15 then 1 else 0 end ) as c,
            SUM(case when day(loadtime)=16 or day(loadtime)=17 or day(loadtime)=18 or day(loadtime)=19 or day(loadtime)=20 then 1 else 0 end ) as d,
            SUM(case when day(loadtime)=21 or day(loadtime)=22 or day(loadtime)=23 or day(loadtime)=24 or day(loadtime)=25 then 1 else 0 end ) as e,
            SUM(case when day(loadtime)=26 or day(loadtime)=27 or day(loadtime)=28 or day(loadtime)=29 or day(loadtime)=30 or day(loadtime)=31 then 1 else 0 end ) as f
        from ship.alarmrecordinfo
        where year(loadtime) = #{year}
          and month(loadtime) = #{month}
          and type = 301
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
        group by year(loadtime)
    </select>
    <select id="GetFXAlarmCount_year" resultType="com.bd.entity.TongjiModel">
        select
            SUM(case when MONTH(loadtime)=1 or MONTH(loadtime)=2 then 1 else 0 end ) as a,
            SUM(case when MONTH(loadtime)=3 or MONTH(loadtime)=4 then 1 else 0 end ) as b,
            SUM(case when MONTH(loadtime)=5 or MONTH(loadtime)=6 then 1 else 0 end ) as c,
            SUM(case when MONTH(loadtime)=7 or MONTH(loadtime)=8 then 1 else 0 end ) as d,
            SUM(case when MONTH(loadtime)=9 or MONTH(loadtime)=10 then 1 else 0 end ) as e,
            SUM(case when MONTH(loadtime)=11 or MONTH(loadtime)=12 then 1 else 0 end ) as f
        from ship.alarmrecordinfo
        where type = 301
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
        group by year(loadtime) order by year(loadtime) desc limit 1
    </select>
    <select id="GetJJAlarmCount_month" resultType="com.bd.entity.TongjiModel">
        select
            SUM(case when day(loadtime)=1 or day(loadtime)=2 or day(loadtime)=3 or day(loadtime)=4 or day(loadtime)=5 then 1 else 0 end ) as a,
            SUM(case when day(loadtime)=6 or day(loadtime)=7 or day(loadtime)=8 or day(loadtime)=9 or day(loadtime)=10 then 1 else 0 end ) as b,
            SUM(case when day(loadtime)=11 or day(loadtime)=12 or day(loadtime)=13 or day(loadtime)=14 or day(loadtime)=15 then 1 else 0 end ) as c,
            SUM(case when day(loadtime)=16 or day(loadtime)=17 or day(loadtime)=18 or day(loadtime)=19 or day(loadtime)=20 then 1 else 0 end ) as d,
            SUM(case when day(loadtime)=21 or day(loadtime)=22 or day(loadtime)=23 or day(loadtime)=24 or day(loadtime)=25 then 1 else 0 end ) as e,
            SUM(case when day(loadtime)=26 or day(loadtime)=27 or day(loadtime)=28 or day(loadtime)=29 or day(loadtime)=30 or day(loadtime)=31 then 1 else 0 end ) as f
        from ship.alarmrecordinfo
        where year(loadtime) = #{year}
          and month(loadtime) = #{month}
          and type = 102
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
        group by year(loadtime)
    </select>
    <select id="GetJJAlarmCount_year" resultType="com.bd.entity.TongjiModel">
        select
            SUM(case when MONTH(loadtime)=1 or MONTH(loadtime)=2 then 1 else 0 end ) as a,
            SUM(case when MONTH(loadtime)=3 or MONTH(loadtime)=4 then 1 else 0 end ) as b,
            SUM(case when MONTH(loadtime)=5 or MONTH(loadtime)=6 then 1 else 0 end ) as c,
            SUM(case when MONTH(loadtime)=7 or MONTH(loadtime)=8 then 1 else 0 end ) as d,
            SUM(case when MONTH(loadtime)=9 or MONTH(loadtime)=10 then 1 else 0 end ) as e,
            SUM(case when MONTH(loadtime)=11 or MONTH(loadtime)=12 then 1 else 0 end ) as f
        from ship.alarmrecordinfo
        where type = 102
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
        group by year(loadtime) order by year(loadtime) desc limit 1
    </select>
    <select id="GetZYAlarmCount_month" resultType="com.bd.entity.TongjiModel">
        select
            SUM(case when day(loadtime)=1 or day(loadtime)=2 or day(loadtime)=3 or day(loadtime)=4 or day(loadtime)=5 then 1 else 0 end ) as a,
            SUM(case when day(loadtime)=6 or day(loadtime)=7 or day(loadtime)=8 or day(loadtime)=9 or day(loadtime)=10 then 1 else 0 end ) as b,
            SUM(case when day(loadtime)=11 or day(loadtime)=12 or day(loadtime)=13 or day(loadtime)=14 or day(loadtime)=15 then 1 else 0 end ) as c,
            SUM(case when day(loadtime)=16 or day(loadtime)=17 or day(loadtime)=18 or day(loadtime)=19 or day(loadtime)=20 then 1 else 0 end ) as d,
            SUM(case when day(loadtime)=21 or day(loadtime)=22 or day(loadtime)=23 or day(loadtime)=24 or day(loadtime)=25 then 1 else 0 end ) as e,
            SUM(case when day(loadtime)=26 or day(loadtime)=27 or day(loadtime)=28 or day(loadtime)=29 or day(loadtime)=30 or day(loadtime)=31 then 1 else 0 end ) as f
        from ship.alarmrecordinfo
        where year(loadtime) = #{year}
          and month(loadtime) = #{month}
          and type = 1
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
        group by year(loadtime)
    </select>
    <select id="GetZYAlarmCount_year" resultType="com.bd.entity.TongjiModel">
        select
            SUM(case when MONTH(loadtime)=1 or MONTH(loadtime)=2 then 1 else 0 end ) as a,
            SUM(case when MONTH(loadtime)=3 or MONTH(loadtime)=4 then 1 else 0 end ) as b,
            SUM(case when MONTH(loadtime)=5 or MONTH(loadtime)=6 then 1 else 0 end ) as c,
            SUM(case when MONTH(loadtime)=7 or MONTH(loadtime)=8 then 1 else 0 end ) as d,
            SUM(case when MONTH(loadtime)=9 or MONTH(loadtime)=10 then 1 else 0 end ) as e,
            SUM(case when MONTH(loadtime)=11 or MONTH(loadtime)=12 then 1 else 0 end ) as f
        from ship.alarmrecordinfo
        where type = 1
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
        group by year(loadtime) order by year(loadtime) desc limit 1
    </select>
    <select id="GetTXAlarmCount_month" resultType="com.bd.entity.TongjiModel">
        select
            SUM(case when day(loadtime)=1 or day(loadtime)=2 or day(loadtime)=3 or day(loadtime)=4 or day(loadtime)=5 then 1 else 0 end ) as a,
            SUM(case when day(loadtime)=6 or day(loadtime)=7 or day(loadtime)=8 or day(loadtime)=9 or day(loadtime)=10 then 1 else 0 end ) as b,
            SUM(case when day(loadtime)=11 or day(loadtime)=12 or day(loadtime)=13 or day(loadtime)=14 or day(loadtime)=15 then 1 else 0 end ) as c,
            SUM(case when day(loadtime)=16 or day(loadtime)=17 or day(loadtime)=18 or day(loadtime)=19 or day(loadtime)=20 then 1 else 0 end ) as d,
            SUM(case when day(loadtime)=21 or day(loadtime)=22 or day(loadtime)=23 or day(loadtime)=24 or day(loadtime)=25 then 1 else 0 end ) as e,
            SUM(case when day(loadtime)=26 or day(loadtime)=27 or day(loadtime)=28 or day(loadtime)=29 or day(loadtime)=30 or day(loadtime)=31 then 1 else 0 end ) as f
        from ship.BDSENDRECORD
        where year(loadtime) = #{year}
          and month(loadtime) = #{month}
        group by year(loadtime)
    </select>
    <select id="GetTXAlarmCount_year" resultType="com.bd.entity.TongjiModel">
        select
            SUM(case when MONTH(loadtime)=1 or MONTH(loadtime)=2 then 1 else 0 end ) as a,
            SUM(case when MONTH(loadtime)=3 or MONTH(loadtime)=4 then 1 else 0 end ) as b,
            SUM(case when MONTH(loadtime)=5 or MONTH(loadtime)=6 then 1 else 0 end ) as c,
            SUM(case when MONTH(loadtime)=7 or MONTH(loadtime)=8 then 1 else 0 end ) as d,
            SUM(case when MONTH(loadtime)=9 or MONTH(loadtime)=10 then 1 else 0 end ) as e,
            SUM(case when MONTH(loadtime)=11 or MONTH(loadtime)=12 then 1 else 0 end ) as f
        from ship.BDSENDRECORD
        group by year(loadtime) order by year(loadtime) desc limit 1
    </select>
    <select id="GetZFAlarmCount_month" resultType="com.bd.entity.TongjiModel">
        select
            SUM(case when day(loadtime)=1 or day(loadtime)=2 or day(loadtime)=3 or day(loadtime)=4 or day(loadtime)=5 then 1 else 0 end ) as a,
            SUM(case when day(loadtime)=6 or day(loadtime)=7 or day(loadtime)=8 or day(loadtime)=9 or day(loadtime)=10 then 1 else 0 end ) as b,
            SUM(case when day(loadtime)=11 or day(loadtime)=12 or day(loadtime)=13 or day(loadtime)=14 or day(loadtime)=15 then 1 else 0 end ) as c,
            SUM(case when day(loadtime)=16 or day(loadtime)=17 or day(loadtime)=18 or day(loadtime)=19 or day(loadtime)=20 then 1 else 0 end ) as d,
            SUM(case when day(loadtime)=21 or day(loadtime)=22 or day(loadtime)=23 or day(loadtime)=24 or day(loadtime)=25 then 1 else 0 end ) as e,
            SUM(case when day(loadtime)=26 or day(loadtime)=27 or day(loadtime)=28 or day(loadtime)=29 or day(loadtime)=30 or day(loadtime)=31 then 1 else 0 end ) as f
        from ship.LAWRECORDINFO
        where year(loadtime) = #{year}
          and month(loadtime) = #{month}
        group by year(loadtime)
    </select>
    <select id="GetZFAlarmCount_year" resultType="com.bd.entity.TongjiModel">
        select
            SUM(case when MONTH(loadtime)=1 or MONTH(loadtime)=2 then 1 else 0 end ) as a,
            SUM(case when MONTH(loadtime)=3 or MONTH(loadtime)=4 then 1 else 0 end ) as b,
            SUM(case when MONTH(loadtime)=5 or MONTH(loadtime)=6 then 1 else 0 end ) as c,
            SUM(case when MONTH(loadtime)=7 or MONTH(loadtime)=8 then 1 else 0 end ) as d,
            SUM(case when MONTH(loadtime)=9 or MONTH(loadtime)=10 then 1 else 0 end ) as e,
            SUM(case when MONTH(loadtime)=11 or MONTH(loadtime)=12 then 1 else 0 end ) as f
        from ship.LAWRECORDINFO
        group by year(loadtime) order by year(loadtime) desc limit 1
    </select>

    <select id="GetHlsImg" resultType="java.lang.String">
        SELECT IMG from ship.camerainfo where ID = #{id} and PORTID = #{portId}
    </select>

    <!-- 获取摄像头信息查询 -->
    <!-- 查询所有摄像头信息，包含关联的港口名称和区域名称 -->
    <select id="GetCameraInfo" resultType="com.bd.entity.CameraInfo">
        <!-- 查询摄像头表的所有字段，同时关联港口表获取港口名称 -->
        SELECT c.*,                           -- 摄像头表所有字段
               p.PORTNAME as portName,        -- 港口名称（来自港口表）
               c.AREANAME as areaName         -- 区域名称（来自摄像头表）
        from ship.camerainfo c                -- 摄像头信息表
        left join ship.portinfo p             -- 左连接港口信息表
        on c.portId = p.id                    -- 通过港口ID关联
    </select>

    <!-- 根据用户区域筛选摄像头 - 只返回用户有权限查看的摄像头 -->
    <select id="GetCameraInfoByUserArea" resultType="com.bd.entity.CameraInfo">
        SELECT c.*, p.PORTNAME as portName, c.AREANAME as areaName
        from ship.camerainfo c
        left join ship.portinfo p
        on c.portId = p.id
        WHERE
            -- 过滤掉无效坐标的摄像头（经度和纬度都不能为0）
            (c.LON != 0 AND c.LAT != 0)
            AND
            -- 根据用户区域筛选（使用areaName字段）
            c.AREANAME = #{userArea}
    </select>

    <!-- 根据用户多区域筛选摄像头 - 支持逗号分隔的多个区域 -->
    <select id="GetCameraInfoByUserAreas" resultType="com.bd.entity.CameraInfo">
        SELECT c.*, p.PORTNAME as portName, c.AREANAME as areaName
        from ship.camerainfo c
        left join ship.portinfo p
        on c.portId = p.id
        WHERE
            -- 过滤掉无效坐标的摄像头（经度和纬度都不能为0）
            (c.LON != 0 AND c.LAT != 0)
            AND
            -- 根据用户多区域筛选（使用IN操作符）
            c.AREANAME IN
            <foreach collection="areaList" item="area" open="(" separator="," close=")">
                #{area}
            </foreach>
    </select>

    <select id="GetCameraInfoPortCount" resultType="java.lang.Integer">
        SELECT count (distinct portId) from ship.camerainfo
    </select>
    <select id="GetCameraIndexByPortId" resultType="java.lang.String">
        SELECT HLS from SHIP.CAMERAINFO where PORTID = #{portId}
    </select>
    <select id="GetHlsById" resultType="java.lang.String">
        SELECT HLS from SHIP.CAMERAINFO where ID = #{id}
    </select>

    <select id="GetCameraInfoBySearch" resultType="com.bd.entity.CameraInfo">
        select *
        from SHIP.CAMERAINFO
        where NAME like concat('%',#{searchName},'%')
    </select>

    <select id="GetHistoryWarningInfo" resultType="com.bd.entity.BusinessManagement.AlarmRecord">
        SELECT * from
        SHIP.ALARMRECORDINFO
        where loadTime > #{startTime}
          and loadTime &lt; #{endTime}
          and type = #{type}
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
          ORDER BY state ASC, loadtime DESC
    </select>

    <select id="GetWarningShipInfoByShipId" resultType="com.bd.entity.BusinessManagement.AlarmRecord">
        select * from
            SHIP.ALARMRECORDINFO
            where loadTime > #{time}
              AND USERID in (0, #{userId})
              and STATICSHIPID = #{id}
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>;
    </select>

    <select id="GetShipInfoByPort" resultType="com.bd.entity.PortShipInfo">
        select s.id as shipId, s.SHIPNAME as shipName, p.PORTNAME as portName, s.BDID as bdId, s.MMSI as mmsi,
               s.OWNER as owner, s.LXDH as lxdh, s.BOUTSIDE as bOutSide, d.LON as lon, d.LAT as lat, d.LON as lon, d.LAT as lat
        from SHIP.SHIP_DYNAMICTION d
                 left join SHIP.SHIP_STATICINFO s on s.id = d.staticshipid
                 left join SHIP.PORTINFO p on p.id = d.INPORTID
        where d.loadTIme > #{time} and d.inportstate = 1 and p.ID = #{portId} and s.boutside in (0,1)
          and (s.shiptype = 2 or s.shipType = 61)
          and s.shipname not like '%HUCHONGYU%'
          and s.shipname not like '%HU CHONG YU%'
          and s.shipname not like '%HUFENGYU%'
          and s.shipname not like '%HU FENG YU%'
          and s.shipname not like '%HUPUYU%'
          and s.shipname not like '%HU PU YU%'
          and s.shipname not like '%HUHANG%'
        order by p.portname, s.shipname asc
    </select>

    <select id="GetAllShipInfo" resultType="com.bd.entity.PortShipInfo">
        select distinct s.id as shipId, s.SHIPNAME as shipName, p.PORTNAME as portName, s.BDID as bdId, s.MMSI as mmsi,
                        s.OWNER as owner, s.LXDH as lxdh, s.BOUTSIDE as bOutSide, d.LON as lon, d.LAT as lat,
                        (select o.CBSYRMC from SHIP.SHIP_NAME_REGIS_INFO o where o.CM = d.SHIPNAME order by o.SQRQ desc limit 1) as captain,
                        e.OUTLINEREASON as outLineReason
        from SHIP.SHIP_DYNAMICTION d
            left join SHIP.SHIP_STATICINFO s on s.id = d.staticshipid
            left join SHIP.PORTINFO p on p.id = d.INPORTID
            left join SHIP.SHIP_ENCRYPTION e on e.SHIPID = d.staticshipid
        where s.boutside = 0 and s.shiptype = 2 order by s.shipname asc
    </select>

    <select id="GetOutLineShipInfo" resultType="com.bd.entity.PortShipInfo">
        select distinct s.id as shipId, s.SHIPNAME as shipName, p.PORTNAME as portName, s.BDID as bdId, s.MMSI as mmsi,
        s.OWNER as owner,
        (select o.CBSYRMC from SHIP.SHIP_NAME_REGIS_INFO o where o.CM = d.SHIPNAME order by o.SQRQ desc limit 1) as captain,
         s.LXDH as lxdh, s.BOUTSIDE as bOutSide, d.LON as lon, d.LAT as lat, d.LOADTIME as loadTime,e.OUTLINEREASON as outLineReason
        from SHIP.SHIP_DYNAMICTION d
        left join SHIP.SHIP_STATICINFO s on s.id = d.staticshipid
        left join SHIP.PORTINFO p on p.id = d.INPORTID
        left join SHIP.SHIP_ENCRYPTION e on e.shipId = d.staticshipid
        where s.boutside = 0 and s.shiptype = 2
        <if test="type == 0">
            and d.loadtime &lt; #{time2} and d.loadtime > #{time1}
        </if>
        <if test="type == 1">
            and d.loadtime &lt; #{time1}
        </if>
        order by d.LOADTIME asc
    </select>

    <select id="GetOnLineShipInfo" resultType="com.bd.entity.PortShipInfo">
        select distinct s.id as shipId, s.SHIPNAME as shipName, p.PORTNAME as portName, s.BDID as bdId, s.MMSI as mmsi,
               s.OWNER as owner,
               (select o.CBSYRMC from SHIP.SHIP_NAME_REGIS_INFO o where o.CM = d.SHIPNAME order by o.SQRQ desc limit 1) as captain,
                s.LXDH as lxdh, s.BOUTSIDE as bOutSide, d.LON as lon, d.LAT as lat, d.LOADTIME as loadTime
        from SHIP.SHIP_DYNAMICTION d
                 left join SHIP.SHIP_STATICINFO s on s.id = d.staticshipid
                 left join SHIP.PORTINFO p on p.id = d.INPORTID
        where s.boutside = 0 and s.shiptype = 2 and d.loadtime > #{time} order by p.portname, s.shipname asc
    </select>

    <select id="GetOutPortShipInfo" resultType="com.bd.entity.PortShipInfo">
        select distinct s.id as shipId, s.SHIPNAME as shipName, p.PORTNAME as portName, s.BDID as bdId, s.MMSI as mmsi,
               s.OWNER as owner,
               (select o.CBSYRMC from SHIP.SHIP_NAME_REGIS_INFO o where o.CM = d.SHIPNAME order by o.SQRQ desc limit 1) as captain,
                s.LXDH as lxdh, s.BOUTSIDE as bOutSide, d.LON as lon, d.LAT as lat,d.INFISHAREAID as inFishAreaId
        from SHIP.SHIP_DYNAMICTION d
                 left join SHIP.SHIP_STATICINFO s on s.id = d.staticshipid
                 left join SHIP.PORTINFO p on p.id = d.INPORTID
        where s.boutside = 0 and s.shiptype = 2 and d.inportstate = 0 and d.loadtime > #{time} order by s.shipname asc
    </select>

    <select id="GetInPortShipInfo" resultType="com.bd.entity.PortShipInfo">
        select distinct s.id as shipId, s.SHIPNAME as shipName, p.PORTNAME as portName, s.BDID as bdId, s.MMSI as mmsi,
        s.OWNER as owner,
        (select o.CBSYRMC from SHIP.SHIP_NAME_REGIS_INFO o where o.CM = d.SHIPNAME order by o.SQRQ desc limit 1) as captain,
        s.LXDH as lxdh, s.BOUTSIDE as bOutSide, d.LON as lon, d.LAT as lat
        from SHIP.SHIP_DYNAMICTION d
        left join SHIP.SHIP_STATICINFO s on s.id = d.staticshipid
        left join SHIP.PORTINFO p on p.id = d.INPORTID
        where s.boutside = 0 and s.shiptype = 2 and d.inportstate = 1
        <if test="portId != 0">
            and d.InPortId = #{portId}
        </if>
        and d.loadtime > #{time} order by p.portname, s.shipname asc
    </select>

    <select id="GetWSInShangHaiShipInfo" resultType="com.bd.entity.PortShipInfo">
        select s.id as shipId, s.SHIPNAME as shipName, p.PORTNAME as portName, s.BDID as bdId, s.MMSI as mmsi,
               s.OWNER as owner, s.LXDH as lxdh, s.BOUTSIDE as bOutSide, d.LON as lon, d.LAT as lat
        from SHIP.SHIP_DYNAMICTION d
        left join SHIP.SHIP_STATICINFO s on s.id = d.staticshipid
        left join SHIP.PORTINFO p on p.id = d.INPORTID
        where (s.boutside = 1 or s.shiptype = 61)
          and (d.bInshanghai in (1, 2) or d.INPORTSTATE = 1)
          and s.shipname not like '%HUCHONGYU%'
          and s.shipname not like '%HU CHONG YU%'
          and s.shipname not like '%HUFENGYU%'
          and s.shipname not like '%HU FENG YU%'
          and s.shipname not like '%HUPUYU%'
          and s.shipname not like '%HU PU YU%'
          and s.shipname not like '%HUHANG%'
          and d.loadtime > #{time} order by s.shipname desc
    </select>

    <select id="GetWSInJinBuShipInfo" resultType="com.bd.entity.PortShipInfo">
        select s.id as shipId, s.SHIPNAME as shipName, p.PORTNAME as portName, s.BDID as bdId, s.MMSI as mmsi,
               s.OWNER as owner, s.LXDH as lxdh, s.BOUTSIDE as bOutSide, d.LON as lon, d.LAT as lat
        from SHIP.SHIP_DYNAMICTION d
        left join SHIP.SHIP_STATICINFO s on s.id = d.staticshipid
        left join SHIP.PORTINFO p on p.id = d.INPORTID
        where (s.boutside = 1 or s.shiptype = 61)
          and d.bInshanghai = 2
          and s.shipname not like '%HUCHONGYU%'
          and s.shipname not like '%HU CHONG YU%'
          and s.shipname not like '%HUFENGYU%'
          and s.shipname not like '%HU FENG YU%'
          and s.shipname not like '%HUPUYU%'
          and s.shipname not like '%HU PU YU%'
          and s.shipname not like '%HUHANG%'
          and d.loadtime > #{time} order by s.shipname desc
    </select>

    <select id="GetWSInPortShipInfo" resultType="com.bd.entity.PortShipInfo">
        select s.id as shipId, s.SHIPNAME as shipName, p.PORTNAME as portName, s.BDID as bdId, s.MMSI as mmsi,
        s.OWNER as owner, s.LXDH as lxdh, s.BOUTSIDE as bOutSide, d.LON as lon, d.LAT as lat
        from SHIP.SHIP_DYNAMICTION d
        left join SHIP.SHIP_STATICINFO s on s.id = d.staticshipid
        left join SHIP.PORTINFO p on p.id = d.INPORTID
        where (s.boutside = 1 or s.shiptype = 61) and d.inportstate = 1
        <if test="portId != 0">
            and d.InPortId = #{portId}
        </if>
        and s.shipname not like '%HUCHONGYU%'
        and s.shipname not like '%HU CHONG YU%'
        and s.shipname not like '%HUFENGYU%'
        and s.shipname not like '%HU FENG YU%'
        and s.shipname not like '%HUPUYU%'
        and s.shipname not like '%HU PU YU%'
        and s.shipname not like '%HUHANG%'
        and d.loadtime > #{time} order by p.portname, s.shipname desc
    </select>
    <select id="GetWSIn168169ShipInfo" resultType="com.bd.entity.PortShipInfo">
        select s.id as shipId, s.SHIPNAME as shipName, s.BDID as bdId, s.MMSI as mmsi,
               s.OWNER as owner, s.LXDH as lxdh, s.BOUTSIDE as bOutSide
        from SHIP.SHIP_DYNAMICTION d
        left join SHIP.SHIP_STATICINFO s on s.id = d.staticshipid
        where (s.boutside = 1 or s.shiptype = 61)
          and d.bInshanghai = 3
          and s.shipname not like '%HUCHONGYU%'
          and s.shipname not like '%HU CHONG YU%'
          and s.shipname not like '%HUFENGYU%'
          and s.shipname not like '%HU FENG YU%'
          and s.shipname not like '%HUPUYU%'
          and s.shipname not like '%HU PU YU%'
          and s.shipname not like '%HUHANG%'
          and d.loadtime > #{time} order by s.shipname asc
    </select>

    <select id="GetWarningStatistics" resultType="java.lang.Integer">
        SELECT count (*)
        from SHIP.ALARMRECORDINFO
        where loadTime > #{time}
          AND USERID in (0, #{userId})
          AND TYPE = #{type}
        <if test="model == 0">
            and MODEL = #{model}
        </if>
        <if test="model == 1">
            and MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and MODEL in (#{model}, 0)
        </if>
            ORDER BY state ASC
    </select>
    <select id="GetInPortShipCount" resultType="java.lang.Integer">
        SELECT COUNT(d.id) from ship.ship_dynamiction d
        left join SHIP.SHIP_STATICINFO s on s.id = d.staticshipid
        where d.inportid = #{portId} and d.inportstate = 1
          and d.shiptype in (2,61) and s.boutside in (0,1) and d.loadtime >= #{time}
          and s.shipname not like '%HUCHONGYU%'
          and s.shipname not like '%HU CHONG YU%'
          and s.shipname not like '%HUFENGYU%'
          and s.shipname not like '%HU FENG YU%'
          and s.shipname not like '%HUPUYU%'
          and s.shipname not like '%HU PU YU%'
          and s.shipname not like '%HUHANG%'
    </select>
    <select id="GetInportShip" resultType="com.bd.entity.ShipDynamicInfo">
        SELECT d.*, s.OWNER, s.lxdh, s.bOutside, s.bWhiteFuXiu, s.length, s.width FROM SHIP.SHIP_DYNAMICTION d
        INNER JOIN SHIP.SHIP_STATICINFO s on
            d.STATICSHIPID = s.id
        WHERE d.inportstate = 1
    </select>
    <select id="GetUpdateShip" resultType="com.bd.entity.ShipDynamicInfo">
        SELECT d.*, s.OWNER, s.lxdh, s.bOutside, s.bWhiteFuXiu, s.length, s.width FROM SHIP.SHIP_DYNAMICTION d
        INNER JOIN SHIP.SHIP_STATICINFO s on
            d.STATICSHIPID = s.id
        WHERE d.loadtime > '2022-10-23'
    </select>
    <select id="GetFishAreaShipInfo" resultType="com.bd.entity.PortShipInfo">
        select s.id as shipId, s.SHIPNAME as shipName, s.BDID as bdId, s.MMSI as mmsi,
               s.OWNER as owner, s.LXDH as lxdh, s.BOUTSIDE as bOutSide, d.inFishAreaId as fishAreaId
        from SHIP.SHIP_DYNAMICTION d
        left join SHIP.SHIP_STATICINFO s on s.id = d.staticshipid
        where s.boutside = 0 and s.shiptype = 2
        <if test="areaName != 0">
            and d.inFishAreaId = #{areaName}
        </if>
        order by s.shipname asc
    </select>
    <select id="GetOutPortCount" resultType="java.lang.Integer">
        SELECT count(o.id)
        from SHIP.OUTINPORTRECORD_2 o
        LEFT JOIN  SHIP.SHIP_STATICINFO s
        on s.ID = o.STATICSHIPID
        WHERE o.REPORTTIME &gt; #{time}
        AND s.BOUTSIDE = 0
        AND s.SHIPTYPE = 2
        AND o.state = 0
    </select>
    <select id="GetInPortCount" resultType="java.lang.Integer">
        SELECT count(o.id)
        from SHIP.OUTINPORTRECORD_2 o
        LEFT JOIN  SHIP.SHIP_STATICINFO s
        on s.ID = o.STATICSHIPID
        WHERE o.REPORTTIME &gt; #{time}
        AND s.BOUTSIDE = 0
        AND s.SHIPTYPE = 2
        AND o.state = 1
    </select>
    <select id="getTyphoonAlarm" resultType="java.lang.Integer">
        select count(*) from SHIP.ALARMRECORDINFO
        where type = 102
        and staticshipid = #{shipDynamicInfo.staticShipId}
        and loadtime &gt;= #{time}
    </select>
    <select id="GetTodayFuxiuAlarmByShipName" resultType="java.lang.Integer">
        select count(*) from SHIP.ALARMRECORDINFO where SHIPNAME = #{shipname} and loadtime >= trunc((sysdate))
    </select>
    <select id="selectShipInoutReport" resultType="com.bd.entity.FisheryBoatInOutReport">
        select id as portId, portname, shipname, TIME, crewstr, inorout
        from SHIP.PORTNODE
        where  shipName like concat('%',#{shipName},'%')
          and TIME between #{startTime} and #{endTime}
        <if test="state != 3">
            and inOrOut = #{state}
        </if>
        order by TIME desc
    </select>
    <select id="GetOutInPortRecordByShipId" resultType="com.bd.entity.OutInPortRecord">
        SELECT
            s.staticShipId,
            s.portId,
            s.shipName,
            s.bdid,
            s.state,
            s.REPORTTIME,
            s.loadTime,
            p.portName
        from SHIP.OUTINPORTRECORD_2 s
                 LEFT JOIN SHIP.PORTINFO p
                           on s.portId = p.id
                 LEFT JOIN  SHIP.SHIP_STATICINFO t
                            on t.ID = s.STATICSHIPID
        where s.STATICSHIPID = #{shipId}
        <if test="state != -1">
            and state = #{state}
        </if>
        order by s.REPORTTIME desc
        limit 1
    </select>
    <select id="select7DayRecord" resultType="com.bd.entity.FisheryBoatInOutReport">
        select id as portId, portname, shipname, TIME, crewstr, inorout
        from SHIP.PORTNODE
        where   TIME &gt;= CURRENT_DATE - 6 AND TIME &lt;= CURRENT_DATE
        order by TIME desc
    </select>
    <select id="getUnReportByTimePeriod" resultType="com.bd.entity.BusinessManagement.AlarmRecord">
        SELECT *
        FROM SHIP.ALARMRECORDINFO
        WHERE (ALARMRECORDINFO.CONTENT LIKE '%未进行进港报告%' OR ALARMRECORDINFO.CONTENT LIKE '%未进行出港报告%')
          AND STATE = 0
          AND TYPE = 401
          AND LOADTIME between #{time1} and #{time2}
        ORDER BY LOADTIME DESC
    </select>
    <select id="GetAllShanghaiShipsInfo" resultType="com.bd.entity.PortShipInfo">
        select distinct s.id as shipId,s.BOUTSIDE as bOutSide, s.SHIPNAME as shipName,
                        s.OWNER as owner, s.LXDH as lxdh,
                        s.SJOPERATOR as sjOperator,s.SJOPERATORLXDH as sjOperatorLxdh,
                        s.MAXPEOPLECOUNT as maxPeopleCount,
                        p.PORTNAME as portName,d.INPORTSTATE as inPortState
        from SHIP.SHIP_DYNAMICTION d
            left join SHIP.SHIP_STATICINFO s on s.id = d.staticshipid
            left join SHIP.PORTINFO p on p.id = d.INPORTID
            left join SHIP.SHIP_ENCRYPTION e on e.SHIPID = d.staticshipid
        where s.boutside = 0 and s.shiptype = 2 order by s.shipname asc
    </select>


</mapper>
