package com.bd.entity;

import lombok.Data;

/**
 * @description 船员培训证明
 * @date 22.08.26
 */
@Data
public class CrewTrain {

    public int id;

    /**
     * 有效标志
     */
    public String yxbz;

    /**
     * 姓名
     */
    public String xm;

    /**
     * 证书类别代码
     */
    public String zslbdm;

    /**
     * 培训机构代码
     */
    public String pxjgdm;

    /**
     * 培训证明信息唯一标识
     */
    public String pxzmxxwybs;

    /**
     * 培训出勤率
     */
    public String pxcql;

    /**
     * 培训开始日期
     */
    public String pxksrq;

    /**
     * 签发日期
     */
    public String qfrq;

    /**
     * 证书类别名称
     */
    public String zslbmc;

    /**
     * 培训结束日期
     */
    public String pxjsrq;

    /**
     * 性别
     */
    public String xb;

    /**
     * 更新时间
     */
    public String gxsj;

    /**
     * 培训期数
     */
    public String pxqs;

    /**
     * 操作时间
     */
    public String czsj;

    /**
     * 船员所属地区代码
     */
    public String cyssdqdm;

    /**
     * 身份证号码
     */
    public String sfzhm;

    /**
     * 流水号
     */
    public String lsh;

    /**
     * 插入时间
     */
    public String etl_dt;
}
