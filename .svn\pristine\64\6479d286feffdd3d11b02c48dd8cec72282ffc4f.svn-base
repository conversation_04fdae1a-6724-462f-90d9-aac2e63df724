<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="User Data Source" uuid="b6e75e25-0964-4c89-b66f-09ced77c8ea0">
      <driver-ref>java.sql.Driver</driver-ref>
      <synchronize>true</synchronize>
      <configured-by-url>true</configured-by-url>
      <jdbc-driver>dm.jdbc.driver.DmDriver</jdbc-driver>
      <jdbc-url>jdbc:dm://10.90.6.94:5236</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>