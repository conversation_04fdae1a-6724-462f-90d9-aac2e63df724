package com.bd;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.github.pagehelper.PageHelper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;

@Configuration
public class MPConfig {
    // @Bean
    // public MybatisConfiguration mybatisConfiguration(){
    //     MybatisConfiguration configuration = new MybatisConfiguration();
    //
    //     return configuration;
    // }
    @Autowired
    DataSource dataSource;

    // @Bean
    // public SqlSessionFactory SqlSessionFactory(DataSource dataSource)
    //         throws Exception {
    //     MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
    //     bean.setDataSource(dataSource);
    //     bean.setMapperLocations(new ClassPathResource("mapper/*.xml"));
    //     return bean.getObject();
    // }
}
