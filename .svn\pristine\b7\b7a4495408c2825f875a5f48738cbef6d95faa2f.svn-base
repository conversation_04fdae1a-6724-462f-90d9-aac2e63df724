<!-- 实时船舶预警-主界面 -->
<template>
    <div id="loginWindow" style="width: 100%; height: 100%; position: absolute; top: 0px; left: 0px;">
        <!-- 头部标题栏 -->
        <div id="LoginTopTitle" style="width: 100%; height: 10%; position: absolute; top: 0px; left: 0px; z-index: 5;">
            <img src="../../static/img/top-title-background.png" style="width: 100%; height: 100%; position: absolute; top: 0px; left: 0px; z-index: 3;">

        </div>
        <div id="LoginTopTitle" style="width: 100%; height: 100%; position: absolute; top: 0px; left: 0px; z-index: 4;">
            <img src="../../static/img/BG.jpeg" style="width: 100%; height: 100%; position: absolute; top: 0px; left: 0px; z-index: 3;">

        </div>

        <div id="Login" style="position: absolute; width: 100%; height: 90%; top: 8%;">
            <div id="login" style="position: absolute; width: 550px; height: 350px; background-color: rgb(9, 37, 88); background: rgba(9, 37, 88, 0.5);left: 0; right: 0; top: 0; bottom: 0; margin: auto; z-index: 11; border-radius: 8px;">
                <div style="position: absolute; width: 550px; height: 80px; top: 20px; background-color: none">
                    <span style="position: absolute; width: 350px; height: 80px; top: 35px; left: 110px; background-color: none; color: white; font-size: 25px;">上海市渔港渔船监管系统</span>
                </div>
                <div style="position: absolute; width: 550px; height: 80px; top: 100px; background-color: none;">
                    <input v-model="username" type="text" style="position: absolute; width: 300px; height: 40px; top: 25px; left: 150px; color: white; background-color: rgb(9, 37, 88); background: rgba(9, 37, 88, 0.1); border: 1px solid white; border-top-right-radius: 3px; border-bottom-right-radius: 3px;" placeholder=" 请输入用户名">
                    <div style="position: absolute; width: 40px; height: 40px; top: 25px; left: 110px; background-color: rgb(9, 37, 88); background: rgba(9, 37, 88, 0.8); border: 1px solid white; border-top-left-radius: 5px; border-bottom-left-radius: 5px;">
                        <img src="../../static/img/account.png" style="width: 100%; height: 100%; padding: 28%;">
                    </div>
                </div>
                <div style="position: absolute; width: 550px; height: 80px; top: 180px; background-color: none;">
                    <input v-model="password" id="psw" type="password" style="position: absolute; width: 300px; height: 40px; top: 15px; left: 150px; color: white; background-color: rgb(9, 37, 88); background: rgba(9, 37, 88, 0.1); border: 1px solid white; border-top-right-radius: 3px; border-bottom-right-radius: 3px;" placeholder=" 请输入密码">
                    <div style="position: absolute; width: 40px; height: 40px; top: 15px; left: 110px; background-color: rgb(9, 37, 88); background: rgba(9, 37, 88, 0.8); border: 1px solid white; border-top-left-radius: 5px; border-bottom-left-radius: 5px;">
                        <img src="../../static/img/password.png" style="width: 100%; height: 100%; padding: 28%;">
                    </div>
                </div>
                <button @click="userLogin" style="position: absolute; width: 340px; height: 40px; top: 260px; left: 110px; background-color: rgb(36 , 111, 168); border: 0; border-radius: 4px; font-size: 20px; color: white">
                    登录
                </button>
            </div>
        </div>
    </div>
</template>
  
<script>
import global from './Global.vue';
import $ from "jquery";
import getCurTime from '../../static/js/getCurTime.js'
var _this = {};

export default {
    name: 'HelloWorld',
    data () {
        return {
            msg: 'Welcome to Your Vue.js App',
            leftUpperContext: {},
            leftUpperContext1: {},
            leftLowerContext: {},
            rightUpperContext: {},
            righVedioContext: {},
            righWeatherContext: {},
            canvasContext: {},
            time: '',
            shipId: 0,
            iShipState: 0,
            pointLayerPosCamera: 0,
            ringData1: 50,
            ringData2: 50,
            portName: 'Xijiagang',
            isFirstTime: true,
            productList:[{id:"奚家港",title:"奚家港"},{id:"上海港",title:"上海港"}],
            ProductActive:"奚家港",//获取被选中的value值 默认选中的是1(北京)
            initCount: 0,
            pubKey: '',
            leftShow: true,
            rightShow: true,
            leftShowdistence: 0,
            rightShowdistence: 0,
            hls: '',
            oWebControl: null,
            lawenforceitem: 0,
            areasitem: 0,
            pwdshow: 0,
            username: "",
            password: "",
        }
    },
    beforeCreate() {
        _this = this;
        _this.username = "";
        _this.password = "";
        // alert(window.location.href);    
    },
    mounted () {

    },
    methods: {
        userLogin: function () {    
            var usernameu = _this.username;
            var passwordu = _this.password;
            // if(usernameu == "admin") {
            //     setAlertWindowShow('loginWindow', '该账号已被锁定', '', 2, 1);
            //     return
            // }
            var data = {};
            data["username"] = usernameu;
            data["password"] = passwordu;
            $.ajax({
            url: global.IP + "/web/Login",
            type:"POST",
            data: JSON.stringify(data),
            dataType: "json",
            contentType:"application/json",
            dateString: true,

            success: function(data){
                var states = data.state;
                var success = "登陆成功";
                // console.log(data);
                if (states == success) {
                    sessionStorage.setItem("isLogin", true);
                    sessionStorage.setItem("userId", data.id);
                    sessionStorage.setItem("username", data.username);
                    sessionStorage.setItem("jurisdiction", data.level);
                    sessionStorage.setItem("systemPage", 0);
                    if(sessionStorage.getItem(data.username) != null){
                        sessionStorage.setItem(data.username);
                    }
                    
                    var operationInfo = {};
                    // var myDate = new Date();
                    // var year = myDate.getFullYear();    //获取完整的年份(4位,1970-????)
                    // var month = myDate.getMonth() + 1;       //获取当前月份(0-11,0代表1月)
                    // var date = myDate.getDate();        //获取当前日(1-31)
                    // var hour = myDate.getHours();       //获取当前小时数(0-23)
                    // var minute = myDate.getMinutes();     //获取当前分钟数(0-59)
                    // var second = myDate.getSeconds();     //获取当前秒数(0-59)
                    // month = month <= 9 ? "0" + month : month;
                    // date = date <= 9 ? "0" + date : date;
                    // hour = hour <= 9 ? "0" + hour : hour;
                    // minute = minute <= 9 ? "0" + minute : minute;
                    // second = second <= 9 ? "0" + second : second;
                    // var time = year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second;
                    var timeElement = getCurTime();
                    var time = timeElement.year + "-" + timeElement.month + "-" + timeElement.date + " " + timeElement.hour + ":" + timeElement.minute + ":" + timeElement.second;
                    // console.log("测试" + time);
                    operationInfo["userId"] = data.id;
                    operationInfo["type"] = 0;
                    operationInfo["content"] = "登录成功";
                    operationInfo["loadTime"] = time;
                    //operationInfo['ipAdress'] = "***********";
                    $.ajax({
                        url: global.IP + "/web/SetUserOperationInfo",
                        type:"POST",
                        data: JSON.stringify(operationInfo),
                        dataType: "json",
                        contentType:"application/json",
                        dateString: true,

                        success: function(data){
                            
                        },
                        error: function(data){
                            
                        },
                    });

                    _this.$router.push({
                        name: "ComprehensiveSituation",
                        params: { id: "0" },
                    });

                    _this.$router.go(0);
                }
                else {
                    let state = data.state;
                    switch(state){
                        case '用户不存在':
                            setAlertWindowShow('loginWindow', state, '', 2, 1);
                            break;
                        case '密码错误':
                            setAlertWindowShow('loginWindow', state, '', 2, 1);
                            break;
                        case '输入错误密码超出限制':
                            setAlertWindowShow('loginWindow', state, '', 2, 1);
                            break;
                    }
                    _this.SetUserOperationInfo(0,"登录失败", "已记录登陆失败", "未能记录登陆失败");
                }
            },
                error: function(jqXHR){
                    setAlertWindowShow('loginWindow', '网络异常', '', 2, 1);
                    //alert("网络异常");
                },
            });
        },

    },
    beforeDestroy() {

    }
}
</script>


<style scoped>
@import '../../static/css/ul.css';
input{
    outline:none;
}

</style>