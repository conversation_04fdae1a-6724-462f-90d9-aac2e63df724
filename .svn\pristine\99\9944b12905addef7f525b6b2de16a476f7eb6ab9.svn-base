package com.bd;

import com.bd.service.*;
import com.bd.thread.DataReadThread;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.annotation.Resource;

@MapperScan("com.bd.mapper")
@SpringBootApplication
@EnableAsync
public class Application implements CommandLineRunner {

    @Resource
    private ShipService shipService;

    @Resource
    private PortService portService;

    @Resource
    private AreaService areaService;

    @Resource
    private ShipStaticInfoService shipStaticInfoService;

    @Resource
    private PeopleService peopleService;

    public static void main(String[] args){
        SpringApplication.run(Application.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        //new Thread(new DataReadThread(shipService, portService, areaService)).start();
    }

}

