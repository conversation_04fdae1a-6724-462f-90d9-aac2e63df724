<template>
    <div class="menu-dialog">
        <div class="menu-header">
            <h3>菜单</h3>
        </div>
        <div class="menu-content">
            <div class="menu-grid">
                <button class="menu-item" :class="{ 'active': isRanging }" @click="handleMenuClick('ranging')">
                    <i class="icon iconfont icon-map-ruler"></i>
                    <span>{{ isRanging ? '退出测距' : '测距' }}</span>
                </button>
                <button class="menu-item" @click="handleMenuClick('location')">
                    <i class="icon iconfont icon-dingwei"></i>
                    <span>定位</span>
                </button>
                <button class="menu-item" @click="handleMenuClick('add-annotation')">
                    <i class="icon iconfont icon-biaozhu"></i>
                    <span>添加标注</span>
                </button>
                <button class="menu-item" @click="handleMenuClick('typhoon-list')">
                    <i class="icon iconfont icon-taifeng"></i>
                    <span>台风列表</span>
                </button>
                <button class="menu-item" @click="handleMenuClick('nearby-ships')">
                    <i class="icon iconfont icon-chuanbo"></i>
                    <span>附近船舶</span>
                </button>
                <button class="menu-item" @click="handleMenuClick('ship-statistics')">
                    <i class="icon iconfont icon-tongji"></i>
                    <span>船舶统计</span>
                </button>
            </div>
        </div>
        <div class="menu-footer">
            <button class="close-btn" @click="closeDialog">关闭</button>
        </div>
    </div>
</template>

<script>
export default {
    name: 'MenuDialog',
    props: {
        isRanging: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        closeDialog() {
            this.$emit('close');
        },
        handleMenuClick(action) {
            this.$emit('menu-action', action);
            this.closeDialog();
        }
    }
}
</script>

<style scoped>
.menu-dialog {
    position: fixed;
    top: 60px;
    right: 90px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid #e8e8e8;
    width: 75px;
    color: #333;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.menu-header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #e8f4ff;
    background: #1890ff;
    border-radius: 8px 8px 0 0;
}

.menu-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
}

.menu-content {
    padding: 12.5px;
}

.menu-grid {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.menu-item {
    width: 50px;
    height: 50px;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    background-color: rgba(249, 249, 249, 1);
    border: none;
    font-size: 12px;
    cursor: pointer;
    line-height: 1.2;
    transition: all 0.2s ease;
}

.menu-item:hover {
    background-color: rgba(240, 240, 240, 1);
}

.menu-item.active {
    background: rgba(255, 77, 79, 0.1);
    color: #ff4d4f;
    border: 1px solid #ff4d4f;
}

.menu-item.active:hover {
    background: rgba(255, 77, 79, 0.2);
    color: #ff4d4f;
}

.menu-item i {
    margin-left: 0;
    margin-right: 0;
    font-size: 18px;
    margin-bottom: 4px;
}

.menu-item span {
    font-size: 12px;
    font-weight: normal;
}

.menu-footer {
    padding: 8px;
    border-top: 1px solid #f0f0f0;
    border-radius: 0 0 8px 8px;
}

.close-btn {
    width: 100%;
    height: 32px;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    color: #666;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #e6f7ff;
    border-color: #1890ff;
    color: #1890ff;
}

/* 滑入动画 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 移动设备适配 */
@media (max-width: 768px) {
    .menu-dialog {
        top: 44px;
        right: 60px;
        width: 65px;
    }

    .menu-header {
        padding: 8px;
    }

    .menu-header h3 {
        font-size: 16px;
    }

    .menu-content {
        padding: 9.5px;
    }

    .menu-item {
        width: 46px;
        height: 46px;
    }

    .menu-item i {
        font-size: 16px;
    }

    .menu-item span {
        font-size: 11px;
    }

    .menu-footer {
        padding: 6px;
    }

    .close-btn {
        height: 28px;
        font-size: 11px;
    }
}
</style>
