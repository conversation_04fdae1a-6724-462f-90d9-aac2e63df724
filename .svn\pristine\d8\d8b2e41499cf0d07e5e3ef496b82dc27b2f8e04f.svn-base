package com.bd.service;

import com.bd.entity.*;
import com.bd.entity.dto.ShipQueryDto;
import com.bd.entity.other.ExcelEntity;
import com.bd.entity.other.SpecialShip;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface ShipService {

    List<Ship> GetBDShipPosition(int sec);
    List<ShipDynamicInfo> GetBDShipPosition_ronghe(int sec);
    List<ShipDynamicInfo> GetBDShipPosition_ronghe_strTime(String time);
    List<ShipDynamicInfo> GetAllBDShipPosition_ronghe(int pageNum);
    int GetBDShipPosition_rongheCount();
    ShipDynamicInfo GetOneShipDynamicInfoById(int id);
    List<Ship> GetBDShipPositionOtherProvinces(int sec);
    void UpdateShipInPortState(int portId, int state, int ID);
    void InsertOutInPortRecord(ShipDynamicInfo ship);
    List<ShipTrack> GetShipHistoryTrackById(int id, long startTime, long endTime, int addAis, int addBd, int bOutSide);

    int selectshipidfromterm(@Param("BDID") String BDID);

    //重点跟踪
    List<ShipDynamicInfo> GetImportanceShip();
    void SetImportanceShip(int id);//staticShipId
    void DeleteImportanceShip(int id);

    void SetFuxiuWhiteShip(int id);//staticShipId
    void DeleteFuxiuWhiteShip(int id);

    List<ShipDynamicInfo> GetSpecialShip();

    List<Ship_Yuzheng> GetYuzhengShipInfo();

    //4个数量统计
    int GetRegisteredFish();
    int GetInPortFish();
    int GetOutPortFish();
    int GetWsInPortFish();

    int getAllShipCount();
    int getOnlineShipCount();
    int GetInPortFish2(int portId);
    int GetWSInShanghai();
    int GetWSInJinbu();
    int GetWSInPort(int portId);

    List<SpecialShip> GetSpecialShipToZhongtai();

    List<ShipOnlineCount> GetShipOnlineStatistics(String shipName, String startTime, String endTime, int pageNum);

    List<ShipOnlineCount> GetAllShipOnlineStatistics(String shipName, String startTime, String endTime);

    int GetShipOnlineStatisticsCount(String shipName, String startTime, String endTime);

    void SetShipWithArea(int shipId, int areaId);

    List<ShipTrack> GetPlayAreaHistoryInfo(String startTime, String endTime, String minLon, String maxLon, String minLat, String maxLat, int addAis);

    // 获取监测船舶信息
    List<JianCeShipInfo> GetJianCeShipInfo(String shipName, int pageNum);

    int GetJianCeShipInfoCount(String shipName);

    List<JianCeShipInfo> GetJianCeShipInfo_Export(String shipName);

    void DeleteJianCeShip(int id);

    void AddJianCeShip(JianCeShipInfo jianCeShipInfo);

    List<BlackAndWhiteList> GetWhiteAndBlackListInfoByType(int type);

    BlackAndWhiteList GetWhiteAndBlackListInfoById(int typeId);

    void SetWhiteOrBlackList(BlackAndWhiteList blackAndWhiteList);

    void DeleteWhiteOrBlackList(int id);

    void UpdateWhiteOrBlackList(BlackAndWhiteList blackAndWhiteList);

    ShipCount GetShipCount();

    List<BlackAndWhiteList> GetAllWhiteList();

    List<BlackAndWhiteList> GetAllBlackList();

    List<BlackAndWhiteList> GetAllWhiteAndBlackShip(String shipName, String bdId, String mmsi, int shipType, int specialType);
    List<BlackAndWhiteList> GetWhiteAndBlackShip(String shipName, String bdId, String mmsi, int shipType, int specialType, int pageNum);
    int GetWhiteAndBlackShipCount(String shipName, String bdId, String mmsi, int shipType, int specialType);

    void AddShipTerm(String name, int userId);

    void DeleteShipTerm(int id);

    List<Ship_Term> GetShipTerm(int userId);

    void InsertShipForTerm(int termId, int staticShipId);

    void DeleteShipFromTerm(int id);

    List<ShipStaticInfo_all> GetShipFromTerm(int termId);

    int CheckShipInTerm(int termId, int staticShipId);

    void InsertShipVoyage(Ship_Voyage voyage);

    List<ShipDynamicInfo> GetBDShipPosition_ronghe_strTime_thread(String getNowTimeString);

    int CheckShipBout(int shipId);

    Ship_DistributeCount GetShipdistribute(int portId);

    List<ShipDynamicInfo> GetBDShipPosition_ronghe_strTime_thread_ws(String getNowTimeString);

    void updateShipInShanghaiState(ShipDynamicInfo ship, int state);

    JianCeShipInfo GetJianCeShipInfoByShipId(int shipId);

    JianCeShipInfo GetJianCeShipInfoById(int id);

    String GetshipNameById(int shipId);

    String GetShipImgUrl(int shipId);

    String GetShipSetting(int userId);

    void UpdateShipSetting(int userId, String content);

    void SetShipSetting(int userId, String content);

    void updateFishAreaId(ShipDynamicInfo ship, int name);

    int addExcelEntity(List<ExcelEntity> entities);

    List<String> GetTrackBdidList();

    int GetStaticshipidByBdid(String bdid);

    void InsertTrack(int staticshipid, String bdid);

    int GetstaticshipidCount(String bdid);

    CheckRecord getCheckRecordByShipName(String shipName);

    List<String> GetshipNamesByIds(List<Integer> shipIds);

}
