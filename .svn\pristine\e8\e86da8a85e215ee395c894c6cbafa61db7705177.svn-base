import Vue from 'vue'
import Router from 'vue-router'
// import Unicast from '@/components/Index'
import Unicast from '@/components/Unicast'
import Multicast from '@/components/Multicast'
import Index from '@/components/Index'
import XiangxiDialog from '@/components/XiangxiDialog'
import SimpleDialog from '@/components/SimpleDialog'

Vue.use(Router)

export default new Router({
  routes: [
    {
      path: '/',
      name: 'Index',
      component: Index,
      //component: Unicast,
      //component: Multicast,
      //component: XiangxiDialog,
      //component: SimpleDialog,
    },
    {
      path: '/simple-dialog',
      name: 'SimpleDialog',
      component: () => import('@/components/SimpleDialog.vue')
    },
    {
      path: '/xiangxi-dialog',
      name: 'XiangxiDialog',
      component: () => import('@/components/XiangxiDialog.vue')
    }
  ]
})
