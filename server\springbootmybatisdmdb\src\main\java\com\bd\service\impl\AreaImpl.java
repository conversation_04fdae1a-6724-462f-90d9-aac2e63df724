package com.bd.service.impl;
import com.bd.entity.*;
import com.bd.mapper.AreaMapper;
import com.bd.service.AreaService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
@Service
public class AreaImpl implements AreaService {
    @Resource
    private AreaMapper areaMapper;
    @Override
    public List<AreaInfo> GetAreaInfo() {
        return areaMapper.GetAreaInfo();
    }
    @Override
    public List<AreaInfo> GetAreaInfoByUserId(String userId){
        return areaMapper.GetAreaInfoByUserId(userId);
    }
    @Override
    public AreaInfo GetAreaInfoById(int id) {
        return areaMapper.GetAreaInfoById(id);
    }
    @Override
    public void UpdateAreaInfo(AreaInfo areaInfo) {
        areaMapper.UpdateAreaInfo(areaInfo);
    }
    @Override
    public void DeleteAreaInfo(int id) {
        areaMapper.DeleteAreaInfo(id);
    }
    @Override
    public void InsertAreaInfo(AreaInfo areaInfo) {
        areaMapper.InsertAreaInfo(areaInfo);
    }
    @Override
    public List<AreaInfo> GetAllAreaInfo(int pageNum) {
        return areaMapper.GetAllAreaInfo(pageNum);
    }
    @Override
    public int GetAllAreaInfoCount() {
        return areaMapper.GetAllAreaInfoCount();
    }
    @Override
    public List<MarkInfo> GetAllMarkInfo(int userId) {
        return areaMapper.GetAllMarkInfo(userId);
    }
    @Override
    public int GetAllMarkInfoCount(int userId) {
        return areaMapper.GetAllMarkInfoCount(userId);
    }
    @Override
    public void InsertMarkInfo(MarkInfo markInfo) {
        areaMapper.InsertMarkInfo(markInfo);
    }
    @Override
    public void deleteMarkInfoById(int markId, int userId) {
        areaMapper.deleteMarkInfoById(markId, userId);
    }
    @Override
    public void UpdateMarkInfo(MarkInfo markInfo) {
        areaMapper.UpdateMarkInfo(markInfo);
    };
    @Override
    public MarkInfo GetMarkInfoById(int id) {
        return areaMapper.GetMarkInfoById(id);
    }
    @Override
    public List<AreaInfo> GetAreaInfoByUserIdPageNum(int userId, int pageNum) {
        return areaMapper.GetAreaInfoByUserIdPageNum(userId, pageNum);
    }
    @Override
    public int GetAreaInfoByUserIdPageNumCount(int userId) {
        return areaMapper.GetAreaInfoByUserIdPageNumCount(userId);
    }
    @Override
    public List<FishArea> GetFishAreaInfo() {
        return areaMapper.GetFishAreaInfo();
    }
    @Override
    public List<ShipSerch> GetMarkInfoByName(int userId, String keyword) {
        return areaMapper.GetMarkInfoByName(userId, keyword);
    }
}
