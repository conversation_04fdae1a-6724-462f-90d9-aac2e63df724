//自定义alert模态框
//弹出的内容
//右边按钮的文字
//左边按钮的文字
//点击右边按钮的回掉函数、
//点击左边按钮的回掉函数
//底部是否两个按钮，true 两个，false 一个
function Vvalert(txt,yesText,Notext,yescallback,nocallback,footIsTwoBtn)
{
    //$('.remindSelfModal').remove();
    //$('.remindSelfModalBody').remove();
    var strHtml = "";
    strHtml +='<div class = "remindSelfModal">';
    strHtml +='</div>';
    strHtml +='<div class = "remindSelfModalBody" style = "z-index:333; position: absolute; top: 0; left: 0; width: 100%; height: 100px; background-color: #FF0000">';
    strHtml +='<div style = "min-height:100px;display: table;width:100%;"><div style = "padding:10px;display: table-cell;color:#333;vertical-align:middle;line-height:30px;">'+txt+'</div></div>';
    strHtml +='<div class = "alertBottomBtn" style = "border-top:1px solid #ddd;width:100%;">';
    if(footIsTwoBtn){
        strHtml +='<div class = "deleteCancelBtn" style = "color:#bbb;">'+Notext+'</div>';
    }
    strHtml +='<div class = "remindSelfBtn">'+yesText+'</div>';
    strHtml +='</div>';
    strHtml +='</div>';
    $(document.body).append(strHtml);
    $('.modelRecoverAcroll').addClass('modelNoAcroll').removeClass('modelRecoverAcroll');
    $('.alertBottomBtn').each(function(){
        if($(this).children().length == 1){
            $(this).children().css('width','100%');
        }
    });

    //弹出框根据高度定位
    var remindSelfModalBodyHeight = $('.remindSelfModalBody').height();
    var windowHeight = $(window).height();
    if(windowHeight >= remindSelfModalBodyHeight){
        var topPosition = (windowHeight-remindSelfModalBodyHeight)/2;
        $('.remindSelfModalBody').css('top',topPosition);
    }else{
        $('.remindSelfModalBody').css('bottom',"50px");
    }
    //自定义alert模态框确定
    $('.remindSelfBtn').click(function(){
        $(this).parents('.remindSelfModalBody').prev('.remindSelfModal').remove();
        $(this).parents('.remindSelfModalBody').remove();
        $('#reviseRTimeModal').remove();
        if($('.remindSelfModal').length == 0){//防止ios下模态框弹出时底部按钮
          $('.modelNoAcroll').addClass('modelRecoverAcroll').removeClass('modelNoAcroll');
        }
        yescallback();
    });
    //自定义alert模态框取消
    $('.deleteCancelBtn').click(function(){
        $(this).parents('.remindSelfModalBody').prev('.remindSelfModal').remove();
        $(this).parents('.remindSelfModalBody').remove();
        $('#reviseRTimeModal').remove();
        if($('.remindSelfModal').length == 0){
         $('.modelNoAcroll').addClass('modelRecoverAcroll').removeClass('modelNoAcroll');
        }
        nocallback();
    });
};

