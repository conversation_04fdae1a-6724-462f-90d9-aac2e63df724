package com.bd.entity.ship;

import lombok.Data;

@Data
public class Ship_NameCard {
    public String user_send_user_id;
    public String dept_send_dept_id;
    public String dict_ship_business_type;
    public String shipname_app_number;
    public String dict_ship_source;
    public String dict_ship_type;
    public String norm_permit_number;
    public String permit_number;
    public String owner_name;
    public String owner_no;
    public String owner_addr;
    public String owner_tel;
    public String dist_ship_district;
    public String ship_port;
    public String ship_port_en;
    public String state_owned_enter_abb;
    public String app_ship_name;
    public String app_ship_name_en;
    public String ship_length;
    public String dict_ship_material;
    public String ship_tot_power;
    public String ship_tot_ton;
    public String dict_job_type;
    public String job_place;
    public String ship_yard;
    public String ship_yard_tel;
    public String ship_yard_addr;
    public String ship_name_old;
    public String ship_no;
    public String logout_shipregister_number;
    public String fishing_permit_number;
    public String ship_register_number;
    public String ship_create_number;
    public String ship_nationality;
    public String app_reason_other;
    public String shipname_number;
    public String print_num;
    public String dept_accept_dept_id;
    public String accept_message;
    public String user_accept_user_id;
    public String accept_time;
    public String dept_issue_dept_id;
    public String issue_message;
    public String issue_user_id;
    public String issue_date;
    public String dict_app_reason;
    public String note;
    public String ocean_permit_number;
    public String job_way;
    public String breed_number;
    public String job_way2;
    public String dict_job_type2;
    public String dict_job_way_fuzhu;
    public String log_shipname;
    public String op_state;
    public String create_by;
    public String create_time;
    public String update_by;
    public String update_time;
    public String is_deleted;

}
