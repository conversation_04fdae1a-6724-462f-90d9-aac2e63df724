// 在浏览器控制台中运行此脚本来调试摄像头显示问题

console.log('=== 摄像头调试脚本 ===');

// 1. 检查SessionStorage状态
console.log('1. SessionStorage状态:');
const keys = ['isLogin', 'userId', 'username', 'userArea', 'shipQueryAreas', 'jurisdiction'];
keys.forEach(key => {
    const value = sessionStorage.getItem(key);
    console.log(`  ${key}: ${value || '(未设置)'}`);
});

// 2. 检查用户区域
const userArea = sessionStorage.getItem('userArea');
console.log('\n2. 用户区域分析:');
if (userArea) {
    console.log(`  原始userArea: "${userArea}"`);
    if (userArea.includes(',')) {
        const areas = userArea.split(',').map(area => area.trim());
        console.log(`  解析后的区域列表: [${areas.join(', ')}]`);
        console.log(`  区域数量: ${areas.length}`);
    } else {
        console.log(`  单个区域: "${userArea}"`);
    }
} else {
    console.log('  用户区域未设置！');
}

// 3. 测试后端API
console.log('\n3. 测试后端摄像头API:');
const testAPI = () => {
    let requestUrl = "http://localhost:7001/web/GetCameraInfo";
    if (userArea && userArea.trim() !== '') {
        requestUrl += "?userArea=" + encodeURIComponent(userArea.trim());
    }
    
    console.log(`  请求URL: ${requestUrl}`);
    
    fetch(requestUrl)
        .then(response => response.json())
        .then(cameras => {
            console.log(`  后端返回摄像头数量: ${cameras.length}`);
            
            // 按区域分组
            const areaGroups = {};
            cameras.forEach(camera => {
                const area = camera.areaName || '未知区域';
                if (!areaGroups[area]) {
                    areaGroups[area] = [];
                }
                areaGroups[area].push(camera);
            });
            
            console.log('  各区域摄像头分布:');
            Object.keys(areaGroups).sort().forEach(area => {
                console.log(`    ${area}: ${areaGroups[area].length}个`);
            });
            
            // 检查是否有坐标为(0,0)的摄像头
            const invalidCameras = cameras.filter(camera => camera.lon === 0 && camera.lat === 0);
            if (invalidCameras.length > 0) {
                console.log(`  警告: 发现${invalidCameras.length}个无效坐标的摄像头`);
                invalidCameras.forEach(camera => {
                    console.log(`    - ${camera.name} (ID: ${camera.id})`);
                });
            }
        })
        .catch(error => {
            console.error('  API请求失败:', error);
        });
};

testAPI();

// 4. 检查地图API状态
console.log('\n4. 检查地图API状态:');
if (typeof API_GetLayerPosById !== 'undefined') {
    console.log('  地图API已加载');
    if (typeof global !== 'undefined' && global.g_iCameraLayerId) {
        const layerPos = API_GetLayerPosById(global.g_iCameraLayerId);
        console.log(`  摄像头图层ID: ${global.g_iCameraLayerId}`);
        console.log(`  摄像头图层位置: ${layerPos}`);
        if (layerPos <= -1) {
            console.error('  错误: 摄像头图层未找到或未初始化！');
        }
    } else {
        console.error('  错误: global.g_iCameraLayerId 未定义！');
    }
} else {
    console.error('  错误: 地图API未加载！');
}

// 5. 提供手动测试函数
console.log('\n5. 手动测试函数:');
console.log('  运行 testCameraDisplay() 来手动测试摄像头显示');

window.testCameraDisplay = function() {
    console.log('开始手动测试摄像头显示...');
    
    // 模拟前端的addCameraInfo方法
    const userArea = sessionStorage.getItem('userArea');
    let requestUrl = "http://localhost:7001/web/GetCameraInfo";
    if (userArea && userArea.trim() !== '') {
        requestUrl += "?userArea=" + encodeURIComponent(userArea.trim());
    }
    
    fetch(requestUrl)
        .then(response => response.json())
        .then(cameraInfo => {
            console.log(`获取到${cameraInfo.length}个摄像头`);
            
            let successCount = 0;
            let failCount = 0;
            
            cameraInfo.forEach((camera, index) => {
                console.log(`处理摄像头 ${index + 1}/${cameraInfo.length}: ${camera.name} (区域: ${camera.areaName})`);
                
                // 检查地图API
                if (typeof API_GetLayerPosById !== 'undefined' && global.g_iCameraLayerId) {
                    const layerPos = API_GetLayerPosById(global.g_iCameraLayerId);
                    if (layerPos > -1) {
                        console.log(`  图层检查通过，layerPos: ${layerPos}`);
                        successCount++;
                    } else {
                        console.error(`  图层检查失败，layerPos: ${layerPos}`);
                        failCount++;
                    }
                } else {
                    console.error(`  地图API或图层ID未定义`);
                    failCount++;
                }
            });
            
            console.log(`测试完成: 成功 ${successCount}, 失败 ${failCount}`);
        })
        .catch(error => {
            console.error('测试失败:', error);
        });
};

console.log('\n=== 调试脚本执行完成 ===');
