package com.bd.service.impl;

import com.bd.entity.*;
import com.bd.entity.BusinessManagement.AlarmRecord;
import com.bd.entity.dto.ShipQueryDto;
import com.bd.entity.ship.*;
import com.bd.mapper.ShipStaticInfoMapper;
import com.bd.service.ShipStaticInfoService;
import com.bd.util.Utils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.List;
@Service
public class ShipStaticInfoImpl implements ShipStaticInfoService {
    @Resource
    private ShipStaticInfoMapper shipStaticInfoMapper;
    @Override
    public List<ShipSerch> GetShipInfoByNameOrTerminalNumber(String name) {
        return shipStaticInfoMapper.GetShipInfoByNameOrTerminalNumber(name);
    }
    @Override
    public List<ShipStaticInfo_all> GetOneShipInfoByShipName(String name) {
        List<ShipStaticInfo_all> shipList = shipStaticInfoMapper.GetOneShipInfoByShipName(name);
        return shipList;
    }
    @Override
    public List<ShipStaticInfo_all> GetOneShipInfoById(int id) {
        return shipStaticInfoMapper.GetOneShipInfoById(id);
    }
    @Override
    public List<ShipStaticInfo_all> GetOneWSShipInfoById(int id) {
        return shipStaticInfoMapper.GetOneWSShipInfoById(id);
    }
    @SneakyThrows
    @Override
    public PageInfo<ShipStaticInfo_all> GetAllFishShip(ShipQueryDto queryDto) {
        PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        List<ShipStaticInfo_all> list = shipStaticInfoMapper.GetAllFishShip(queryDto);
        DateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (int i = 0; i < list.size(); i++) {
            String a = list.get(i).getLoadTime();
            String c = Utils.GetNowTimeString(3600 * 3);
            if (sf.parse(a).getTime() < sf.parse(c).getTime()) {
                list.get(i).setBOnline("离线");
            } else {
                list.get(i).setBOnline("在线");
            }
        }
        return PageInfo.of(list);
    }

    @Override
    public PageInfo<ShipStaticInfo_all> getAllFishShip(ShipQueryDto queryDto) {
        return PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize()).doSelectPageInfo(() -> {
            shipStaticInfoMapper.getAllFishShip(queryDto);
        });
    }

    @Override
    public int GetAllFishShipCount(ShipQueryDto queryDto) {
        return shipStaticInfoMapper.GetAllFishShipCount(queryDto);
    }
    @Override
    public List<ShipStaticInfo_all> GetAllFishShip_special(String shipName, String bdId, String mmsi, int shipType, int specialType, int pageNum) {
        return shipStaticInfoMapper.GetAllFishShip_special(shipName, bdId, mmsi, shipType, specialType, pageNum);
    }
    @Override
    public List<ShipStaticInfo_all> GetFishShip_special(String shipName, String bdId, String mmsi, int shipType, int specialType) {
        return shipStaticInfoMapper.GetFishShip_special(shipName, bdId, mmsi, shipType, specialType);
    }
    @Override
    public int GetAllFishShipCount_special(String shipName, String bdId, String mmsi, int shipType, int specialType) {
        return shipStaticInfoMapper.GetAllFishShipCount_special(shipName, bdId, mmsi, shipType, specialType);
    }
    @Override
    public void SetSpecialShip(int id, int specialType) {
        shipStaticInfoMapper.SetSpecialShip(id, specialType);
    }
    @Override
    public void DeleteSpecialShip(int id, int specialType) {
        shipStaticInfoMapper.DeleteSpecialShip(id, specialType);
    }
    @Override
    public int GetOnlineShipCount() {
        return shipStaticInfoMapper.GetOnlineShipCount(Utils.GetNowTimeString(3600 * 3));
    }

    @Override
    public int GetTotalShipCount() {
        return shipStaticInfoMapper.GetTotalShipCount();
    }

    @Override
    public List<ShipStaticInfo_all> GetAllInPortShip(String portName, int pageNum) {
        return shipStaticInfoMapper.GetAllInPortShip(portName, pageNum);
    }
    @Override
    public List<ShipStaticInfo_all> GetAllInPortShipCount(String portName) {
        return shipStaticInfoMapper.GetAllInPortShipCount(portName);
    }
    @Override
    public List<ShipStaticInfo_all> GetAllInPortShip_Export(String portName) {
        return shipStaticInfoMapper.GetAllInPortShip_Export(portName);
    }
    @Override
    public List<AlarmRecord> GetAllShutDownInfo(String shipName, int pageNum, int model) {
        return shipStaticInfoMapper.GetAllShutDownInfo(shipName, pageNum, model);
    }
    @Override
    public int GetAllShutDownInfoCount(String shipName, int model) {
        return shipStaticInfoMapper.GetAllShutDownInfoCount(shipName, model);
    }
    @Override
    public List<AlarmRecord> GetAllShutDownInfo_Export(String shipName, int model) {
        return shipStaticInfoMapper.GetAllShutDownInfo_Export(shipName, model);
    }
    @SneakyThrows
    @Override
    public List<ShipStaticInfo_all> GetAllFishShip_Export(String shipName, String bdId, String mmsi, int shipType, List<Long> managerIdList,
                                                          int shipLength, int shipSmallOrBig, int shipPerson, int personSmallOrBig) {
        List<ShipStaticInfo_all> list = shipStaticInfoMapper.GetAllFishShip_Export(shipName, bdId, mmsi, shipType, managerIdList, shipLength, shipSmallOrBig, shipPerson, personSmallOrBig);
        for (int i = 0; i < list.size(); i++){
            String a = list.get(i).getLoadTime();
            String c = Utils.GetNowTimeString(3600 * 3);
            DateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (sf.parse(a).getTime()< sf.parse(c).getTime()) {
                list.get(i).setBOnline("离线");
            }else {
                list.get(i).setBOnline("在线");
            }
        }
        return list;
    }
    @Override
    public List<ShipStaticInfo_card> GetOneShipCardById(int id) {
        return shipStaticInfoMapper.GetOneShipCardById(id);
    }

    @Override
    public FisheryPermitInfo GetOneShipCardTexuById(int id) {
        return shipStaticInfoMapper.GetOneShipCardTexuById(id);
    }

    @Override
    public PlayShipInfo GetPlayShipInfoById(int id) {
        return shipStaticInfoMapper.GetPlayShipInfoById(id);
    }
    @Override
    public List<Ship_Voyage> GetAllVoyageInfo(String shipName, int pageNum) {
        return shipStaticInfoMapper.GetAllVoyageInfo(shipName, pageNum);
    }
    @Override
    public int GetAllVoyageInfoCount(String shipName) {
        return shipStaticInfoMapper.GetAllVoyageInfoCount(shipName);
    }
    @Override
    public List<Ship_Voyage> GetAllVoyageInfo_Export(String shipName) {
        return shipStaticInfoMapper.GetAllVoyageInfo_Export(shipName);
    }
    @Override
    public List<Ship_WorkInfo> GetAllShipWorkInfo(String shipName,String startTime, String endTime, int bdOrAis, int pageNum) {
        return shipStaticInfoMapper.GetAllShipWorkInfo(shipName, startTime, endTime, bdOrAis, pageNum);
    }
    @Override
    public List<Ship_WorkInfo> GetAllShipWorkInfoCount(String shipName,String startTime, String endTime, int bdOrAis) {
        return shipStaticInfoMapper.GetAllShipWorkInfoCount(shipName, startTime, endTime, bdOrAis);
    }
    @Override
    public List<Ship_WorkInfo> GetAllShipWorkInfo_Export(String shipName,String startTime, String endTime, int bdOrAis) {
        return shipStaticInfoMapper.GetAllShipWorkInfo_Export(shipName, startTime, endTime, bdOrAis);
    }
    @Override
    public List<AllShipType> GetFocusShipType() {
        return shipStaticInfoMapper.GetFocusShipType();
    }
    @Override
    public List<AllShipType> GetWarningShipType(int userId, int model) {
        return shipStaticInfoMapper.GetWarningShipType(userId, Utils.GetTodayTimeString(), model);
    }
    @Override
    public List<AllShipType> GetJianCeShipType() {
        return shipStaticInfoMapper.GetJianCeShipType();
    }
    @Override
    public void InsertShipNameCard(List<Ship_NameCard> shipList) {
        shipStaticInfoMapper.InsertShipNameCard(shipList);
    }
    @Override
    public void InsertShipNation(List<Ship_Nation> shipList) {
        shipStaticInfoMapper.InsertShipNation(shipList);
    }
    @Override
    public void InsertShipNet(List<Ship_Net> shipList) {
        shipStaticInfoMapper.InsertShipNet(shipList);
    }
    @Override
    public void InsertShipPermit(List<Ship_Permit> shipList) {
        shipStaticInfoMapper.InsertShipPermit(shipList);
    }
    @Override
    public void InsertShipCheck(List<Ship_Check> shipList) {
        shipStaticInfoMapper.InsertShipCheck(shipList);
    }
    @Override
    public void InsertShipSave(List<Ship_Save> shipList) {
        shipStaticInfoMapper.InsertShipSave(shipList);
    }
    @Override
    public void ClearShipInfo() {
        shipStaticInfoMapper.ClearShipInfo();
    }
    @Override
    public ShipStaticInfo_all GetShipIdByName(String shipName) {
        return shipStaticInfoMapper.GetShipIdByName(shipName);
    }
    @Override
    public void ClearOldInfo() {
        shipStaticInfoMapper.ClearOldInfo();
    }
    @Override
    public int GetManagerCount(List<Long> managerIdList) {
        return shipStaticInfoMapper.GetManagerCount(managerIdList);
    }
    @Override
    public List<ShipSerch> GetShipInfoByShipNameList(List<String> shipNameList) {
        return shipStaticInfoMapper.GetShipInfoByShipNameList(shipNameList);
    }
    @Override
    public List<ShipStaticInfo_all> selectShipName() {
        return shipStaticInfoMapper.selectShipName();
    }
    @Override
    public List<Ship> GetCurrentInPortShips() {
        return shipStaticInfoMapper.GetCurrentInPortShips();
    }

    @Override
    public List<Port> GetCurrentAllPorts() {
        return shipStaticInfoMapper.GetCurrentAllPorts();
    }

    @Override
    public List<Ship> GetCurrentShipsByPortName(String portName) {
        return shipStaticInfoMapper.GetCurrentShipsByPortName(portName);
    }
    //获取外省船只停靠的上海港口
    @Override
    public List<Port> GetOutsideInPorts() {
        return shipStaticInfoMapper.GetOutsideInPorts();
    }

    @Override
    public List<Ship> GetOutsideShipsByPortName(String portName) {
        return shipStaticInfoMapper.GetOutsideShipsByPortName(portName);
    }

}
