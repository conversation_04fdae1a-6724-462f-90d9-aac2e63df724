<template>
    <div class="fishing-vessel-statistics">
        <div class="card-header">
            <h3>渔船分布统计</h3>
            <!-- <span class="status-text">实时船舶统计报表</span> -->
            <button class="close-btn" @click="handleClose">×</button>
        </div>
        <div class="card-body">
            <!-- 统计数据区域 -->
            <div class="statistics-container">
                <!-- 第一行统计 -->
                <div class="stats-row">
                    <div class="stat-item large" @click="showOnlineShipList">
                        <div class="stat-number">{{ shangHaiOtherPlaceShipCount }}/{{ allShipCount }}</div>
                        <div class="stat-label">沪籍在线渔船/渔船总数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ shangHaiFashingShipCount }}</div>
                        <div class="stat-label">港外渔船</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ shangHaiParkingShipCount }}</div>
                        <div class="stat-label">停港渔船</div>
                    </div>
                </div>

                <!-- 第二行统计 -->
                <div class="stats-row">
                    <div class="stat-item">
                        <div class="stat-number">{{ otherPlaceShipInShangHaiCount }}</div>
                        <div class="stat-label">沪管水域外籍渔船</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ otherPlaceShipInNoFashingCount }}</div>
                        <div class="stat-label-small">禁捕区内外省渔船</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ otherPlaceShipParkingInShangHaiCount }}</div>
                        <div class="stat-label">外省停港渔船</div>
                    </div>
                </div>

                <!-- 分类统计 -->
                <div class="category-stats">
                    <div class="category-row">
                        <span class="category-label">各渔港在港渔船</span>
                        <div class="category-controls">
                            <span class="control-item">本市渔船</span>
                            <span class="control-item">外省渔船</span>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="chart-container">
                    <div class="chart-placeholder">
                        <!-- 加载状态 -->
                        <div v-if="loading" class="loading-overlay">
                            <span class="loading-text">加载中...</span>
                        </div>

                        <!-- 图表数据 -->
                        <div v-else class="chart-bars">

                            <div v-for="(port, index) in portStats" :key="index" class="chart-bar">
                                <div class="bar-container">
                                    <div class="bar-stack"
                                         :style="{ height: getStackedBarHeight(port.local, port.foreign) + '%' }"
                                         :title="`总计: ${port.local + port.foreign} (本市: ${port.local}, 外省: ${port.foreign})`">
                                        <!-- 外省渔船 (上层) -->
                                        <div class="bar-segment foreign"
                                             :style="{ height: getForeignSegmentHeight(port.local, port.foreign) + '%' }"
                                             :title="`外省渔船: ${port.foreign}`"></div>
                                        <!-- 本市渔船 (下层) -->
                                        <div class="bar-segment local"
                                             :style="{ height: getLocalSegmentHeight(port.local, port.foreign) + '%' }"
                                             :title="`本市渔船: ${port.local}`"></div>
                                    </div>
                                </div>
                                <div class="bar-label" :title="port.name">{{ port.name }}</div>
                            </div>

                            <!-- 如果没有数据显示提示 -->
                            <div v-if="portStats.length === 0" style="text-align: center; color: #999;">
                                暂无港口数据
                            </div>
                        </div>

                    </div>
                </div>

            </div>
        </div>

    </div>
</template>

<script>
import global from './Global.vue';

export default {
    name: 'FishingVesselStatistics',
    components: {
    },
    data() {
        return {
            shangHaiOtherPlaceShipCount: 0, //沪籍在线渔船
            allShipCount: 0, //渔船总数
            shangHaiFashingShipCount: 0, //港外渔船
            shangHaiParkingShipCount: 0, //停港渔船
            otherPlaceShipInShangHaiCount: 0, //沪管水域外省渔船
            otherPlaceShipInNoFashingCount: 0, //禁捕区内外省渔船
            otherPlaceShipParkingInShangHaiCount: 0, //外省停港渔船
            portStats: [], // 原始港口船舶信息
            loading: false
        }
    },

    mounted() {
        this.initializeData();
    },

    methods: {
        // 初始化数据
        initializeData() {
            this.updateShipStatistics(0);
            this.drawColumnChart();
        },

        //更新渔船信息
        updateShipStatistics(portId) {
            this.loading = true;
            $.get(global.IP + "/web/GetShipDistribute?portId=" + portId, function (data, status) {
                this.allShipCount = data.allShipCount;
                this.shangHaiOtherPlaceShipCount = data.onlineCount;
                this.shangHaiFashingShipCount = data.otherAreaCount;
                this.shangHaiParkingShipCount = data.inPortShipCount;
                this.otherPlaceShipInShangHaiCount = data.inShanghai_ws;
                this.otherPlaceShipInNoFashingCount = data.inJinbu_ws;
                this.otherPlaceShipParkingInShangHaiCount = data.inPortShipCount_ws;
            }) .fail(function (error) {
                console.error('获取渔船统计数据失败:', error);
            }).always(() => {
                this.loading = false;
            });
        },

        // 获取港口船舶统计数据
        drawColumnChart() {
            const _this = this;
            _this.loading = true;

            $.get(global.IP + "/web/GetPortShipCount", function (data) {
                console.log("API返回的原始数据:", data);
                _this.portStats = [];

                for (var i = 0; i < data.length; i++) {
                    const portData = {
                        name: data[i].portName,
                        local: parseInt(data[i].tongdaoShipCount) || 0,
                        foreign: parseInt(data[i].wsShipCount) || 0,
                        portId: data[i].portId
                    };
                    _this.portStats.push(portData);
                }
                _this.$forceUpdate();
            }).fail(function (error) {
                console.error('获取港口船舶统计数据失败:', error);
            }).always(() => {
                _this.loading = false;
            });
        },

        // 处理关闭按钮点击
        handleClose() {
            this.$emit('close');
        },

        // 计算堆叠柱状图总高度百分比
        getStackedBarHeight(local, foreign) {
            if (this.portStats.length === 0) {
                return 0;
            }

            const total = local + foreign;
            const maxTotal = Math.max(...this.portStats.map(port => port.local + port.foreign));
            return maxTotal > 0 ? (total / maxTotal) * 100 : 0;
        },

        // 计算本市渔船段的高度百分比（相对于整个柱子）
        getLocalSegmentHeight(local, foreign) {
            const total = local + foreign;
            return total > 0 ? (local / total) * 100 : 0;
        },

        // 计算外省渔船段的高度百分比（相对于整个柱子）
        getForeignSegmentHeight(local, foreign) {
            const total = local + foreign;
            return total > 0 ? (foreign / total) * 100 : 0;
        },

        // 显示在线船舶列表
        showOnlineShipList() {
            // 发出事件，让父组件处理显示在线船舶列表
            this.$emit('show-online-ship-list');
        },
    }
}
</script>

<style scoped>
.fishing-vessel-statistics {
    position: fixed;
    bottom: 20px;
    width: 90%;
    max-width: 450px;
    left: 50%;
    transform: translateX(-50%);
    height: auto;
    max-height: 80vh;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    background-color: #2196f3;
    color: white;
    border-radius: 10px 10px 0 0;
    flex-shrink: 0;
}

.card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
}

.status-text {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 4px 8px;
    line-height: 1;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.close-btn:active {
    background-color: rgba(255, 255, 255, 0.2);
}

.card-body {
    padding: 12px 15px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.statistics-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.stats-row {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.stat-item {
    flex: 1;
    text-align: center;
    padding: 2px 8px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    height: 50px;
}

.stat-item.large {
    flex: 1.5;
    cursor: pointer;
    transition: all 0.2s ease;
}

.stat-item.large:hover {
    background-color: #e3f2fd;
    border-color: #2196f3;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
}

.stat-number {
    font-size: 18px;
    font-weight: bold;
    color: #2196f3;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 11px;
    color: #666;
    line-height: 1.2;
}

.stat-label-small {
    font-size: 10px;
    color: #666;
    margin-bottom: 4px;
}

.category-stats {
    margin-top: 10px;
}

.category-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.category-label {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.category-controls {
    display: flex;
    gap: 15px;
}

.control-item {
    font-size: 12px;
    color: #666;
    position: relative;
}

.control-item::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
    vertical-align: middle;
}

.control-item:first-child::before {
    background-color: #2196f3;
}

.control-item:last-child::before {
    background-color: #ff9800;
}

.chart-container {
    flex: 1;
    min-height: 120px;
    margin-top: 10px;
}

.chart-placeholder {
    width: 100%;
    height: 100%;
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 10px;
    display: flex;
    align-items: flex-end;
    position: relative;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(248, 249, 250, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
}

.loading-text {
    font-size: 14px;
    color: #666;
}

.no-data {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 14px;
    color: #999;
}

.chart-bars {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    width: 100%;
    height: 80px;
    gap: 0;
    overflow-x: auto;
}

.chart-bar {
    flex: none;
    width: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
}

.bar-container {
    position: relative;
    width: 100%;
    height: 60px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
}

.bar-stack {
    width: 24px;
    min-height: 4px;
    border-radius: 2px;
    display: flex;
    flex-direction: column-reverse; /* 反向排列，让本市在下面 */
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.bar-segment {
    width: 100%;
    min-height: 2px;
    position: relative;
    transition: all 0.3s ease;
}

.bar-segment.local {
    background-color: #3D6FFF;
    /* 本市渔船在下面，由于使用了column-reverse，这里是第一个 */
}

.bar-segment.foreign {
    background-color: #FEC34A;
    /* 外省渔船在上面，由于使用了column-reverse，这里是第二个 */
}

/* 鼠标悬停效果 */
.bar-stack:hover .bar-segment {
    opacity: 0.8;
}

.bar-stack:hover .bar-segment:hover {
    opacity: 1;
}

/* 保留原有样式作为备用 */
.bar {
    width: 45%;
    min-height: 2px;
    border-radius: 2px 2px 0 0;
}

.bar.local {
    background-color: #3D6FFF;
}

.bar.foreign {
    background-color: #FEC34A;
}

.bar-label {
    font-size: 9px;
    color: #666;
    margin-top: 4px;
    text-align: center;
    transform: rotate(-45deg);
    transform-origin: center;
    white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .fishing-vessel-statistics {
        width: 95%;
        max-width: none;
    }

    .stat-number {
        font-size: 16px;
    }

    .stat-label {
        font-size: 10px;
    }

    .chart-bars {
        gap: 2px;
    }

    .bar-label {
        font-size: 8px;
    }
}
</style>
