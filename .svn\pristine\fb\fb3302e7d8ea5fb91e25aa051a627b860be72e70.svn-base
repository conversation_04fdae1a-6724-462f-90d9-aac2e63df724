package com.bd.util;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;

@Slf4j
public class AesUtil {

    private static final String ALGORITHM = "AES";

    private static final String MODE = "AES/ECB/PKCS5Padding";

    private static final String KEY = "abcdefghijklmnop";

    public static void main(String[] args) {
        String s = aesDecrypt("qRqHvcrg14mfbmPoH9uWyA==");
        String ss = aesEncrypt("张三");
        System.out.println(s + "," + ss);
    }

    /**
     * 将字符串【AES加密】为base 64 code
     *
     * @param content 待加密的内容
     * @return 加密后的base 64 code
     */
    public static String aesEncrypt(String content) {
        try {
            // 创建密码器
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            kgen.init(128);
            // 初始化为加密模式的密码器
            Cipher cipher = Cipher.getInstance(MODE);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(KEY.getBytes(), ALGORITHM));

            byte[] bytes = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
            // 使用base64解码
            return Base64.encodeBase64String(bytes);
        } catch (Exception e) {
            return content;
        }

    }

    /**
     * 将base 64 code 【AES解密】为字符串
     *
     * @param encryptStr 待解密的base 64 code
     * @return 解密后的String
     */
    public static String aesDecrypt(String encryptStr) {
        try {
            if (StringUtils.isEmpty(encryptStr)) {
                return null;
            }
            // 将字符串转为byte，返回解码后的byte[]
            byte[] encryptBytes = java.util.Base64.getDecoder().decode(encryptStr);

            // 创建密码器
            KeyGenerator kgen = KeyGenerator.getInstance(ALGORITHM);
            kgen.init(128);
            // 初始化为解密模式的密码器
            Cipher cipher = Cipher.getInstance(MODE);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(KEY.getBytes(), ALGORITHM));
            byte[] decryptBytes = cipher.doFinal(encryptBytes);

            return new String(decryptBytes);
        } catch (Exception e) {
            return encryptStr;
        }

    }
}


