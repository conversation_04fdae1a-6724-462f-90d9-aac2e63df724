package com.bd.entity.ship;

import lombok.Data;

@Data
public class Ship_Check {
    public String sheet_id1;
    public String sheet_id2;
    public String check_person_names;
    public String sheet_id;
    public String dict_main_work_category;
    public String link_ship_name_ch;
    public String link_ship_owner_ch;
    public String link_ship_owner_address;
    public String link_verifyenr_no;
    public String link_ship_coding;
    public String link_build_compdate;
    public String dict_main_verifyenr_type;
    public String dict_main_verifyenr_warrant;
    public String dist_main_ship_district;
    public String main_norm_num_new;
    public String dict_main_norm_material;
    public String dict_main_norm_work_type;
    public String main_work_area;
    public String main_norm_num_power;
    public String main_norm_captain;
    public String main_norm_total_tonnage;
    public String dict_link_ship_type_ch;
    public String link_pactship_long;
    public String link_total_lenght;
    public String link_ship_wide;
    public String link_ship_deep;
    public String link_total_tonnage;
    public String link_net_tonnage;
    public String link_ship_type_code;
    public String link_design_speed;
    public String link_rated_load_num;
    public String link_total_power;
    public String link_ship_id;
    public String link_put_keel_time;
    public String link_fishing_factory_ch;
    public String link_ship_operating_ch;
    public String main_verify_start_time;
    public String main_verify_end_time;
    public String main_next_verify_time;
    public String main_verify_dept_id;
    public String main_certificate_validity;
    public String main_exhibition_validity;
    public String main_last_visa_validity_term;
    public String main_verifyenr_address_ch;
    public String main_address_ch;
    public String dict_main_next_verify_type;
    public String main_ceritificate_date;
    public String main_certificate_number;
    public String log_vessel;
    public String op_state;
    public String create_by;
    public String create_time;
    public String update_by;
    public String update_time;
    public String is_deleted;

}
