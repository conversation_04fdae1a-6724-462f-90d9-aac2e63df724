<template>
  <div class="simple-dialog">
    <!-- 关闭按钮（如需弹窗效果可用v-if控制） -->
    <span class="close-btn" title="关闭" @click="closeDialog">×</span>
    <!-- 顶部信息 -->
    <div class="header" v-if="ship">
      <span class="ship-name">{{ ship.shipname || ship.shipName || '-' }}</span>
      <span class="bd-label">北斗终端号：</span><span class="bd-value">{{ ship.bdid || '-' }}</span>
    </div>
    <div class="divider"></div>
    <!-- 基本信息 -->
    <div class="section info-grid" v-if="ship">
      <div class="item"><span class="label">船舶类型</span><span class="value">{{ ship.shiptype || '-' }}</span></div>
      <div class="item"><span class="label">MMSI</span><span class="value highlight">{{ ship.mmsi || ship.MMSI || '-' }}</span></div>
      <div class="item"><span class="label">核定人数</span><span class="value">{{ ship.personnum || '-' }}</span></div>
      <div class="item"><span class="label">最低配员</span><span class="value">{{ ship.minpersonnum || '-' }}</span></div>
      <div class="item"><span class="label">功率(千瓦)</span><span class="value">{{ ship.power || '-' }}</span></div>
      <div class="item"><span class="label">船长(米)</span><span class="value">{{ ship.length || '-' }}</span></div>
    </div>
    <div class="divider"></div>
    <!-- 船东/联系人信息 -->
    <div class="section contact-section" v-if="ship">
      <div class="contact-row"><span class="label">船舶所有人</span><span class="value">{{ ship.owner || '-' }}</span><span class="label">联系方式</span><span class="value">{{ ship.lxdh || '-' }}</span></div>
      <div class="contact-row"><span class="label">船长</span><span class="value">{{ ship.captain || '-' }}</span><span class="label">联系方式</span><span class="value">{{ ship.captainPhone || '-' }}</span></div>
      <div class="contact-row"><span class="label">应急联系人</span><span class="value">{{ ship.emergencyContact || '-' }}</span><span class="label">联系方式</span><span class="value">{{ ship.emergencyPhone || '-' }}</span></div>
      <div class="contact-row"><span class="label">实际操作者</span><span class="value">{{ ship.operator || '-' }}</span><span class="label">联系方式</span><span class="value">{{ ship.operatorPhone || '-' }}</span></div>
    </div>
    <div class="divider"></div>
    <!-- 速度、角度、终端号 -->
    <div class="section row speed-row" v-if="ship">
      <div><span class="label">速度</span><span class="value">{{ ship.speed ? ship.speed + '节' : '-' }}</span></div>
      <div><span class="label">角度</span><span class="value">{{ ship.cog ? ship.cog + '°' : '-' }}</span></div>
      <div><span class="label">报位终端号</span><span class="value">{{ ship.bdid || '-' }}</span></div>
    </div>
    <div class="divider"></div>
    <!-- 定位时间、最新位置 -->
    <div class="section time-section" v-if="ship">
      <div><span class="label">北斗定位时间</span><span class="value">{{ ship.bdTime || '-' }}</span></div>
      <div><span class="label">AIS定位时间</span><span class="value">{{ ship.aisTime || '-' }}</span></div>
      <div><span class="label">船舶最新位置</span><span class="value">{{ formatLonLat(ship.lon, ship.lat) }}</span></div>
    </div>
    <div v-if="loading" style="text-align:center;padding:20px;">加载中...</div>
    <div v-if="error" style="color:red;text-align:center;padding:20px;">{{ error }}</div>
  </div>
</template>

<script>
import global from './Global.vue'
export default {
  name: 'SimpleDialog',
  data() {
    return {
      ship: null,
      loading: false,
      error: ''
    }
  },
  created() {
    this.fetchShipInfo();
  },
  methods: {
    fetchShipInfo() {
      const shipId = this.$route.query.shipId;
      if (!shipId) {
        this.error = '未获取到船舶ID';
        return;
      }
      this.loading = true;
      this.error = '';
      // 获取船舶详情
      // 返回数组，取第一个
      this.$nextTick(() => {
        window.$ = window.$ || require('jquery');
        $.get(global.IP + '/web/GetOneShipInfoById?id=' + shipId, (data, status) => {
          if (data && data.length > 0) {
            this.ship = data[0];
          } else {
            this.error = '未找到该船舶信息';
          }
          this.loading = false;
        }).fail((msg) => {
          this.error = '获取船舶信息失败';
          this.loading = false;
        });
      });
    },
    closeDialog() {
      this.$router.back();
    },
    formatLonLat(lon, lat) {
      if (!lon || !lat) return '-';
      const lonD = parseInt(lon);
      const lonM = parseInt((lon - lonD) * 60);
      const lonS = parseInt((((lon - lonD) * 60) - lonM) * 60);
      const latD = parseInt(lat);
      const latM = parseInt((lat - latD) * 60);
      const latS = parseInt((((lat - latD) * 60) - latM) * 60);
      return lonD + '°' + lonM + "'" + lonS + '" - ' + latD + '°' + latM + "'" + latS + '"';
    }
  }
}
</script>

<style scoped>
.simple-dialog {
  background: linear-gradient(135deg, #eaf6fa 60%, #d2e6f3 100%);
  border-radius: 16px;
  padding: 26px 28px 18px 28px;
  width: 370px;
  font-size: 15px;
  color: #222;
  box-shadow: 0 4px 24px rgba(0,0,0,0.13);
  position: relative;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}
.close-btn {
  position: absolute;
  top: 12px;
  right: 16px;
  font-size: 22px;
  color: #1976d2;
  cursor: pointer;
  font-weight: bold;
  transition: color 0.2s;
}
.close-btn:hover {
  color: #d32f2f;
}
.header {
  display: flex;
  align-items: center;
  gap: 12px 18px;
  flex-wrap: wrap;
  margin-bottom: 6px;
}
.ship-name {
  font-size: 20px;
  font-weight: bold;
  color: #1976d2;
  margin-right: 10px;
}
.bd-label {
  color: #1976d2;
  font-weight: 500;
}
.bd-value {
  color: #333;
  font-weight: 500;
}
.divider {
  border-bottom: 1px dashed #b3c6d6;
  margin: 10px 0 14px 0;
}
.section {
  margin-bottom: 0px;
}
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 18px;
}
.item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.label {
  color: #1976d2;
  font-weight: 500;
  min-width: 80px;
  text-align: right;
  margin-right: 4px;
}
.value {
  color: #222;
  margin-right: 12px;
  font-weight: 400;
}
.value.highlight {
  color: #d32f2f;
  font-weight: bold;
}
.contact-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.contact-row {
  display: flex;
  align-items: center;
  gap: 6px;
}
.row.speed-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f3fafd;
  border-radius: 6px;
  padding: 6px 10px;
  margin-bottom: 0;
}
.time-section > div {
  margin-bottom: 2px;
}
@media (max-width: 420px) {
  .simple-dialog {
    width: 98vw;
    padding: 10px 2vw 10px 2vw;
    font-size: 13px;
  }
  .ship-name {
    font-size: 16px;
  }
  .label {
    min-width: 60px;
  }
}
</style>
