package com.bd.entity;

import lombok.Data;

/**
 * @description 船员考试成绩
 * @date 22.08.26
 */
@Data
public class CrewExam {

    public int id;

    /**
     * 考试证明号
     */
    public String kszmh;

    /**
     * 考试成绩信息唯一标识
     */
    public String kscjxxwybs;

    /**
     * 姓名
     */
    public String xm;

    /**
     * 考试科目代码
     */
    public String kskmdm;

    /**
     * 考试成绩分数
     */
    public String kscjfs;

    /**
     * 更新时间
     */
    public String gxsj;

    /**
     * 考点
     */
    public String kd;

    /**
     * 考试科目名称
     */
    public String kskmmc;

    /**
     * 操作时间
     */
    public String czsj;

    /**
     * 船员所属地区代码
     */
    public String cyssdqdm;

    /**
     * 准考证号
     */
    public String zkzh;

    /**
     * 身份证号码
     */
    public String sfzhm;

    /**
     * 流水号
     */
    public String lsh;

    /**
     * 是否及格
     */
    public String sfjg;

    /**
     * 插入时间
     */
    public String etl_dt;

}
