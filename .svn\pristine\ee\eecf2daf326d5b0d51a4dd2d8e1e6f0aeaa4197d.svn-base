package com.bd.config;

import org.apache.catalina.connector.Connector;
import org.springframework.boot.web.embedded.tomcat.TomcatConnectorCustomizer;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.ConfigurableWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
    @Component
    public class MyMvcConfig implements WebMvcConfigurer {
        @Override
        public void addCorsMappings(@NonNull CorsRegistry registry) {
            registry.addMapping("/**")  // 所有方法都会处理跨域请求
                    .allowedOrigins("*")    // 允许请求的域名
                    .allowedHeaders("*")    // 允许的请求头
                    .allowCredentials(true)
                    .allowedMethods("GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS");  // 允许的请求方式
            WebMvcConfigurer.super.addCorsMappings(registry);
        }

        @Override
        public void addResourceHandlers(@NonNull ResourceHandlerRegistry registry) {
            //  /images/** 映射到哪里去,前端里面的路由      file:/home/<USER>/ 表示需要访问linux系统文件所在的文件夹路径名称
            registry.addResourceHandler("/map/**").addResourceLocations("file:/opt/map/map/");
            registry.addResourceHandler("/wxMap/**").addResourceLocations("file:/opt/map/wxMap/");
            registry.addResourceHandler("/YimaMap1/**").addResourceLocations("file:/opt/map/map1/");
            registry.addResourceHandler("/YimaMap2/**").addResourceLocations("file:/opt/map/map2/");
            registry.addResourceHandler("/shipImg/**").addResourceLocations("file:/opt/ShipImg/");
            WebMvcConfigurer.super.addResourceHandlers(registry);
        }

//        @Bean
//        @ConditionalOnExpression("'${spring.profiles.active}'.equals('pro')")
//        public Connector connector(){
//            Connector connector=new Connector("org.apache.coyote.http11.Http11NioProtocol");
//            connector.setScheme("http");
//            connector.setPort(8091);
//            connector.setSecure(false);
//            connector.setRedirectPort(443);
//            return connector;
//        }
//
//        @Bean
//        @ConditionalOnExpression("'${spring.profiles.active}'.equals('pro')")
//        public TomcatServletWebServerFactory tomcatServletWebServerFactory(Connector connector){
//            TomcatServletWebServerFactory tomcat=new TomcatServletWebServerFactory(){
//                @Override
//                protected void postProcessContext(Context context) {
//                    SecurityConstraint securityConstraint=new SecurityConstraint();
//                    securityConstraint.setUserConstraint("CONFIDENTIAL");
//                    SecurityCollection collection=new SecurityCollection();
//                    collection.addPattern("/*");
//                    securityConstraint.addCollection(collection);
//                    context.addConstraint(securityConstraint);
//                }
//            };
//            tomcat.addAdditionalTomcatConnectors(connector);
//            return tomcat;
//        }
    @Bean
    public WebServerFactoryCustomizer<ConfigurableWebServerFactory> containerCustomizer(TomcatConnectorCustomizer connectorCustomizer) {
        return new WebServerFactoryCustomizer<ConfigurableWebServerFactory>() {
            @Override
            public void customize(ConfigurableWebServerFactory  container) {
                TomcatServletWebServerFactory tomcat = (TomcatServletWebServerFactory) container;
                tomcat.addConnectorCustomizers(connectorCustomizer);
            }
        };
    }

    @Bean
    public TomcatConnectorCustomizer connectorCustomizer() {
        return new TomcatConnectorCustomizer() {
            @Override
            public void customize(Connector connector) {
                connector.setAttribute("sslEnabledProtocols", "TLSv1.2");
            }
        };
    }

    }


