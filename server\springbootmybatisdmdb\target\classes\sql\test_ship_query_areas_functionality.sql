-- 测试SHIP_QUERY_AREAS功能的SQL脚本
-- 执行日期: 2025-07-29
-- 说明: 验证SHIP_QUERY_AREAS字段和相关功能是否正常工作

-- ========================================
-- 1. 验证字段是否存在
-- ========================================
PRINT '=== 验证SHIP_QUERY_AREAS字段是否存在 ===';
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'USERINFO' 
AND TABLE_SCHEMA = 'SHIP' 
AND COLUMN_NAME = 'SHIP_QUERY_AREAS';

-- ========================================
-- 2. 查看当前用户数据
-- ========================================
PRINT '=== 当前用户数据 ===';
SELECT 
    ID,
    USERNAME,
    NAME,
    LEVEL,
    USER_AREA,
    SHIP_QUERY_AREAS,
    CASE 
        WHEN LEVEL = 0 THEN '管理员'
        WHEN LEVEL = 1 THEN '普通用户'
        ELSE '其他'
    END AS USER_TYPE
FROM SHIP.USERINFO 
ORDER BY LEVEL, USERNAME;

-- ========================================
-- 3. 测试登录查询（模拟GetUser方法）
-- ========================================
PRINT '=== 测试登录查询 ===';
-- 模拟管理员登录
SELECT 
    ID as id, 
    USERNAME as username, 
    PASSWORD as password, 
    NAME as name,
    LXDH as lxdh, 
    LEVEL as level, 
    TOKEN as token, 
    BM as bm, 
    USER_AREA as userArea,
    SHIP_QUERY_AREAS as shipQueryAreas
FROM SHIP.USERINFO
WHERE username = 'admin';

-- 模拟普通用户登录（如果存在崇明用户）
SELECT 
    ID as id, 
    USERNAME as username, 
    PASSWORD as password, 
    NAME as name,
    LXDH as lxdh, 
    LEVEL as level, 
    TOKEN as token, 
    BM as bm, 
    USER_AREA as userArea,
    SHIP_QUERY_AREAS as shipQueryAreas
FROM SHIP.USERINFO
WHERE USER_AREA = '崇明'
AND USERNAME != 'admin'
ORDER BY ID
LIMIT 1;

-- ========================================
-- 4. 测试按ID查询用户（模拟GetUserInfoById方法）
-- ========================================
PRINT '=== 测试按ID查询用户 ===';
SELECT 
    ID as id, 
    USERNAME as username, 
    PASSWORD as password, 
    NAME as name,
    LXDH as lxdh, 
    LEVEL as level, 
    TOKEN as token, 
    BM as bm, 
    USER_AREA as userArea,
    SHIP_QUERY_AREAS as shipQueryAreas
FROM SHIP.USERINFO 
WHERE id = 1;

-- ========================================
-- 5. 统计权限分布
-- ========================================
PRINT '=== 权限分布统计 ===';
SELECT 
    CASE 
        WHEN SHIP_QUERY_AREAS IS NULL THEN '未设置'
        WHEN SHIP_QUERY_AREAS = '' THEN '无权限'
        ELSE SHIP_QUERY_AREAS
    END AS QUERY_AREAS,
    COUNT(*) AS USER_COUNT
FROM SHIP.USERINFO 
GROUP BY SHIP_QUERY_AREAS
ORDER BY USER_COUNT DESC;

-- ========================================
-- 6. 验证权限设置是否合理
-- ========================================
PRINT '=== 验证权限设置 ===';
-- 检查管理员是否有完整权限
SELECT 
    USERNAME,
    LEVEL,
    USER_AREA,
    SHIP_QUERY_AREAS,
    CASE 
        WHEN LEVEL = 0 AND (SHIP_QUERY_AREAS IS NULL OR SHIP_QUERY_AREAS = '') THEN '警告：管理员没有查询权限'
        WHEN LEVEL = 0 THEN '正常：管理员有查询权限'
        ELSE '普通用户'
    END AS STATUS
FROM SHIP.USERINFO 
WHERE LEVEL = 0;

-- 检查普通用户权限是否与所在区域匹配
SELECT 
    USERNAME,
    LEVEL,
    USER_AREA,
    SHIP_QUERY_AREAS,
    CASE 
        WHEN LEVEL = 1 AND USER_AREA IS NOT NULL AND USER_AREA != '' AND SHIP_QUERY_AREAS != USER_AREA THEN '警告：用户权限与所在区域不匹配'
        WHEN LEVEL = 1 AND USER_AREA IS NOT NULL AND USER_AREA != '' AND SHIP_QUERY_AREAS = USER_AREA THEN '正常：用户权限与所在区域匹配'
        WHEN LEVEL = 1 AND (USER_AREA IS NULL OR USER_AREA = '') THEN '普通用户无区域信息'
        ELSE '其他情况'
    END AS STATUS
FROM SHIP.USERINFO 
WHERE LEVEL = 1;

PRINT '=== 测试完成 ===';
