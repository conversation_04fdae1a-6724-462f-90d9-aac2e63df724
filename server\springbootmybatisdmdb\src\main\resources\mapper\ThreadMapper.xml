<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bd.mapper.ThreadMapper">
    <insert id="insertCheckRecords">
        INSERT INTO SHIP.CHECK_RECORD (
        view_id, bill_no, dept_ids, dept_cnames, enf_le_ids,
        enf_le_names, category_ids, suobj_type, suobj_cname, font_name,
        tyshxydm, legal, legal_id_type, legal_id_no, address,
        telephone, longitude, latitude, check_place, check_content,
        check_result, enf_result, other_check_content, handl_opinions,
        handl_type, suobj_opinion, sign_date, result, result_time, etl_time
        ) VALUES
        <foreach collection="list" item="record" separator=",">
            (
            #{record.VIEW_ID}, #{record.BILL_NO}, #{record.DEPT_IDS},
            #{record.DEPT_CNAMES}, #{record.ENF_LE_IDS}, #{record.ENF_LE_NAMES},
            #{record.CATEGORY_IDS}, #{record.SUOBJ_TYPE}, #{record.SUOBJ_CNAME},
            #{record.FONT_NAME}, #{record.TYSHXYDM}, #{record.LEGAL},
            #{record.LEGAL_ID_TYPE}, #{record.LEGAL_ID_NO}, #{record.ADDRESS},
            #{record.TELEPHONE}, #{record.LONGITUDE}, #{record.LATITUDE},
            #{record.CHECK_PLACE}, #{record.CHECK_CONTENT}, #{record.CHECK_RESULT},
            #{record.ENF_RESULT}, #{record.OTHER_CHECK_CONTENT}, #{record.HANDL_OPINIONS},
            #{record.HANDL_TYPE}, #{record.SUOBJ_OPINION}, #{record.SIGN_DATE},
            #{record.RESULT}, #{record.RESULT_TIME}, #{record.ETL_TIME}
            )
        </foreach>
    </insert>
    <delete id="deleteCheckRecords">
        truncate table SHIP.CHECK_RECORD
    </delete>
</mapper>