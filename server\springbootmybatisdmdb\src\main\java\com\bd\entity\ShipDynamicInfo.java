package com.bd.entity;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ShipDynamicInfo {
    private int Id;
    private int staticShipId;
    private String BDID;
    private int MMSI;
    private String SHIPNAME;
    private String callSign;
    private int LON;
    private int LAT;
    private float SPEED;
    private float COG;
    private float ROT;
    private int SHIPTYPE;
    private int INPORTID;
    private int INPORTSTATE;
    private int ReportTime;
    private String LoadTime;
    private String BdTime;
    private String AisTime;
    private String owner;
    private String lxdh;
    private int bOutside;
    private int bWhiteFuXiu;
    private String LastPosTermNo;
    private String LastPosBDId;
    private int length;
    private int width;
    private int type;
    private String outLineReason;//离线原因，未在数据库中插入

    private int bAlarm = 0;//用于外省渔船伏休报警判断
    private int bAlarm2 = 0;//用于外省渔船进入禁捕区判断
    private int bAlarm3 = 0;//用于外省渔船进入168169

    private List<Integer> alarmList_gaowei = new ArrayList<>();
    private List<Integer> alarmList_teshu = new ArrayList<>();
    private List<Integer> alarmList_zidingyi = new ArrayList<>();
    private int bAlarm_shewai_ri;
    private int bAlarm_shewai_han;
    private int bAlarm_shewai_yue;

}
