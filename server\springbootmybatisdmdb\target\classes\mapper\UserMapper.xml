<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bd.mapper.UserMapper">
    <insert id="InsertUser">
        insert into "SHIP"."USERINFO"("USERNAME", "PASSWORD", "NAME", "LEVEL", "BM")
        VALUES(#{user.username}, #{user.password}, #{user.name}, #{user.level}, #{user.bm});
    </insert>

    <select id="GetUser" resultType="com.bd.entity.UserInfo">
        SELECT ID as id, USERNAME as username, PASSWORD as password, NAME as name,
               LXDH as lxdh, LEVEL as level, TOKEN as token, BM as bm, USER_AREA as userArea,
               SHIP_QUERY_AREAS as shipQueryAreas
        FROM SHIP.USERINFO
        WHERE username = #{userInfo.username} and password = #{userInfo.password}
    </select>
    <select id="GetUsername" resultType="java.lang.Integer">
        SELECT count(*) FROM SHIP.USERINFO
        WHERE username = #{username}
    </select>

    <select id="GetAllUser" resultType="com.bd.entity.UserInfo">
        SELECT * FROM SHIP.USERINFO
            where USERNAME like concat('%',#{userName},'%')
        ORDER BY level ASC, name desc
        limit #{pageNum}, 10;
    </select>
    <select id="GetAllUserByType" resultType="com.bd.entity.UserInfo">
        SELECT * FROM SHIP.USERINFO
        where LEVEL = #{userType} and
            USERNAME like concat('%',#{userName},'%')
            limit #{pageNum}, 10;
    </select>
    <select id="GetAllUserCount" resultType="java.lang.Integer">
        SELECT count(*) FROM SHIP.USERINFO
        where USERNAME like concat('%',#{userName},'%')
    </select>
    <select id="GetAllUserCountByType" resultType="java.lang.Integer">
        SELECT count(*) FROM SHIP.USERINFO
        where LEVEL = #{userType} and
            USERNAME like concat('%',#{userName},'%')
    </select>

    <select id="GetUserInfo" resultType="com.bd.entity.UserInfo">
        SELECT * FROM SHIP.USERINFO
    </select>

    <update id="UpdateUser">

        UPDATE SHIP.USERINFO SET USERNAME = #{userInfo.username}, NAME = #{userInfo.name}, PASSWORD = #{userInfo.password}, LXDH = #{userInfo.lxdh}, LEVEL = #{userInfo.level} WHERE ID = #{userId};

    </update>
    <update id="UpdateToken">
        update ship.maptoken set token = #{access_token} where id = 1
    </update>
    <delete id="DeleteUser">
        DELETE FROM SHIP.USERINFO WHERE ID = #{id};
    </delete>

    <insert id="addUserOperationInfo">
        insert into
            SHIP.OPERATIONINFO("USERID", "TYPE", "CONTENT", "LOADTIME", "IPADRESS")
            VALUES
                   (
                    #{userOperation.userId},
                    #{userOperation.type},
                    #{userOperation.content},
                    #{userOperation.loadTime},
                    #{userOperation.ipAdress}
                    );
    </insert>

    <select id="GetUserOperation" resultType="com.bd.entity.UserOperation">
        select o.*, u.*
        from SHIP.OPERATIONINFO o
        left join SHIP.USERINFO u on o.USERID = u.ID
        where o.LOADTIME > #{startTime}
        and o.LOADTIME &lt; #{endTime}
          <if test="type != -1">
              and o.TYPE = #{type}
          </if>
        and u.USERNAME like concat('%', #{userName}, '%')
        order by o.LOADTIME desc
        limit #{pageNum}, 10;
    </select>

    <select id="GetUserOperationCount" resultType="java.lang.Integer">
        select COUNT (*)
        from SHIP.OPERATIONINFO o
        left join SHIP.USERINFO u on o.USERID = u.ID
        where o.LOADTIME > #{startTime}
        and o.LOADTIME &lt; #{endTime}
        <if test="type != -1">
            and o.TYPE = #{type}
        </if>
        and u.USERNAME like concat('%', #{userName}, '%')
    </select>

    <select id="GetUserCount" resultType="java.lang.Integer">
        select COUNT (*)
        from SHIP.OPERATIONINFO
        where LOADTIME > #{time} and TYPE = 0
    </select>
    <select id="GetUserInfoByUsername" resultType="com.bd.entity.UserInfo">
        SELECT ID as id, USERNAME as username, PASSWORD as password, NAME as name,
               LXDH as lxdh, LEVEL as level, TOKEN as token, BM as bm, USER_AREA as userArea,
               SHIP_QUERY_AREAS as shipQueryAreas
        FROM SHIP.USERINFO WHERE username = #{username} limit 1
    </select>
    <select id="GetUserInfoById" resultType="com.bd.entity.UserInfo">
        SELECT ID as id, USERNAME as username, PASSWORD as password, NAME as name,
               LXDH as lxdh, LEVEL as level, TOKEN as token, BM as bm, USER_AREA as userArea,
               SHIP_QUERY_AREAS as shipQueryAreas
        FROM SHIP.USERINFO WHERE id = #{id} limit 1
    </select>
    <select id="GetMapToken" resultType="java.lang.String">
        SELECT token from ship.maptoken where id = 1
    </select>

    <select id="GetErrorCount" resultType="java.lang.Integer">
        SELECT ERRORCOUNT FROM SHIP.USERINFO WHERE USERNAME = #{name}
    </select>

    <update id="SetErrorCount">
        UPDATE SHIP.USERINFO
                SET ERRORCOUNT = #{count}
                WhERE USERNAME = #{name}
    </update>

    <insert id="SetUserSetting">
        insert into ship.usersettinginfo(USERID, SETTING, TYPE, CONTENT)
        values (
                #{userSettingInfo.userId},
                #{userSettingInfo.setting},
                #{userSettingInfo.type},
                #{userSettingInfo.content}
               )
    </insert>

    <update id="UpdateUserSetting">
        <selectKey resultType="java.lang.Integer" keyProperty="result" order="BEFORE">
            SELECT COUNT(*) AS result FROM ship.usersettinginfo WHERE  USERID = #{userSettingInfo.userId} and setting = #{userSettingInfo.setting}
        </selectKey>
        <if test="result >= 1">
            update ship.usersettinginfo set TYPE = #{userSettingInfo.type},content = #{userSettingInfo.content}
            where USERID = #{userSettingInfo.userId} and setting = #{userSettingInfo.setting}
        </if>
        <if test="result == 0">
            insert into SHIP.USERSETTINGINFO ( USERID, SETTING, TYPE, CONTENT) values(
            #{userSettingInfo.userId},
            #{userSettingInfo.setting},
            #{userSettingInfo.type},
            #{userSettingInfo.content})
        </if>

    </update>
    <update id="UpdateOpenCenterToken">
        update ship.opencentertoken set token = #{token} where id = 1;
    </update>

    <delete id="DeleteUserSetting">
        DELETE FROM SHIP.usersettinginfo WHERE USERID = #{userId} and setting = #{setting};
    </delete>
    <delete id="deletePortNode">
        delete from SHIP.PORTNODE;
    </delete>

    <select id="GetUserSetting" resultType="com.bd.entity.UserSettingInfo">
        select * from SHIP.usersettinginfo where USERID = #{userId}
    </select>

    <select id="isCheckAccount" resultType="com.bd.entity.UserInfo">
        select * from SHIP.USERINFO where username = #{sendName}
    </select>
    <select id="GetOpenCenterToken" resultType="java.lang.String">
        SELECT token from ship.OPENCENTERTOKEN where id = 1
    </select>

    <insert id="SendWarningToOtherAccount">
        insert into ship.PUSHINFORMATION(SENDID, RECEIVEID, WARNINGID)
        values (
                #{sendId},
                #{receiveId},
                #{warningId}
               )
    </insert>

    <select id="GetWarningToOtherAccount" resultType="com.bd.entity.PushInformation">
        select a.CONTENT as warningInfo, a.TYPE as warningType, p.STATUS as isRead from SHIP.ALARMRECORDINFO a
            left join SHIP.PUSHINFORMATION p
            on p.WARNINGID = a.ID
            where p.RECEIVEID = #{userId}
        <if test="model == 0">
            and a.MODEL = #{model}
        </if>
        <if test="model == 1">
            and a.MODEL in (#{model}, 0)
        </if>
        <if test="model == 2">
            and a.MODEL in (#{model}, 0)
        </if>
    </select>
    <select id="GetOpenCenterToken_HIK" resultType="java.lang.String">
        SELECT token from ship.OPENCENTERTOKEN where id = 2
    </select>
  <select id="getUserPermissionAreas" resultType="java.lang.String">
    SELECT SHIP_QUERY_AREAS from ship.USERINFO where id = #{id}
  </select>

  <update id="SetWarningToOtherAccountIsRead">
        update SHIP.PUSHINFORMATION
        set STATUS = 1
        where RECEIVEID = #{userId};
    </update>
    <update id="UpdateOpenCenterToken_HIK">
        update ship.opencentertoken set token = #{token} where id = 2;
    </update>

</mapper>
