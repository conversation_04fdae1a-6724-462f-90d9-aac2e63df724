import getCurTime from "./getCurTime";
import * as XLSX from 'xlsx';
// excelName：表格名称，最后会拼接时间
// titleList：需要导出的表格的表头数组
// exportInfo：需要导出的表格信息-数组
// titleShunXu：需要导出的部分字段以及顺序
export default function exportExcel(excelName, titleList, exportInfo, titleShunXu){
    var timeElement = getCurTime();
    var filename = timeElement.year + "-" + timeElement.month + "-" + timeElement.date + "" + timeElement.hour + "-" + timeElement.minute + "-" + timeElement.second;
    // console.log(exportInfo);
    var bob = [];
    // 设置表头
    bob.push(titleList);
    for(var i = 0; i < exportInfo.length; i++) {
        var info = new Array();
        // var curContent = "";
        // console.log(exportInfo[i].length);
        for(let j=0; j<titleShunXu.length;j++){
            // console.log(exportInfo[i]);
            // console.log(exportInfo[i][titleShunXu[j]]);

            info.push(exportInfo[i][titleShunXu[j]])
            // console.log(p);
        }
        // for(let p in exportInfo[i]){
        //     console.log(p);
        // }
        // console.log('---------------------');
        // info = [
        //     exportInfo[i].shipName,
        //     ((exportInfo[i].portName == "-") ? "" : exportInfo[i].portName),
        //     ((exportInfo[i].owner == "-") ? "" : exportInfo[i].owner),
        //     ((exportInfo[i].phone == "-") ? "" : exportInfo[i].phone),
        // ];
        bob.push(info);
    }
    
    var sheet = XLSX.utils.aoa_to_sheet(bob);
    // openDownloadDialog(sheet2blob(sheet), '导出.xlsx');
    openDownloadDialog(sheet2blob(sheet), excelName + filename + '.xlsx');
}

function openDownloadDialog (url, saveName) {
    if (typeof url == 'object' && url instanceof Blob) {
        url = URL.createObjectURL(url); // 创建blob地址
    }
    var aLink = document.createElement('a');
    aLink.href = url;
    aLink.download = saveName || ''; // HTML5新增的属性，指定保存文件名，可以不要后缀，注意，file:///模式下不会生效
    var event;
    if (window.MouseEvent) event = new MouseEvent('click');
    else {
        event = document.createEvent('MouseEvents');
        event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
    }
    aLink.dispatchEvent(event);
}

// 将一个sheet转成最终的excel文件的blob对象，然后利用URL.createObjectURL下载
function sheet2blob(sheet, sheetName) {
    sheetName = sheetName || 'sheet1';
    var workbook = {
        SheetNames: [sheetName],
        Sheets: {}
    };
    workbook.Sheets[sheetName] = sheet; // 生成excel的配置项

    var wopts = {
        bookType: 'xlsx', // 要生成的文件类型
        bookSST: false, // 是否生成Shared String Table，官方解释是，如果开启生成速度会下降，但在低版本IOS设备上有更好的兼容性
        type: 'binary'
    };
    var wbout = XLSX.write(workbook, wopts);
    var blob = new Blob([s2ab(wbout)], {
        type: "application/octet-stream"
    }); // 字符串转ArrayBuffer
    function s2ab(s) {
        var buf = new ArrayBuffer(s.length);
        var view = new Uint8Array(buf);
        for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
        return buf;
    }
    return blob;
}