<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bd.mapper.ShipMapper">

    <resultMap id="BaseResultMap" type="com.bd.entity.Ship">
        <result column="ID" jdbcType="INTEGER" property="id" />
        <result column="SHIPID" jdbcType="INTEGER" property="shipId" />
        <result column="SHIPNAME" jdbcType="VARCHAR" property="shipName" />
        <result column="TERMNO" jdbcType="VARCHAR" property="termNo" />
        <result column="LOADTIME" jdbcType="VARCHAR" property="loadTime" />
        <result column="LON" jdbcType="INTEGER" property="lon" />
        <result column="LAT" jdbcType="INTEGER" property="lat" />
        <result column="SPEED" jdbcType="INTEGER" property="speed" />
        <result column="AZIMUTH" jdbcType="INTEGER" property="head" />
        <result column="RPTTIME" jdbcType="INTEGER" property="reportTime" />
        <result column="CBLX" jdbcType="VARCHAR" property="shipType" />
        <result column="PORTNAME" jdbcType="VARCHAR" property="portName" />
        <result column="OUTINPORT" jdbcType="INTEGER" property="inPortState" />
    </resultMap>

    <select id="GetBDShipPosition" parameterType="int" resultMap="BaseResultMap">
        SELECT distinct
            p.ID,
            p.SHIPID,
            p.TERMNO,
            p.LOADTIME,
            p.RPTTIME,
            p.LONGITUDE*10 as LON,
            p.LATITUDE*10 as LAT,
            p.SPEED,
            p.AZIMUTH,
            p.PORTNAME,
            p.OUTINPORT,
            f.SHIPNAME,
            s.CBLX
        FROM SHIP.BDTERM_LAST_POS p
        left JOIN SHIP.FISHER f ON p.SHIPID = f.SHIPID
        left JOIN SHIP.SHIP_SHANGHAISHIPINFO s ON f.SHIPNAME = s.CM
        where p.RPTTIME &gt; #{sec}
        order by p.RPTTIME desc
    </select>

    <select id="GetBDShipPositionOtherProvinces" resultType="com.bd.entity.Ship" resultMap="BaseResultMap">
        select
            ID,
            TERMNO,
            SHIPNAME,
            POSTIME as RPTTIME,
            LONGITUDE as LON,
            LATITUDE as LAT,
            SPEED,
            AZIMUTH,
            LOADTIME,
            '外省' as CBLX
        from SHIP.BDTERM_OTHERPROVINCES
        order by POSTIME desc
        limit 1500
    </select>

    <select id="GetShipHistoryTrackById" resultType="com.bd.entity.ShipTrack" >
        <if test="bOutSide == 0">
            <if test="addBd != 0 and addAis != 0">
                select
                LON,
                LAT,
                SPEED,
                COG as head,
                LOADTIME
                from SHIP.SHIP_HISTORYTRACKPOINT_TMP
                where
                REPORTTIME &lt; #{endTime} and
                REPORTTIME &gt; #{startTime} and
                STATICSHIPID = #{id}

                union all

                select
                LON,
                LAT,
                SPEED,
                heading as head,
                LOADTIME
                from SHIP.SHIP_HISTORYTRACKPOINT_AIS
                where
                timestamp &lt; #{endTime} and
                timestamp &gt; #{startTime} and
                STATICSHIPID = #{id}

                order by loadtime asc
            </if>
            <if test="addBd == 0 and addAis != 0">
                select
                LON,
                LAT,
                SPEED,
                heading as head,
                LOADTIME
                from SHIP.SHIP_HISTORYTRACKPOINT_AIS
                where
                timestamp &lt; #{endTime} and
                timestamp &gt; #{startTime} and
                STATICSHIPID = #{id}
                order by loadtime asc
            </if>
            <if test="addBd != 0 and addAis == 0">
                select
                LON,
                LAT,
                SPEED,
                COG as head,
                LOADTIME
                from SHIP.SHIP_HISTORYTRACKPOINT_TMP
                where
                REPORTTIME &lt; #{endTime} and
                REPORTTIME &gt; #{startTime} and
                STATICSHIPID = #{id}
                order by loadtime asc
            </if>
        </if>
        <if test="bOutSide != 0">
            <if test="addBd != 0 and addAis != 0">
                select
                LON,
                LAT,
                SPEED,
                COG as head,
                LOADTIME
                from SHIP.SHIP_HISTORYTRACKPOINT_HQ
                where
                REPORTTIME &lt; #{endTime} and
                REPORTTIME &gt; #{startTime} and
                STATICSHIPID = #{id}

                union all

                select
                LON,
                LAT,
                SPEED,
                heading as head,
                LOADTIME
                from SHIP.SHIP_HISTORYTRACKPOINT_AIS
                where
                timestamp &lt; #{endTime} and
                timestamp &gt; #{startTime} and
                STATICSHIPID = #{id}

                order by loadtime asc
            </if>
            <if test="addBd == 0 and addAis != 0">
                select
                LON,
                LAT,
                SPEED,
                heading as head,
                LOADTIME
                from SHIP.SHIP_HISTORYTRACKPOINT_AIS
                where
                timestamp &lt; #{endTime} and
                timestamp &gt; #{startTime} and
                STATICSHIPID = #{id}
                order by loadtime asc
            </if>
            <if test="addBd != 0 and addAis == 0">
                select
                LON,
                LAT,
                SPEED,
                COG as head,
                LOADTIME
                from SHIP.SHIP_HISTORYTRACKPOINT_HQ
                where
                REPORTTIME &lt; #{endTime} and
                REPORTTIME &gt; #{startTime} and
                STATICSHIPID = #{id}
                order by loadtime asc
            </if>
        </if>
    </select>

    <select id="GetPlayAreaHistoryInfo" resultType="com.bd.entity.ShipTrack" >
        select h.STATICSHIPID as id, h.LON, h.LAT, h.SPEED, h.COG as head, h.REPORTTIME as loadTime
        from SHIP.SHIP_HISTORYTRACKPOINT_TMP h
        where h.REPORTTIME > #{startTime} and h.REPORTTIME &lt; #{endTime}
        and h.LON > #{minLon} and h.LON &lt; #{maxLon}
        and h.Lat > #{minLat} and h.LAT &lt; #{maxLat}

        <if test="addAis != 0">
            union all
            select h.STATICSHIPID as id, h.LON, h.LAT, h.SPEED, h.heading as head, h.timestamp as loadTime
            from SHIP.SHIP_HISTORYTRACKPOINT_AIS h
            where h.timestamp > #{startTime} and h.timestamp &lt; #{endTime}
            and h.LON > #{minLon} and h.LON &lt; #{maxLon}
            and h.Lat > #{minLat} and h.LAT &lt; #{maxLat}

        </if>
        order by loadTime asc
    </select>

    <select id="selectshipidfromterm" resultType="java.lang.Integer">
        SELECT SHIPID from SHIP.FISHER_AND_TERM WHERE TERMNO = #{BDID}
    </select>

    <select id="GetBDShipPosition_ronghe" resultType="com.bd.entity.ShipDynamicInfo">
        SELECT d.*, s.OWNER, s.lxdh, s.bOutside FROM SHIP.SHIP_DYNAMICTION d
        INNER JOIN SHIP.SHIP_STATICINFO s on
        d.STATICSHIPID = s.id
        WHERE d.REPORTTIME &gt; #{sec}
        order by d.REPORTTIME desc
    </select>
    <select id="GetBDShipPosition_ronghe_strTime" resultType="com.bd.entity.ShipDynamicInfo">
        SELECT d.*, s.OWNER, s.lxdh, s.bOutside, s.bWhiteFuXiu, s.length, s.width FROM SHIP.SHIP_DYNAMICTION d
        INNER JOIN SHIP.SHIP_STATICINFO s on
            d.STATICSHIPID = s.id
        WHERE s.BOUTSIDE = 0
        and s.SHIPTYPE = 2
        order by d.LOADTIME desc
    </select>
    <select id="GetImportanceShip" resultType="com.bd.entity.ShipDynamicInfo">
        SELECT d.*, s.OWNER, s.lxdh FROM SHIP.SHIP_DYNAMICTION d
        INNER JOIN SHIP.SHIP_STATICINFO s on
            d.STATICSHIPID = s.id
        WHERE
            s.BFOCUS = 1
    </select>
    <select id="GetYuzhengShipInfo" resultType="com.bd.entity.Ship_Yuzheng">
        SELECT * FROM SHIP.SHIP_STATICINFO WHERE SHIPNAME like '%渔政%' and boutside = 0;
    </select>

    <update id="UpdateShipInPortState">
        UPDATE SHIP.SHIP_DYNAMICTION SET INPORTID = #{portId}, INPORTSTATE = #{state} where ID = #{ID}
    </update>

    <update id="SetImportanceShip">
        UPDATE SHIP.SHIP_STATICINFO SET BFOCUS = 1 where id = #{id}
    </update>
    <update id="DeleteImportanceShip">
        UPDATE SHIP.SHIP_STATICINFO SET BFOCUS = 0 where id = #{id}
    </update>
    <update id="SetFuxiuWhiteShip">
        UPDATE SHIP.SHIP_STATICINFO SET BWHITEFUXIU = 1 where id = #{id}
    </update>
    <update id="DeleteFuxiuWhiteShip">
        UPDATE SHIP.SHIP_STATICINFO SET BWHITEFUXIU = 0 where id = #{id}
    </update>

    <insert id="InsertOutInPortRecord" parameterType="com.bd.entity.ShipDynamicInfo">
        insert into SHIP.OUTINPORTRECORD_2
            (STATICSHIPID,PORTID,SHIPNAME,BDID,STATE,REPORTTIME,LOADTIME)
        VALUES(
               #{ship.staticShipId},
               #{ship.INPORTID},
               #{ship.SHIPNAME},
               #{ship.BDID},
               #{ship.INPORTSTATE},
               #{ship.ReportTime},
               #{ship.LoadTime});
    </insert>

    <select id="GetRegisteredFish" resultType="java.lang.Integer">
        select count(*) from (select count(cm) from ship.ship_shanghaishipinfo group by cm)
    </select>
    <select id="GetInPortFish" resultType="java.lang.Integer">
        select count(*) from ship.ship_dynamiction d
        inner join ship.ship_staticinfo s on d.staticshipid = s.id
        where s.boutside = 0 and d.inportstate = 1 and d.loadtime > #{time}
    </select>
    <select id="GetOutPortFish" resultType="java.lang.Integer">
        select count(*) from ship.ship_dynamiction d
        inner join ship.ship_staticinfo s on d.staticshipid = s.id
        where s.boutside = 0 and s.shiptype = 2 and d.inportstate = 0 and d.loadtime > #{time}
    </select>
    <select id="GetWsInPortFish" resultType="java.lang.Integer">
        select count(*) from ship.ship_dynamiction d
        inner join ship.ship_staticinfo s on d.staticshipid = s.id
        where (s.boutside = 1 or s.shiptype = 61) and d.inportstate = 1 and d.loadtime > #{time}
    </select>
    <select id="GetOneShipDynamicInfoById" resultType="com.bd.entity.ShipDynamicInfo">
        SELECT d.*, s.OWNER, s.lxdh, s.bOutside, s.SHIPTYPE as type, s.WIDTH as width, s.LENGTH as length FROM SHIP.SHIP_DYNAMICTION d
        INNER JOIN SHIP.SHIP_STATICINFO s
            on d.STATICSHIPID = s.id
        WHERE d.STATICSHIPID = #{id}
    </select>
    <select id="GetSpecialShip" resultType="com.bd.entity.ShipDynamicInfo">
        SELECT d.*, s.OWNER, s.lxdh FROM SHIP.SHIP_DYNAMICTION d
        INNER JOIN SHIP.SHIP_STATICINFO s on
            d.STATICSHIPID = s.id
        WHERE
            s.BWhiteFuXiu = 1
    </select>
    <select id="GetBDShipPosition_rongheCount" resultType="java.lang.Integer">
        SELECT count(d.id) FROM SHIP.SHIP_DYNAMICTION d
        INNER JOIN SHIP.SHIP_STATICINFO s on
            d.STATICSHIPID = s.id
        WHERE s.bOutside = 0
        order by d.LOADTIME desc
    </select>

    <select id="GetAllBDShipPosition_ronghe" resultType="com.bd.entity.ShipDynamicInfo">
        SELECT d.*, s.OWNER, s.lxdh, s.bOutside, s.bWhiteFuXiu FROM SHIP.SHIP_DYNAMICTION d
        INNER JOIN SHIP.SHIP_STATICINFO s on
            d.STATICSHIPID = s.id
        WHERE s.bOutside = 0
        order by d.id desc
            limit #{pageNum}, 400;
    </select>
    <select id="GetSpecialShipToZhongtai" resultType="com.bd.entity.other.SpecialShip">
        SELECT d.*, s.* FROM SHIP.SHIP_JIANCESHIP d
        INNER JOIN SHIP.SHIP_STATICINFO s on
            d.STATICSHIPID = s.id
    </select>
    <select id="GetShipOnlineStatistics" resultType="com.bd.entity.ShipOnlineCount">
        SELECT s.shipName, s.bdId, s.mmsi, s.owner, s.lxdh, s.lxdz, s.dw, s.shipType, s.length, s.width FROM SHIP.SHIP_DYNAMICTION d
        INNER JOIN SHIP.SHIP_STATICINFO s on
            d.STATICSHIPID = s.id
        WHERE s.boutside = 0
        and d.shipName like concat('%',#{shipName},'%')
        limit #{pageNum}, 10
    </select>
    <select id="GetAllShipOnlineStatistics" resultType="com.bd.entity.ShipOnlineCount">
        SELECT s.shipName, s.bdId, s.mmsi, s.owner, s.lxdh, s.lxdz, s.dw, s.shipType, s.length, s.width FROM SHIP.SHIP_DYNAMICTION d
        INNER JOIN SHIP.SHIP_STATICINFO s on
            d.STATICSHIPID = s.id
        WHERE s.boutside = 0
          and d.shipName like concat('%',#{shipName},'%')
    </select>
    <select id="GetShipOnlineStatisticsCount" resultType="java.lang.Integer">
        SELECT count(s.shipName) FROM SHIP.SHIP_DYNAMICTION d
        INNER JOIN SHIP.SHIP_STATICINFO s on
            d.STATICSHIPID = s.id
        WHERE s.boutside = 0
        and d.shipName like concat('%',#{shipName},'%')
    </select>

    <insert id="SetShipWithArea">
        insert into SHIP.SHIPWITHAREA(SHIPID, AREAID)
        values (
            #{shipId},
            #{areaId}
        )
    </insert>

    <select id="GetJianCeShipInfo" resultType="com.bd.entity.JianCeShipInfo">
        select *
        from ship.ship_jianceship
            where SHIPNAME like concat('%',#{shipName},'%')
            limit #{pageNum}, 10;
    </select>

    <select id="GetJianCeShipInfoCount" resultType="java.lang.Integer">
        select count (*)
        from ship.ship_jianceship
            where SHIPNAME like concat('%',#{shipName},'%');
    </select>

    <select id="GetJianCeShipInfo_Export" resultType="com.bd.entity.JianCeShipInfo">
        select *
        from ship.ship_jianceship
        where SHIPNAME like concat('%',#{shipName},'%');
    </select>

    <delete id="DeleteJianCeShip">
        delete from ship.ship_jianceship
            where ID = #{id};
    </delete>

    <insert id="AddJianCeShip">
        insert into ship.ship_jianceship(STATICSHIPID, SHIPNAME, WORKTYPE, WORKAREA, SHIPTYPE, STARTTIME, ENDTIME, POINTCOUNT, POINTSTR, TIME)
        values (
                   #{jianCeShipInfo.staticShipId},
                   #{jianCeShipInfo.shipName},
                   #{jianCeShipInfo.workType},
                   #{jianCeShipInfo.workArea},
                   #{jianCeShipInfo.shipType},
                   #{jianCeShipInfo.startTime},
                   #{jianCeShipInfo.endTime},
                   #{jianCeShipInfo.pointCount},
                   #{jianCeShipInfo.pointStr},
                   #{jianCeShipInfo.time}
               )
    </insert>

    <select id="GetWhiteAndBlackListInfoByType" resultType="com.bd.entity.BlackAndWhiteList">
        select *
        from ship.BLACKANDWHITELIST b inner join ship.SHIP_STATICINFO s on b.STATICSHIPID = s.id
        left join SHIP.SHIP_DYNAMICTION d on d.STATICSHIPID = s.id
        <if test="type != 0">
            where b.TYPE = #{type};
        </if>
    </select>

    <select id="GetWhiteAndBlackListInfoById" resultType="com.bd.entity.BlackAndWhiteList">
        select *
        from ship.BLACKANDWHITELIST b left join ship.SHIP_STATICINFO s on b.STATICSHIPID = s.id
                                      left join SHIP.SHIP_DYNAMICTION d on d.STATICSHIPID = s.id
        where b.ID = #{id};
    </select>

    <insert id="SetWhiteOrBlackList">
        insert into SHIP.BLACKANDWHITELIST
            (STATICSHIPID, TYPE)
        VALUES(
              #{blackAndWhiteList.staticShipId},
              #{blackAndWhiteList.type});
    </insert>
    <insert id="AddShipTerm">
        INSERT INTO SHIP.SHIP_TERM(NAME, USERID) VALUES (#{name}, #{userId})
    </insert>
    <insert id="InsertShipForTerm">
        INSERT INTO SHIP.SHIP_TERM_SHIP(STATICSHIPID, TERMID) VALUES (#{staticShipId}, #{termId})
    </insert>
    <insert id="InsertShipVoyage">
        insert into "SHIP"."SHIP_VOYAGE"
            ("STATICSHIPID", "SHIPNAME", "STARTPORT", "STARTTIME", "ENDPORT",
             "ENDTIME", "DIS", "WORKTIME", "WORKDATE")
        VALUES(#{voyage.STATICSHIPID},
                #{voyage.SHIPNAME},
                #{voyage.STARTPORT},
                #{voyage.STARTTIME},
                #{voyage.ENDPORT},
                #{voyage.ENDTIME},
                #{voyage.DIS},
                #{voyage.WORKTIME},
                #{voyage.STARTTIME}
               );
    </insert>

    <delete id="DeleteWhiteOrBlackList">
        delete from SHIP.BLACKANDWHITELIST where ID = #{typeId}
    </delete>

    <delete id="DeleteShipTerm">
        delete from SHIP.SHIP_TERM where ID = #{id}
    </delete>
    <delete id="DeleteShipFromTerm">
        delete from SHIP.SHIP_TERM_SHIP where ID = #{id}
    </delete>

    <update id="UpdateWhiteOrBlackList">
        update SHIP.BLACKANDWHITELIST
        set STATICSHIPID = #{blackAndWhiteList.staticShipId},
            TYPE = #{blackAndWhiteList.type}
        where ID = #{blackAndWhiteList.id}
    </update>

    <update id="updateShipInShanghaiState">
        UPDATE SHIP.SHIP_DYNAMICTION SET bInShanghai = #{state}
        where staticShipId = #{ship.staticShipId}
    </update>

    <select id="GetShipCount" resultType="com.bd.entity.ShipCount">
        SELECT COUNT(s.id) as allShipCount,
               SUM(CASE WHEN s.SHIPTYPE = 1 THEN 1 ELSE 0 END) AS otherShipCount,
               SUM(CASE WHEN s.SHIPTYPE = 2 THEN 1 ELSE 0 END) AS fisherShipCount,
               SUM(CASE WHEN s.SHIPTYPE = 3 THEN 1 ELSE 0 END) AS yuzhengShipCount,
               SUM(CASE WHEN s.SHIPTYPE = 4 THEN 1 ELSE 0 END) AS jiuyuanShipCount
        from SHIP.SHIP_STATICINFO s
        inner join SHIP.SHIP_DYNAMICTION d on s.id = d.staticshipid
        where s.boutside = 0
    </select>

    <select id="GetAllWhiteList" resultType="com.bd.entity.BlackAndWhiteList">
        select *
        from ship.BLACKANDWHITELIST b left join ship.SHIP_STATICINFO s on b.STATICSHIPID = s.id
        left join SHIP.SHIP_DYNAMICTION d on d.STATICSHIPID = s.id
        where b.TYPE in (1, 2, 3, 4);
    </select>

    <select id="GetAllBlackList" resultType="com.bd.entity.BlackAndWhiteList">
        select *
        from ship.BLACKANDWHITELIST b left join ship.SHIP_STATICINFO s on b.STATICSHIPID = s.id
                                      left join SHIP.SHIP_DYNAMICTION d on d.STATICSHIPID = s.id
        where b.TYPE in (5, 6, 7, 8);
    </select>

    <select id="GetAllWhiteAndBlackShip" resultType="com.bd.entity.BlackAndWhiteList">
        select *
        from ship.BLACKANDWHITELIST b left join ship.SHIP_STATICINFO s on b.STATICSHIPID = s.id
        left join SHIP.SHIP_DYNAMICTION d on d.STATICSHIPID = s.id
        where s.SHIPNAME like concat('%',#{shipName},'%')
        and IFNULL(s.bdId,'-') like concat('%',#{bdId},'%')
        and s.MMSI like concat('%',#{mmsi},'%')
        <if test="shipType == 1 or shipType == 2 or shipType == 3 or shipType == 4">
            and shipType =  #{shipType}
        </if>

        <if test="specialType == 1">
            and b.TYPE in (5, 6, 7, 8)
        </if>
        <if test="specialType == 2">
            and b.TYPE in (1, 2, 3, 4)
        </if>
    </select>

    <select id="GetWhiteAndBlackShip" resultType="com.bd.entity.BlackAndWhiteList">
        select *
        from ship.BLACKANDWHITELIST b left join ship.SHIP_STATICINFO s on b.STATICSHIPID = s.id
                                      left join SHIP.SHIP_DYNAMICTION d on d.STATICSHIPID = s.id
        where s.SHIPNAME like concat('%',#{shipName},'%')
        and IFNULL(s.bdId,'-') like concat('%',#{bdId},'%')
        and s.MMSI like concat('%',#{mmsi},'%')
        <if test="shipType == 1 or shipType == 2 or shipType == 3 or shipType == 4">
            and shipType =  #{shipType}
        </if>

        <if test="specialType == 1">
            and b.TYPE in (5, 6, 7, 8)
        </if>
        <if test="specialType == 2">
            and b.TYPE in (1, 2, 3, 4)
        </if>
        limit #{pageNum}, 10;
    </select>

    <select id="GetWhiteAndBlackShipCount" resultType="java.lang.Integer">
        select count (*)
        from ship.BLACKANDWHITELIST b left join ship.SHIP_STATICINFO s on b.STATICSHIPID = s.id
        left join SHIP.SHIP_DYNAMICTION d on d.STATICSHIPID = s.id
        where s.SHIPNAME like concat('%',#{shipName},'%')
        and IFNULL(s.bdId,'-') like concat('%',#{bdId},'%')
        and s.MMSI like concat('%',#{mmsi},'%')
        <if test="shipType == 1 or shipType == 2 or shipType == 3 or shipType == 4">
            and shipType =  #{shipType}
        </if>

        <if test="specialType == 1">
            and b.TYPE in (5, 6, 7, 8)
        </if>
        <if test="specialType == 2">
            and b.TYPE in (1, 2, 3, 4)
        </if>
    </select>
    <select id="GetShipTerm" resultType="com.bd.entity.Ship_Term">
        SELECT * FROM SHIP.SHIP_TERM where USERID = #{userId}
    </select>
    <select id="GetShipFromTerm" resultType="com.bd.entity.ShipStaticInfo_all">
        SELECT s.*, t.id as termShipId FROM SHIP.SHIP_TERM_SHIP t
        INNER JOIN SHIP.SHIP_STATICINFO s
        ON t.STATICSHIPID = s.ID
        where t.TERMID = #{termId}
    </select>
    <select id="CheckShipInTerm" resultType="java.lang.Integer">
        SELECT COUNT(ID) FROM SHIP.SHIP_TERM_SHIP
        WHERE TERMID = #{termId}
        and STATICSHIPID = #{staticShipId}
    </select>
    <select id="GetBDShipPosition_ronghe_strTime_thread" resultType="com.bd.entity.ShipDynamicInfo">
        SELECT d.*, s.OWNER, s.lxdh, s.bOutside, s.bWhiteFuXiu, s.length, s.width FROM SHIP.SHIP_DYNAMICTION d
        INNER JOIN SHIP.SHIP_STATICINFO s on
            d.STATICSHIPID = s.id
        WHERE s.boutside = 0
        and s.shipType = 2
        order by d.LOADTIME desc
    </select>

    <select id="CheckShipBout" resultType="java.lang.Integer">
        select BOUTSIDE
        from SHIP.SHIP_STATICINFO
            where ID = #{shipId};
    </select>
    <select id="GetShipdistribute" resultType="com.bd.entity.Ship_DistributeCount">
<!--        SELECT-->
<!--        COUNT(SELECT distinct shipname FROM fisher-->
<!--        where shipid in (SELECT shipid FROM fisher_and_term where termtype in (1))-->
<!--        and managerid in (select userid from fisher_manager start with userid in (2550136832) connect by prior userid=superid)) as allShipCount,-->
<!--        SUM (CASE WHEN s.BOUTSIDE = 1 and d.loadTIme > #{time} THEN 1 ELSE 0 END) as onlineCount,-->
<!--        SUM (CASE WHEN s.BOUTSIDE = 1 and d.loadTIme > #{time} THEN 1 ELSE 0 END) as onlineCount,-->
<!--        FROM SHIP.SHIP_DYNAMICTION d-->
<!--        LEFT JOIN SHIP.STATICINFO s ON d.staticShipId = s.id-->
<!--        where-->
<!--        d.LOADTIME &gt; #{time}-->
<!--        and d.inportstate = 1-->
<!--        <if test="portId != 0">-->
<!--            and d.inportId = #{portId}-->
<!--        </if>-->

    </select>
    <select id="getAllShipCount" resultType="java.lang.Integer">
        SELECT COUNT(id) FROM ship.ship_staticinfo where boutside = 0 and shiptype = 2
    </select>

    <select id="getOnlineShipCount" resultType="java.lang.Integer">
        select count(*) from ship.ship_dynamiction d
        inner join ship.ship_staticinfo s on d.staticshipid = s.id
        where s.boutside = 0 and s.shiptype = 2 and d.loadtime > #{time}
    </select>

    <select id="GetInPortFish2" resultType="java.lang.Integer">
        select count(*) from ship.ship_dynamiction d
        inner join ship.ship_staticinfo s on d.staticshipid = s.id
        where s.boutside = 0 and s.shiptype = 2 and d.inportstate = 1
        <if test="portId != 0">
          and d.InPortId = #{portId}
        </if>
          and d.loadtime > #{time}
    </select>

    <select id="GetWSInShanghai" resultType="java.lang.Integer">
        select count(*) from ship.ship_dynamiction d
        inner join ship.ship_staticinfo s on d.staticshipid = s.id
        where (s.boutside = 1 or s.shiptype = 61)
          and (d.bInshanghai in (1, 2) or d.INPORTSTATE = 1)
          and s.shipname not like '%HUCHONGYU%'
          and s.shipname not like '%HU CHONG YU%'
          and s.shipname not like '%HUFENGYU%'
          and s.shipname not like '%HU FENG YU%'
          and s.shipname not like '%HUPUYU%'
          and s.shipname not like '%HU PU YU%'
          and s.shipname not like '%HUHANG%'
        and d.loadtime > #{time}
    </select>
    <select id="GetWSInJinbu" resultType="java.lang.Integer">
        select count(*) from ship.ship_dynamiction d
        inner join ship.ship_staticinfo s on d.staticshipid = s.id
        where (s.boutside = 1 or s.shiptype = 61)
          and d.bInshanghai = 2
          and s.shipname not like '%HUCHONGYU%'
          and s.shipname not like '%HU CHONG YU%'
          and s.shipname not like '%HUFENGYU%'
          and s.shipname not like '%HU FENG YU%'
          and s.shipname not like '%HUPUYU%'
          and s.shipname not like '%HU PU YU%'
          and s.shipname not like '%HUHANG%'
          and d.loadtime > #{time}
    </select>
    <select id="GetWSInPort" resultType="java.lang.Integer">
        select count(*) from ship.ship_dynamiction d
        inner join ship.ship_staticinfo s on d.staticshipid = s.id
        where (s.boutside = 1 or s.shiptype = 61)
        and d.inportstate = 1
        <if test="portId != 0">
            and d.InPortId = #{portId}
        </if>
        and s.shipname not like '%HUCHONGYU%'
        and s.shipname not like '%HU CHONG YU%'
        and s.shipname not like '%HUFENGYU%'
        and s.shipname not like '%HU FENG YU%'
        and s.shipname not like '%HUPUYU%'
        and s.shipname not like '%HU PU YU%'
        and s.shipname not like '%HUHANG%'
        and d.loadtime > #{time}
    </select>
    <select id="GetBDShipPosition_ronghe_strTime_thread_ws" resultType="com.bd.entity.ShipDynamicInfo">
        SELECT d.*, s.OWNER, s.lxdh, s.bOutside, s.bWhiteFuXiu, s.length, s.width FROM SHIP.SHIP_DYNAMICTION d
        INNER JOIN SHIP.SHIP_STATICINFO s on
        d.STATICSHIPID = s.id
        WHERE d.LOADTIME &gt; #{time}
        and (s.boutside = 1 or s.shipType = 61)
          and s.shipname not like '%HUCHONGYU%'
          and s.shipname not like '%HU CHONG YU%'
          and s.shipname not like '%HUFENGYU%'
          and s.shipname not like '%HU FENG YU%'
          and s.shipname not like '%HUPUYU%'
          and s.shipname not like '%HU PU YU%'
          and s.shipname not like '%HUHANG%'
        order by d.LOADTIME desc
    </select>

    <select id="GetJianCeShipInfoByShipId" resultType="com.bd.entity.JianCeShipInfo">
        select *
        from SHIP.SHIP_JIANCESHIP
        where STATICSHIPID = #{shipId}
        limit 1;
    </select>

    <select id="GetJianCeShipInfoById" resultType="com.bd.entity.JianCeShipInfo">
        select *
        from SHIP.SHIP_JIANCESHIP
        where ID = #{id};
    </select>

    <select id="GetshipNameById" resultType="java.lang.String">
        SELECT shipName from ship.ship_staticinfo where id = #{shipId}
    </select>

    <select id="GetshipNamesByIds" resultType="java.lang.String">
        SELECT shipName
        FROM ship.ship_staticinfo
        WHERE id IN
        <foreach collection="shipIds" item="shipId" open="(" close=")" separator=",">
            #{shipId}
        </foreach>
    </select>

    <select id="GetShipImgUrl" resultType="java.lang.String">
        select IMG
        from ship.ship_staticinfo where ID = #{shipId} and BOUTSIDE = 0;
    </select>

    <select id="GetShipSetting" resultType="java.lang.String">
        select content
        from ship.shipsettinginfo where USERID = #{userId};
    </select>
    <select id="GetTrackBdidList" resultType="java.lang.String">
        select termno from sjzx.SHPOSIITON20230228 where termtype = 1 group by termno;
    </select>
    <select id="GetStaticshipidByBdid" resultType="java.lang.Integer">
        select id from ship.ship_staticinfo where IFNULL(bdId,'-') like concat('%',#{bdid},'%')
    </select>
    <select id="GetstaticshipidCount" resultType="java.lang.Integer">
        select count(id) from ship.ship_staticinfo where IFNULL(bdId,'-') like concat('%',#{bdid},'%')
    </select>
    <select id="getCheckRecordByShipName" resultType="com.bd.entity.CheckRecord">
        select *
        from SHIP.CHECK_RECORD
        where SUOBJ_CNAME = #{shipName}
        and SIGN_DATE > '2000'
        order by SIGN_DATE desc
        limit 1;
    </select>
    <select id="getMaxBerth" resultType="java.lang.Integer">
        SELECT  COALESCE(MAX(current_count), 0) AS max_ships_count
        FROM (
        SELECT LOADTIME,
        SUM(CASE WHEN state = 0 THEN 1 ELSE -1 END)
        OVER (ORDER BY LOADTIME ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS current_count
        FROM "SHIP"."OUTINPORTRECORD_2"
        WHERE  LOADTIME &gt;= #{startTime}
        AND LOADTIME &lt;= #{endTime}
        and portid = #{portId}
        ORDER BY LOADTIME
        ) AS cumulative_count;
    </select>
  <select id="getAreaByShipName" resultType="java.lang.String">
    select YCSSDQMC
    from SHIP.FISHING_BOAT_INFO
    where CM = #{shipName}
      AND ROWNUM = 1
  </select>


  <insert id="SetShipSetting">
        insert into ship.shipsettinginfo(USERID, CONTENT)
        values (
                   #{userId},
                   #{content}
               )
    </insert>
    <insert id="InsertTrack">
        insert into ship.SHIP_HISTORYTRACKPOINT_TMP
            ("BDID", "LON", "LAT", "SPEED", "COG", "REPORTTIME", "LOADTIME", "STATICSHIPID")
        select termno as bdid,
               longitude*10 as lon,
               latitude*10 as lat,
               speed,
               azimuth as cog,
               postime as REPORTTIME,
               loadtime,
                #{staticshipid} as staticshipid
        from sjzx.SHPOSIITON20230228 where termtype = 1 and termno = #{bdid}
        and loadtime &gt; '2021-01-01' and loadtime &lt; '2021-06-30'
    </insert>

    <update id="UpdateShipSetting">
        update ship.shipsettinginfo set CONTENT = #{content}
        where USERID = #{userId}
    </update>
    <update id="updateFishAreaId">
        update ship.ship_dynamiction set infishareaid = #{name} where staticshipid = #{ship.staticShipId}
    </update>

    <update id="updateExcelEntity">
        <foreach collection="entities" item="entity" separator=";">
            update ship.ship_staticInfo
            <set>
                <if test="entity.shipOwner!=null and entity.shipOwner!=''">
                    Owner = #{entity.shipOwner},
                </if>
                <if test="entity.contact1!=null and entity.contact1!=''">
                    lxdh = #{entity.contact1},
                </if>
                <if test="entity.captain!=null and entity.captain!=''">
                    captain = #{entity.captain},
                </if>
                <if test="entity.contact2!=null and entity.contact2!=''">
                    captainlxdh = #{entity.contact2},
                </if>
                <if test="entity.wxlxdh!=null and entity.wxlxdh!=''">
                    wxlxdh = #{entity.wxlxdh},
                </if>
                <if test="entity.emergency!=null and entity.emergency!=''">
                    emergencyContact = #{entity.emergency},
                </if>
                <if test="entity.contact3!=null and entity.contact3!=''">
                    emergencyContactLxdh = #{entity.contact3},
                </if>
            </set>
            where
                shipName = #{entity.shipName}
        </foreach>
    </update>


</mapper>
