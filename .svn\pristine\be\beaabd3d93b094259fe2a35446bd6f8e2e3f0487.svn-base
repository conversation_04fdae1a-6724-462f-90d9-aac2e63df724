// 坐标转换
// geo-经纬度，标准格式
function coordinateTransformation(geo) {
    if (typeof (geo) == "undefined" || geo == "") {
        return "";
    }
    
    if(typeof geo == 'number') {
        geo = String(geo);
    }
    
    var i = geo.indexOf('.');

    var strDu = i < 0 ? geo : geo.substring(0, i);//获取度
    var strFen = 0;
    var strMiao = 0;
    if (i > 0) {
        var strFen = "0" + geo.substring(i);
        strFen = strFen * 60 + "";
        i = strFen.indexOf('.');
        if (i > 0) {
            strMiao = "0" + strFen.substring(i);
            strFen = strFen.substring(0, i);//获取分
            strMiao = strMiao * 60 + "";
            i = strMiao.indexOf('.');
            strMiao = strMiao.substring(0, i + 4);//取到小数点后面三位
            strMiao = parseFloat(strMiao).toFixed(2);//精确小数点后面两位
        }
    }
    return strDu + "°" + strFen + "′" + strMiao + "″";
}

// 坐标转换
// 度分秒-度
function coordinateTransformationToDu(geo) {
    if (typeof (geo) == "undefined" || geo == "") {
        return {du: 0, fen: 0, miao: 0};
    }

    var geoDu = geo.split("°");

    // 只有度
    if(geoDu.length == 2 && geoDu[1] == "") {
        return {du: parseFloat(geoDu[0]), fen: 0, miao: 0};
    }

    // 只有分
    if(geoDu.length == 1 && geoDu[0].split("′").length == 2 && geoDu[0].split("′")[1] == "") {
        return {du: 0, fen: parseFloat(geoDu[0].split("′")[0]), miao: 0};
    }

    // 只有秒
    if(geoDu.length == 1 && geoDu[0].split("′").length == 1 && geoDu[0].split("″").length == 2 && geoDu[0].split("″")[1] == "") {
        return {du: 0, fen: 0, miao: parseFloat(geoDu[0].split("″")[0])};
    }

    // 度-分
    if(geoDu.length == 2 && geoDu[1] != "" && geoDu[1].split("′").length == 2 && geoDu[1].split("′")[1] == "") {
        return {du: parseFloat(geoDu[0]), fen: parseFloat(geoDu[1].split("′")[0]), miao: 0};
    }

    // 度-秒
    if(geoDu.length == 2 && geoDu[1] != "" && geoDu[1].split("′").length == 1 && geoDu[1].split("″").length == 2 && geoDu[1].split("″")[1] == "") {
        return {du: parseFloat(geoDu[0]), fen: 0, miao: parseFloat(geoDu[1].split("″")[0])};
    }

    // 分-秒
    if(geoDu.length == 1 && geoDu[0] != "" && geoDu[0].split("′").length == 2 && geoDu[0].split("″").length == 2
            && geoDu[0].split("′")[1] != "" && geoDu[0].split("″")[1] == "") {
        return {du: 0, fen: parseFloat(geoDu[0].split("′")[0]), miao: parseFloat(geoDu[0].split("′")[1].split("″")[0])};
    }

    // 度-分-秒
    if(geoDu.length == 2 && geoDu[1] != "" && geoDu[1].split("′").length == 2 && geoDu[1].split("″").length == 2
            && geoDu[1].split("′")[1] != "" && geoDu[1].split("″")[1] == "") {
        return {du: parseFloat(geoDu[0]), fen: parseFloat(geoDu[1].split("′")[0]), miao: parseFloat(geoDu[1].split("′")[1].split("″")[0])};
    }
}