#situationView {
    position: absolute; 
    width: 100%; 
    height: 100%;
    left: 0px; 
    top: 0px;
}

#map {
    position: absolute; 
    width: 100%; 
    height: 100%;
    overflow: hidden;
    left: 0px; 
    top: 0px;
    z-index: 1;
    background-color: white;
}

.ui3-control-wrap{
    box-sizing: border-box;
    position: relative;
}
.ui3-control-wrap .left.float-l{
    width: 100px;
}
.ui3-control-wrap .left, .ui3-control-wrap .right{
    height: 34px;
    background: #00143F;
    border-radius: 0 2px 2px 0;
    background: rgba(0,20,63,0.9);
    box-shadow: 1px 2px 1px rgb(0 0 0 / 15%);
}
.ui3-control-wrap .boxopt, .ui3-control-wrap .detail-box{
    width: 100%;
}
.ui3-control-wrap .boxopt{
    padding-right: 20px;
    line-height: 34px;
    float: left;
    cursor: pointer;
    display: inline-block;
}
.ui3-control-wrap .boxutils{
    padding-right: 7px;
    background-position: -34px -116px;
}
.ui3-control-wrap i{
    float: left;
    font-size: 12px;
    font-style: normal;
    height: 34px;
    line-height: 34px;
    display: inline-block;
}
.ui3-control-wrap .boxopt em{
    width: 7px;
    height: 7px;
    float: left;
    margin-top: 13px;
    margin-left: 5px;
    background-image: url(//webmap1.bdimg.com/wolfman/static/common/images/ui3/tools/newtools_9ddbaad.png);
    background-repeat: no-repeat;
    background-position: -13px -17px;
}
.ui3-control-wrap .detail-box{
    position: absolute;
    background: #00143F;
    background: rgba(0,20,63,0.9);
    box-shadow: 1px 2px 1px rgb(0 0 0 / 15%);
    top: 34px;
    right: 0;
    float: left;
    z-index: 80000;
}
.ui3-control-wrap .boxinfo{
    list-style-type: none;
}
ul{
    margin: 0;
    padding: 0;
    list-style: none;
}
.ui3-control-wrap .boxinfo li{
    box-sizing: border-box;
    width: 100%;
    cursor: pointer;
    height: 34px;
    line-height: 34px;
    padding-left: 7px;
}
.ui3-control-wrap .boxinfo li .measure{
    background-position: -80px -117px;
}
.ui3-control-wrap .left span, .ui3-control-wrap .right span{
    float: left;
    height: 34px;
    width: 29px;
    background-image: url(//webmap1.bdimg.com/wolfman/static/common/images/ui3/tools/newtools_9ddbaad.png);
    background-repeat: no-repeat;
}
.ui3-control-wrap .boxinfo li i{
    margin-left: 2px;
}
.ui3-control-wrap i{
    float: left;
    font-size: 12px;
    font-style: normal;
    height: 34px;
    line-height: 34px;
    display: inline-block;
}
.ui3-control-wrap .last{
    border: 0!important;
}
.boxinfo .ranging{
    background-image: url(../../static/ico/测距.png)!important;
    background-position: center;
    background-size: 18px;
}
.boxinfo .measureArea{
    background-image: url(../../static/ico/测面积.png)!important;
    background-position: center;
    background-size: 18px;
}
.boxinfo .layLine{
    background-image: url(../../static/ico/方位.png)!important;
    background-position: center;
    background-size: 18px;
}
.boxinfo .search-circle{
    background-image: url(../../static/ico/圆搜.png)!important;
    background-position: center;
    background-size: 18px;
}
.boxinfo .search-square{
    background-image: url(../../static/ico/方搜.png)!important;
    background-position: center;
    background-size: 18px;
}
.boxinfo .search-polygon{
    background-image: url(../../static/ico/多边形.png)!important;
    background-position: center;
    background-size: 18px;
}
.boxinfo .electronic-fence{
    background-image: url(../../static/ico/电子围栏.png)!important;
    background-position: center;
    background-size: 18px;
}
.boxinfo .area-playback{
    background-image: url(../../static/ico/回放.png)!important;
    background-position: center;
    background-size: 18px;
}
.boxinfo .mark-point{
    background-image: url(../../static/ico/点.png)!important;
    background-position: center;
    background-size: 18px;
}
.boxinfo .mark-line{
    background-image: url(../../static/ico/线.png)!important;
    background-position: center;
    background-size: 18px;
}
.boxinfo .mark-face{
    background-image: url(../../static/ico/圆.png)!important;
    background-position: center;
    background-size: 18px;
}
#replay-info1 span{
    font-size: 15px;
}
#replay-info1 input{
    background-color: rgb(0,20,63);
    width: 200px;
    border-radius: 8px;
    border: 1px solid rgb(27,103,244);
}
#replay-btn button{
    border-radius: 10px;
    border: 1px solid rgb(14,47,136);
    background-color: rgb(14,47,136);
    width: 80px;
    height: 30px;
}

#divFullscreen {
  width: 20%;
  height: 16%;
  background-color: white;
  /* border: 2px solid black; */
  position: absolute;
  z-index: 20;
  left: 40%;
  top: 10%;
  border-radius: 5%;
}

.boxBigDivStyle
{
	position: absolute; 
	z-index: 505;
	width: 270px; 
	height: 260px;
	left: 280px; 
	top: 100px;
	background-color:White; 
	border: 4px solid #7cc7da; 
	border-radius: 5px; 
	padding: 0px 0px; 
	
	-webkit-opacity: 0.9; /* Netscape and Older than Firefox 0.9 */
    -moz-opacity: 0.9; /* Safari 1.x (pre WebKit!) 老式khtml内核的Safari浏览器*/
    -khtml-opacity: 0.9; /* IE9 + etc...modern browsers */
    opacity: .9; /* IE 4-9 */
    filter: alpha(opacity=0); /*This works in IE 8 & 9 too*/
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=90)"; /*IE4-IE9*/
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=90);
	}

  .boxDivStyle
{
	position: absolute; 
	z-index: 505;
	width: 270px; 
	height: 260px;
	left: 280px; 
	top: 100px;
	background-color:rgb(0 , 20, 63); 
	border: 4px solid rgb(0 , 20, 63); 
	border-radius: 5px; 
	padding: 0px 0px; 
	
	-webkit-opacity: 0.9; /* Netscape and Older than Firefox 0.9 */
    -moz-opacity: 0.9; /* Safari 1.x (pre WebKit!) 老式khtml内核的Safari浏览器*/
    -khtml-opacity: 0.9; /* IE9 + etc...modern browsers */
    opacity: .9; /* IE 4-9 */
    filter: alpha(opacity=0); /*This works in IE 8 & 9 too*/
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=90)"; /*IE4-IE9*/
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=90);
	}

  .boxTitleDivStyle
{
	width: 100%; 
	height: 25px; 
	background-color: rgb(36 , 111, 168); 
	border-bottom: 3px; 
    border-bottom-color: rgb(36 , 111, 168);
	}
.boxTitleDivStyle span
{
	position: relative; 
	top: 5px; 
	left: 5px; 
	font-size: 13px;
	color:#ffffff;	
	}

    
/* #menuBar{
    position: absolute;
    background: #00143F;
    box-shadow: 1px 2px 1px rgb(0 0 0 / 15%);
    background: rgba(0, 20, 63, 0.8);
    top: 34px;
    right: 0;
    float: left;
    z-index: 80000;
    border: 0;
} */
/* #menuBar .boxsinfo{
    list-style-type: none;
    border: 0;
}
ul{
    margin: 0;
    padding: 0;
    list-style: none;
}
#menuBar .boxsinfo li{
    box-sizing: border-box;
    width: 100%;
    cursor: pointer;
    height: 34px;
    line-height: 34px;
    padding-left: 7px;
}
#menuBar .boxsinfo li .measure{
    background-position: -80px -117px;
} */

#getShipInfoByNameOrTrTbody {
    width: 200px; 
    height: auto; 
    position: absolute; 
    top: 33px; 
    left: 0px; 
    background-color: #00143F;
    /* overflow-x: hidden; */
    overflow-y: scroll;
    font-size: 12px;
    padding: 0;
    text-align: center;
    border: 0;
    max-height: 300px;
}

#getShipInfoByNameOrTrTbody::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    opacity: 0.8;
}
#getShipInfoByNameOrTrTbody::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
    border-radius: 5px;
    background-color: #00143F;
}
#getShipInfoByNameOrTrTbody::-webkit-scrollbar-thumb {
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
    background-color: #1A2C53;
}


/* button 样式 */
.defaultButtonStyle {
    background-color: rgb(25, 86, 200);
    border: 0;
}

/* top-left */
.topLeftOrigin {
    /* transform-origin: 0% 0%; */
}

/* top-right */
.topRightOrigin {
    /* transform-origin: 100% 0%; */
}

/* bottom-left */
.bottomLeftOrigin {
    /* transform-origin: 0% 100%; */
}

/* bottom-right */
.boottomRightOrigin {
    /* transform-origin: 100% 100%; */
}