package com.bd.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;

import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.junit.jupiter.api.Test;

import java.io.*;
import java.net.URI;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


public class HttpTool {

    @Test
    public void test() throws IOException {

        String url = "http://10.89.9.132:11080/api/uaa/oauth/token?socpe=all&grant_type=password&username=taoql&password=taoql@123&tenantCode=90001";
        String data = HttpTool.doPostJson(url, "","Basic Y2xpZW50OnNlY3JldA==");
        String refresh_token = JSONObject.parseObject(data).getString("refresh_token");
        //System.out.println(refresh_token);

        String url2 = "http://10.89.9.132:11080/api/uaa/oauth/token?grant_type=refresh_token&refresh_token=" + refresh_token;
        String data2 = HttpTool.doGet(url2, "Basic Y2xpZW50OnNlY3JldA==");
        String access_token = JSONObject.parseObject(data2).getString("access_token");
        System.out.println(access_token);
    }

    private static final Logger logger = LogManager.getLogger(HttpTool.class);

    /**
     * GET请求.
     *
     * @param url 请求地址
     * @return 响应数据
     */
    public static String doGet(String url, String header){
        try {
            // 创建客户端
            HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
            // 建立连接
            HttpGet httpGet = new HttpGet(url);
            // 请求配置：超时时间，单位：毫秒
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).build();
            httpGet.setConfig(requestConfig);
            // 设置请求头：内容类型
            httpGet.setHeader("Content-Type", "application/json;charset=UTF-8");
            if(!Objects.equals(header, ""))
                httpGet.setHeader("Authorization", header);
            return getResponse(httpClientBuilder, httpGet);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    /**
     * POST请求：携带表单数据form-data.
     *
     * @param url      请求地址
     * @param paramMap 表单数据
     * @return 响应数据
     */
    public static String doPostFormData(String url, Map<String, Object> paramMap) {
        try {
            // 创建客户端
            HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
            // 建立连接
            HttpPost httpPost = new HttpPost(url);
            // 请求配置：超时时间，单位：毫秒
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).build();
            httpPost.setConfig(requestConfig);
            // 设置请求头：内容类型
            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");

            if (Objects.nonNull(paramMap) && !paramMap.isEmpty()) {
                List<BasicNameValuePair> formDataList = new ArrayList<>();
                Set<Map.Entry<String, Object>> entrySet = paramMap.entrySet();
                for (Map.Entry<String, Object> mapEntry : entrySet) {
                    formDataList.add(new BasicNameValuePair(mapEntry.getKey(), mapEntry.getValue().toString()));
                }
                httpPost.setEntity(new UrlEncodedFormEntity(formDataList, "UTF-8"));
            }

            return postResponse(httpClientBuilder, httpPost);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    /**
     * POST请求：携带JSON数据.
     *
     * @param url    请求地址
     * @param params 请求数据：JSON字符串
     * @return 响应数据
     */
       public static String doPostJson(String url, String params, String header) {
        try {
            // 创建客户端
            HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
            // 建立连接
            HttpPost httpPost = new HttpPost(url);
            // 请求配置：超时时间，单位：毫秒
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(20000).setConnectTimeout(20000).build();
            httpPost.setConfig(requestConfig);
            // 设置请求头：内容类型
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            if(header != "" && header != "hls")
                httpPost.setHeader("Authorization", header);
            if(params != ""){
                StringEntity stringEntity = new StringEntity(params);
                stringEntity.setContentType("text/json");
                httpPost.setEntity(stringEntity);
            }
            if(header == "hls"){
                httpPost.setHeader("X-Ca-Signature", "c3INWpDiRxmLnaS0E3+7vWuA6LyXtvBZ99OClfWpzy0=");
                httpPost.setHeader("X-Ca-Key", "29602464");
                httpPost.setHeader("X-Ca-signature-headers", "x-ca-key");
            }
            return postResponse(httpClientBuilder, httpPost);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    /**
     * 获取GET请求数据.
     *
     * @param httpClientBuilder 客户端对象
     * @param httpGet           GET请求
     * @return 响应数据
     */
    public static String getResponse(HttpClientBuilder httpClientBuilder, HttpGet httpGet) {
        try (CloseableHttpResponse closeableHttpResponse = httpClientBuilder.build().execute(httpGet)) {
            HttpEntity httpEntity = closeableHttpResponse.getEntity();
            return EntityUtils.toString(httpEntity, "UTF-8");
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    /**
     * 获取POST请求数据.
     *
     * @param httpClientBuilder 客户端对象
     * @param httpPost          POST请求
     * @return 响应数据
     */
    public static String postResponse(HttpClientBuilder httpClientBuilder, HttpPost httpPost) {
        try (CloseableHttpResponse closeableHttpResponse = httpClientBuilder.build().execute(httpPost)) {
            HttpEntity httpEntity = closeableHttpResponse.getEntity();

            return EntityUtils.toString(httpEntity, "UTF-8");
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    public static String GetTokenFormCenter(){
        JSONObject user = new JSONObject();
        user.put("username", "nwycyg");
        user.put("password", "Nw013579");
        user.put("rememberMe", "true");
        String dataToken = HttpTool.doPostJson("http://10.90.7.5:18080/api/authenticate", user.toString(), "");
        return JSONObject.parseObject(dataToken).getString("id_token");
    }

    public static String GetTokenFromCenter2(){
        JSONObject user = new JSONObject();
        user.put("username", "nwycyg");
        user.put("password", "Nw013579");
        String dataToken = HttpTool.doPostJson("http://10.90.7.5:9092/api/token",user.toString(), "");
        return JSONObject.parseObject(JSONObject.parseObject(dataToken).getString("data")).getString("token");
    }

    public static String addseries() {
        String token  = HttpTool.GetTokenFromCenter2();
        System.out.println(token);
        String url = "http://10.89.9.251:9092/api/series/add";
        JSONObject series = new JSONObject();
        series.put("token", token);
        series.put("clientId", "ygyc");
        series.put("series", "menu");
        series.put("seriesName", "菜单权限");
        return HttpTool.doPostJson(url, series.toString(), "");
    }

    public static String InsertSeries(){
        String token  = HttpTool.GetTokenFromCenter2();
        String url = "http://10.89.9.251:9092/api/resource/add";
        JSONObject series = new JSONObject();
        series.put("token", token);
        series.put("appId", "ygyc");
        series.put("series", "menu2");
        series.put("series_name", "菜单权限");
        series.put("roleId", "727020216812953600");
        String perms = "\"test\":[{\"id\":1700000,\"name\":\"综合态势分析\",\"action\":\"calesOaFlow/approvallist\",\"code\":\"cales_oa_approvallist\",\"parentId\":null,\"projectId\":4,\"imgPath\":\"el-icon-alitodotask\",\"orderNo\":1700000,\"type\":1,\"isSelect\":\"0\",\"children\":[{\"id\":1700010,\"name\":\"渔船分类展示\",\"action\":\"calesOaFlow/agree|calesOaFlow/reject|calesOaFlow/oppose\",\"code\":\"cales_oa_approval\",\"parentId\":1700000,\"projectId\":4,\"imgPath\":null,\"orderNo\":1700010,\"type\":4,\"isSelect\":\"0\",\"children\":[]}]}]";
        series.put("perms", perms);
        return HttpTool.doPostJson(url, series.toString(), "");
    }
    /**
     * 从HttpEntity中获取指定字段的值
     * @param entity HttpEntity对象
     * @param fieldName 字段名称
     * @return
     * @throws IOException
     */
    public String getFieldFromEntity(HttpEntity entity, String fieldName) throws IOException {
        // 将 HttpEntity 转换为字符串 然后使用 JSON 解析库（如 Jackson、Gson 或 org.json）来解析字符串并获取所需字段的值。
        String responseString = EntityUtils.toString(entity, "UTF-8");

        // 创建 JSONObject 并解析字符串
        JSONObject jsonObject = JSONObject.parseObject(responseString);

        // 获取指定字段的值
        return jsonObject.getString(fieldName); // 第二个参数是默认值，如果字段不存在则返回 null
    }
    // public static String signData2(String b64OriginData){
    //     JSONObject jsonSign = new JSONObject();
    //     jsonSign.put("b64OriginData", b64OriginData);
    //     return HttpTool.doPostJson("http://*************:5001/api/svs/bss/signData",jsonSign.toString(), "");
    // }
    /**
     * 签名数据
     * @param b64OriginData
     */

    public static String signData(String b64OriginData) {
        if (b64OriginData == null || b64OriginData.isEmpty())
        {
            return "Error:b64OriginData cannot be null";
        }
        // 请求的地址
        String urlString = "http://*************:5001/api/svs/bss/signData";
        StringBuilder response = new StringBuilder();
        try {
            // 创建URL对象
            URL url = new URL(urlString);
            // 打开连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            // 设置请求方法
            connection.setRequestMethod("POST");
            // 设置请求头
            connection.setRequestProperty("Host", "*************:5001");
            connection.setRequestProperty("Http-Method", "post");
            connection.setRequestProperty("Content-Type", "application/json;charset=utf-8");
            System.out.println("请求头:"+connection.getRequestProperties());
            // 允许向服务器发送数据
            connection.setDoOutput(true);

            // 构造请求体
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("b64OriginData", b64OriginData);
            System.out.println(jsonBody);
            // 发送请求体
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonBody.toString().getBytes(StandardCharsets.UTF_8);
                os.write(input,0, input.length);
            }

            // 获取响应码
            int responseCode = connection.getResponseCode();
                System.out.println("响应码："+responseCode );
            // 读取响应体
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        response.append(line);
                    }
                }
                System.out.println("响应体:" + response.toString());
            }else{
                System.out.println("errorResponse:"+response.toString());
            }
            String responseBody= response.toString();
            JSONObject jsonObject = JSONObject.parseObject(responseBody);
            String b64SignedData = jsonObject.getString("b64SignedData");
            String errorCode = jsonObject.getString("errorCode");
            System.out.println("错误代码:"+errorCode);
            System.out.println("签名后的数据:"+b64SignedData);
            // 断开连接
            connection.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
            return "Error:"+e.getMessage();
        }
        return response.toString();
    }

    /**
     * 验证签名数据
     * @param b64OriginData
     * @param b64SignedData
     * @return
         * @throws Exception
     */
    public static String verifySignData(String b64OriginData, String b64SignedData) throws Exception {
        // 请求的地址
        String urlString = "http://*************:5001/api/svs/bss/verifySignData";

        StringBuilder response = new StringBuilder();
        try {
            // 创建URL对象
            URL url = new URL(urlString);
            // 打开连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法
            connection.setRequestMethod("POST");
            // 设置请求头
            connection.setRequestProperty("host", "/api/svs/bss/verifySignedData");
            connection.setRequestProperty("http method", "post");
            connection.setRequestProperty("content-type", "application/json;charset=utf-8");
            // 允许向服务器发送数据
            connection.setDoOutput(true);

            // 构造请求体
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("b64OriginData", b64OriginData);
            jsonBody.put("b64SignedData", b64SignedData);
            // String requestBody = String.format("{\"b64OriginData\": \"%s\", \"b64SignedData\": \"%s\"}", b64OriginData, b64SignedData);

            // 发送请求体
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonBody.toString().getBytes(StandardCharsets.UTF_8);
                os.write(input,0, input.length);
            }
            // 获取响应码
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 读取响应体
                try (InputStream inputStream = connection.getInputStream();
                     BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    // 打印响应体
                    System.out.println("Response: " + response.toString());
                    }
                } else {System.out.println("Request failed with response code: " + responseCode);}
                // 断开连接
                    connection.disconnect();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return response.toString();
            }

}

