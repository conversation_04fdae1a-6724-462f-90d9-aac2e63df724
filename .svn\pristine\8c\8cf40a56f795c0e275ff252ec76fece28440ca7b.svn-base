<template>
  <div style="width: 100%; height: 100%;position: absolute;left: 0;top: 0;z-index: 1;">
    <div id="map" ref="myDiv" @contextmenu.prevent="handleContextMenu"></div>
  </div>
</template>

<script>
import global from './Global.vue';

var _this;
export default {
  name: "Index",
  data() {
    return {
      msg: "Welcome to Your Vue.js App",
    };
  },
  beforeCreate() {
    _this = this;
  },
  mounted() {
    function regist() {
    }
    function getMapToken() {
    }
    function Test_AddLayer() {}
    function Test_AddShipStyle() {}
    function init() {
      var objMapInfo = [];
      var mapObj = document.getElementById("map");
      API_SetMapImgMode(1);
      API_SetShipInObj(true);
      API_SetMapMinMaxLevel(1, 18);

      objMapInfo.div = "map"; //海图容器div的id

      objMapInfo.model = "pc"; //用于pc环境
      API_InitYimaMap(objMapInfo);
      regist();
      API_HiddenYimaEncText();
      getMapToken();
      // API_SetUserYima(false);
      API_SetMapImagesUrl("http://**************:8089/dianxin_chart/");
      // API_SetMapImagesUrl("http://***********:11080/api/map/cuttingMap/");
      // API_SetMapImagesUrl("http://***********:11080/api/map/cuttingMap/");
      // $.get(global.IP + "/web/GetMapToken", function (data, status) {
      //   var wmsToken = '?graphNode=' + global.mapModel + '&token=' + data;
      //   API_RefreshMapImg();
      // }).fail(function (msg) {
      //   // 服务器地址
      //   // API_SetMapImagesUrl("http://**************:8089/YimaMap1/");
      //   API_SetMapImagesUrl("http://***********:7001/map/");
      //   API_RefreshMapImg();
      // });


      API_SetSaveTrackPoCount(1000);
      API_SetDrawCenterPoToFixedLen(6);
      API_SetMousePoInfoDivPosition(false, 500, 50); //显示鼠标位置
      API_SetScaleInfoDivPosition(false, 20, 50); //显示比例尺位置
      API_SetScaleLenInfoPosition(false, 20, 60); //显示比例尺长度
      API_SetShowToolBarOrNot(false);

      global.g_showSimpleInfoDiv = document.getElementById("ShowSimpleInfoByMouseMoveDiv");
      global.g_showDrawObjInfoByMouseMoveDiv = document.getElementById("ShowDrawObjInfoByMouseMoveDiv");
      global.g_showDetailShipInfoDiv = document.getElementById("ShowDetailShipInfoDiv");

      Test_AddLayer(); //添加图层
      Test_AddShipStyle(); //添加船舶样式
      API_SetFocusShipShowStyleByMinScale(10240000); //设置选中船舶至少显示的最小比例尺样式

      API_SetMapLevel(10, { x: 121.83219484335935, y: 31.417383188620214 });
      // API_SetMapLevel(7, { x: 131.83219484335935, y: 39.417383188620214});

      API_SetSelectAllObjectByMouseMove(50000000, true);
      API_SetShowShipHistroyTrackScale(5000000);
      API_SetIsShowShipHistroyTrackInfoBox(false);

      // setTimeout(API_ReDrawLayer, 500);

      API_SetIsShowShipHistroyTrackInfoBox(true);

      API_SteIsShowHistoryTrackDirectionSign(true);

      API_SetSelectShipByScrnPoStartScale(500000000);
      API_SetShowShipHistroyTrackScale(500000000);
      API_SetStartShowShipScale(500000000);

      API_SetWarnShipCircleStyle(2, "#FFFF00", 100);

      var optionClustererStyle = [];
      optionClustererStyle.push({ src: "../../static/img/clusterers1.png", w: 53, h: 52 });//个位
      optionClustererStyle.push({ src: "../../static/img/clusterers2.png", w: 56, h: 55 });//十位
      optionClustererStyle.push({ src: "../../static/img/clusterers3.png", w: 66, h: 65 });//百位
      optionClustererStyle.push({ src: "../../static/img/clusterers4.png", w: 78, h: 77 });//千位
      optionClustererStyle.push({ src: "../../static/img/clusterers5.png", w: 90, h: 89 });//万位
      var bInitClustererStyle = API_InitClustererStyle(optionClustererStyle);//设置聚合底图
      API_SetClustererShowOption(true, 1, 7, true);//显示聚合0～13级
      API_SetSelectRectByMouseMoveNotShow(true);
      API_DBClickByRigth(true);
      var CancelButtonStyle = [];
      CancelButtonStyle.lineWidth = 1;
      CancelButtonStyle.lineColor = "#FF0000";
      CancelButtonStyle.lineOpacity = 90;
      CancelButtonStyle.setFill = true;
      CancelButtonStyle.fillColor = "#FFFFFF";
      CancelButtonStyle.fillOpacity = 100;
      CancelButtonStyle.width = 10;
      CancelButtonStyle.height = 10;
      API_SetCancelButtonStyle(CancelButtonStyle);

      // 设置渔船名显示样式
      API_SetShowShipInfoStyle("20px 黑体", "#0000FF", 90);
      API_SetFishAreaName(true);
      //船舶选中
      API_SetShowSelectShipInfoStyle(true, "12px 黑体", "#FF00FF", 80);
      API_ShipTrackTimeMarkShowOrNot(false);
      API_SetShipShowNameFrameStyle(1, 10, 20, 1, "#FF0000", 100);
      API_SetShipSimpleDetailsShowOrNot(true);

      API_SetShipImgRotate(true);
      API_SteIsShowHistoryTrackDirectionSign(false);
      API_SetShowAllTyphoonPredictTrack(true);
      global.isCheckInit = true;
    }

    $(function () {
      console.log('s');
      init();
    })
  },
  methods: {
    handleContextMenu(event) {
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
h1,
h2 {
  font-weight: normal;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}

#map{
  width: 100%;
  height: 100%;
}
</style>
