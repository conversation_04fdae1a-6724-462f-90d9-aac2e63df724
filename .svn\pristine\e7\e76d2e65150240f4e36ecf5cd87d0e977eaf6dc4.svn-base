package com.bd.mapper;

import com.bd.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrewExamMapper {
    int insertCrewExam(@Param("crewExams") List<CrewExam> crewExams);

    void insertCrewBX(@Param("crewBxes") List<CrewBx> crewBxes);

    void delete();

    void updateLxdh();

    void insertLawCase(@Param("lawCases") List<LawCase> lawCases);

    void deleteLawCase();

    void insertFisheryPermitInfo(@Param("InfoList") List<FisheryPermitInfo> permitInfos);

    void deleteFisheryPermitInfo();

    void insertFishingBoatInfo(@Param("InfoList") List<FishingBoatInfo> boatInfos);

    void insertShipNationalRegisInfo(@Param("InfoList") List<ShipNationalRegisInfo> Infos);

    void deleteShipNationRegisInfo();

    void insertShipNameRegisInfo(@Param("InfoList") List<ShipNameRegisInfo> shipNameRegisInfos);

    void deleteShipNameRegisInfo();

    void insertAdminLaw(@Param("adminLaws")List<AdminLawExample> lawExamples);

    void deleteAdminLaw();

    void insertWorkDays(@Param("shipId")int shipId);

    List<Integer> getShipIdList();

    void insertWorkDays2(@Param("shipId")int shipId);
    void insertWorkDaysTest(@Param("shipId")int shipId, @Param("time1")String time1,@Param("time2")String time2);

    void insertWorkDays_BD(@Param("shipId")int shipId, @Param("time1")String time1, @Param("time2")String time2);

    void insertWorkDays_AIS(@Param("shipId")int shipId, @Param("time1")String time1, @Param("time2")String time2);

    void insertWorkDays_BD_day(@Param("shipId")int shipId, @Param("time1")String time1, @Param("time2")String time2);

    void insertWorkDays_AIS_day(@Param("shipId")int shipId, @Param("time1")String time1, @Param("time2")String time2);


    void insertShipInOutReport(@Param("reports") List<FisheryBoatInOutReport> report);

    /**
     * 查询给出的401报警消息是否存在
     */
    Long select401MsgCount(@Param("msg") String msg);
}
