<template>
    <div class="video-player-card">
        <div class="card-header">
            <h3>{{ (Camera && Camera.name) || '视频播放' }}</h3>
            <span v-if="videoStatus != '准备就绪'" class="video-status">{{ videoStatus }}</span>
            <button class="close-btn" @click="handleClose">×</button>
        </div>
        <div class="card-body">
            <!-- 视频播放区域 -->
            <div class="video-container">
                <video ref="videoPlayer" class="video-element" @loadstart="onVideoLoadStart" @canplay="onVideoCanPlay"
                    @play="onVideoPlay" @pause="onVideoPause" @ended="onVideoEnded" @error="onVideoError" controls muted
                    playsinline>
                    您的浏览器不支持视频播放
                </video>

                <!-- 加载状态 -->
                <div v-if="loading" class="loading-overlay">
                    <span class="loading-text">加载中...</span>
                </div>

                <!-- 视频控制按钮 -->
                <div class="video-controls">
                    <button class="control-btn play-btn" @click="handlePlay" :disabled="!canPlay">
                        <span class="icon iconfont icon-kaishi"></span>
                        <span class="btn-text">开始</span>
                    </button>

                    <button class="control-btn pause-btn" @click="handlePause" :disabled="!isPlaying">
                        <span class="icon iconfont icon-zanting"></span>
                        <span class="btn-text">停止</span>
                    </button>

                    <button class="control-btn screenshot-btn" @click="handleScreenshot" :disabled="!canPlay">
                        <span class="icon iconfont icon-screenshot-fill"></span>
                        <span class="btn-text">截图</span>
                    </button>

                    <button class="control-btn record-btn" @click="handleRecord" :class="{ 'recording': isRecording }"
                        :disabled="!canPlay">
                        <span class="icon iconfont icon-yduiluxiang"></span>
                        <span class="btn-text">{{ isRecording ? '停止录像' : '录像' }}</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import global from './Global.vue';
import Hls from 'hls.js';

export default {
    name: 'VideoPlayerCard',
    props: {
        Camera: {
            type: Object,
            required: false,
            default: () => ({})
        }
    },
    data() {
        return {
            loading: false,
            canPlay: false,
            isPlaying: false,
            isRecording: false,
            videoStatus: '准备就绪',
            videoSrc: '',
            cameraInfo: null,
            hls: null // HLS播放器实例
        }
    },

    mounted() {
        this.initializeVideo();
    },

    beforeDestroy() {
        // 清理HLS播放器
        if (this.hls) {
            this.hls.destroy();
            this.hls = null;
        }
    },

    watch: {
        Camera: {
            handler(newCamera, oldCamera) {
                if (newCamera && newCamera.id && (!oldCamera || newCamera.id !== oldCamera.id)) {
                    this.initializeVideo();
                }
            },
            deep: true,
            immediate: false
        }
    },

    methods: {
        // 初始化视频
        initializeVideo() {
            this.fetchVideoPath(this.Camera.hls);
        },

        // 从后端获取视频路径
        fetchVideoPath(cameraHls) {
            this.loading = true;
            this.videoStatus = '获取视频路径中...';
            console.log('this.Camera', this.Camera);

            // 使用摄像头ID或默认值
            const address = '10';

            $.get(global.IP + '/web/GetHlsUrlByIndex?index=' + cameraHls + '&adress=' + address, (response, status) => {
                if (Hls.isSupported()) {
                    const video = this.$refs.videoPlayer;  // 获取video元素
                    if (!video) {
                        console.log('视频元素未找到');
                        return;
                    }

                    const hls = new Hls();  // 创建HLS实例
                    const m3u8Url = response;  // 直接使用response，不需要解码

                    hls.loadSource(m3u8Url);  // 加载M3U8播放列表
                    hls.attachMedia(video);  // 将HLS实例绑定到video元素

                    // 保存hls实例到组件
                    this.hls = hls;

                    hls.on(Hls.Events.MANIFEST_PARSED, () => {  // 使用箭头函数保持this上下文
                        this.canPlay = true;
                        this.videoStatus = '准备就绪';
                        this.handlePlay();
                    });
                } else {
                    console.log('浏览器不支持HLS，尝试原生播放');
                    const video = this.$refs.videoPlayer;
                    if (video) {
                        video.src = response;
                        video.load();
                        this.videoStatus = '使用原生播放器';
                    }
                }

                this.videoSrc = response;
                this.videoStatus = '视频地址获取成功';
            }).fail((jqXHR, textStatus, errorThrown) => {
                this.videoStatus = '获取视频路径失败';
                console.error('获取视频路径时发生网络错误:', textStatus, errorThrown);

            }).always(() => {
                this.loading = false;
            });
        },
        // 处理关闭按钮点击
        handleClose() {
            console.log('关闭视频播放器');
            this.handlePause(); // 关闭前先暂停视频
            this.$emit('close');
        },

        // 播放视频
        handlePlay() {
            if (this.$refs.videoPlayer && this.canPlay) {
                this.$refs.videoPlayer.play();
            }
        },

        // 暂停视频
        handlePause() {
            if (this.$refs.videoPlayer) {
                this.$refs.videoPlayer.pause();
            }
        },

        // 截图功能
        handleScreenshot() {
            console.log('开始截图');
        },

        // 录像功能
        handleRecord() {
            console.log('开始录像');
        },


        // 视频事件处理
        onVideoLoadStart() {
            this.loading = true;
            this.videoStatus = '加载中...';
        },

        onVideoCanPlay() {
            this.loading = false;
            this.canPlay = true;
            this.videoStatus = '准备就绪';
        },

        onVideoPlay() {
            this.isPlaying = true;
            this.videoStatus = this.isRecording ? '录像中...' : '播放中';
        },

        onVideoPause() {
            this.isPlaying = false;
            this.videoStatus = this.isRecording ? '录像中...' : '已暂停';
        },

        onVideoEnded() {
            this.isPlaying = false;
            this.videoStatus = '播放结束';
        },

        onVideoError(error) {
            this.loading = false;
            this.canPlay = false;
            this.videoStatus = '加载失败';
            console.error('视频加载错误:', error);
        }
    }
}
</script>

<style scoped>
.video-player-card {
    position: fixed;
    bottom: 20px;
    width: 90%;
    max-width: 450px;
    left: 50%;
    transform: translateX(-50%);
    height: auto;
    max-height: 80vh;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    background-color: #2196f3;
    color: white;
    border-radius: 10px 10px 0 0;
    flex-shrink: 0;
}

.card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
}

.video-status {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 4px 8px;
    line-height: 1;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.close-btn:active {
    background-color: rgba(255, 255, 255, 0.2);
}

.card-body {
    padding: 12px 15px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.video-container {
    position: relative;
    width: 100%;
    height: 300px;
    background-color: #000;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.video-element {
    width: 100%;
    height: 240px;
    object-fit: contain;
    background-color: #000;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 60px;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.loading-text {
    font-size: 14px;
}

.video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background-color: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 0 10px;
    border-top: 1px solid #eee;
}

.control-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px 6px;
    border-radius: 6px;
    transition: all 0.2s;
    min-width: 60px;
    font-size: 12px;
}

.control-btn:hover:not(:disabled) {
    background-color: rgba(33, 150, 243, 0.1);
}

.control-btn:active:not(:disabled) {
    background-color: rgba(33, 150, 243, 0.2);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-icon {
    font-size: 18px;
    margin-bottom: 2px;
    color: #2196f3;
}

.btn-text {
    font-size: 11px;
    color: #333;
    white-space: nowrap;
}

.control-btn:disabled .btn-icon,
.control-btn:disabled .btn-text {
    color: #999;
}

.record-btn.recording .btn-icon {
    color: #f44336;
    animation: pulse 1s infinite;
}

.record-btn.recording .btn-text {
    color: #f44336;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 480px) {
    .video-player-card {
        width: 95%;
        max-width: none;
    }

    .control-btn {
        min-width: 50px;
        padding: 6px 4px;
    }

    .btn-icon {
        font-size: 16px;
    }

    .btn-text {
        font-size: 10px;
    }
}
</style>
