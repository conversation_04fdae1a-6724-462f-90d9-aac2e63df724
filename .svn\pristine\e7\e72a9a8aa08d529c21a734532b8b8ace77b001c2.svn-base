package com.bd.thread;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bd.entity.FisheryPermitInfo;
import com.bd.mapper.CrewExamMapper;
import com.bd.service.UserService;
import com.bd.util.HttpTool;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@Controller
@EnableScheduling
public class UpdateFisheryPermitInfo {
    @Resource
    private CrewExamMapper crewExamMapper;

    @Resource
    private UserService userService;

    public List<String> GetFishPermitInfoList(int pageNum) {
        String token = userService.GetOpenCenterToken();
        System.out.println("-.-" + token);
        String header = "Bearer " + token;
        String url = "http://10.90.7.5:18080/api/service/share/P431503515051888640?conditions=null&pageNum=" + pageNum + "&pageSize=" + 500;
        String lawData = HttpTool.doGet(url, header);
        JSONArray jsonArray = JSONArray.parseArray(lawData);

        List<String> list = new ArrayList<>();
        for (Object obj : jsonArray) {
            list.add(obj.toString());
        }
        return list;
    }

    public void updateFishPermitInfo() throws Exception {
        int pageNum = 1;
        List<String> infoList = new ArrayList<>();
        while (true) {
            try {
                infoList = GetFishPermitInfoList(pageNum);
            } catch (Exception e) {
                continue;
            }
            List<FisheryPermitInfo> permitInfos = new ArrayList<>();
            if (infoList.size() < 2) break;
            for (String permitStr : infoList) {
                JSONObject permitJson = JSONObject.parseObject(permitStr);
                FisheryPermitInfo permitInfo = new FisheryPermitInfo();
                permitInfo.setYcblxkxxwybs(permitJson.getString("ycblxkxxwybs"));
                permitInfo.setYcxxwybs(permitJson.getString("ycxxwybs"));
                permitInfo.setGxsj(permitJson.getString("gxsj"));
                permitInfo.setGxcz(permitJson.getString("gxcz"));
                permitInfo.setYcssdqdm(permitJson.getString("ycssdqdm"));
                permitInfo.setYcssdqmc(permitJson.getString("ycssdqmc"));
                permitInfo.setYyblxkzbh(permitJson.getString("yyblxkzbh"));
                permitInfo.setCzr(permitJson.getString("czr"));
                permitInfo.setSfzhhqyzch(permitJson.getString("sfzhhqyzch"));
                permitInfo.setCzrdz(permitJson.getString("czrdz"));
                permitInfo.setSqbm(permitJson.getString("sqbm"));
                permitInfo.setSqrq(permitJson.getString("sqrq"));
                permitInfo.setSqryzbm(permitJson.getString("sqryzbm"));
                permitInfo.setSqrdh(permitJson.getString("sqrdh"));
                permitInfo.setSqxkzlx(permitJson.getString("sqxkzlx"));
                permitInfo.setSqlx(permitJson.getString("sqlx"));
                permitInfo.setSqly(permitJson.getString("sqly"));
                permitInfo.setQfr(permitJson.getString("qfr"));
                permitInfo.setQfbm(permitJson.getString("qfbm"));
                permitInfo.setQfsj(permitJson.getString("qfsj"));
                permitInfo.setCm(permitJson.getString("cm"));
                permitInfo.setYclb(permitJson.getString("yclb"));
                permitInfo.setYcbm(permitJson.getString("ycbm"));
                permitInfo.setYcjyzsbh(permitJson.getString("ycjyzsbh"));
                permitInfo.setGjdjzsbh(permitJson.getString("gjdjzsbh"));
                permitInfo.setCjg(permitJson.getString("cjg"));
                permitInfo.setCbhhsbm(permitJson.getString("cbhhsbm"));
                permitInfo.setJzwgrq(permitJson.getString("jzwgrq"));
                permitInfo.setCz(permitJson.getString("cz"));
                permitInfo.setXk(permitJson.getString("xk"));
                permitInfo.setXs(permitJson.getString("xs"));
                permitInfo.setZdw(permitJson.getString("zdw"));
                permitInfo.setJdw(permitJson.getString("jdw"));
                permitInfo.setZjzgl(permitJson.getString("zjzgl"));
                permitInfo.setZjxhy(permitJson.getString("zjxhy"));
                permitInfo.setZjxhe(permitJson.getString("zjxhe"));
                permitInfo.setZjxhs(permitJson.getString("zjxhs"));
                permitInfo.setZjgly(permitJson.getString("zjgly"));
                permitInfo.setZjgle(permitJson.getString("zjgle"));
                permitInfo.setZjgls(permitJson.getString("zjgls"));
                permitInfo.setZjjhy(permitJson.getString("zjjhy"));
                permitInfo.setZjjhe(permitJson.getString("zjjhe"));
                permitInfo.setZjjhs(permitJson.getString("zjjhs"));
                permitInfo.setZjsl(permitJson.getString("zjsl"));
                permitInfo.setYzjgl(permitJson.getString("yzjgl"));
                permitInfo.setCtcz(permitJson.getString("ctcz"));
                permitInfo.setYcsl(permitJson.getString("ycsl"));
                permitInfo.setYcrj(permitJson.getString("ycrj"));
                permitInfo.setCwgjzbpzsbh(permitJson.getString("cwgjzbpzsbh"));
                permitInfo.setSkgl(permitJson.getString("skgl"));
                permitInfo.setZccm(permitJson.getString("zccm"));
                permitInfo.setZcsl(permitJson.getString("zcsl"));
                permitInfo.setZczgl(permitJson.getString("zczgl"));
                permitInfo.setZczjxh(permitJson.getString("zczjxh"));
                permitInfo.setZczjsl(permitJson.getString("zczjsl"));
                permitInfo.setZzylx(permitJson.getString("zzylx"));
                permitInfo.setZzydyzyfs(permitJson.getString("zzydyzyfs"));
                permitInfo.setZzydyzyfs_zycs(permitJson.getString("zzydyzyfs_zycs"));
                permitInfo.setZzydyzyfs_jtcsmc(permitJson.getString("zzydyzyfs_jtcsmc"));
                permitInfo.setZzydyzyfs_zykssj(permitJson.getString("zzydyzyfs_zykssj"));
                permitInfo.setZzydyzyfs_zyjssj(permitJson.getString("zzydyzyfs_zyjssj"));
                permitInfo.setZzydyzyfs_zysxms(permitJson.getString("zzydyzyfs_zysxms"));
                permitInfo.setZzydyzyfs_yjmc(permitJson.getString("zzydyzyfs_yjmc"));
                permitInfo.setZzydyzyfs_yjgg(permitJson.getString("zzydyzyfs_yjgg"));
                permitInfo.setZzydyzyfs_yjsl(permitJson.getString("zzydyzyfs_yjsl"));
                permitInfo.setZzydyzyfs_zyblpz(permitJson.getString("zzydyzyfs_zyblpz"));
                permitInfo.setZzydyzyfs_blxe(permitJson.getString("zzydyzyfs_blxe"));
                permitInfo.setZzydyzyfs_blscpsldw(permitJson.getString("zzydyzyfs_blscpsldw"));
                permitInfo.setZzydyzyfs_zxwmhwncc(permitJson.getString("zzydyzyfs_zxwmhwncc"));
                permitInfo.setZzydezyfs_zyfs(permitJson.getString("zzydezyfs_zyfs"));
                permitInfo.setZzydezyfs_sqzycs(permitJson.getString("zzydezyfs_sqzycs"));
                permitInfo.setZzydezyfs_jtcsmc(permitJson.getString("zzydezyfs_jtcsmc"));
                permitInfo.setZzydezyfs_zykssj(permitJson.getString("zzydezyfs_zykssj"));
                permitInfo.setZzydezyfs_zyjssj(permitJson.getString("zzydezyfs_zyjssj"));
                permitInfo.setZzydezyfs_sqzysxms(permitJson.getString("zzydezyfs_sqzysxms"));
                permitInfo.setZzydezyfs_yjmc(permitJson.getString("zzydezyfs_yjmc"));
                permitInfo.setZzydezyfs_yjgg(permitJson.getString("zzydezyfs_yjgg"));
                permitInfo.setZzydezyfs_yjsl(permitJson.getString("zzydezyfs_yjsl"));
                permitInfo.setZzydezyfs_zyblpz(permitJson.getString("zzydezyfs_zyblpz"));
                permitInfo.setZzydezyfs_blxe(permitJson.getString("zzydezyfs_blxe"));
                permitInfo.setZzydezyfs_bldw(permitJson.getString("zzydezyfs_bldw"));
                permitInfo.setZzydezyfs_zxwmhwncc(permitJson.getString("zzydezyfs_zxwmhwncc"));
                permitInfo.setJzylx(permitJson.getString("jzylx"));
                permitInfo.setJzydyzyfs(permitJson.getString("jzydyzyfs"));
                permitInfo.setJzydyzyfs_zycs(permitJson.getString("jzydyzyfs_zycs"));
                permitInfo.setJzydyzyfs_jtcsmc(permitJson.getString("jzydyzyfs_jtcsmc"));
                permitInfo.setJzydyzyfs_zykssj(permitJson.getString("jzydyzyfs_zykssj"));
                // permitInfo.setJzydyzyfs_zyjssj(permitJson.getString("jzydyzyfs_zyjssj"));
                // permitInfo.setJzydyzyfs_zysxms(permitJson.getString("jzydyzyfs_zysxms"));
                // permitInfo.setJzydyzyfs_yjmc(permitJson.getString("jzydyzyfs_yjmc"));
                // permitInfo.setJzydyzyfs_yjgg(permitJson.getString("jzydyzyfs_yjgg"));
                // permitInfo.setJzydyzyfs_yjsl(permitJson.getString("jzydyzyfs_yjsl"));
                // permitInfo.setJzydyzyfs_zyblpz(permitJson.getString("jzydyzyfs_zyblpz"));
                // permitInfo.setJzydyzyfs_blxe(permitJson.getString("jzydyzyfs_blxe"));
                // permitInfo.setJzydyzyfs_bldw(permitJson.getString("jzydyzyfs_bldw"));
                // permitInfo.setJzydyzyfs_zxwmhwncc(permitJson.getString("jzydyzyfs_zxwmhwncc"));
                // permitInfo.setJzydezyfs_zyfs(permitJson.getString("jzydezyfs_zyfs"));
                // permitInfo.setJzydezyfs_zycs(permitJson.getString("jzydezyfs_zycs"));
                // permitInfo.setJzydezyfs_jtcsmc(permitJson.getString("jzydezyfs_jtcsmc"));
                // permitInfo.setJzydezyfs_zykssj(permitJson.getString("jzydezyfs_zykssj"));
                // permitInfo.setJzydezyfs_zyjssj(permitJson.getString("jzydezyfs_zyjssj"));
                // permitInfo.setJzydezyfs_zysxms(permitJson.getString("jzydezyfs_zysxms"));
                // permitInfo.setJzydezyfs_yjmc(permitJson.getString("jzydezyfs_yjmc"));
                // permitInfo.setJzydezyfs_yjgg(permitJson.getString("jzydezyfs_yjgg"));
                // permitInfo.setJzydezyfs_yjsl(permitJson.getString("jzydezyfs_yjsl"));
                // permitInfo.setJzydezyfs_zyblpz(permitJson.getString("jzydezyfs_zyblpz"));
                // permitInfo.setJzydezyfs_blxe(permitJson.getString("jzydezyfs_blxe"));
                // permitInfo.setJzydezyfs_bldw(permitJson.getString("jzydezyfs_bldw"));
                // permitInfo.setJzydezyfs_zxwmhwncc(permitJson.getString("jzydezyfs_zxwmhwncc"));
                // permitInfo.setFzzy_fzfs(permitJson.getString("fzzy_fzfs"));
                // permitInfo.setFzzy_zycs(permitJson.getString("fzzy_zycs"));
                // permitInfo.setFzzy_jtzycs(permitJson.getString("fzzy_jtzycs"));
                // permitInfo.setFzzy_zykssj(permitJson.getString("fzzy_zykssj"));
                // permitInfo.setFzzy_zyjssj(permitJson.getString("fzzy_zyjssj"));
                // permitInfo.setFzzy_zysxms(permitJson.getString("fzzy_zysxms"));
                // permitInfo.setFzzy_qtxztj(permitJson.getString("fzzy_qtxztj"));
                // permitInfo.setZzydyzyfs_yjssbz(permitJson.getString("zzydyzyfs_yjssbz"));
                // permitInfo.setZzydezyfs_yjssbz(permitJson.getString("zzydezyfs_yjssbz"));
                // permitInfo.setJzydyzyfs_yjssbz(permitJson.getString("jzydyzyfs_yjssbz"));
                // permitInfo.setJzydezyfs_yjssbz(permitJson.getString("jzydezyfs_yjssbz"));
                permitInfo.setSqsbh(permitJson.getString("sqsbh"));
                permitInfo.setDycs(permitJson.getString("dycs"));
                // permitInfo.setCzsm(permitJson.getString("czsm"));
                permitInfo.setEtl_dt(permitJson.getString("etl_dt"));
                permitInfo.setZsyxq(permitJson.getString("zsyxq"));
                permitInfos.add(permitInfo);
                if (permitInfos.size()>100) {
                    crewExamMapper.insertFisheryPermitInfo(permitInfos);
                    permitInfos.clear();
                }
            }

            System.out.println("updateFisheryPermitInfo-pageNum:" + pageNum);
            //Thread.sleep(5000);
            pageNum++;

            crewExamMapper.insertFisheryPermitInfo(permitInfos);

        }
    }

    //@Scheduled(fixedRate = Integer.MAX_VALUE)
    //@Scheduled(cron = "0 0 1 * * ?")
    public void run() throws Exception {
        crewExamMapper.deleteFisheryPermitInfo();
        updateFishPermitInfo();
    }
}
