package com.bd.entity;

import lombok.Data;

/**
 * 行政执法案例
 *
 * <AUTHOR>
 */
@Data
public class AdminLawExample {
    /**
     * 案件名称
     */
    private String ajmc;
    /**
     * 案发水域
     */
    private String afsy;
    /**
     * 作案水域归类
     */
    private String zasygl;
    /**
     * 立案日期
     */
    private String larq;
    /**
     * 结案日期
     */
    private String jarq;
    /**
     * 处罚结果
     */
    private String cf_result;
    /**
     * 处罚机关
     */
    private String cfjg;
    /**
     * 船名号
     */
    private String cmh;
    /**
     * 案件来源
     */
    private String ajly;
    /**
     * 案发地点
     */
    private String afdd;
    /**
     * 网具
     */
    private String wj;
    /**
     * 是否禁用渔具
     */
    private String sfjyyj;
    /**
     * 渔获物
     */
    private String yhw;

}
