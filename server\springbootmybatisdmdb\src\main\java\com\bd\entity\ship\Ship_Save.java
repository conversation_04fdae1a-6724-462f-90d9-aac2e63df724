package com.bd.entity.ship;

public class Ship_Save {
    public String ship_name;
    public String ship_no;
    public String verifyenr_no;
    public String seaworthy_to_area;
    public String link_rated_load_num;
    public String safe_fishery_remark;
    public String passengers_num;
    public String save_equipment_can_used_num;
    public String ship_check_left_type;
    public String save_eqt_left_num;
    public String save_eqt_left_accommodated;
    public String ship_check_right_type;
    public String save_eqt_right_num;
    public String save_eqt_right_accommodated;
    public String life_saving_data_model_cn1;
    public String life_saving_data_max_permit1;
    public String life_saving_data_amount1;
    public String life_saving_data_model_cn2;
    public String life_saving_data_max_permit2;
    public String life_saving_data_amount2;
    public String life_saving_float_type_cn;
    public String life_saving_float_crew_quota;
    public String life_saving_float_amount;
    public String life_buoy_model_name_cn;
    public String life_buoy_num;
    public String life_jacket_model;
    public String life_jacket_num;
    public String vhf_type;
    public String vhf_power;
    public String cgr_rmhf_type;
    public String cngrrmfh_power;
    public String cngrrtel_type;
    public String cngrr_num;
    public String cngrres_type;
    public String cngrres_num;
    public String cngrras_type;
    public String cngrras_num;
    public String cngrrr_type;
    public String cngrrr_num;
    public String cngrrn_type;
    public String cngrrn_num;
    public String cngrrother_eq;
    public String op_state;
    public String create_by;
    public String create_time;
    public String update_by;
    public String update_time;
    public String is_deleted;

}
