package com.bd.entity.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * 渔船信息查询封装对象
 */
@Data
public class ShipQueryDto extends PageQueryDto {
    private String shipName;
    private String shipCode;
    private String shipLength;
    private String shipPerson;
    private String shipOwnerName;
    private String licenseNumber;
    private String nineDigitCode;
    private String householdNumber;
    private String regionOfShip;
    private Boolean includesSubArea;
    private String minEnginPower;
    private String maxEnginPower;
    private String workType1;
    private String workType2;
    private String shipMaterial;
    private String shipTypes;
    private String minShipLength;
    private String maxShipLength;
    private String minTonnage;
    private String maxTonnage;
    private String startDateOfConstruction;
    private String endDateOfConstruction;
    private String isInProcedure;
    private String bdId;
    private String mmsi;
    private String shipType;
    private String shipSmallOrBig;
    private String personSmallOrBig;
    private List<Long> managerIdList;

    @Override
    public int getPageNum() {
        return pageNum;
    }

    public List<String> getShipTypes() {
        if (shipTypes == null || shipTypes.isEmpty())
            return new ArrayList<>();
        return Arrays.asList(shipTypes.split(","));
    }

    public List<String> getShipMaterial() {
        if (shipMaterial == null || shipMaterial.isEmpty())
            return new ArrayList<>();
        return Arrays.asList(shipMaterial.split(","));
    }

    public List<String> getWorkType1() {
        if (workType1 == null || workType1.isEmpty())
            return new ArrayList<>();
        return Arrays.asList(workType1.split(","));
    }
    public List<String> getWorkType2() {
        if (workType2 == null || workType2.isEmpty())
            return new ArrayList<>();
        return Arrays.asList(workType2.split(","));
    }
}
