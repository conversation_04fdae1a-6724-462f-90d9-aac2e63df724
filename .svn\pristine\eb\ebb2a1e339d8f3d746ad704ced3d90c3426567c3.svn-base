!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("ExcelJS")):"function"==typeof define&&define.amd?define(["ExcelJS"],t):"object"==typeof exports?exports.Table2Excel=t(require("ExcelJS")):e.Table2Excel=t(e.ExcelJS)}(this,function(e){return function(e){function t(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}var n={};return t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:r})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=3)}([function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),n.d(t,"saveAsExcel",function(){return i}),n.d(t,"columnIndex",function(){return l}),n.d(t,"cellPosition",function(){return c}),n.d(t,"mergeCells",function(){return u}),n.d(t,"argb",function(){return s});var r=n(1),o=n(6),i=(n.n(o),function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"table",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"xlsx",i=r.a[n];if(!i)return void console.error(n+" file extension is not supported");e.xlsx.writeBuffer().then(function(e){Object(o.saveAs)(new Blob([e.buffer],{type:i}),t+"."+n)})}),a=function(e){var t="A".charCodeAt(0);return String.fromCharCode(t+e-1)},l=function(e){var t=void 0;if((e+=1)<=26)t=a(e);else{var n=e%26,r=Math.floor(e/26);t=0===n?a(r-1)+a(26):a(r)+a(n)}return t},c=function(e,t){return""+l(e)+(t+1)},u=function(e,t,n,r,o){var i=c(t,n),a=c(r,o);return e.mergeCells(i,a),e.getCell(i)},s=function(e){var t=e.split("(")[1].split(")")[0].split(",").map(function(e,t){return 3===t?255*e:e});return 3===t.length&&t.push(255),t.unshift(t.pop()),t.map(function(e){var t=parseInt(e).toString(16);return 1===t.length?"0"+t:t}).join("").toUpperCase()}},function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"b",function(){return o});var r={xls:"application/vnd.ms-excel",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},o=.14},function(e,t,n){"use strict";var r=n(9),o=n(10),i=n(11),a=n(12),l=n(13),c=n(14);t.a={fontPlugin:r.a,fillPlugin:o.a,formPlugin:i.a,alignmentPlugin:a.a,hyperlinkPlugin:l.a,autoWidthPlugin:c.a}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(4),o=n(2),i=n(0);r.a.plugins=o.a,r.a.utils=i,t.default=r.a},function(e,t,n){"use strict";function r(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var i=n(5),a=n.n(i),l=n(0),c=n(1),u=n(2),s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},f=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),p=["workbookCreated","worksheetCreated","worksheetCompleted","workcellCreated"],d={views:[{x:0,y:0,width:1e4,height:2e4,firstSheet:0,activeTab:1,visibility:"visible"}]},v={workbook:d,widthRatio:c.b,exportStyle:!0,plugins:[]},h=function(){function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"table",i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};o(this,e),this.tables=Array.from("string"==typeof n?document.querySelectorAll(n):n),this.options=Object.assign({},v,i),this.options.exportStyle&&(this.options.plugins=[].concat(r(Object.values(u.a)),r(this.options.plugins))),this.plugins={},p.forEach(function(e){t.plugins[e]=t.options.plugins.filter(function(t){return t[e]}).map(function(t){return t[e]})}),this.pluginContext={}}return f(e,[{key:"_invokePlugin",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.pluginContext=s({},this.pluginContext,n),this.plugins[e].forEach(function(e){return e.call(t,t.pluginContext)})}},{key:"toExcel",value:function(){var e=this,t=this.tables,n=this.options,r=new a.a.Workbook;return Object.assign(r,n),this._invokePlugin("workbookCreated",{workbook:r,tables:t}),t.forEach(function(t,n){var o=r.addWorksheet("Sheet "+(n+1));e._invokePlugin("worksheetCreated",{worksheet:o,table:t}),e.toSheet(t,o),e._invokePlugin("worksheetCompleted",{worksheet:o,table:t})}),this.workbook=r}},{key:"toSheet",value:function(e,t){var n=this,o=e.rows.length,i=Math.max.apply(Math,r(Array.from(e.rows).map(function(e){return e.cells.length}))),a=[];Array.from(e.rows).forEach(function(e){Array.from(e.cells).forEach(function(e){a.push({rowRange:{},colRange:{},el:e})})});for(var c=[],u=0;u<o;u++){for(var s=[],f=0;f<i;f++)s.push({cell:null});c.push(s)}for(var p=0,d=0;d<o;d++)for(var v=0;v<i;v++)if(!c[d][v].cell){var h=a[p++],g=h.el,w=g.rowSpan,y=g.colSpan;h.rowRange={from:d,to:d},h.colRange={from:v,to:v};for(var b=d;b<d+w;b++)for(var m=v;m<v+y;m++)c[b][m].cell=h,h.colRange.to=m,h.rowRange.to=b}a.forEach(function(e){var r=e.rowRange,o=e.colRange,i=e.el,a=i.innerText,c=Object(l.mergeCells)(t,o.from,r.from,o.to,r.to),u=getComputedStyle(i);c.value=a,n._invokePlugin("workcellCreated",{workcell:c,cell:i,rowRange:r,colRange:o,cellStyle:u})})}},{key:"export",value:function(e,t){this.workbook||this.toExcel(),Object(l.saveAsExcel)(this.workbook,e,t)}}]),e}();t.a=h},function(t,n){t.exports=e},function(e,t,n){var r,o=o||function(e){"use strict";if(!(void 0===e||"undefined"!=typeof navigator&&/MSIE [1-9]\./.test(navigator.userAgent))){var t=e.document,n=function(){return e.URL||e.webkitURL||e},r=t.createElementNS("http://www.w3.org/1999/xhtml","a"),o="download"in r,i=function(e){var t=new MouseEvent("click");e.dispatchEvent(t)},a=/constructor/i.test(e.HTMLElement)||e.safari,l=/CriOS\/[\d]+/.test(navigator.userAgent),c=function(t){(e.setImmediate||e.setTimeout)(function(){throw t},0)},u=function(e){var t=function(){"string"==typeof e?n().revokeObjectURL(e):e.remove()};setTimeout(t,4e4)},s=function(e,t,n){t=[].concat(t);for(var r=t.length;r--;){var o=e["on"+t[r]];if("function"==typeof o)try{o.call(e,n||e)}catch(e){c(e)}}},f=function(e){return/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e},p=function(t,c,p){p||(t=f(t));var d,v=this,h=t.type,g="application/octet-stream"===h,w=function(){s(v,"writestart progress write writeend".split(" "))};if(v.readyState=v.INIT,o)return d=n().createObjectURL(t),void setTimeout(function(){r.href=d,r.download=c,i(r),w(),u(d),v.readyState=v.DONE});!function(){if((l||g&&a)&&e.FileReader){var r=new FileReader;return r.onloadend=function(){var t=l?r.result:r.result.replace(/^data:[^;]*;/,"data:attachment/file;");e.open(t,"_blank")||(e.location.href=t),t=void 0,v.readyState=v.DONE,w()},r.readAsDataURL(t),void(v.readyState=v.INIT)}if(d||(d=n().createObjectURL(t)),g)e.location.href=d;else{e.open(d,"_blank")||(e.location.href=d)}v.readyState=v.DONE,w(),u(d)}()},d=p.prototype,v=function(e,t,n){return new p(e,t||e.name||"download",n)};return"undefined"!=typeof navigator&&navigator.msSaveOrOpenBlob?function(e,t,n){return t=t||e.name||"download",n||(e=f(e)),navigator.msSaveOrOpenBlob(e,t)}:(d.abort=function(){},d.readyState=d.INIT=0,d.WRITING=1,d.DONE=2,d.error=d.onwritestart=d.onprogress=d.onwrite=d.onabort=d.onerror=d.onwriteend=null,v)}}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||this.content);void 0!==e&&e.exports?e.exports.saveAs=o:null!==n(7)&&null!==n(8)&&void 0!==(r=function(){return o}.call(t,n,t,e))&&(e.exports=r)},function(e,t){e.exports=function(){throw new Error("define cannot be used indirect")}},function(e,t){(function(t){e.exports=t}).call(t,{})},function(e,t,n){"use strict";var r=n(0),o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e};t.a={workcellCreated:function(e){var t=e.workcell,n=(e.cell,e.cellStyle),i=t.style&&t.style.font?t.style.font:{},a=n.fontWeight;t.style=o({},t.style,{font:o({},i,{name:n.fontFamily,color:{argb:Object(r.argb)(n.color)},bold:"bold"===a||+a>600})})}}},function(e,t,n){"use strict";var r=n(0),o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e};t.a={workcellCreated:function(e){var t=e.workcell,n=(e.cell,e.cellStyle),i=Object(r.argb)(n.backgroundColor);t.style="00000000"===i?o({},t.style,{fill:{type:"pattern",pattern:"none"}}):o({},t.style,{fill:{type:"pattern",pattern:"solid",fgColor:{argb:i}}})}}},function(e,t,n){"use strict";t.a={workcellCreated:function(e){var t=e.workcell,n=e.cell,r=n.children[0];r&&["INPUT","SELECT","TEXTAREA"].includes(r.tagName)&&(t.value=r.value)}}},function(e,t,n){"use strict";var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e};t.a={workcellCreated:function(e){var t=e.workcell,n=(e.cell,e.cellStyle),o=n.verticalAlign,i=n.textAlign;t.style=r({},t.style,{alignment:{vertical:o,horizontal:i}})}}},function(e,t,n){"use strict";t.a={workcellCreated:function(e){var t=e.workcell,n=e.cell,r=n.children[0];r&&"A"===r.tagName&&(t.value={text:r.innerText,hyperlink:r.href})}}},function(e,t,n){"use strict";t.a={workcellCreated:function(e){var t=e.worksheet,n=e.colRange,r=(e.cell,e.cellStyle);n.from===n.to&&(t.getColumn(n.from+1).width=+r.width.split("px")[0]*this.options.widthRatio)}}}]).default});