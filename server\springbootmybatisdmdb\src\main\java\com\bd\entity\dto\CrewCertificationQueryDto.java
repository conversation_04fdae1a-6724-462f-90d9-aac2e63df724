package com.bd.entity.dto;

import lombok.Data;

/**
 * 船员信息查询封装对象
 */
@Data
public class CrewCertificationQueryDto extends PageQueryDto {
    final String[] rowLabelArr = {"签发机构", "证书类别", "证书等级", "证书职务", "性别"};
    final String[] colLabelArr = {"证书类别", "证书等级", "证书职务", "年龄", "性别"};
    final String[] analysisTypeArr = {"数量", "占比（%）", "同比（%）", "环比（%）", "自定义"};

    private String rowLabel;
    private String colLabel;
    private String analysisType;
}
