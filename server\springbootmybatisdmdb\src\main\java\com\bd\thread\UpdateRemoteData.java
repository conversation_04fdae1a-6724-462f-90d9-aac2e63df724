package com.bd.thread;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.bd.entity.CheckRecord;
import com.bd.mapper.ThreadMapper;
import com.bd.service.UserService;
import com.bd.util.HttpTool;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Set;

@Component
@EnableScheduling
public class UpdateRemoteData {
    @Resource
    private ThreadMapper threadMapper;

    @Resource
    private UserService userService;

    private String header;

    //@Scheduled(fixedRate = Integer.MAX_VALUE)
    //@Scheduled(cron = "0 0/5 * * * ?")
    public void run() throws Exception {
        System.out.println("UpdateRemoteData.run");
        threadMapper.deleteCheckRecords();
        UpdateCheckRecord();
    }

    /**
     * 执法记录更新
     */
    public void UpdateCheckRecord() {
        String url = "http://10.90.7.5:18080/api/service/share/P502224000835915776?conditions=null&pageNum=%s&pageSize=500";
        int pageNo = 1;
        while (true) {
            try {
                List<CheckRecord> dataList = getDataList(String.format(url, pageNo), CheckRecord.class);
                if (dataList.size() <= 0) break;
                System.out.println("UpdateCheckRecord " + pageNo + "pageSize:" + dataList.size());
                threadMapper.insertCheckRecords(dataList);
            }catch (Exception e){
                System.err.println(e.getMessage());
                break;
            }finally {
                pageNo++;
            }
        }
        System.out.println("UpdateRemoteData.UpdateCheckRecord  end");

    }

    public <T> List<T> getDataList(String url, Class<T> clazz) {
        String data = HttpTool.doGet(url, header);

        List<T> objectList = new ArrayList<>();

        try {
            JSONArray jsonArray = JSON.parseArray(data);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                T object = createObjectFromJson(jsonObject, clazz);
                objectList.add(object);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return objectList;
    }

    public <T> T createObjectFromJson(JSONObject jsonObject, Class<T> clazz) {
        T object = null;

        try {
            object = clazz.newInstance();

            Field[] fields = clazz.getDeclaredFields();

            for (Field field : fields) {
                String fieldName = field.getName();
                String underlineCase = StrUtil.toUnderlineCase(fieldName);
                if (jsonObject.containsKey(fieldName.toLowerCase(Locale.ROOT))) {
                    field.setAccessible(true);
                    Object value = jsonObject.get(underlineCase.toLowerCase(Locale.ROOT));
                    if (value != null) {
                        field.set(object, String.valueOf(value));
                    }
                }
            }
        } catch (InstantiationException | IllegalAccessException | JSONException e) {
            e.printStackTrace();
        }

        return object;
    }


    @PostConstruct
    private void init() {
        this.header = "Bearer " + userService.GetOpenCenterToken();
    }

}
