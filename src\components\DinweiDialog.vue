<template>
  <div v-if="showDialog" class="dialog-overlay">
    <div
      class="dialog-container"
      :style="{ top: position.top + 'px', left: position.left + 'px' }"
    >
      <div
        class="dialog-header"
        @mousedown.prevent="dragStart"
        @touchstart.prevent="dragStart"
      >
        <span>定位</span>
        <span class="close-btn" @mousedown.stop @touchstart.stop @click="close"
          >×</span
        >
      </div>
      <div class="dialog-body">
        <div class="input-group">
          <label>经度:</label>
          <input type="number" v-model.number="longitude.deg" placeholder="">
          <span>度</span>
          <input type="number" v-model.number="longitude.min" placeholder="">
          <span>分</span>
          <input type="number" v-model.number="longitude.sec" placeholder="">
          <span>秒</span>
        </div>
        <div class="input-group">
          <label>纬度:</label>
          <input type="number" v-model.number="latitude.deg" placeholder="">
          <span>度</span>
          <input type="number" v-model.number="latitude.min" placeholder="">
          <span>分</span>
          <input type="number" v-model.number="latitude.sec" placeholder="">
          <span>秒</span>
        </div>
      </div>
      <div class="dialog-footer">
        <button @click="handleLocate">定位</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DinweiDialog',
  data() {
    return {
      showDialog: false,
      longitude: {
        deg: null,
        min: null,
        sec: null,
      },
      latitude: {
        deg: null,
        min: null,
        sec: null,
      },
      isDragging: false,
      position: { top: 280, left: 70 },
      dragStartMouse: { x: 0, y: 0 },
      dragStartDialog: { x: 0, y: 0 },
    };
  },
  methods: {
    open() {
      this.showDialog = true;
    },
    close() {
      this.showDialog = false;
      this.$emit('on-close');
    },
    handleLocate() {
      const { longitude, latitude } = this;
      const isLongitudeEmpty =
        longitude.deg === null &&
        longitude.min === null &&
        longitude.sec === null;
      const isLatitudeEmpty =
        latitude.deg === null &&
        latitude.min === null &&
        latitude.sec === null;

      if (isLongitudeEmpty && isLatitudeEmpty) {
        alert('请输入有效的经纬度');
        return;
      }

      const location = {
        longitude: { ...this.longitude },
        latitude: { ...this.latitude },
      };
      
      // 调用父组件的locationOnMap方法，而不是发出locate事件
      this.$emit('on-locate', location);
    },
    dragStart(event) {
      this.isDragging = true;

      const isTouchEvent = event.type.startsWith('touch');
      const moveEvent = isTouchEvent ? 'touchmove' : 'mousemove';
      const endEvent = isTouchEvent ? 'touchend' : 'mouseup';

      const clientX = isTouchEvent ? event.touches[0].clientX : event.clientX;
      const clientY = isTouchEvent ? event.touches[0].clientY : event.clientY;

      this.dragStartMouse.x = clientX;
      this.dragStartMouse.y = clientY;

      this.dragStartDialog.x = this.position.left;
      this.dragStartDialog.y = this.position.top;

      this._dragMove = this.dragMove.bind(this);
      this._dragEnd = this.dragEnd.bind(this);

      window.addEventListener(moveEvent, this._dragMove, { passive: false });
      window.addEventListener(endEvent, this._dragEnd);
    },
    dragMove(event) {
      if (!this.isDragging) return;

      event.preventDefault();

      const isTouchEvent = event.type.startsWith('touch');
      const clientX = isTouchEvent ? event.touches[0].clientX : event.clientX;
      const clientY = isTouchEvent ? event.touches[0].clientY : event.clientY;

      const dx = clientX - this.dragStartMouse.x;
      const dy = clientY - this.dragStartMouse.y;

      this.position.left = this.dragStartDialog.x + dx;
      this.position.top = this.dragStartDialog.y + dy;
    },
    dragEnd() {
      this.isDragging = false;

      window.removeEventListener('mousemove', this._dragMove);
      window.removeEventListener('mouseup', this._dragEnd);
      window.removeEventListener('touchmove', this._dragMove);
      window.removeEventListener('touchend', this._dragEnd);
    },
  },
};
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  pointer-events: none;
}

.dialog-container {
  position: absolute;
  background-color: #fff;
  color: #333;
  border-radius: 4px;
  width: auto;
  max-width: 220px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  pointer-events: auto;
  font-size: 12px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  padding: 4px 8px;
  background-color: #1890ff;
  color: white;
  cursor: move;
}

.close-btn {
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
}

.dialog-body {
  padding: 8px;
}

.input-group {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  gap: 2px;
}

.input-group:last-of-type {
  margin-bottom: 0;
}

.input-group label {
  width: 30px;
  flex-shrink: 0;
}

.input-group input {
  width: 100%;
  background-color: #fff;
  border: 1px solid #ccc;
  color: #333;
  padding: 3px;
  text-align: center;
  border-radius: 2px;
  -moz-appearance: textfield; /* Firefox */
  appearance: textfield;
}

.input-group input::-webkit-outer-spin-button,
.input-group input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.input-group input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 3px rgba(24, 144, 255, 0.4);
}

.input-group span {
  padding: 0 1px;
}

.dialog-footer {
  text-align: right;
  padding: 6px 8px;
}

.dialog-footer button {
  background-color: #1890ff;
  color: #fff;
  border: 1px solid #1890ff;
  padding: 3px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.dialog-footer button:hover {
  background-color: #40a9ff;
}
</style>
