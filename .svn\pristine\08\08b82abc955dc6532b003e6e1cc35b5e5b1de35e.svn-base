package com.bd.service;

import com.bd.entity.*;
import com.bd.entity.BusinessManagement.AlarmRecord;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface PortService {

    List<PortInfo> GetAllPortInfo();
    List<PortInfo> GetPortInfo(int pageNum);
    int GetPortInfoCount();

    List<PortInfo> GetOnePortInfo(int portId);
    void AddPort(String name, int pointCount, String pointStr, String content, int maxshapcount, int windlevel, String navmarkmmsi);
    void EditPort(int portId, String newName, int pointCount, String pointStr, String content, int maxshapcount, int windlevel, String navmarkmmsi);
    void DeletePort(int portId);
    List<OutInPortRecord> GetOutInPortRecord();
    List<PortShipCount> GetPortShipCount();

    PageInfo<FisheryBoatInOutReport> getShipInoutReport(String name, String portname, int status, String startTime, String endTime, int pageNum);

    List<OutInPortRecord>GetShip_inOrOutPortInfoDetail(String name, String portName, int statue, String startTime, String endTime, int pageNum);
    List<OutInPortRecord> GetShip_inOrOutPortInfoDetailCount(String name, String portName, int statue, String startTime, String endTime);

    List<OutInPortRecord> GetAllShip_inOrOutPortInfoDetail(String name, String portName, int statue, String startTime, String endTime);

    //在港渔船统计相关
    //判断记录有无
    int queryOnePortCountInfo(Port_InPortShipCount info);
    void insertOnePortCountInfo(Port_InPortShipCount info);
    void updateOnePortCountInfo(Port_InPortShipCount info);

    void InsertOneAlarmInfo(ShipDynamicInfo shipDynamicInfo, int type, String msg, int userId, String areaName);

    List<AlarmRecord> GetTodayWarningInfo(int userId, String updateTime, int model);
    List<AlarmRecord> GetTodayWarningInfo_noResolve();
    void UpdateAlarmState(int id,String updateTime);

    AlarmRecord GetAlarmDetialById(int id, int model);

    List<AlarmRecord> GetAllAlarmRecordInfo(String shipName, String startTime, String endTime, int type, int pageNum, int model);


    List<AlarmRecord> GetAlarmRecordInfo(String shipName, String startTime, String endTime, int type, int model);

    void DeleteAlarmRecordInfo(int alarmRecordId);

    int GetAllAlarmRecordInfoCount(String shipName, String startTime, String endTime, int type, int model);

//    数量

    int GetTodayWarningAllStatistics(int userId, String updateTime, int model);
    List<WarningInfo> GetTodayWarningNewUpdateTime(int userId, String updateTime, int model);


    int GetTodayWarningPortAllInfo(int userId, int model);
    int GetTodayWarningPortUntreatedCountInfo(int userId, int model);
    int GetTodayWarningFishCountInfo(int userId, int model);
    int GetTodayWarningFishUntreatedCountInfo(int userId, int model);
    int GetTodayWarningCrewCountInfo(int userId, int model);
    int GetTodayWarningCrewUntreatedCountInfo(int userId, int model);
    int GetTodayWarningFishCountInfo2(int userId, int model);
    int GetTodayWarningFishUntreatedCountInfo2(int userId, int model);

    //预警统计
    //1
    TongjiModel GetAllAlarmCount_month(int model);
    TongjiModel GetAllAlarmCount_year(int model);
    //2
    TongjiModel GetFXAlarmCount_month(int model);
    TongjiModel GetFXAlarmCount_year(int model);
    //3
    TongjiModel GetJJAlarmCount_month(int model);
    TongjiModel GetJJAlarmCount_year(int model);
    //4
    TongjiModel GetZYAlarmCount_month(int model);
    TongjiModel GetZYAlarmCount_year(int model);
    //5
    TongjiModel GetTXAlarmCount_month();
    TongjiModel GetTXAlarmCount_year();
    //6
    TongjiModel GetZFAlarmCount_month();
    TongjiModel GetZFAlarmCount_year();

    // 视频
    String GetHlsImg(int id, int portId);
    List<CameraInfo> GetCameraInfo();

    int GetCameraInfoPortCount();

    List<String> GetCameraIndexByPortId(int portId);

    String GetHlsById(int id);

    List<CameraInfo> GetCameraInfoBySearch(String searchName);

    List<AlarmRecord> GetHistoryWarningInfo(int type, String startTime, String endTime, int model);

    AlarmRecord GetWarningShipInfoByShipId(int id, int userId, int model);

    List<PortShipInfo> GetShipInfoByPort(int portId);

    List<PortShipInfo> GetAllShipInfo();

    List<PortShipInfo> GetOnLineShipInfo();

    List<PortShipInfo> GetOutLineShipInfo(int type);

    List<PortShipInfo> GetOutPortShipInfo();

    List<PortShipInfo> GetInPortShipInfo(int portId);

    List<PortShipInfo> GetWSInShangHaiShipInfo();

    List<PortShipInfo> GetWSInJinBuShipInfo();

    List<PortShipInfo> GetWSInPortShipInfo(int portId);

    List<PortShipInfo> GetWSIn168169ShipInfo();

    int GetWarningStatistics(int isCheck, int userId, int type, int model);

    int GetInPortShipCount(int id);

    List<ShipDynamicInfo> GetInportShip();

    void updateInportState(int staticShipId);

    void updateInportState2(int staticShipId, int id);

    List<ShipDynamicInfo> GetUpdateShip();

    List<PortShipInfo> GetFishAreaShipInfo(int areaName);

    int GetOutPortCount();
    int GetInPortCount();

    int getTyphoonAlarm(ShipDynamicInfo shipDynamicInfo, String time);

    void UpdateOneAlarmInfo(ShipDynamicInfo shipDynamicInfo, String time);

    int GetTodayFuxiuAlarmByShipName(String shipname);


    List<OutInPortRecord> GetOutInPortRecordByShipId(String shipId, int state);

    List<FisheryBoatInOutReport> get7DaysRecord();

    void UpdateOutlineReason(int id,String newOutLineReason);

    List<PortShipInfo> GetAllShanghaiShipsInfo();

}
