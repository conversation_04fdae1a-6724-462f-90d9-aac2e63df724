<template>
  <div class="xiangxi-dialog">
    <!-- 顶部标题栏 -->
    <div class="header">
      <span class="back-btn" @click="goBack">&lt;</span>
      <span class="title">船舶详细信息</span>
      <div class="header-actions">
        <!-- <span class="action">更多信息</span> -->
        <span class="action">定位</span>
      </div>
    </div>
    <div class="main-content">
      <!-- 船舶基本信息 -->
      <div class="section base-info" v-if="ship">
        <div>船名：{{ ship.shipname || ship.shipName || '-' }}</div>
        <div>船主：{{ ship.owner || '-' }}</div>
        <div>船舶类型：{{ ship.shiptype || '-' }}</div>
        <div>终端类型：{{ ship.terminalType || '-' }}</div>
        <div>作业类型：{{ ship.worktype || '-' }}</div>
        <div>船舶电话：{{ ship.lxdh || '-' }}</div>
        <div>吨位：{{ ship.tonnage ? ship.tonnage + '吨' : '-' }}</div>
        <div>组织机构：{{ ship.organization || '-' }}</div>
      </div>
      <div class="section base-info" v-if="loading" style="text-align:center;">加载中...</div>
      <div class="section base-info" v-if="error" style="color:red;text-align:center;">{{ error }}</div>

      <!-- 终端信息 -->
      <div class="section terminal-info" v-if="ship && ship.terminals && ship.terminals.length">
        <div class="terminal-block" v-for="(terminal, idx) in ship.terminals" :key="idx">
          <div class="terminal-title">{{ terminal.type || '-' }}</div>
          <div>终端号: {{ terminal.number || '-' }}</div>
          <div>经度: {{ formatLonLat(terminal.lon) }}</div>
          <div>纬度: {{ formatLonLat(terminal.lat, true) }}</div>
          <div>航向: {{ terminal.cog ? terminal.cog + '°' : '-' }}</div>
          <div>速度: {{ terminal.speed ? terminal.speed + ' 节' : '-' }}</div>
          <div>报位时间: {{ terminal.time || '-' }}</div>
        </div>
      </div>
      <div class="section terminal-info" v-if="ship && (!ship.terminals || !ship.terminals.length)">
        <div style="color:#bbb;text-align:center;">暂无终端信息</div>
      </div>
      <div class="flex-fill"></div>
    </div>
    <!-- 底部操作按钮 -->
    <div class="footer-btns">
      <button>短信</button>
      <button>调位</button>
      <button>航迹</button>
      <button>收藏</button>
    </div>
  </div>
</template>

<script>
import global from './Global.vue'
export default {
  name: 'XiangxiDialog',
  data() {
    return {
      ship: null,
      loading: false,
      error: ''
    }
  },
  created() {
    this.fetchShipInfo();
  },
  methods: {
    fetchShipInfo() {
      const shipId = this.$route.query.shipId;
      if (!shipId) {
        this.error = '未获取到船舶ID';
        return;
      }
      this.loading = true;
      this.error = '';
      window.$ = window.$ || require('jquery');
      $.get(global.IP + '/web/GetOneShipInfoById?id=' + shipId, (data, status) => {
        if (data && data.length > 0) {
          // 兼容终端信息结构
          let ship = data[0];
          // 假设接口返回的终端信息字段为terminals（如无请调整字段名）
          // 若没有，则可将北斗/AIS等相关字段组装成数组
          if (!ship.terminals) {
            ship.terminals = [];
            // 示例组装（请根据实际字段调整）
            if (ship.bdid || ship.lon || ship.lat) {
              ship.terminals.push({
                type: '北斗',
                number: ship.bdid,
                lon: ship.lon,
                lat: ship.lat,
                cog: ship.cog,
                speed: ship.speed,
                time: ship.bdTime
              });
            }
            if (ship.aisMmsi || ship.aisLon || ship.aisLat) {
              ship.terminals.push({
                type: 'AIS',
                number: ship.aisMmsi,
                lon: ship.aisLon,
                lat: ship.aisLat,
                cog: ship.aisCog,
                speed: ship.aisSpeed,
                time: ship.aisTime
              });
            }
          }
          this.ship = ship;
        } else {
          this.error = '未找到该船舶信息';
        }
        this.loading = false;
      }).fail((msg) => {
        this.error = '获取船舶信息失败';
        this.loading = false;
      });
    },
    goBack() {
      this.$router.back();
    },
    formatLonLat(val, isLat) {
      if (!val) return '-';
      const v = parseFloat(val);
      if (isNaN(v)) return '-';
      const d = parseInt(v);
      const m = parseInt((v - d) * 60);
      const s = parseInt((((v - d) * 60) - m) * 60);
      return d + '°' + m + '′' + s + (isLat ? '″' : '″');
    }
  }
}
</script>

<style scoped>
.xiangxi-dialog {
  background: #fff;
  min-height: 100vh;
  font-size: 16px;
  color: #666;
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding-bottom: 0;
}
.header {
  background: #2196f3;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  height: 56px;
  position: sticky;
  top: 0;
  z-index: 10;
  position: relative;
}
.back-btn {
  font-size: 24px;
  cursor: pointer;
  margin-right: 8px;
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
}
.title {
  font-size: 22px;
  font-weight: bold;
  flex: 0 0 auto;
  margin: 0 auto;
  text-align: center;
}
.header-actions {
  display: flex;
  gap: 12px;
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
}
.action {
  font-size: 16px;
  margin-left: 8px;
  cursor: pointer;
}
.main-content {
  flex: 1 1 auto;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background: #fff;
}
.section {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  text-align: left;
  background: #fff;
}
.base-info div {
  margin-bottom: 6px;
}
.terminal-info {
  background: #fafbfc;
}
.terminal-block {
  margin-bottom: 18px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #e0e0e0;
}
.terminal-title {
  font-weight: bold;
  color: #2196f3;
  margin-bottom: 4px;
}
.footer-btns {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #e3f2fd;
  display: flex;
  justify-content: flex-start;
  padding: 10px 0 10px 16px;
  border-top: 1px solid #b3e5fc;
  z-index: 20;
}
.footer-btns button {
  background: #4fc3f7;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 10px 22px;
  font-size: 16px;
  font-weight: bold;
  box-shadow: 0 2px 6px rgba(33,150,243,0.08);
  cursor: pointer;
  transition: background 0.2s;
  margin-right: 16px;
}
.footer-btns button:active {
  background: #0288d1;
}
.flex-fill {
  flex: 1 1 auto;
}
</style>
