package com.bd.entity;

import com.bd.util.M_POINT;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
public class FishArea {
        private int name;
        private String pointStr;
        private List<M_POINT> points;

        public List<M_POINT> pointsByStr(){
                if (pointStr == "")
                        return null;
                List<M_POINT> points = new ArrayList<>();
                for (int i = 0; i < pointStr.split("#").length; i++){
                        M_POINT point = new M_POINT();
                        String sLon = pointStr.split("#")[i].split("@")[0];
                        String sLat = pointStr.split("#")[i].split("@")[1];
                        //System.out.println(String.format("%10s0", sLon));
                        point.x = Integer.parseInt(sLon.trim());
                        point.y = Integer.parseInt(sLat.trim());

                        points.add(point);
                }
                this.points = points;
                return points;
        }
}


