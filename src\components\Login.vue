<!-- 实时船舶预警-主界面 -->
<template>
    <div id="loginWindow" style="width: 100%; height: 100%; position: absolute; top: 0px; left: 0px;">
        <!-- 头部标题栏 -->
        <!-- <div id="LoginTopTitle" style="width: 100%; height: 10%; position: absolute; top: 0px; left: 0px; z-index: 5;">
            <img src="../../static/img/top-title-background.png" style="width: 100%; height: 100%; position: absolute; top: 0px; left: 0px; z-index: 3;">

        </div> -->
        <div id="LoginTopTitle" style="width: 100%; height: 100%; position: absolute; top: 0px; left: 0px; z-index: 4;">
            <img src="../../static/img/BG.jpeg" style="width: 100%; height: 100%; position: absolute; top: 0px; left: 0px; z-index: 3;">

        </div>

        <div id="Login" style="display: flex; justify-content: center; align-items: center; position: absolute; width: 100%; height: 90%; top: 8%;">
            <div id="login" style="position: relative; width: 550px; max-width: 90%; height: auto; min-height: 350px; background-color: rgb(9, 37, 88); background: rgba(9, 37, 88, 0.8); margin: auto; z-index: 11; border-radius: 8px; padding: 20px;">
                <div style="width: 100%; height: auto; margin-bottom: 25px; text-align: center;">
                    <span style="color: white; font-size: 25px; line-height: 1.3; display: inline-block; max-width: 100%; word-wrap: break-word;">上海市渔港渔船监管系统</span>
                </div>
                <div style="width: 100%; height: auto; margin-bottom: 20px; display: flex; align-items: center;">
                    <div style="width: 36px; /* 图标容器 */ height: 42px; background-color: rgb(9, 37, 88); background: rgba(9, 37, 88, 0.8); border: 1px solid white; border-top-left-radius: 5px; border-bottom-left-radius: 5px; display: flex; align-items: center; justify-content: center;">
  <img src="../../static/img/account.png" style="width: 55%; height: 55%;"></div>
  <input v-model="username" type="text" style="flex: 1; height: 42px; color: white; background-color: rgb(9, 37, 88); background: rgba(9, 37, 88, 0.1); border: 1px solid white; border-top-right-radius: 3px; border-bottom-right-radius: 3px; padding: 0 16px 0 44px !important; /* 增加左内边距确保文本从图标右侧开始显示 */ border-left: none; box-sizing: border-box !important;" placeholder="请输入用户名">
                </div>
                <div style="width: 100%; height: auto; margin-bottom: 20px; display: flex; align-items: center;">
                    <div style="width: 36px; /* 图标容器 */ height: 42px; background-color: rgb(9, 37, 88); background: rgba(9, 37, 88, 0.8); border: 1px solid white; border-top-left-radius: 5px; border-bottom-left-radius: 5px; display: flex; align-items: center; justify-content: center;">
  <img src="../../static/img/password.png" style="width: 55%; height: 55%;"></div>
  <input v-model="password" id="psw" type="password" style="flex: 1; height: 42px; color: white; background-color: rgb(9, 37, 88); background: rgba(9, 37, 88, 0.1); border: 1px solid white; border-top-right-radius: 3px; border-bottom-right-radius: 3px; padding: 0 16px 0 44px !important; /* 增加左内边距确保文本从图标右侧开始显示 */ border-left: none; box-sizing: border-box !important;" placeholder="请输入密码">
                </div>
                <button @click="userLogin" style="width: 90%; /* 减小按钮宽度 */ height: 50px; /* 增加按钮高度 */ background-color: rgb(36 , 111, 168); border: 0; border-radius: 4px; font-size: 18px; /* 增大字体大小 */ color: white; padding: 0; margin: 25px auto 0; /* 下移25px并水平居中 */">
                    登录
                </button>
            </div>
        </div>
    </div>
</template>
  
<script>
import global from './Global.vue';
import $ from "jquery";
import getCurTime from '../../static/js/getCurTime.js'
var _this = {};

export default {
    name: 'HelloWorld',
    data () {
        return {
            msg: 'Welcome to Your Vue.js App',
            leftUpperContext: {},
            leftUpperContext1: {},
            leftLowerContext: {},
            rightUpperContext: {},
            righVedioContext: {},
            righWeatherContext: {},
            canvasContext: {},
            time: '',
            shipId: 0,
            iShipState: 0,
            pointLayerPosCamera: 0,
            ringData1: 50,
            ringData2: 50,
            portName: 'Xijiagang',
            isFirstTime: true,
            productList:[{id:"奚家港",title:"奚家港"},{id:"上海港",title:"上海港"}],
            ProductActive:"奚家港",//获取被选中的value值 默认选中的是1(北京)
            initCount: 0,
            pubKey: '',
            leftShow: true,
            rightShow: true,
            leftShowdistence: 0,
            rightShowdistence: 0,
            hls: '',
            oWebControl: null,
            lawenforceitem: 0,
            areasitem: 0,
            pwdshow: 0,
            username: "",
            password: "",
        }
    },
    beforeCreate() {
        _this = this;
        _this.username = "";
        _this.password = "";
        // alert(window.location.href);    
    },
    mounted () {

    },
    methods: {
        userLogin: function () {    
            var usernameu = _this.username;
            var passwordu = _this.password;
            // if(usernameu == "admin") {
            //     setAlertWindowShow('loginWindow', '该账号已被锁定', '', 2, 1);
            //     return
            // }
            var data = {};
            data["username"] = usernameu;
            data["password"] = passwordu;
            $.ajax({
            url: global.IP + "/web/Login",
            type:"POST",
            data: JSON.stringify(data),
            dataType: "json",
            contentType:"application/json",
            dateString: true,

            success: function(data){
                var states = data.state;
                var success = "登陆成功";
                // console.log(data);
                if (states == success) {
                    // 保存用户登录信息到sessionStorage
                    sessionStorage.setItem("isLogin", true);
                    sessionStorage.setItem("userId", data.id);
                    sessionStorage.setItem("username", data.username);
                    sessionStorage.setItem("jurisdiction", data.level);
                    sessionStorage.setItem("systemPage", 0);
                    // 保存用户区域信息，用于摄像头过滤
                    sessionStorage.setItem("userArea", data.userArea || '');
                    // 保存用户可查询的船舶区域信息
                    sessionStorage.setItem("shipQueryAreas", data.shipQueryAreas || '');
                    if(sessionStorage.getItem(data.username) != null){
                        sessionStorage.setItem(data.username);
                    }
                    
                    var operationInfo = {};
                    // var myDate = new Date();
                    // var year = myDate.getFullYear();    //获取完整的年份(4位,1970-????)
                    // var month = myDate.getMonth() + 1;       //获取当前月份(0-11,0代表1月)
                    // var date = myDate.getDate();        //获取当前日(1-31)
                    // var hour = myDate.getHours();       //获取当前小时数(0-23)
                    // var minute = myDate.getMinutes();     //获取当前分钟数(0-59)
                    // var second = myDate.getSeconds();     //获取当前秒数(0-59)
                    // month = month <= 9 ? "0" + month : month;
                    // date = date <= 9 ? "0" + date : date;
                    // hour = hour <= 9 ? "0" + hour : hour;
                    // minute = minute <= 9 ? "0" + minute : minute;
                    // second = second <= 9 ? "0" + second : second;
                    // var time = year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second;
                    var timeElement = getCurTime();
                    var time = timeElement.year + "-" + timeElement.month + "-" + timeElement.date + " " + timeElement.hour + ":" + timeElement.minute + ":" + timeElement.second;
                    // console.log("测试" + time);
                    operationInfo["userId"] = data.id;
                    operationInfo["type"] = 0;
                    operationInfo["content"] = "登录成功";
                    operationInfo["loadTime"] = time;
                    //operationInfo['ipAdress'] = "***********";
                    $.ajax({
                        url: global.IP + "/web/SetUserOperationInfo",
                        type:"POST",
                        data: JSON.stringify(operationInfo),
                        dataType: "json",
                        contentType:"application/json",
                        dateString: true,

                        success: function(data){
                            
                        },
                        error: function(data){
                            
                        },
                    });

                    // 登录成功后跳转到首页
                    _this.$router.push({
                        name: "Index"
                    });
                }
                else {
                    let state = data.state;
                    switch(state){
                        case '用户不存在':
                            setAlertWindowShow('loginWindow', state, '', 2, 1);
                            break;
                        case '密码错误':
                            setAlertWindowShow('loginWindow', state, '', 2, 1);
                            break;
                        case '输入错误密码超出限制':
                            setAlertWindowShow('loginWindow', state, '', 2, 1);
                            break;
                    }
                    _this.SetUserOperationInfo(0,"登录失败", "已记录登陆失败", "未能记录登陆失败");
                }
            },
                error: function(jqXHR){
                    setAlertWindowShow('loginWindow', '网络异常', '', 2, 1);
                    //alert("网络异常");
                },
            });
        },

    },
    beforeDestroy() {

    }
}
</script>


<style scoped>
@import '../../static/css/ul.css';
input{
    outline:none;
}

/* 移动端适配 */
  @media screen and (max-width: 768px) {
      #Login {
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          position: absolute !important;
          width: 100% !important;
          height: 100% !important;
          top: 0 !important;
          left: 0 !important;
      }
      
      #login {
          position: relative !important;
          width: 90% !important;
          max-width: 350px !important;
          min-width: 280px !important;
          height: auto !important;
          min-height: 320px !important;
          padding: 20px 15px !important;
          margin: auto !important;
          box-sizing: border-box !important;
          background-color: rgba(9, 37, 88, 0.8) !important;
      }
      
      #login > div:first-child {
          width: 100% !important;
          height: auto !important;
          position: relative !important;
          margin-bottom: 20px !important;
      }
      
      #login > div:first-child span {
          width: 100% !important;
          height: auto !important;
          text-align: center !important;
          display: block !important;
          font-size: 20px !important;
          line-height: 1.3 !important;
          color: white !important;
          padding: 0 10px !important;
          box-sizing: border-box !important;
      }
      
      #login > div:nth-child(2),
      #login > div:nth-child(3) {
          width: 100% !important;
          height: auto !important;
          position: relative !important;
          margin-bottom: 15px !important;
          padding: 0 5px !important;
          box-sizing: border-box !important;
      }
      
      #login > div:nth-child(2) input,
      #login > div:nth-child(3) input {
          
          height: 42px !important;
          position: relative !important;
          
          box-sizing: border-box !important;
          font-size: 12px !important; /* 进一步减小字体防止截断 */ /* 减小字体大小确保完整显示 */
          padding: 0 10px !important;
          color: white !important;
          border: 1px solid rgba(255, 255, 255, 0.5) !important;
      }
      
      #login > div:nth-child(2) div,
      #login > div:nth-child(3) div {
          position: absolute !important;
          top: 0 !important;
          left: 5px !important;
          width: 40px !important;
          height: 42px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
      }
      
      #login button {
          width: 100% !important;
          height: 44px !important;
          position: relative !important;
          margin: 10px 0 !important;
          box-sizing: border-box !important;
          font-size: 16px !important;
          background-color: rgb(36, 111, 168) !important;
          color: white !important;
          border: none !important;
          border-radius: 4px !important;
          padding: 0 !important;
      }
  }
  
  @media screen and (max-width: 480px) {
    #login {
        width: 95% !important;
        max-width: 400px !important; /* 最大化登录框宽度 */
        min-width: 260px !important;
        padding: 15px 15px !important; /* 增加内边距提供更多空间 */
        min-height: 300px !important;
    }
    
    #login > div:first-child span {
        font-size: 18px !important;
    }
    
    #login button {
        font-size: 16px !important;
        height: 42px !important;
    }
}

@media screen and (max-width: 320px) {
    #login > div:first-child span {
        font-size: 16px !important;
        padding: 0 5px !important;
    }
    
    #login {
        padding: 10px 8px !important;
        min-height: 280px !important;
    }
    
    #login > div:nth-child(2),
    #login > div:nth-child(3) {
        margin-bottom: 12px !important;
    }
    
    #login > div:nth-child(2) input,
#login > div:nth-child(3) input {
    box-sizing: border-box !important;
        height: 40px !important;
        font-size: 13px !important;
    }
    
    #login button {
        height: 40px !important;
        font-size: 15px !important;
    }
}

  </style>