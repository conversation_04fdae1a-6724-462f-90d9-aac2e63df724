import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.bd.util.ExcelUtils;
import com.bd.util.Utils;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.apache.poi.ss.usermodel.Sheet;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class NormalTest {

    @Test
    public void test1(){
        ArrayList<Integer> arr = new ArrayList<>();
        for (int i = 0; i < 1152; i++) {
            arr.add(i);
        }
        int batchSize = 100;
        for (int i = 0; i < arr.size(); i+=batchSize) {
            int end = Math.min(i+batchSize,arr.size());
            // System.out.println(arr.subList(i,end));
        }

        System.out.println(Utils.GetNowTimeString(24*3600));
        System.out.println(StrUtil.repeat("=",50));
        List<List<String>> data = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            DateTime date = DateUtil.date().offset(DateField.YEAR,-i);
            String startTime = DateUtil.format(DateUtil.beginOfYear(date), "yyyy-MM-dd HH:mm:ss");
            String endTime = DateUtil.format(DateUtil.endOfYear(date), "yyyy-MM-dd HH:mm:ss");
            System.out.println(startTime);
            System.out.println(endTime);
            data.add(Arrays.asList(startTime,endTime));
        }


        System.out.println(IdUtil.nanoId());
        System.out.println(IdcardUtil.getAgeByIdCard("31011019711216621X"));
        System.out.println(NumberUtil.parseInt(""));
        System.out.println(Utils.GetNowTimeString(3600 * 3));

        ExcelWriter writer = ExcelUtil.getWriter("C:\\Users\\<USER>\\Desktop\\dd.xlsx");
//跳过当前行，既第一行，非必须，在此演示用
        writer.passCurrentRow();

//合并单元格后的标题行，使用默认标题样式
        writer.merge(data.size() - 1, "测试标题");
       writer.write(data,true);
       writer.close();

    }
    @Test
    public void test() throws IOException {
        String filePath = "C:/Users/<USER>/Desktop/excel1.html"; // 替换为实际的HTML文件路径

        try {
            FileInputStream input = new FileInputStream(filePath);
            Document doc = Jsoup.parse(new File(filePath), "utf-8");

            // 定位到表格元素
            Element table = doc.select("table").first(); // 假设表格是页面上的第一个 table 元素

            // 解析表格数据
            Elements rows = table.select("tr"); // 获取所有行元素
            JSONArray data = new JSONArray();
            JSONObject object = new JSONObject();

            // 解析表头
            Element headerRow = rows.get(2); // 假设表头是第一行。如果不是，请调整索引值。
            Elements headerCells = headerRow.select("td"); // 获取表头单元格
            JSONArray headers = new JSONArray();
            for (Element cell : headerCells) {
                headers.put(convertToPinyin(cell.text().trim()));
            }

            // 解析表格数据
            for (int i = 3; i < rows.size() - 2; i++) { // 跳过第一行，假设第一行是表头
                Element row = rows.get(i);
                Elements tdElements = row.select("td"); // 获取当前行的所有列元素
                JSONObject rowData = new JSONObject();

                for (int j = 0; j < tdElements.size(); j++) {
                    String cellValue = tdElements.get(j).text().trim();
                    rowData.put(headers.optString(j, "Column " + (j + 1)), cellValue); // 若没有表头，则使用默认的列名
                }

                // data.put(rowData);
                object.put(rowData.getString("cm"),rowData);
            }

            // 将结果转换为 JSON 字符串
            // String jsonResult = data.toString();
            System.out.println(object);
            FileUtil.writeString(object.toString(), new File("json.json"), "utf-8");


        } catch (IOException | JSONException e) {
            e.printStackTrace();
        }

        // System.out.println(convertToPinyin("你好"));

    }

    public static String convertToPinyin(String chineseText) {
        StringBuilder pinyinText = new StringBuilder();

        // 将每个汉字转换为拼音的首字母
        for (char c : chineseText.toCharArray()) {
            if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
                String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c);

                // 汉字可能有多个发音，只取第一个发音的首字母
                if (pinyinArray != null && pinyinArray.length > 0) {
                    pinyinText.append(pinyinArray[0].charAt(0));
                } else {
                    // 非汉字直接追加
                    pinyinText.append(c);
                }
            } else {
                // 非汉字直接追加
                pinyinText.append(c);
            }
        }

        return pinyinText.toString();
    }

}
