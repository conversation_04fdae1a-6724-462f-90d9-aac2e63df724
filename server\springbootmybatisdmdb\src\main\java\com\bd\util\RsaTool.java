package com.bd.util;

import java.io.ByteArrayOutputStream;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import javax.crypto.Cipher;

import org.apache.commons.codec.binary.Base64;

/**
 * <AUTHOR>
 * @Date 2024/2/17 16:26
 * @Version 1.0
 */
public class RsaTool {
    /**
     * 16      * RSA最大加密明文大小
     * 17
     */
    private static final int MAX_ENCRYPT_BLOCK = 117;

    /**
     * 21      * RSA最大解密密文大小
     * 22
     */
    private static final int MAX_DECRYPT_BLOCK = 128;

    /**
     * 26      * 获取密钥对
     * 27      *
     * 28      * @return 密钥对
     * 29
     */
    public static KeyPair getKeyPair() throws Exception {
        KeyPairGenerator generator = KeyPairGenerator.getInstance("RSA");
        generator.initialize(1024);
        return generator.generateKeyPair();
    }

    /**
     * 37      * 获取私钥
     * 38      *
     * 39      * @param privateKey 私钥字符串
     * 40      * @return
     * 41
     */
    public static PrivateKey getPrivateKey(String privateKey) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        byte[] decodedKey = Base64.decodeBase64(privateKey.getBytes());
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodedKey);
        return keyFactory.generatePrivate(keySpec);
    }

    /**
     * 50      * 获取公钥
     * 51      *
     * 52      * @param publicKey 公钥字符串
     * 53      * @return
     * 54
     */
    public static PublicKey getPublicKey(String publicKey) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        byte[] decodedKey = Base64.decodeBase64(publicKey.getBytes());
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decodedKey);
        return keyFactory.generatePublic(keySpec);
    }

    /**
     * 63      * RSA加密
     * 64      *
     * 65      * @param data 待加密数据
     * 66      * @param publicKey 公钥
     * 67      * @return
     * 68
     */
    public static String encrypt(String data, PublicKey publicKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        int inputLen = data.getBytes().length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offset = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段加密
        while (inputLen - offset > 0) {
            if (inputLen - offset > MAX_ENCRYPT_BLOCK) {
                cache = cipher.doFinal(data.getBytes(), offset, MAX_ENCRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(data.getBytes(), offset, inputLen - offset);
            }
            out.write(cache, 0, cache.length);
            i++;
            offset = i * MAX_ENCRYPT_BLOCK;
        }
        byte[] encryptedData = out.toByteArray();
        out.close();
        // 获取加密内容使用base64进行编码,并以UTF-8为标准转化成字符串
        // 加密后的字符串
        return new String(Base64.encodeBase64String(encryptedData));
    }

    /**
     * 96      * RSA解密
     * 97      *
     * 98      * @param data 待解密数据
     * 99      * @param privateKey 私钥
     * 100      * @return
     * 101
     */
    public static String decrypt(String data, PrivateKey privateKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] dataBytes = Base64.decodeBase64(data);
        int inputLen = dataBytes.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offset = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段解密
        while (inputLen - offset > 0) {
            if (inputLen - offset > MAX_DECRYPT_BLOCK) {
                cache = cipher.doFinal(dataBytes, offset, MAX_DECRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(dataBytes, offset, inputLen - offset);
            }
            out.write(cache, 0, cache.length);
            i++;
            offset = i * MAX_DECRYPT_BLOCK;
        }
        byte[] decryptedData = out.toByteArray();
        out.close();
        // 解密后的内容
        return new String(decryptedData, "UTF-8");
    }

    /**
     * 129      * 签名
     * 130      *
     * 131      * @param data 待签名数据
     * 132      * @param privateKey 私钥
     * 133      * @return 签名
     * 134
     */
    public static String sign(String data, PrivateKey privateKey) throws Exception {
        byte[] keyBytes = privateKey.getEncoded();
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey key = keyFactory.generatePrivate(keySpec);
        // 不同的签名算法
        Signature signature = Signature.getInstance("MD5withRSA");
        // Signature signature = Signature.getInstance("MD2withRSA");
        // Signature signature = Signature.getInstance("SHA1withRSA");
        // Signature signature = Signature.getInstance("SHA224withRSA");
        // Signature signature = Signature.getInstance("SHA256withRSA");
        // Signature signature = Signature.getInstance("SHA384withRSA");
        // Signature signature = Signature.getInstance("SHA512withRSA");

        signature.initSign(key);
        signature.update(data.getBytes());
        return new String(Base64.encodeBase64(signature.sign()));
    }

    /**
     * 147      * 验签
     * 148      *
     * 149      * @param srcData 原始字符串
     * 150      * @param publicKey 公钥
     * 151      * @param sign 签名
     * 152      * @return 是否验签通过
     * 153
     */
    public static boolean verify(String srcData, PublicKey publicKey, String sign) throws Exception {
        byte[] keyBytes = publicKey.getEncoded();
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey key = keyFactory.generatePublic(keySpec);
        // 不同的签名算法
        Signature signature = Signature.getInstance("MD5withRSA");
        // Signature signature = Signature.getInstance("MD2withRSA");
        // Signature signature = Signature.getInstance("SHA1withRSA");
        // Signature signature = Signature.getInstance("SHA224withRSA");
        // Signature signature = Signature.getInstance("SHA256withRSA");
        // Signature signature = Signature.getInstance("SHA384withRSA");
        // Signature signature = Signature.getInstance("SHA512withRSA");
        signature.initVerify(key);
        signature.update(srcData.getBytes());
        return signature.verify(Base64.decodeBase64(sign.getBytes()));
    }
}