package com.bd.entity;

import com.bd.util.M_POINT;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AreaInfo {
    private int ID;
    private String NAME;
    private int POSCOUNT;
    private String POINTSTR;
    private int TYPE;
    private int ROUTETYPE;
    private String STARTTIME;
    private String ENDTIME;
    private int BSPEED;
    private int SPEED;
    private String LOADTIME;
    private int USERID;

    private List<M_POINT> points;

    public List<M_POINT> pointsByStr(){
        if (POINTSTR == "" || POINTSTR == null)
            return null;
        List<M_POINT> points = new ArrayList<>();
        for (int i = 0; i < POINTSTR.split("#").length; i++){
            M_POINT point = new M_POINT();
            String sLon = POINTSTR.split("#")[i].split("@")[0];
            String sLat = POINTSTR.split("#")[i].split("@")[1];
            point.x = Integer.parseInt(sLon);
            point.y = Integer.parseInt(sLat);

            points.add(point);
        }
        this.points = points;
        return points;
    }
}
