<!-- 船舶详情 -->
<template>
    <div class="vesselDetailsFirst">
        <div id="vesselDetails" class="ShowMouseMoveInfoStyle" style="width: 297px; height: 380px; position: absolute; z-index: 2; color: white;">
            <div id="vesselDetails-head" style="width: 297px; height: 52px; position: absolute; top: 0px; left: 0px; background-color: #0E2F88CC;">
              <div style="width: 200px; height: 20px; position: absolute; top: 18px; left: 15px; z-index: 13; text-align: left;">
                <span id="shipName">
                  {{shipInfoList[0].shipname}}
                </span>
                <span style="color: rgb(247, 153, 157);">
                  {{shipInfoList[0].boutside == 1?'（外省）':''}}
                </span>
              </div>
              <div style="width: 30px; height: 30px; position: absolute; top: 15px; left: 250px;">
                <button style="width: 30px; height: 30px; background-color: #FF0000; border: 0; position: absolute; z-index: 13; background: none; " @click="closeDiv('vesselDetailsFirst')">X</button>
              </div>

              <div v-show="shipImgShow" style="width: 297px; height: 120px; position: absolute; top: 52px; display: none;">
                <img id="pic" style="width: 297px; height: 120px; top: 0px; left: 0px;">
              </div>

            </div>
            <div id="butdiv" style="width: 297px; height: 112px; position: absolute; top: 52px; left: 0px; position: absolute; z-index: 13; background-color: #0E2F88;">
              <button id="fousceInfo" @click="trackPlayback(1)" style="width: 85px; height: 32px; position: absolute; top: 64px; left: 201px; background-color: #4876FF; border: 0;border-radius:4px">
                重点跟踪
              </button>
              <button @click="queryShipInfo()" style="width: 85px; height: 32px; position: absolute; top: 16px; left: 11px; background-color: #4876FF; border: 0;border-radius:4px">
                船舶信息
              </button>
              <button v-show="warningShow == true" @click="queryCrewInfo()" style="width: 85px; height: 32px; position: absolute; top: 16px; left: 106px; background-color: #4876FF; border: 0;border-radius:4px">
                船员信息
              </button>
              <button v-show="warningShow == false" @click="queryCrewInfo()" style="width: 85px; height: 32px; position: absolute; top: 16px; left: 106px; background-color: #FF0000; border: 0;border-radius:4px">
                船员信息
              </button>
              <button @click="bds()" style="width: 85px; height: 32px; position: absolute; top: 64px; left: 11px; background-color: #4876FF; border: 0;border-radius:4px">
                北斗通信
              </button>
              <button @click="trackPlayback(5)" style="width: 85px; height: 32px; position: absolute; top: 64px; left: 106px; background-color: #4876FF; border: 0;border-radius:4px">
                一键回放
              </button>
              <button id="lawInfo" @click="showLawEnforcement()" style="width: 85px; height: 32px; position: absolute; top: 16px; left: 201px; background-color: #4876FF; border: 0;border-radius:4px;">
                执法检查
              </button>
              <button id="areaWithShipInfo" @click="trackPlayback(7)" style="width: 85px; height: 32px; position: absolute; top: 64px; left: 201px; background-color: #00ADEF; border: 0;border-radius:4px; display: none;">
                区域绑定
              </button>
              <button id="wakeInfo" @click="trackPlayback(6)" style="width: 85px; height: 32px; position: absolute; top: 112px; left: 11px; background-color: #FFA500; border: 0;border-radius:4px; display: none;">
                尾迹展示
              </button>

            </div>
            <div id="detailsmess" style="width: 297px; height: 210px; position: absolute; top: 164px; left: 0px; position: absolute; z-index: 13; background-color: #FFFFFF; color: black; text-align: left; font-size: 13px; background: rgba(255,255,255,0.7);overflow:hidden;display: block;">
              <!-- <div style="display: flex; justify-content: space-evenly; margin: 6px auto;">
                <button @click="AisOrBeidou=true">Ais信息</button>
                <button @click="AisOrBeidou=false">北斗信息</button>
              </div> -->

              <div style="position: relative;top: -10px;" v-show="AisOrBeidou">
                <span style="width: auto; height: 20px; position: absolute; top: 10px; left: 10px; font-weight: bold;">
                MMSI:
              </span>
              <span style="width: 90px; height: 20px; position: absolute; top: 10px; left: 50px; overflow: hidden;">
                412040220
              </span>
              <span style="width: 120px; height: 20px; position: absolute; top: 10px; left: 140px; font-weight: bold;">
                船舶类型：
              </span>
              <span style="width: 120px; height: 20px; position: absolute; top: 10px; left: 205px;">
                公务船
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 35px; left: 10px; font-weight: bold;">
                国籍
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 35px;  left: 50px; overflow: hidden;">
                china
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 35px; left: 140px;font-weight: bold;">
                IMO
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 35px; left: 205px;">
                2265353890
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 60px; left: 10px; font-weight: bold;">
                呼号:
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 60px;  left: 50px; overflow: hidden;">
                BSLZ
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 60px; left: 140px; font-weight: bold;">
                设备分类
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 60px; left: 205px;">
                A
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 85px; left: 10px; font-weight: bold;">
                航向：
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 85px;  left: 50px; overflow: hidden;">
                118.5度
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 85px; left: 140px; font-weight: bold;">
                航速:
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 85px; left: 205px;">
                9.5节
              </span>
              <span style="width: 120px; height: 20px; position: absolute; top: 110px; left: 10px; font-weight: bold;">
                纬度:
              </span>
              <span style="width: 85px; height: 20px; position: absolute; top: 110px;  left: 50px; overflow: hidden;">
                31° 06′ 34.07″ N
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 110px; left: 140px; font-weight: bold;">
                经度:
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 110px; left: 205px;">
                121° 56′ 46.68″ E
              </span>

              <span style="width: 120px; height: 20px; position: absolute; top: 135px; left: 10px; font-weight: bold;">
                船长：
              </span>
              <span style="width: 85px; height: 20px; position: absolute; top: 135px;  left: 50px; overflow: hidden;">
                42米
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 135px; left: 140px; font-weight: bold;">
                船宽:
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 135px; left: 205px;">
                8米
              </span>
              <span style="width: 120px; height: 20px; position: absolute; top: 160px; left: 10px; font-weight: bold;">
                艏向:
              </span>
              <span style="width: 85px; height: 20px; position: absolute; top: 160px;  left: 50px; overflow: hidden;">
                118.0度
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 160px; left: 140px; font-weight: bold;">
                吃水:
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 160px; left: 205px;">
                169899664.3米
              </span>

              </div>

              <div style="position: relative;top: 0px;" v-show="!AisOrBeidou">
                <span style="width: auto; height: 20px; position: absolute; top: 10px; left: 10px; font-weight: bold;">
                船名：
              </span>
              <span style="width: 90px; height: 20px; position: absolute; top: 10px; left: 50px; overflow: hidden;">
                {{shipInfoList[0].shipname}}
              </span>
              <span style="width: 120px; height: 20px; position: absolute; top: 10px; left: 140px; font-weight: bold;">
                北斗终端：
              </span>
              <span style="width: 100px; height: 20px; position: absolute; top: 8px; left: 203px; overflow-wrap: break-word;line-height: 1.1;">
                {{shipInfoList[0].bdid}}
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 35px; left: 10px; font-weight: bold;">
                船舶类型：
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 35px; left: 75px;">
                {{shipInfoList[0].shiptype}}
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 38px; left: 140px;font-weight: bold;">
                MMSI：
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 38px; left: 203px;">
                {{shipInfoList[0].mmsi === 0 ? "": shipInfoList[0].mmsi}}
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 60px; left: 10px; font-weight: bold;">
                核载人数：
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 60px; left: 75px;">
                {{(shipInfoList[0].maxPeopleCount == null)? '' : shipInfoList[0].maxPeopleCount}}
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 60px; left: 140px; font-weight: bold;">
                最低配员：
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 60px; left: 205px;">
                {{shipInfoList1[0].littleCount === 0 ? 1 : shipInfoList1[0].littleCount}}
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 85px; left: 10px; font-weight: bold;">
                功率(千瓦)：
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 85px; left: 80px;">
                {{shipInfoList[0].zjzgl === 0 ? "": shipInfoList[0].zjzgl}}
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 85px; left: 140px; font-weight: bold;">
                船长(米)：
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 85px; left: 200px;">
                {{shipInfoList[0].cz === 0 ? "": shipInfoList[0].cz}}
              </span>
              <span style="width: 120px; height: 20px; position: absolute; top: 110px; left: 10px; font-weight: bold;">
                船舶所有人：
              </span>
              <span style="width: 85px; height: 20px; position: absolute; top: 110px; left: 85px;" :title="shipInfoList[0].cbsyrmc">
                {{(shipInfoList[0].cbsyrmc || '-') | shortShow(4)}}
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 110px; left: 140px; font-weight: bold;">
               联系方式：
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 110px; left: 205px;">
                {{shipInfoList[0].lxdh}}
              </span>
              <span style="width: 120px; height: 20px; position: absolute; top: 128px; left: 10px; font-weight: bold;">
                船长：
              </span>
              <span style="width: 60px; height: 20px; position: absolute; top: 128px; left: 85px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;" :title="shipInfoList[0].owner">
                {{shipInfoList[0].captain}}
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 128px; left: 140px; font-weight: bold;">
                联系方式：
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 128px; left: 205px;">
                {{shipInfoList[0].captainlxdh}}
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 143px; left: 140px; font-weight: bold;">
                卫星电话：
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 143px; left: 205px;">
                {{shipInfoList[0].wxlxdh}}
              </span>
              <span style="width: 120px; height: 20px; position: absolute; top: 160px; left: 10px; font-weight: bold;">
                应急联系人：
              </span>
              <span style="width: 60px; height: 20px; position: absolute; top: 160px; left: 85px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;" :title="shipInfoList[0].emergencyContact">
                 {{shipInfoList[0].emergencyContact}}
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 160px; left: 140px; font-weight: bold;">
                联系方式：
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 160px; left: 205px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                {{shipInfoList[0].emergencyContactLxdh}}
              </span>
              <span style="width: 120px; height: 20px; position: absolute; top: 175px; left: 10px; font-weight: bold;">
                实际经营人：
              </span>
              <span style="width: 60px; height: 20px; position: absolute; top: 175px; left: 85px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;" :title="shipInfoList[0].sjOperator">
                 {{shipInfoList[0].sjOperator}}
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 175px; left: 140px; font-weight: bold;">
                联系方式：
              </span>
              <span style="width: auto; height: 20px; position: absolute; top: 175px; left: 205px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                {{shipInfoList[0].sjOperatorLxdh}}
              </span>
              </div>

            </div>
            <div id="sendmess" style="width: 297px; height: 263px; position: absolute; top: 164px; left: 0px; position: absolute; z-index: 13; background-color: rgb(14, 47, 136); color: black; text-align: left; font-size: 13px; background: rgba(255,255,255,0.7);overflow:hidden;display: none;">
              <div style="position: absolute; width: 297px; height: 226px; top: 1px; left: 0; resize: none; background-color: #FFFFFF; color: black;">
                <div style="width: 297px; height: 140px; max-height: 140px; position: absolute; top: 1px; left: 0; overflow-x: hidden; overflow-y: scroll;">
                  <div v-for="(item, index) in realBdMsg" :key="index" style="width: 100%; height: auto; min-height: 20px; color: black;">
                    {{item.time}} [{{item.sender}}] 发送了"{{item.msg}}"
                  </div>
                </div>
                <div style="position: absolute; width: 297px; height: 86px; top: 141px; left: 0; resize: none; background-color: #FFFFFF; color: black;">
                  <textarea v-model="bdMsg" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0; border: 1px solid #0E2F88; resize: none;" placeholder="请输入通信内容">

                  </textarea>
                </div>
              </div>

              <button @click="openBeiDouDetial()" style="position: absolute; background-color: rgb(27,103,244); bottom: 5px; right: 120px; border: 0; color: white; border-radius: 4px; font-size: 16px;">详情</button>
              <button @click="send()" style="position: absolute; background-color: rgb(27,103,244); bottom: 5px; right: 70px; border: 0; color: white; border-radius: 4px; font-size: 16px;">发送</button>
              <button @click="cancel()" style="position: absolute; background-color: rgb(27,103,244); bottom: 5px; right: 20px; border: 0; color: white; border-radius: 4px; font-size: 16px;">取消</button>
            </div>
            <div id="speedmess" style="width: 297px; height: 60px; position: absolute; top: 360px; left: 0px; position: absolute; z-index: 13; background-color: #0E2F88; background: rgba(14,47,136,0.8);">
              <div style="width: 95px; height: 60px; top: 0px; left: 0px; position: absolute; background-color: #192965; border-radius:8px">
                <span style="position: absolute; left: 10px; top: 10px; font-weight: bold;">
                  速度：
                </span>
                <br />
                <span style="position: absolute; left: 10px; top: 35px;">
                  {{shipInfoList1[0].speed}}节
                </span>
              </div>
              <div style="width: 95px; height: 60px; top: 0px; left: 101px;  position: absolute; background-color: #192965;border-radius:8px">
                <span style="position: absolute; left: 10px; top: 10px; font-weight: bold;">
                  角度：
                </span>
                <br />
                <span style="position: absolute; left: 10px; top: 35px;">
                  {{shipInfoList1[0].course}}°
                </span>
              </div>
              <div style="width: 95px; height: 60px; top: 0px; left: 202px;  position: absolute; background-color: #192965;border-radius:8px">
                <span style="position: absolute; left: 10px; top: 10px; font-weight: bold;">
                  报位终端号：
                </span>
                <br />
                <span style="position: absolute; left: 10px; top: 35px;">
                  {{shipInfoList[0].lastPosTermNo}}
                </span>
              </div>
            </div>
            <div id="localmess" style="width: 297px; height: 91px; position: absolute; top: 420px; left: 0px; position: absolute; z-index: 13; background-color: #0E2F88; text-align: left; background: rgba(14,47,136,0.8);">
              <span style="position: absolute; left: 10px; top: 10px; font-weight: bold;">
                北斗报位时间：
              </span>
              <span style="position: absolute; left: 110px; top: 10px;">
                {{shipInfoList1[0].bdTime == null ? '-' : shipInfoList1[0].bdTime}}
              </span>
              <br />
              <span style="position: absolute; left: 10px; top: 35px; font-weight: bold;">
                AIS报位时间：
              </span>
              <span style="position: absolute; left: 110px; top: 35px;">
                {{shipInfoList1[0].aisTime == null ? '-' : shipInfoList1[0].aisTime}}
              </span>
              <br />
              <span style="position: absolute; left: 10px; top: 60px; font-weight: bold;">
                船舶最新位置：
              </span>
              <span style="position: absolute; left: 110px; top: 60px;">
                {{shipInfoList1[0].point}}
              </span>
              <button id="clientInfo" class="button" @click="showClientInfo()" style=" position: absolute; top: 20px; right: 5px; display: none;">
                终端信息
              </button>
            </div>
          </div>

    </div>
</template>

<script>

import global from './Global.vue';
import $ from 'jquery';
import shipdetails from '@/components/ShipDetails.vue';
var _this = {};

export default {
  name: "HelloWorld",
  props: {
    shipInfoImg: {
      type: String,
      required: true,
    },
    shipInfoList: {
      type: Array,
      required: true,
    },
    shipInfoList1: {
      type: Array,
      required: true,
    },
    wakeIsShowOrNot: {
      type: Boolean,
      required: true,
    },
    fouInfo: {
      type: String,
      required: true,
    }
  },
  data() {
    return {
      // shipInfoShow1: require('http://***********:7001/camera/hsyg05.jpg'),
      msg: "Welcome to Your Vue.js App",
      shipImgShow: false,
      leftUpperContext: {},
      leftUpperContext1: {},
      leftLowerContext: {},
      rightUpperContext: {},
      righVedioContext: {},
      righWeatherContext: {},
      canvasContext: {},
      time: "",
      shipId: 0,
      iShipState: 0,
      pointLayerPosCamera: 0,
      ringData1: 50,
      ringData2: 50,
      portName: "Xijiagang",
      isFirstTime: true,
      productList: [
        { id: "奚家港", title: "奚家港" },
        { id: "上海港", title: "上海港" },
      ],
      ProductActive: "奚家港", //获取被选中的value值 默认选中的是1(北京)
      initCount: 0,
      pubKey: "",
      leftShow: true,
      rightShow: true,
      leftShowdistence: 0,
      rightShowdistence: 0,
      shipInfoByList: [],
      bdMsg: '',
      isTrackShow: false,
      realBdMsg: [],
      AisOrBeidou:false,
      warningShow: true,
    };
  },
  beforeCreate() {
    _this = this;
  },
  mounted() {
  },
  updated(){
    console.log('ship',this.shipInfoList);
  },
  methods: {
    // 用来显示终端信息
    showClientInfo(){
      this.$emit("shipSimpleInformationchangeCurIndex", "showclientInfo");
      _this.$emit("showClientInfo", true);
    },
    bds: function() {
      _this.bdMsg = '';
      document.getElementById('detailsmess').style.display = 'none';
      document.getElementById('sendmess').style.display = 'block';
      document.getElementById('speedmess').style.display = 'none';
      document.getElementById('localmess').style.display = 'none';

      var url = global.IP + "/web/GetAllBdMsgByUserId?userId=" + sessionStorage.getItem("userId") + "&shipId=" + _this.shipInfoList[0].id;
      $.get(url, function (data, status) {
          _this.realBdMsg = [];
          for(var i = 0; i < data.length; i++) {
            if(data[i].msg == undefined){
              continue;
            }
             if(data[i].userId == 0){
              _this.realBdMsg.push(
                  {
                      shipName: data[i].shipName,
                      msg: data[i].msg,
                      time: data[i].loadTime,
                      sender: data[i].shipName
                  }
              );
             }else{
              _this.realBdMsg.push(
                  {
                      shipName: data[i].shipName,
                      msg: data[i].msg,
                      time: data[i].loadTime,
                      sender: data[i].name
                  }
              );
             }
          }
      });
    },
    cancel: function() {
      document.getElementById('detailsmess').style.display = 'block';
      document.getElementById('sendmess').style.display = 'none';
      document.getElementById('speedmess').style.display = 'block';
      document.getElementById('localmess').style.display = 'block';
    },
    send: function() {
      $.ajaxSettings.async = false;
      var data = {};
      // ==
      data["staticShipId"] = _this.shipInfoList[0].id;
      data["bdId"] = _this.shipInfoList[0].bdid;
      data["msg"] = _this.bdMsg;
      data["userId"] = sessionStorage.getItem("userId");
      console.log(data);
      if(_this.bdMsg == null || _this.bdMsg == undefined || _this.bdMsg == '') {
        setAlertWindowShow('situationView', '信息框不能为空', '', 1);
        return
      }
      $.ajax({
      url: global.IP + "/web/InsertBdMsg",
      type:"POST",
      data: JSON.stringify(data),
      dataType: "json",
      contentType:"application/json",
      dateString: true,

      success: function(data){
          if (data) {
            document.getElementById('detailsmess').style.display = 'block';
            document.getElementById('sendmess').style.display = 'none';
            document.getElementById('speedmess').style.display = 'block';
            document.getElementById('localmess').style.display = 'block';
            setAlertWindowShow('situationView', '北斗信息发送成功', '', 1);
            // 记录发送短信操作
            _this.SetUserOperationInfo(2,"发送短信", "记录发送短信操作成功", "记录发送短信操作失败");

            // 实时消息记录
            // global.realTimeMessage = saveRealTimeMessages(2, _this.shipInfoList[0].shipname, global.realTimeMessage);
            // _this.$emit("GetBdRealTimeMsgInfo", global.realTimeMessage);
          }
      },
      error: function(jqXHR){

      },
      });
      $.ajaxSettings.async = true;
    },

    openBeiDouDetial: function () {
      this.$emit("openBeiDouShip", {
        id: _this.shipInfoList[0].id,
        bdid: _this.shipInfoList[0].bdid,
        shipName: _this.shipInfoList[0].shipname,
        type: _this.shipInfoList[0].shiptype,
        owner: _this.shipInfoList[0].owner,
      }
      );

      document.getElementById('detailsmess').style.display = 'block';
      document.getElementById('sendmess').style.display = 'none';
      document.getElementById('speedmess').style.display = 'block';
      document.getElementById('localmess').style.display = 'block';
    },

    trackPlayback: function (index) {
      if(index == 5){
        this.$emit("shipSimpleInformationchangeCurIndex", "replay");
      }else if(index == 7){
        this.$emit("shipSimpleInformationchangeCurIndex", "shipWithArea");
      }
      this.$emit("shipSimpleInformationChangeComprehensiveSituationData", index, _this.shipInfoByList[0].id);
    },
    // x的关闭函数
    closeDiv: function (id) {
      document.getElementsByClassName(id)[0].style.display = "none";
      document.getElementById('detailsmess').style.display = 'block';
      document.getElementById('sendmess').style.display = 'none';
      document.getElementById('speedmess').style.display = 'block';
      document.getElementById('localmess').style.display = 'block';
      if(_this.isTrackShow == true) {
        _this.isTrackShow = false;
      }
      else {
        _this.isTrackShow = true;
      }
      _this.$emit("isTrackShow", _this.isTrackShow);
      this.$emit("cancelFloowInSim", true);
    },


    queryShipInfo: function () {
      this.$emit('shipDetailShow',true,  _this.shipInfoByList[0].id);
      this.$emit("shipSimpleInformationchangeCurIndex", "shipDetailsTwoID");
      $.ajaxSettings.async = true;
      document.getElementById('fishingVesselStatisticsView').style.filter = 'blur(5px)';
      document.getElementById('fishingVesselMonitoringView').style.filter = 'blur(5px)';
      document.getElementById('earlyWarningMonitoringView').style.filter = 'blur(5px)';
      document.getElementById('videoSurveillanceView').style.filter = 'blur(5px)';
      document.getElementById('meteorologicalMonitoringView').style.filter = 'blur(5px)';
      document.getElementById('Legeng').style.filter = 'blur(5px)';
      document.getElementById('longitudeAndLatitudeDisplay').style.filter = 'blur(5px)';
      document.getElementById('leftImg').style.filter = 'blur(5px)';
      document.getElementById('rightImg').style.filter = 'blur(5px)';
      document.getElementById('searchDiv').style.filter = 'blur(5px)';
      document.getElementById('toolCabinet').style.filter = 'blur(5px)';
      // document.getElementById('bt1').style.filter = 'blur(5px)';
      document.getElementById('bt2').style.filter = 'blur(5px)';
      document.getElementById('bt3').style.filter = 'blur(5px)';
      document.getElementById('bt4').style.filter = 'blur(5px)';
      document.getElementById('bt5').style.filter = 'blur(5px)';
      document.getElementById('bt6').style.filter = 'blur(5px)';
      document.getElementById('bt7').style.filter = 'blur(5px)';
      document.getElementById('bt8').style.filter = 'blur(5px)';
      document.getElementById('bt9').style.filter = 'blur(5px)';
      document.getElementById('bt10').style.filter = 'blur(5px)';
      document.getElementById('bt11').style.filter = 'blur(5px)';
      document.getElementById('bt12').style.filter = 'blur(5px)';
      document.getElementById('bt13').style.filter = 'blur(5px)';
      document.getElementById('bt14').style.filter = 'blur(5px)';
      document.getElementById('bt15').style.filter = 'blur(5px)';
      document.getElementById('bt16').style.filter = 'blur(5px)';
      document.getElementById('topTitle').style.filter = 'blur(5px)';
      document.getElementById('fishingVesselStatisticsView').style.filter = 'blur(5px)';
      document.getElementById('map').style.filter = 'blur(5px)';
      document.getElementById('shipPortSelect').style.filter = 'blur(5px)';
      document.getElementById('fishingVesselDwellTime').style.filter = 'blur(5px)';
      document.getElementById('bdMsgSendView').style.filter = 'blur(5px)';
    },

    queryCrewInfo: function () {

      this.$emit('showCrewInfo', true, _this.shipInfoByList[0].id);
      console.log("_this.shipInfoByList:",_this.shipInfoByList);
      this.$emit("shipSimpleInformationchangeCurIndex", "crewDetailView");
      $.ajaxSettings.async = true;
      document.getElementById('fishingVesselStatisticsView').style.filter = 'blur(5px)';
      document.getElementById('fishingVesselMonitoringView').style.filter = 'blur(5px)';
      document.getElementById('earlyWarningMonitoringView').style.filter = 'blur(5px)';
      document.getElementById('videoSurveillanceView').style.filter = 'blur(5px)';
      document.getElementById('meteorologicalMonitoringView').style.filter = 'blur(5px)';
      document.getElementById('Legeng').style.filter = 'blur(5px)';
      document.getElementById('longitudeAndLatitudeDisplay').style.filter = 'blur(5px)';
      document.getElementById('leftImg').style.filter = 'blur(5px)';
      document.getElementById('rightImg').style.filter = 'blur(5px)';
      document.getElementById('searchDiv').style.filter = 'blur(5px)';
      document.getElementById('toolCabinet').style.filter = 'blur(5px)';
      // document.getElementById('bt1').style.filter = 'blur(5px)';
      document.getElementById('bt2').style.filter = 'blur(5px)';
      document.getElementById('bt3').style.filter = 'blur(5px)';
      document.getElementById('bt4').style.filter = 'blur(5px)';
      document.getElementById('bt5').style.filter = 'blur(5px)';
      document.getElementById('bt6').style.filter = 'blur(5px)';
      document.getElementById('bt7').style.filter = 'blur(5px)';
      document.getElementById('bt8').style.filter = 'blur(5px)';
      document.getElementById('bt9').style.filter = 'blur(5px)';
      document.getElementById('bt10').style.filter = 'blur(5px)';
      document.getElementById('bt11').style.filter = 'blur(5px)';
      document.getElementById('bt12').style.filter = 'blur(5px)';
      document.getElementById('bt13').style.filter = 'blur(5px)';
      document.getElementById('bt14').style.filter = 'blur(5px)';
      document.getElementById('bt15').style.filter = 'blur(5px)';
      document.getElementById('bt16').style.filter = 'blur(5px)';
      document.getElementById('topTitle').style.filter = 'blur(5px)';
      document.getElementById('fishingVesselStatisticsView').style.filter = 'blur(5px)';
      document.getElementById('map').style.filter = 'blur(5px)';
      document.getElementById('shipPortSelect').style.filter = 'blur(5px)';
      document.getElementById('fishingVesselDwellTime').style.filter = 'blur(5px)';
      document.getElementById('bdMsgSendView').style.filter = 'blur(5px)';
    },

    showLawEnforcement:function () {
      this.$emit('shipDetailShow',true,  _this.shipInfoByList[0].id,9);
      this.$emit("shipSimpleInformationchangeCurIndex", "shipDetailsTwoID");
    },

    update: function (isShow) {
      if(isShow == true) {
        document.getElementById('wakeInfo').innerHTML = '关闭尾迹';
      }
      else {
        document.getElementById('wakeInfo').innerHTML = '尾迹展示';
      }
    },
    update1: function (id) {
      $.get(global.IP + "/web/GetImportanceShip?userId=" + sessionStorage.getItem("userId"), function (data, status) {
        var isShow = false;
        if(data.length == 0) {

        }
        else {
          for(var i = 0; i < data.length; i++) {
            if(id == data[i].staticShipId) {
              isShow = true;
              break;
            }
          }
        }
        if(isShow == true) {
          document.getElementById('fousceInfo').innerHTML = '√ 重点跟踪';
        }
        else {
          document.getElementById('fousceInfo').innerHTML = '重点跟踪';
        }
      }).fail(function(msg){
          console.log("error：" + JSON.stringify(msg))
      });
    },
    changeDiv:function (arg) {
      this.AisOrBeidou = arg
    }
  },
  beforeDestroy() {},
  watch: {
    wakeIsShowOrNot: function(newVal, oldVal){
        _this.update(newVal);
    },

    shipInfoImg: function (newVal, oldVal) {
      //console.log(newVal);
      if(newVal == null || newVal == "") {
        _this.shipImgShow = false;
        document.getElementById("butdiv").style.top = '52px';
        document.getElementById("detailsmess").style.top = '164px';
        document.getElementById("sendmess").style.top = '164px';
        document.getElementById("speedmess").style.top = '301px';
        document.getElementById("localmess").style.top = '361px';
      }
      else {
        _this.shipImgShow = true;
        document.getElementById("butdiv").style.top = '172px';
        document.getElementById("detailsmess").style.top = '284px';
        document.getElementById("sendmess").style.top = '284px';
        document.getElementById("speedmess").style.top = '421px';
        document.getElementById("localmess").style.top = '481px';

        var img = document.getElementById("pic");
        img.src = global.IP + "/shipImg/" + _this.shipInfoImg;
      }
    },

    shipInfoList: function (newVal, oldVal) {
      console.log(newVal);
      _this.update1(newVal[0].id);
      _this.shipInfoByList = newVal;

      console.log(API_GetShipInfoByPos(API_GetShipPosById(newVal[0].id)));
      console.log(API_GetShipInfoByPos(API_GetShipPosById(newVal[0].id)).iShipState);
      if(API_GetShipInfoByPos(API_GetShipPosById(newVal[0].id)).iShipState == 5) {
        _this.warningShow = false;
      }
      else {
        _this.warningShow = true;
      }
      console.log(_this.warningShow);
    },


    fouInfo: function (newVal, oldVal) {
      if(newVal == '1') {
          document.getElementById('fousceInfo').innerHTML = '√ 重点跟踪';
        }
        else if(newVal == '0') {
          document.getElementById('fousceInfo').innerHTML = '重点跟踪';
        }
    },
  },
};
</script>

<style scoped>
.button{
  height: 30px;
  background-color: rgb(25, 86, 200);
  border: 1px solid rgb(27,103,244);
  border: 0;
  border-radius: 3px;
  color: white;
  font-size: 16px;
  margin-top: 10px;
  margin-left: 50px;
}

#vesselDetails{
  user-select:text;
}
</style>
