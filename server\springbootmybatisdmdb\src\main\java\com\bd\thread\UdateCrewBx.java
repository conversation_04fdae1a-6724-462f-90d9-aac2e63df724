package com.bd.thread;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bd.entity.*;
import com.bd.mapper.CrewExamMapper;
import com.bd.service.*;
import com.bd.util.AesUtil;
import com.bd.util.HttpTool;
import com.bd.util.Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@Lazy
public class UdateCrewBx {

    @Resource
    private CrewExamMapper crewExamMapper;

    @Resource
    private UserService userService;

    @Resource
    private ShipService shipService;

    @Resource
    private PeopleService peopleService;

    @Resource
    private PortService portService;

    private List<CrewBx> crewBx_temp = new ArrayList<>();
    @Resource
    private ShipStaticInfoService shipStaticInfoService;

    public List<String> GetCrewList(int pageNum) {
        String token = userService.GetOpenCenterToken();
        //System.out.println("-.-" + token);
        String header = "Bearer " + token;
        String url = "http://*********:18080/api/service/share/P364124952460267520?conditions=null&pageNum=" + pageNum + "&pageSize=" + 1000;
        String crewData = HttpTool.doGet(url, header);
        //System.out.println("-.-" + crewData);
        crewData = crewData.replace("[", "");
        crewData = crewData.replace("]", "");
        crewData = crewData.replace("},{", "}#{");
        List<String> crewJsonList = Arrays.asList(crewData.split("#"));
        return crewJsonList;
    }

    public List<String> GetCrewList2(int pageNum) {
        String token = userService.GetOpenCenterToken();
        //System.out.println("-.-" + token);
        String header = "Bearer " + token;
        String url = "http://*********:18080/api/service/share/P385565773835276288?conditions=null&pageNum=" + pageNum + "&pageSize=" + 1000;
        String crewData = HttpTool.doGet(url, header);
        //System.out.println("-.-" + crewData);
        crewData = crewData.replace("[", "");
        crewData = crewData.replace("]", "");
        crewData = crewData.replace("},{", "}#{");
        List<String> crewJsonList = Arrays.asList(crewData.split("#"));
        return crewJsonList;
    }

    public void updateCrewBx() throws Exception {
        crewExamMapper.delete();
        crewBx_temp.clear();

        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        int pageNum = 1;
        List<String> crewBxList = new ArrayList<>();
        while (true) {
            try {
                crewBxList = GetCrewList(pageNum);
            } catch (Exception e) {
                continue;
            }
            List<CrewBx> crewBxes = new ArrayList<>();
            if (crewBxList.size() < 2) break;
            for (String crewStr : crewBxList) {
                JSONObject crewBxJson = JSONObject.parseObject(crewStr);
                //System.out.println(crewStr);
                CrewBx crewBx = new CrewBx();
                crewBx.setPolicyno(crewBxJson.getString("policyno") + "");
                crewBx.setAppliaddress(crewBxJson.getString("appliaddress") + "");
                crewBx.setAppliidentifynumber(AesUtil.aesDecrypt(crewBxJson.getString("appliidentifynumber") + ""));
                crewBx.setAppliname(AesUtil.aesDecrypt(crewBxJson.getString("appliname") + ""));
                crewBx.setAppliphonenumber(crewBxJson.getString("appliphonenumber") + "");
                crewBx.setApplipostcode(crewBxJson.getString("applipostcode") + "");
                crewBx.setEnddate(crewBxJson.getString("enddate").replace("/", "-"));
                crewBx.setHorsepower(crewBxJson.getString("horsepower") + "");
                crewBx.setInsuredaddress(crewBxJson.getString("insuredaddress") + "");
                crewBx.setInsuredidentifynumber(AesUtil.aesDecrypt(crewBxJson.getString("insuredidentifynumber") + ""));
                crewBx.setInsuredname(AesUtil.aesDecrypt(crewBxJson.getString("insuredname") + ""));
                crewBx.setInsuredphonenumber(crewBxJson.getString("insuredphonenumber") + "");
                crewBx.setInsuredpostcode(crewBxJson.getString("insuredpostcode") + "");
                crewBx.setMakestartdate(crewBxJson.getString("makestartdate") + "");
                crewBx.setPersoncount(crewBxJson.getString("personcount") + "");
                crewBx.setPolicystatus(crewBxJson.getString("policystatus") + "");
                crewBx.setSailareaname(crewBxJson.getString("sailareaname") + "");
                crewBx.setShipcname(crewBxJson.getString("shipcname") + "");
                crewBx.setShipstructname(crewBxJson.getString("shipstructname") + "");
                crewBx.setShiptypename(crewBxJson.getString("shiptypename") + "");
                crewBx.setStartdate(crewBxJson.getString("startdate") + "");
                crewBx.setToncount(crewBxJson.getString("toncount") + "");
                //System.out.println(crewBx.getPolicyno());

                if (sdf.parse(crewBx.getEnddate()).getTime() > date.getTime()) {
                    crewBxes.add(crewBx);
                    crewBx_temp.add(crewBx);
                }
            }
            System.out.println("updateCrewBx-pageNum:" + pageNum);
            pageNum++;
            if (crewBxes.size() > 0)
                crewExamMapper.insertCrewBX(crewBxes);

        }
    }

    public void updateCrewBx2() throws Exception {
        int pageNum = 1;
        List<String> crewBxList = new ArrayList<>();
        while (true) {
            try {
                crewBxList = GetCrewList2(pageNum);
            } catch (Exception e) {
                continue;
            }
            List<CrewBx> crewBxes = new ArrayList<>();
            if (crewBxList.size() < 2) break;
            for (String crewStr : crewBxList) {
                JSONObject crewBxJson = JSONObject.parseObject(crewStr);
                //System.out.println(crewStr);
                CrewBx crewBx = new CrewBx();
                String policyno = (crewBxJson.getString("rk") + "").substring(0, 21);
                //crewBx.setPolicyno(policyno);
                crewBx.setInsuredidentifynumber(AesUtil.aesDecrypt(crewBxJson.getString("drivinglicenseno") + ""));
                crewBx.setInsuredname(AesUtil.aesDecrypt(crewBxJson.getString("drivername") + ""));
                for (CrewBx temp : crewBx_temp) {
                    if (policyno.equals(temp.getPolicyno())) {
                        crewBx.setAppliaddress(temp.getAppliaddress());
                        crewBx.setAppliidentifynumber(temp.getAppliidentifynumber());
                        crewBx.setAppliname(temp.getAppliname());
                        crewBx.setAppliphonenumber(temp.getAppliphonenumber());
                        crewBx.setApplipostcode(temp.getApplipostcode());
                        crewBx.setEnddate(temp.getEnddate());
                        crewBx.setHorsepower(temp.getHorsepower());
                        crewBx.setInsuredaddress(temp.getInsuredaddress());
                        crewBx.setInsuredphonenumber(temp.getInsuredphonenumber());
                        crewBx.setInsuredpostcode(temp.getInsuredpostcode());
                        crewBx.setMakestartdate(temp.getMakestartdate());
                        crewBx.setPersoncount(temp.getPersoncount());
                        crewBx.setPolicystatus(temp.getPolicystatus());
                        crewBx.setSailareaname(temp.getSailareaname());
                        crewBx.setShipcname(temp.getShipcname());
                        crewBx.setShipstructname(temp.getShipstructname());
                        crewBx.setShiptypename(temp.getShiptypename());
                        crewBx.setStartdate(temp.getStartdate());
                        crewBx.setToncount(temp.getToncount());
                        crewBxes.add(crewBx);
                    }
                }
                //System.out.println(crewBx.getPolicyno());
            }
            System.out.println("updateCrewBx2-pageNum:" + pageNum);
            pageNum++;
            if (crewBxes.size() > 0)
                crewExamMapper.insertCrewBX(crewBxes);

        }
    }

    //@Scheduled(fixedRate = Integer.MAX_VALUE)
    //@Scheduled(cron = "0 0 2 * * ?")
    public void sync() throws Exception {
        System.out.println("updateCrewBx---begin!");
        updateCrewBx();
        updateCrewBx2();
        System.out.println("updateCrewBx---end!");
    }

    public void alarmCrew() {
        List<OutInPortRecord> outInPortRecords = portService.GetOutInPortRecord();
        List<Integer> shipIdlist = crewExamMapper.getShipIdList();

        for (int shipId : shipIdlist) {
            String shipName = shipService.GetshipNameById(shipId);
            List<CrewBx> crewBoxes = peopleService.GetCrewBxByShipName(shipName);
            boolean ret = false;
            for (CrewBx bx : crewBoxes) {
                if (bx.getCERTIFICATETIME() == null) {
                    ret = true;
                } else if (!Utils.CompareDate(bx.getCERTIFICATETIME(), Utils.GetNowDateTimeString())) {
                    ret = true;
                }
            }

            if (ret) {
                ShipDynamicInfo shipDynamicInfo = shipService.GetOneShipDynamicInfoById(shipId);
                portService.InsertOneAlarmInfo(shipDynamicInfo, 401, shipName + "存在船员信息异常", 0, "");
            }
        }

        System.out.println("证书无效检查");
    }

    // @Scheduled(fixedRate = Integer.MAX_VALUE)
    public void insert401AlarmInfo(OutInPortRecord recordParam) {
        JSONArray jsonArray = new JSONArray();
        List<OutInPortRecord> outInPortRecords = portService.GetOutInPortRecord(); // 近24小时船舶进出港记录
        List<FisheryBoatInOutReport> recordsOf7 = portService.get7DaysRecord(); // 近7天进出港报告
        log.info("近24小时进出港记录船只数量：{}", outInPortRecords.size());
        List<OutInPortRecord> list = ListUtil.list(false, recordParam);
        for (OutInPortRecord record : list) {
            int staticShipId = record.getStaticShipId();
            String shipName = record.getShipName();
            log.debug("分析船只：" + record);
            FisheryBoatInOutReport newReport = peopleService.GetPortNodeByShipId(staticShipId, null);  // 该船最新一条出港报告
            log.debug("最新报告：" + newReport);
            ShipStaticInfo_all staticInfoAll = shipStaticInfoService.GetOneShipInfoById(staticShipId).get(0);
            log.debug("船舶信息：" + staticInfoAll);
            ShipDynamicInfo shipDynamicInfo = shipService.GetOneShipDynamicInfoById(staticShipId);

            String[] crews = newReport.getCrewStr().split("@");
            // 超载
            if (crews.length > staticInfoAll.getMaxPeopleCount()) {
                String msg = String.format("%s 核载 %s 本次乘船人数：%s 超载", shipName, staticInfoAll.getMaxPeopleCount(), crews.length);
                log.info(msg);
                jsonArray.add(build401Msg(shipDynamicInfo, msg));
            }

            // 配员不足
            int littleCount = getShipLittleCount(staticInfoAll);
            if (crews.length < littleCount) {
                String msg = String.format("%s 配员 %s 本次乘船人数：%s 配员不足", shipName, staticInfoAll.getMaxPeopleCount(), crews.length);
                log.info(msg);
                jsonArray.add(build401Msg(shipDynamicInfo, msg));
            }

            // 证书无效
            for (String crew : crews) {
                String[] arr = crew.split("#");
                // System.out.println(Arrays.toString(arr));
                String zshmEndTime = peopleService.GetEndTimeByIdCard(arr[1]);
                DateTime endTime = DateUtil.parse(zshmEndTime);
                if (endTime != null && endTime.compareTo(DateTime.now()) < 0) {
                    String msg = String.format("%s 船员 %s 证书过期 证书有效期至 %s", shipName, arr[0], endTime);
                    log.info(msg);
                    jsonArray.add(build401Msg(shipDynamicInfo, msg));
                }
            }

            // 保险失效
            List<CrewBx> crewBxes = peopleService.GetCrewBxByShipName(shipName);
            for (CrewBx crewBx : crewBxes) {
                DateTime endDate = DateUtil.parse(crewBx.getEnddate());
                if (endDate != null && endDate.compareTo(DateTime.now()) < 0) {
                    String msg = String.format("%s 船员 %s 保险过期 保险有效期至 %s", shipName, crewBx.getInsuredname(), endDate);
                    log.info(msg);
                    jsonArray.add(build401Msg(shipDynamicInfo, msg));
                }
            }

            // 多船登记
            ArrayList<String> idcardInReport = new ArrayList<>();
            ArrayList<String> nameInReport = new ArrayList<>();
            for (String crew : crews) {
                // 将最新的出港报告中的船员信息添加到名称集合中
                String[] nameAndIdcard = crew.split("#");
                nameInReport.add(nameAndIdcard[0]);
                idcardInReport.add(nameAndIdcard[1]);
            }
            // 遍历近7天的进出港报告查看是否该船中的船员是否多船登记
            for (FisheryBoatInOutReport recordOf7 : recordsOf7) {
                if (recordOf7.getShipName().equals(newReport.getShipName()) || recordOf7.getCrewStr() == null) continue;
                for (String crew : crews) {
                    String[] nameAndIdcard = crew.split("#");
                    String name = nameAndIdcard[0];
                    String idcard = nameAndIdcard[1];
                    if (recordOf7.getCrewStr().contains(idcard+"#")) {
                        String msg = String.format("%s 船员%s %s 多船登记 登记的船舶 %s", shipName, name, idcard, recordOf7.getShipName());

                        JSONObject build401Msg = build401Msg(shipDynamicInfo, msg);
                        if (jsonArray.contains(build401Msg)) continue;
                        log.info(msg);
                        jsonArray.add(build401Msg);
                    }
                }

            }

            DateTime recordTime = DateUtil.parse(record.getLoadTime());
            // 未报告进港
            if (record.getState() == 1) {
                FisheryBoatInOutReport inReport = peopleService.GetPortNodeByShipId(staticShipId, 0);// 获取最新一条进港报告
                log.debug("最新进港报告：" + inReport);
                String msg = null;
                if (inReport == null) {
                    msg = String.format("%s 未进行进港报告 进港记录时间 %s 尚未进行进港报告", shipName, recordTime);
                } else {
                    DateTime inReportTime = DateUtil.parse(inReport.getTime());
                    if (DateUtil.between(recordTime, inReportTime, DateUnit.HOUR) > 24) {
                        msg = String.format("%s 未进行进港报告 进港记录时间 %s 最新一次进港报告时间 %s", shipName, recordTime, inReportTime);
                    }
                }
                log.info(msg);
                jsonArray.add(build401Msg(shipDynamicInfo, msg));
            }
            // 未报告出港
            if (record.getState() == 0) {
                FisheryBoatInOutReport outReport = peopleService.GetPortNodeByShipId(staticShipId, 1);// 获取最新一条出港报告
                log.debug("最新出港报告：" + outReport);
                String msg = null;
                if (outReport == null) {
                    msg = String.format("%s 未进行出港报告 出港记录时间 %s 尚未进行出港报告", shipName, recordTime);
                } else {
                    DateTime outReportTime = DateUtil.parse(outReport.getTime());
                    if (DateUtil.between(recordTime, outReportTime, DateUnit.HOUR) > 24) {
                        msg = String.format("%s 未进行出港报告 出港记录时间 %s 最新一次出港报告时间 %s", shipName, recordTime, outReportTime);
                    }
                }
                log.info(msg);
                jsonArray.add(build401Msg(shipDynamicInfo, msg));
            }

        }

        List<Object> collect = jsonArray.stream().distinct().collect(Collectors.toList());
        jsonArray = new JSONArray(collect);

        // 当前进出港记录的港口名
        String portName = recordParam.getPortName();

        for (Object o : jsonArray) {
            // List<AlarmRecord> alarmRecords = portService.GetTodayWarningInfo(178, "0", 0);
            JSONObject jsonObject = (JSONObject) o;
            ShipDynamicInfo shipDynamicInfo = (ShipDynamicInfo) jsonObject.get("shipDynamicInfo");
            Integer type = (Integer) jsonObject.get("type");
            String msg = (String) jsonObject.get("msg");
            Integer userId = (Integer) jsonObject.get("userId");
            String areaName = (String) jsonObject.get("areaName");
            // Console.log(shipDynamicInfo, type, msg, userId, areaName);
            if (StrUtil.isEmpty(msg) || crewExamMapper.select401MsgCount(msg) > 0) {
                // 确认数据库中是否存在该条信息的记录 存在跳过
                log.debug("{}已存在数据库",msg);
                continue;
            }

            // 未进出港报告发送北斗消息提示
            BdMsg bdMsg = new BdMsg();
            bdMsg.setStaticShipId(shipDynamicInfo.getStaticShipId());
            bdMsg.setBdId(shipDynamicInfo.getBDID());
            bdMsg.setUserId(0);
            bdMsg.setSendType(10);
            bdMsg.setLoadTime(Utils.GetNowTimeString());
            if (msg.contains("未进行进港报告")){
                bdMsg.setMsg(String.format("%s 已进入 %s ，请及时通过渔港通APP/小程序进行进港报告",shipDynamicInfo.getSHIPNAME(),portName));
                peopleService.InsertBdMsg(bdMsg);
            }else if(msg.contains("未进行出港报告")){
                bdMsg.setMsg(String.format("%s 已离开 %s ，请及时通过渔港通APP/小程序进行出港报告",shipDynamicInfo.getSHIPNAME(),portName));
                peopleService.InsertBdMsg(bdMsg);
            }
            portService.InsertOneAlarmInfo(shipDynamicInfo, type, msg, userId, areaName);
        }
    }

    public JSONObject build401Msg(ShipDynamicInfo shipDynamicInfo, String msg) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("shipDynamicInfo", shipDynamicInfo);
        jsonObject.put("type", 401);
        jsonObject.put("msg", msg);
        jsonObject.put("userId", 0);
        jsonObject.put("areaName", "");
        return jsonObject;
    }

    public int getShipLittleCount(ShipStaticInfo_all staticInfoAll) {
        int lengthCount = 0;
        if (staticInfoAll.getLength() >= 12 && staticInfoAll.getLength() < 24) lengthCount = 2;
        else if (staticInfoAll.getLength() >= 24 && staticInfoAll.getLength() < 36) lengthCount = 2;
        else if (staticInfoAll.getLength() >= 36 && staticInfoAll.getLength() < 45) lengthCount = 2;
        else if (staticInfoAll.getLength() >= 45) lengthCount = 3;
        int glCount = 0;
        if (staticInfoAll.getZJGLQW() >= 50 && staticInfoAll.getZJGLQW() < 250) glCount = 1;
        else if (staticInfoAll.getZJGLQW() >= 250 && staticInfoAll.getZJGLQW() < 450) glCount = 2;
        else if (staticInfoAll.getZJGLQW() >= 450 && staticInfoAll.getZJGLQW() < 750) glCount = 2;
        else if (staticInfoAll.getZJGLQW() >= 750 && staticInfoAll.getZJGLQW() < 3000) glCount = 2;
        else if (staticInfoAll.getZJGLQW() >= 3000) glCount = 3;
        if (staticInfoAll.getZJGLQW() > 800) glCount++;
        return lengthCount + glCount;
    }


    //@Scheduled(fixedRate = Integer.MAX_VALUE)
    //@Scheduled(cron = "0 0 1 * * ?")
    public void StaticsWorkDay_day(){
        System.out.println("begin");
        List<Integer> shipIdlist = crewExamMapper.getShipIdList();
        for (int shipId : shipIdlist) {

            //写一个java代码获取当前日期前一天的两个时间：如 "2024-09-12 00:00:00", "2024-09-12 23:59:59"
            DateTime yesterday = DateUtil.yesterday();
            String startTime = DateUtil.format(DateUtil.beginOfDay(yesterday), "yyyy-MM-dd HH:mm:ss");
            String endTime = DateUtil.format(DateUtil.endOfDay(yesterday), "yyyy-MM-dd HH:mm:ss");
            //查询前一天的日期，如果前一天的日期是前一天就插入一条
            crewExamMapper.insertWorkDays_BD_day(shipId, startTime, endTime);
            crewExamMapper.insertWorkDays_AIS_day(shipId, startTime,endTime);
            System.out.println(shipId);
        }
        System.out.println("end");
    }

    //@Scheduled(fixedRate = Integer.MAX_VALUE)
    public void StaticsWorkDay() {

        //这是在线天数
        System.out.println("begin");
        List<Integer> shipIdlist = crewExamMapper.getShipIdList();
        for (int shipId : shipIdlist) {
            if(shipId > 389)
            {
                crewExamMapper.insertWorkDays_AIS(shipId, "2024-08-31","2025-03-01");
                System.out.println(shipId);

            }
        }
        System.out.println("end");
    }
}
