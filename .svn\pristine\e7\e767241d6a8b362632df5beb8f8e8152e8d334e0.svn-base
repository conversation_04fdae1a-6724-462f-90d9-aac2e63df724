package com.bd.thread;

import com.alibaba.fastjson.JSONObject;
import com.bd.service.UserService;
import com.bd.util.HttpTool;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;

@Controller
@EnableScheduling
public class Maptoken {

    @Resource
    public UserService userService;

    //@Scheduled(cron = "0 0 */12 * * ?")
    //@Scheduled(cron = "0/30 * * * * ?")
    //@Scheduled(cron = "0 0/5 * * * ?")
    public void updateToken(){
        GetMapToken();
        UpdateOpenCenterToken();
        UpdateOpenCenterToken_HIK();
        System.out.println("token update end");
    }

    public void GetMapToken(){
        //String url = "http://10.89.9.132:11080/api/uaa/oauth/token?socpe=all&grant_type=password&username=ycyg&password=ycyg@123&tenantCode=90001";
        String url = "http://10.90.4.228:11080/api/uaa/oauth/token?socpe=all&grant_type=password&username=ycyg2&password=ycyg@123!&tenantCode=90001";
        String data = HttpTool.doPostJson(url, "","Basic Y2xpZW50OnNlY3JldA==");
        System.out.println(data);
        String refresh_token = JSONObject.parseObject(data).getString("refresh_token");
        System.out.println(refresh_token);

        //String url2 = "http://10.89.9.132:11080/api/uaa/oauth/token?grant_type=refresh_token&refresh_token=" + refresh_token;
        String url2 = "http://10.90.4.228:11080/api/uaa/oauth/token?grant_type=refresh_token&refresh_token=" + refresh_token;
        String data2 = HttpTool.doGet(url2, "Basic Y2xpZW50OnNlY3JldA==");
        System.out.println(data2);
        String access_token = JSONObject.parseObject(data2).getString("access_token");
        System.out.println(access_token);
        if(access_token != null){
            userService.UpdateToken(access_token);
        }
        else {
            GetMapToken();
        }
    }

    public void UpdateOpenCenterToken(){
        JSONObject user = new JSONObject();
        user.put("username", "nwycyg");
        user.put("password", "Nw013579");
        user.put("rememberMe", "true");
        String dataToken = HttpTool.doPostJson("http://10.90.7.5:18080/api/authenticate", user.toString(), "");
        String token =  JSONObject.parseObject(dataToken).getString("id_token");
        userService.UpdateOpenCenterToken(token);
    }

    public void UpdateOpenCenterToken_HIK(){
        JSONObject user = new JSONObject();
        user.put("username", "nwycyg");
        user.put("password", "Nw013579");
        String dataToken = HttpTool.doPostJson("http://10.90.7.5:9092/api/token",user.toString(), "");
        String token =  JSONObject.parseObject(JSONObject.parseObject(dataToken).getString("data")).getString("token");
        userService.UpdateOpenCenterToken_HIK(token);
    }
}
