package com.bd.util;

import org.apache.commons.codec.binary.Base64;

import java.security.KeyPair;

public class TestRsa {
    public static void main(String[] args) throws Exception {
        KeyPair keyPair = RsaTool.getKeyPair();
        String privateKey = new String(Base64.encodeBase64(keyPair.getPrivate().getEncoded()));
        String publicKey = new String(Base64.encodeBase64(keyPair.getPublic().getEncoded()));
        System.out.println("公钥：" + publicKey);
        System.out.println("私钥：" + privateKey);
        String data = "hello world";
        String encrypt = RsaTool.encrypt(data, RsaTool.getPublicKey(publicKey));
        System.out.println("加密后：" + encrypt);
        String decrypt = RsaTool.decrypt(encrypt, RsaTool.getPrivateKey(privateKey));
        System.out.println("解密后：" + decrypt);
        String sign = RsaTool.sign(data, RsaTool.getPrivateKey(privateKey));
        System.out.println("签名后：" + sign);
        boolean verify = RsaTool.verify(data, RsaTool.getPublicKey(publicKey), sign);
        System.out.println("验签结果" + verify);

    }
}
