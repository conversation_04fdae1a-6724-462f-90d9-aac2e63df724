package com.bd.entity;

import lombok.Data;

@Data
public class CrewBx {

/*
     policyno":"232183101152022000001",
     appliaddress":"浦东新区泥城镇彭镇北泐村6组17幢107室",
     appliidentifynumber":"Hy+qPhIOssFItaFmJ3kRA+HHTKJoU0b5U/RbCR9K5jc=",
     appliname":"sQEz56O6Rdt2yHMhW5demgfG13or+pIBt0O3Ffcpgmw=",
     appliphonenumber":"13601663999",
     applipostcode":"201300",
     enddate":"2023/3/23 ",
     horsepower":"183.00",
     insuredaddress":"浦东新区泥城镇彭镇北泐村6组17幢107室",
     insuredidentifynumber":"Hy+qPhIOssFItaFmJ3kRA+HHTKJoU0b5U/RbCR9K5jc=",
     insuredname":"sQEz56O6Rdt2yHMhW5demgfG13or+pIBt0O3Ffcpgmw=",
     insuredphonenumber":"13601663999",
     insuredpostcode":"201300",
     makestartdate":"2013/12/27 ",
     personcount":"6",
     policystatus":"1",
     sailareaname":"海洋",
     shipcname":"沪浦渔测45678",
     shipstructname":"钢(铁)质",
     shiptypename":"海洋辅助",
     startdate":"2022/3/24 ",
     toncount":"90.00",

*/

    private String appliaddress;
    private String appliidentifynumber;
    private String appliname;
    private String appliphonenumber;
    private String applipostcode;
    private String enddate;
    private String horsepower;
    private String insuredaddress;
    private String insuredphonenumber;
    private String insuredpostcode;
    private String makestartdate;
    private String personcount;
    private String policystatus;
    private String sailareaname;
    private String shipcname;
    private String shipstructname;
    private String shiptypename;
    private String startdate;
    private String toncount;

    private String policyno;
    private String insuredidentifynumber;
    private String insuredname;

    private String CERTIFICATENAME;
    private String CERTIFICATETIME;
    private String NAME;
    private String IDCARD;
    private String IDCARDSIGNCODE;

    private String ZSHM;
}
