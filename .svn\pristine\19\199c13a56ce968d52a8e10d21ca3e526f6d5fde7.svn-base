<template>
  <div class="qiehuan-dialog" :style="{
      left: dialogLeft + 'px',
      top: dialogTop + 'px',
      position: 'fixed',
      zIndex: 9999,
      transform: isMobile ? 'scale(0.8)' : 'scale(1)',
      transformOrigin: 'top left'
  }" @mousedown="onDragStart" @touchstart="onTouchStart">
    <!-- 对话框头部 -->
    <div class="dialog-header">
      <div class="dialog-title">选择地图图层</div>
      <span class="close-btn" @click.stop="closeDialog">×</span>
    </div>
    
    <!-- 对话框内容 -->
    <div class="dialog-content">
      <!-- 基础图底 -->
      <div class="section">
        <div class="section-title">基础图底</div>
        <div class="map-types">
          <div class="map-type-item" 
               :class="{ active: selectedMapType === 'land' }" 
               @click="selectMapType('land')">
            <div class="map-preview land-preview"></div>
            <div class="map-label">陆图</div>
          </div>
          <div class="map-type-item" 
               :class="{ active: selectedMapType === 'sea' }" 
               @click="selectMapType('sea')">
            <div class="map-preview sea-preview"></div>
            <div class="map-label">海图</div>
          </div>
          <div class="map-type-item" 
               :class="{ active: selectedMapType === 'satellite' }" 
               @click="selectMapType('satellite')">
            <div class="map-preview satellite-preview"></div>
            <div class="map-label">卫星图</div>
          </div>
        </div>
        <div class="map-types">
          <div class="map-type-item" 
               :class="{ active: selectedMapType === 'land-sea' }" 
               @click="selectMapType('land-sea')">
            <div class="map-preview land-sea-preview"></div>
            <div class="map-label">陆图+海图</div>
          </div>
          <div class="map-type-item" 
               :class="{ active: selectedMapType === 'satellite-sea' }" 
               @click="selectMapType('satellite-sea')">
            <div class="map-preview satellite-sea-preview"></div>
            <div class="map-label">卫星+海图</div>
          </div>
        </div>
      </div>

      <!-- 时段 -->
      <div class="section">
        <div class="section-title">时段</div>
        <div class="time-periods">
          <div class="time-period-item" 
               :class="{ active: selectedTimePeriod === 'all' }" 
               @click="selectTimePeriod('all')">
            <div class="icon iconfont icon-quanbu"></div>
            <div class="time-label">全部</div>
          </div>
          <div class="time-period-item" 
               :class="{ active: selectedTimePeriod === 'standard' }" 
               @click="selectTimePeriod('standard')">
            <div class="icon iconfont icon-lifangti"></div>
            <div class="time-label">标准</div>
          </div>
          <div class="time-period-item" 
               :class="{ active: selectedTimePeriod === 'basic' }" 
               @click="selectTimePeriod('basic')">
            <div class="icon iconfont icon-quanbu1"></div>
            <div class="time-label">基本</div>
          </div>
        </div>
      </div>

      <!-- 地图模式 -->
      <div class="section">
        <div class="section-title">地图模式</div>
        <div class="map-modes">
          <div class="map-mode-item" 
               :class="{ active: selectedMapMode === 'day' }" 
               @click="selectMapMode('day')">
            <div class="icon iconfont icon-baitian-qing"></div>
            <div class="mode-label">白天</div>
          </div>
          <div class="map-mode-item" 
               :class="{ active: selectedMapMode === 'dusk' }" 
               @click="selectMapMode('dusk')">
            <div class="icon iconfont icon-huanghun"></div>
            <div class="mode-label">黄昏</div>
          </div>
          <div class="map-mode-item" 
               :class="{ active: selectedMapMode === 'night' }" 
               @click="selectMapMode('night')">
            <div class="icon iconfont icon-yewan"></div>
            <div class="mode-label">夜晚</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 对话框底部 -->
    <div class="dialog-footer">
      <button class="confirm-btn" @click="confirmChanges">确认</button>
    </div>
  </div>
</template>

<script>
import Global from './Global.vue'

export default {
  name: 'QiehuanDialog',
  data() {
    const isMobile = /Android|webOS|iPhone|iPod|BlackBerry|iPad|Mobile/i.test(navigator.userAgent);
    return {
      isMobile: isMobile,
      dialogLeft: isMobile ? 10 : 300,
      dialogTop: isMobile ? 60 : 100,
      dragging: false,
      dragOffsetX: 0,
      dragOffsetY: 0,
      selectedMapType: 'sea', // 默认选择海图
      selectedTimePeriod: 'basic', // 默认选择基本
      selectedMapMode: 'day' // 默认选择白天
    }
  },
  mounted() {
    // 检查必要的全局变量和API函数
    this.checkEnvironment();
  },
  methods: {
    // 检查运行环境
    checkEnvironment() {
      const checks = {
        'Global.IP': !!Global.IP,
        'Global.mapModel': !!Global.mapModel,
        'window._this': !!window._this,
        'window.global': !!window.global,
        'window.global.IP': !!(window.global && window.global.IP),
        'jQuery': !!(window.$ || window.jQuery),
        'API_SetUserYima': typeof window.API_SetUserYima === 'function',
        'API_SetUserTianDiTu': typeof window.API_SetUserTianDiTu === 'function',
        'API_SetMapImagesUrl': typeof window.API_SetMapImagesUrl === 'function',
        'API_RefreshMapImg': typeof window.API_RefreshMapImg === 'function',
        'API_GetWmsToken': typeof window.API_GetWmsToken === 'function'
      };

      console.log('环境检查结果:', checks);
      console.log('Global配置:', {
        IP: Global.IP,
        mapModel: Global.mapModel,
        mapToken: Global.mapToken
      });

      const missingItems = Object.entries(checks)
        .filter(([, value]) => !value)
        .map(([key]) => key);

      if (missingItems.length > 0) {
        console.warn('缺少以下必要的全局变量或API函数:', missingItems);
      } else {
        console.log('✅ 所有必要的环境依赖都已就绪');
      }
    },
    // 关闭对话框
    closeDialog() {
      this.$emit('close');
    },
    
    // 选择地图类型
    selectMapType(type) {
      this.selectedMapType = type;
      this.$emit('map-type-change', type);
    },
    
    // 选择时段
    selectTimePeriod(period) {
      this.selectedTimePeriod = period;
      this.$emit('time-period-change', period);
    },
    
    // 选择地图模式
    selectMapMode(mode) {
      this.selectedMapMode = mode;
      this.$emit('map-mode-change', mode);
    },

    // 确认更改
    confirmChanges() {
      // 执行地图切换逻辑
      this.changeMap();
      // 发送确认事件，包含所有当前选择的配置
      this.$emit('confirm', {
        mapType: this.selectedMapType,
        timePeriod: this.selectedTimePeriod,
        mapMode: this.selectedMapMode
      });
      // 关闭对话框
      this.closeDialog();
    },

    // 切换地图显示类型
    changeMap() {
      console.log('开始切换地图，当前选择:', {
        mapType: this.selectedMapType,
        timePeriod: this.selectedTimePeriod,
        mapMode: this.selectedMapMode
      });

      // 根据选择的配置组合确定地图切换索引
      const mapIndex = this.getMapIndex();
      console.log('计算得到的地图索引:', mapIndex);

      if (window._this && window._this.mapShow === true) {
        window._this.mapShow = false;
      }

      this.executeMapChange(mapIndex);
    },

    // 根据当前选择获取地图索引
    getMapIndex() {
      const { selectedMapType, selectedTimePeriod, selectedMapMode } = this;

      // 基础图底映射
      if (selectedMapType === 'land') {
        return 1; // 陆图
      } else if (selectedMapType === 'sea') {
        if (selectedTimePeriod === 'all') return 2; // 海图-全部
        if (selectedTimePeriod === 'standard') return 4; // 海图-标准
        if (selectedTimePeriod === 'basic') return 6; // 海图-基本
      } else if (selectedMapType === 'satellite') {
        return 3; // 卫星图
      } else if (selectedMapType === 'land-sea') {
        if (selectedMapMode === 'day') return 10; // 陆图+海图-白天
        if (selectedMapMode === 'dusk') return 11; // 陆图+海图-黄昏
        if (selectedMapMode === 'night') return 12; // 陆图+海图-夜晚
      } else if (selectedMapType === 'satellite-sea') {
        if (selectedMapMode === 'day') return 13; // 卫星+海图-白天
        if (selectedMapMode === 'dusk') return 14; // 卫星+海图-黄昏
        if (selectedMapMode === 'night') return 15; // 卫星+海图-夜晚
      }

      // 默认返回海图
      return 2;
    },

    // 执行地图切换
    executeMapChange(index) {
      const global = window.global || {};

      switch (index) {
        case 1: // 陆图
          this.setMapChangeIndex(1, 1);
          this.callAPI('API_SetUserYima', true);
          this.callAPI('API_SetUserTianDiTu', false);
          this.callAPI('API_SetMapImagesUrl', "http://10.90.7.64:3000/tianditu/land/");
          this.callAPI('API_RefreshMapImg');
          break;

        case 2: // 海图-全部
          this.setMapChangeIndex(1, 2);
          this.getMapTokenAndSetup('', "http://***********:11080/api/map/cuttingMap/");
          break;

        case 3: // 卫星图
          this.setMapChangeIndex(1, 3);
          this.callAPI('API_SetUserYima', true);
          this.callAPI('API_SetUserTianDiTu', false);
          this.callAPI('API_SetMapImagesUrl', "http://10.90.7.64:3000/tianditu/satellite/");
          this.callAPI('API_RefreshMapImg');
          break;

        case 4: // 海图-标准-全部
          this.setMapChangeIndex(2, 1);
          global.mapModel = 'day_a';
          this.getMapTokenAndSetup('day_a', "http://***********:11080/api/map/cuttingMap/");
          break;

        case 5: // 海图-标准-标准
          this.setMapChangeIndex(2, 2);
          global.mapModel = 'day_s';
          this.getMapTokenAndSetup('day_s', "http://***********:11080/api/map/cuttingMap/");
          break;

        case 6: // 海图-标准-基本
          this.setMapChangeIndex(2, 3);
          global.mapModel = 'day_b';
          this.getMapTokenAndSetup('day_b', "http://***********:11080/api/map/cuttingMap/");
          break;

        case 7: // 地图模式-白天
          this.setMapChangeIndex(3, 1);
          this.getMapTokenAndSetup('day_b', "http://***********:11080/api/map/cuttingMap/", '71');
          break;

        case 8: // 地图模式-黄昏
          this.setMapChangeIndex(3, 2);
          this.getMapTokenAndSetup('day_b', "http://***********:11080/api/map/cuttingMap/", '71');
          break;

        case 9: // 地图模式-夜晚
          this.setMapChangeIndex(3, 3);
          this.getMapTokenAndSetup('day_b', "http://***********:11080/api/map/cuttingMap/", '71');
          break;

        case 10: // 陆图+海图-白天
          this.setMapChangeIndex(1, 4);
          this.callAPI('API_SetUserYima', true);
          this.callAPI('API_SetUserTianDiTu', true);
          this.callAPI('API_SetMapImagesUrl', "http://10.90.5.217:7001/map/");
          this.callAPI('API_RefreshMapImg');
          break;

        case 11: // 陆图+海图-黄昏
          this.setMapChangeIndex(1, 5);
          this.callAPI('API_SetUserYima', true);
          this.callAPI('API_SetUserTianDiTu', true);
          this.callAPI('API_SetMapImagesUrl', "http://10.90.5.217:7001/wxMap/");
          this.callAPI('API_RefreshMapImg');
          break;

        case 12: // 陆图+海图-夜晚
          this.setMapChangeIndex(1, 5);
          this.callAPI('API_SetUserYima', true);
          this.callAPI('API_SetUserTianDiTu', true);
          this.callAPI('API_SetMapImagesUrl', "http://10.90.5.217:7001/map/");
          this.callAPI('API_RefreshMapImg');
          break;

        case 13: // 卫星+海图-白天
          this.setMapChangeIndex(1, 6);
          this.callAPI('API_SetUserYima', true);
          this.callAPI('API_SetUserTianDiTu', true);
          this.callAPI('API_SetMapImagesUrl', "http://10.90.5.217:7001/YimaMap1/");
          this.callAPI('API_RefreshMapImg');
          break;

        case 14: // 卫星+海图-黄昏
          this.setMapChangeIndex(1, 5);
          this.callAPI('API_SetUserYima', true);
          this.callAPI('API_SetUserTianDiTu', true);
          this.callAPI('API_SetMapImagesUrl', "http://10.90.5.217:7001/map/");
          this.callAPI('API_RefreshMapImg');
          break;

        case 15: // 卫星+海图-夜晚
          this.setMapChangeIndex(1, 7);
          this.callAPI('API_SetUserYima', true);
          this.callAPI('API_SetUserTianDiTu', true);
          this.callAPI('API_SetMapImagesUrl', "http://10.90.5.217:7001/YimaMap2/");
          this.callAPI('API_RefreshMapImg');
          break;

        default:
          console.warn('未知的地图切换索引:', index);
          break;
      }
    },

    // 设置地图切换索引
    setMapChangeIndex(type, value) {
      if (window._this) {
        if (type === 1) {
          window._this.mapChangeIndex1 = value;
        } else if (type === 2) {
          window._this.mapChangeIndex2 = value;
        } else if (type === 3) {
          window._this.mapChangeIndex3 = value;
        }
      }
    },

    // 调用API方法
    callAPI(apiName, ...args) {
      try {
        if (window[apiName] && typeof window[apiName] === 'function') {
          window[apiName](...args);
        } else {
          console.warn(`API方法 ${apiName} 不存在`);
        }
      } catch (error) {
        console.error(`调用API ${apiName} 时出错:`, error);
      }
    },

    // 获取地图Token并设置
    getMapTokenAndSetup(graphNode, mapUrl, skipcode = '') {
      const global = window.global || {};
      const $ = window.$ || window.jQuery;

      // 优先使用导入的Global配置，其次使用window.global，最后使用默认值
      const serverIP = Global.IP || global.IP || 'http://*********:7001';
      const defaultMapModel = Global.mapModel || global.mapModel || 'day_b';

      if (!$) {
        console.warn('jQuery 未定义，尝试使用 fetch API');
        this.getMapTokenWithFetch(graphNode, mapUrl, skipcode, serverIP, defaultMapModel);
        return;
      }

      const tokenUrl = serverIP + "/web/GetMapToken";

      $.get(tokenUrl)
        .done((data) => {
          let wmsToken;
          if (skipcode) {
            wmsToken = `?graphNode=${graphNode}&skipcode=${skipcode}&token=${data}`;
          } else if (graphNode) {
            wmsToken = `?graphNode=${graphNode}&token=${data}`;
          } else {
            wmsToken = `?graphNode=${defaultMapModel}&token=${data}`;
          }

          console.log('成功获取地图Token:', data);
          console.log('生成的WMS Token:', wmsToken);

          this.callAPI('API_SetUserYima', false);
          this.callAPI('API_SetUserTianDiTu', true);
          this.callAPI('API_GetWmsToken', wmsToken);
          this.callAPI('API_SetMapImagesUrl', mapUrl);
          this.callAPI('API_RefreshMapImg');
        })
        .fail((jqXHR, status, error) => {
          console.warn('获取地图Token失败:', status, error, jqXHR.responseText);
          this.setDefaultMapConfig(mapUrl);
        });
    },

    // 使用 fetch API 获取 Token（jQuery 不可用时的备选方案）
    async getMapTokenWithFetch(graphNode, mapUrl, skipcode = '', serverIP = null, defaultMapModel = 'day_b') {
      const global = window.global || {};

      // 使用传入的serverIP或从Global配置获取
      const useServerIP = serverIP || Global.IP || global.IP || 'http://*********:7001';

      try {
        const tokenUrl = useServerIP + "/web/GetMapToken";
        const response = await fetch(tokenUrl);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.text();
        let wmsToken;

        if (skipcode) {
          wmsToken = `?graphNode=${graphNode}&skipcode=${skipcode}&token=${data}`;
        } else if (graphNode) {
          wmsToken = `?graphNode=${graphNode}&token=${data}`;
        } else {
          wmsToken = `?graphNode=${defaultMapModel}&token=${data}`;
        }

        this.callAPI('API_SetUserYima', false);
        this.callAPI('API_SetUserTianDiTu', true);
        this.callAPI('API_GetWmsToken', wmsToken);
        this.callAPI('API_SetMapImagesUrl', mapUrl);
        this.callAPI('API_RefreshMapImg');

      } catch (error) {
        console.warn('使用 fetch 获取地图Token失败:', error);
        this.setDefaultMapConfig(mapUrl);
      }
    },

    // 设置默认地图配置（当Token获取失败时）
    setDefaultMapConfig(mapUrl) {
      console.log('使用默认地图配置');
      this.callAPI('API_SetUserYima', true);
      this.callAPI('API_SetUserTianDiTu', false);

      // 根据mapUrl选择合适的备用地址
      let fallbackUrl = mapUrl;
      if (mapUrl.includes('***********') || mapUrl.includes('cuttingMap')) {
        fallbackUrl = "http://120.27.225.138:8089/YimaMap1/";
      }

      this.callAPI('API_SetMapImagesUrl', fallbackUrl);
      this.callAPI('API_RefreshMapImg');
    },

    // 拖拽开始
    onDragStart(e) {
      this.dragging = true;
      const clientX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
      const clientY = e.type === 'touchstart' ? e.touches[0].clientY : e.clientY;
      this.dragOffsetX = clientX - this.dialogLeft;
      this.dragOffsetY = clientY - this.dialogTop;
      
      if (e.type === 'mousedown') {
        document.addEventListener('mousemove', this.onDragMove);
        document.addEventListener('mouseup', this.onDragEnd);
      } else {
        document.addEventListener('touchmove', this.onDragMove);
        document.addEventListener('touchend', this.onDragEnd);
      }
    },

    // 拖拽移动
    onDragMove(e) {
      if (!this.dragging) return;
      e.preventDefault();
      const clientX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
      const clientY = e.type === 'touchmove' ? e.touches[0].clientY : e.clientY;
      this.dialogLeft = clientX - this.dragOffsetX;
      this.dialogTop = clientY - this.dragOffsetY;
    },

    // 拖拽结束
    onDragEnd() {
      this.dragging = false;
      document.removeEventListener('mousemove', this.onDragMove);
      document.removeEventListener('mouseup', this.onDragEnd);
      document.removeEventListener('touchmove', this.onDragMove);
      document.removeEventListener('touchend', this.onDragEnd);
    },

    // 触摸开始
    onTouchStart(e) {
      this.onDragStart(e);
    }
  },
  
  beforeDestroy() {
    // 清理事件监听器
    document.removeEventListener('mousemove', this.onDragMove);
    document.removeEventListener('mouseup', this.onDragEnd);
    document.removeEventListener('touchmove', this.onDragMove);
    document.removeEventListener('touchend', this.onDragEnd);
  }
}
</script>

<style scoped>
.qiehuan-dialog {
  background: #fff;
  border-radius: 8px;
  padding: 0;
  width: 300px;
  font-size: 16px;
  color: #333;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid #e0e0e0;
  position: fixed;
  z-index: 9999;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  cursor: move;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #1890ff;
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid #1890ff;
}

.dialog-title {
  font-size: 20px;
  font-weight: bold;
  color: #fff;
  margin: 0;
}

.close-btn {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
  line-height: 1;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.dialog-content {
  padding: 16px;
}

.dialog-footer {
  padding: 12px 16px;
  border-top: 1px solid #e0e0e0;
  background: #fafafa;
  border-radius: 0 0 8px 8px;
  display: flex;
  justify-content: center;
}

.confirm-btn {
  background: #1890ff;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 10px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 80px;
}

.confirm-btn:hover {
  background: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.confirm-btn:active {
  background: #096dd9;
  transform: translateY(0);
}

.section {
  margin-bottom: 20px;
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
  padding-bottom: 4px;
  border-bottom: 1px solid #e0e0e0;
}

.map-types {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.map-types:last-child {
  margin-bottom: 0;
}

.map-type-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid transparent;
}

.map-type-item:hover {
  background: rgba(24, 144, 255, 0.1);
  border-color: rgba(24, 144, 255, 0.3);
}

.map-type-item.active {
  background: #1890ff;
  border-color: #1890ff;
}

.map-preview {
  width: 60px;
  height: 45px;
  border-radius: 6px;
  margin-bottom: 6px;
  background-size: cover;
  background-position: center;
}

.land-preview {
  background-image: url('../../static/img/map2.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.sea-preview {
  background-image: url('../../static/img/map3.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.satellite-preview {
  background-image: url('../../static/img/map4.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.land-sea-preview {
  background-image: url('../../static/img/map4.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.satellite-sea-preview {
  background-image: url('../../static/img/map4.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.map-label {
  font-size: 14px;
  color: #666;
  text-align: center;
}

.map-type-item.active .map-label {
  color: #fff;
}

.time-periods, .map-modes {
  display: flex;
  gap: 8px;
}

.time-period-item, .map-mode-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid transparent;
}

.time-period-item:hover, .map-mode-item:hover {
  background: rgba(24, 144, 255, 0.1);
  border-color: rgba(24, 144, 255, 0.3);
}

.time-period-item.active, .map-mode-item.active {
  background: #1890ff;
  border-color: #1890ff;
}

.icon {
  font-size: 24px;
  margin-bottom: 6px;
  color: #666;
}

.time-period-item.active .icon,
.map-mode-item.active .icon {
  color: #fff;
}

.time-label, .mode-label {
  font-size: 14px;
  color: #666;
  text-align: center;
}

.time-period-item.active .time-label,
.map-mode-item.active .mode-label {
  color: #fff;
}

/* 移动设备适配 */
@media (max-width: 768px) {
  .qiehuan-dialog {
    width: 280px;
    font-size: 15px;
  }

  .dialog-title {
    font-size: 19px;
  }

  .close-btn {
    font-size: 22px;
  }

  .section-title {
    font-size: 15px;
  }

  .map-preview {
    width: 60px;
    height: 45px;
  }

  .icon {
    font-size: 22px;
  }

  .map-label, .time-label, .mode-label {
    font-size: 13px;
  }

  .confirm-btn {
    font-size: 15px;
    padding: 9px 20px;
  }
}
</style>
