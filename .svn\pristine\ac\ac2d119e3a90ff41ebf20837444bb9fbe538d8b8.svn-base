package com.bd.entity;

// import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
// @TableName("SHIP.OUTINPORTRECORD_2")
public class OutInPortRecord {

    private int id;//数据库id
    private int staticShipId;
    private int portId;
    private String portName;
    private String shipName;
    private String bdId;
    private int state;  // 0出港 1进港
    private int reportTime;
    private String loadTime;
    private int isReport;

    private FisheryBoatInOutReport report;
}
