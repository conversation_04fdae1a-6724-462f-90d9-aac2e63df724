@font-face {
  font-family: "iconfont"; /* Project id 4970697 */
  src: url('iconfont.woff2?t=1752053691310') format('woff2'),
       url('iconfont.woff?t=1752053691310') format('woff'),
       url('iconfont.ttf?t=1752053691310') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-jiahao:before {
  content: "\e626";
}

.icon-03:before {
  content: "\e60e";
}

.icon-xinfeng:before {
  content: "\e64d";
}

.icon-jianhao:before {
  content: "\eaf5";
}

.icon-dingwei1:before {
  content: "\e61f";
}

.icon-chakan:before {
  content: "\e624";
}

.icon-sousuo:before {
  content: "\e6b9";
}

.icon-dingwei:before {
  content: "\e62d";
}

.icon-map-ruler:before {
  content: "\ea07";
}

.icon-dian<PERSON>weilan:before {
  content: "\e677";
}

.icon-gongjuxiang:before {
  content: "\e85b";
}

