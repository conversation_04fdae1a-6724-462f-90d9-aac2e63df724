<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4970697" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xeaf1;</span>
                <div class="name">菜单</div>
                <div class="code-name">&amp;#xeaf1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe856;</span>
                <div class="name">统计</div>
                <div class="code-name">&amp;#xe856;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe632;</span>
                <div class="name">暂停</div>
                <div class="code-name">&amp;#xe632;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">开始</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe691;</span>
                <div class="name">YDUI-录像</div>
                <div class="code-name">&amp;#xe691;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe76e;</span>
                <div class="name">screenshot-fill</div>
                <div class="code-name">&amp;#xe76e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe633;</span>
                <div class="name">船舶</div>
                <div class="code-name">&amp;#xe633;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe628;</span>
                <div class="name">全部</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe783;</span>
                <div class="name">全部</div>
                <div class="code-name">&amp;#xe783;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe672;</span>
                <div class="name">白天-晴</div>
                <div class="code-name">&amp;#xe672;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61d;</span>
                <div class="name">夜晚</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ac;</span>
                <div class="name">立方体</div>
                <div class="code-name">&amp;#xe6ac;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe701;</span>
                <div class="name">黄昏</div>
                <div class="code-name">&amp;#xe701;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">台风</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe850;</span>
                <div class="name">标注</div>
                <div class="code-name">&amp;#xe850;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe626;</span>
                <div class="name">加号</div>
                <div class="code-name">&amp;#xe626;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">地图</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64d;</span>
                <div class="name">信封</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeaf5;</span>
                <div class="name">减号</div>
                <div class="code-name">&amp;#xeaf5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61f;</span>
                <div class="name">定位</div>
                <div class="code-name">&amp;#xe61f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe624;</span>
                <div class="name">查看</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b9;</span>
                <div class="name">搜索</div>
                <div class="code-name">&amp;#xe6b9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62d;</span>
                <div class="name">定位</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xea07;</span>
                <div class="name">地图尺子,测量,测距,距离</div>
                <div class="code-name">&amp;#xea07;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe677;</span>
                <div class="name">电子围栏</div>
                <div class="code-name">&amp;#xe677;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe85b;</span>
                <div class="name">工具箱</div>
                <div class="code-name">&amp;#xe85b;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1753323475350') format('woff2'),
       url('iconfont.woff?t=1753323475350') format('woff'),
       url('iconfont.ttf?t=1753323475350') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-caidan"></span>
            <div class="name">
              菜单
            </div>
            <div class="code-name">.icon-caidan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongji"></span>
            <div class="name">
              统计
            </div>
            <div class="code-name">.icon-tongji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zanting"></span>
            <div class="name">
              暂停
            </div>
            <div class="code-name">.icon-zanting
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kaishi"></span>
            <div class="name">
              开始
            </div>
            <div class="code-name">.icon-kaishi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yduiluxiang"></span>
            <div class="name">
              YDUI-录像
            </div>
            <div class="code-name">.icon-yduiluxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-screenshot-fill"></span>
            <div class="name">
              screenshot-fill
            </div>
            <div class="code-name">.icon-screenshot-fill
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chuanbo"></span>
            <div class="name">
              船舶
            </div>
            <div class="code-name">.icon-chuanbo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quanbu"></span>
            <div class="name">
              全部
            </div>
            <div class="code-name">.icon-quanbu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quanbu1"></span>
            <div class="name">
              全部
            </div>
            <div class="code-name">.icon-quanbu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-baitian-qing"></span>
            <div class="name">
              白天-晴
            </div>
            <div class="code-name">.icon-baitian-qing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yewan"></span>
            <div class="name">
              夜晚
            </div>
            <div class="code-name">.icon-yewan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lifangti"></span>
            <div class="name">
              立方体
            </div>
            <div class="code-name">.icon-lifangti
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huanghun"></span>
            <div class="name">
              黄昏
            </div>
            <div class="code-name">.icon-huanghun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-taifeng"></span>
            <div class="name">
              台风
            </div>
            <div class="code-name">.icon-taifeng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-biaozhu"></span>
            <div class="name">
              标注
            </div>
            <div class="code-name">.icon-biaozhu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiahao"></span>
            <div class="name">
              加号
            </div>
            <div class="code-name">.icon-jiahao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-03"></span>
            <div class="name">
              地图
            </div>
            <div class="code-name">.icon-03
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xinfeng"></span>
            <div class="name">
              信封
            </div>
            <div class="code-name">.icon-xinfeng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jianhao"></span>
            <div class="name">
              减号
            </div>
            <div class="code-name">.icon-jianhao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dingwei1"></span>
            <div class="name">
              定位
            </div>
            <div class="code-name">.icon-dingwei1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chakan"></span>
            <div class="name">
              查看
            </div>
            <div class="code-name">.icon-chakan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sousuo"></span>
            <div class="name">
              搜索
            </div>
            <div class="code-name">.icon-sousuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dingwei"></span>
            <div class="name">
              定位
            </div>
            <div class="code-name">.icon-dingwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-map-ruler"></span>
            <div class="name">
              地图尺子,测量,测距,距离
            </div>
            <div class="code-name">.icon-map-ruler
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dianziweilan"></span>
            <div class="name">
              电子围栏
            </div>
            <div class="code-name">.icon-dianziweilan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongjuxiang"></span>
            <div class="name">
              工具箱
            </div>
            <div class="code-name">.icon-gongjuxiang
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caidan"></use>
                </svg>
                <div class="name">菜单</div>
                <div class="code-name">#icon-caidan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongji"></use>
                </svg>
                <div class="name">统计</div>
                <div class="code-name">#icon-tongji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zanting"></use>
                </svg>
                <div class="name">暂停</div>
                <div class="code-name">#icon-zanting</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kaishi"></use>
                </svg>
                <div class="name">开始</div>
                <div class="code-name">#icon-kaishi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yduiluxiang"></use>
                </svg>
                <div class="name">YDUI-录像</div>
                <div class="code-name">#icon-yduiluxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-screenshot-fill"></use>
                </svg>
                <div class="name">screenshot-fill</div>
                <div class="code-name">#icon-screenshot-fill</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chuanbo"></use>
                </svg>
                <div class="name">船舶</div>
                <div class="code-name">#icon-chuanbo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanbu"></use>
                </svg>
                <div class="name">全部</div>
                <div class="code-name">#icon-quanbu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanbu1"></use>
                </svg>
                <div class="name">全部</div>
                <div class="code-name">#icon-quanbu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baitian-qing"></use>
                </svg>
                <div class="name">白天-晴</div>
                <div class="code-name">#icon-baitian-qing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yewan"></use>
                </svg>
                <div class="name">夜晚</div>
                <div class="code-name">#icon-yewan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lifangti"></use>
                </svg>
                <div class="name">立方体</div>
                <div class="code-name">#icon-lifangti</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huanghun"></use>
                </svg>
                <div class="name">黄昏</div>
                <div class="code-name">#icon-huanghun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-taifeng"></use>
                </svg>
                <div class="name">台风</div>
                <div class="code-name">#icon-taifeng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-biaozhu"></use>
                </svg>
                <div class="name">标注</div>
                <div class="code-name">#icon-biaozhu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiahao"></use>
                </svg>
                <div class="name">加号</div>
                <div class="code-name">#icon-jiahao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-03"></use>
                </svg>
                <div class="name">地图</div>
                <div class="code-name">#icon-03</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinfeng"></use>
                </svg>
                <div class="name">信封</div>
                <div class="code-name">#icon-xinfeng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jianhao"></use>
                </svg>
                <div class="name">减号</div>
                <div class="code-name">#icon-jianhao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dingwei1"></use>
                </svg>
                <div class="name">定位</div>
                <div class="code-name">#icon-dingwei1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chakan"></use>
                </svg>
                <div class="name">查看</div>
                <div class="code-name">#icon-chakan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sousuo"></use>
                </svg>
                <div class="name">搜索</div>
                <div class="code-name">#icon-sousuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dingwei"></use>
                </svg>
                <div class="name">定位</div>
                <div class="code-name">#icon-dingwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-map-ruler"></use>
                </svg>
                <div class="name">地图尺子,测量,测距,距离</div>
                <div class="code-name">#icon-map-ruler</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dianziweilan"></use>
                </svg>
                <div class="name">电子围栏</div>
                <div class="code-name">#icon-dianziweilan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongjuxiang"></use>
                </svg>
                <div class="name">工具箱</div>
                <div class="code-name">#icon-gongjuxiang</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
