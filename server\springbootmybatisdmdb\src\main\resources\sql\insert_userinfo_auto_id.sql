-- 为USERINFO表插入数据（自动生成ID）的SQL脚本
-- 执行日期: 2025-07-29
-- 说明: 不指定ID值，让数据库自动生成自增ID

-- 插入用户数据（不包含ID，让数据库自动生成）
-- 修复：移除ID列，让数据库自动生成自增ID
INSERT INTO SHIP.USERINFO (USERNAME, PASSWORD, NAME, LXDH, LEVEL, TOKEN, BM, ERRORCOUNT, USER_AREA)
VALUES ('chongming01', '123456', '崇明', null, 1, null, null, 0, null);

-- 验证插入结果
SELECT * FROM SHIP.USERINFO WHERE USERNAME = 'admin';

-- 查看自动生成的ID
SELECT LAST_INSERT_ID() AS generated_id;
