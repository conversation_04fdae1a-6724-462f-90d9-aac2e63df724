<configuration>
    <!-- Web_server BdThread  mapToken -->
    <property name="LOG_HOME" value="/opt/tmp/BDStar_System/Web_server" />
    <appender name="INFO_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/[INFO]dataAccess/%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>360</MaxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %boldYellow(%thread) %highlight(%-5level) %msg%n
            </Pattern>
        </encoder>
    </appender>
    <appender name="ERR_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/[WARN]dataAccess/%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>360</maxHistory>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] %level [%thread] %file:%line - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="POST_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/[POST]dataAccess/%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>360</maxHistory>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] %level [%thread] %file:%line - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="INFO_LOG"/>
    </root>
    <logger name="INFO_LOG" additivity="false" level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="INFO_LOG"/>
    </logger>
    <logger name="ERR_LOG" additivity="false" level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="ERR_LOG"/>
    </logger>
    <logger name="POST_LOG" additivity="false" level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="POST_LOG"/>
    </logger>
</configuration>
