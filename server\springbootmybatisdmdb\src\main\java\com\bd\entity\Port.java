package com.bd.entity;

import com.bd.util.M_POINT;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Port {

    private int id;//数据库id
    private String portName;
    private int isUsed;
    private List<M_POINT> points;
    private int pointCount;
    private long organizationID; //组织机构编号
    private int provinceID; //省份编号
    private String provinceName;//省份名称
    private String pointStr = "";
    private List<Integer> ships;
    private int shipCount;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getPortName() {
        return portName;
    }

    public void setPortName(String portName) {
        this.portName = portName;
    }

    public int getIsUsed() {
        return isUsed;
    }

    public void setIsUsed(int isUsed) {
        this.isUsed = isUsed;
    }

    public List<M_POINT> getPoints() {
        return points;
    }

    public void setPoints(List<M_POINT> points) {
        this.points = points;
    }

    public int getPointCount() {
        return pointCount;
    }

    public void setPointCount(int pointCount) {
        this.pointCount = pointCount;
    }

    public long getOrganizationID() {
        return organizationID;
    }

    public void setOrganizationID(long organizationID) {
        this.organizationID = organizationID;
    }

    public int getProvinceID() {
        return provinceID;
    }

    public void setProvinceID(int provinceID) {
        this.provinceID = provinceID;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getPointStr() {
        return pointStr;
    }

    public void setPointStr(String pointStr) {
        this.pointStr = pointStr;
    }

    public List<Integer> getShips() {
        return ships;
    }

    public void setShips(List<Integer> ships) {
        this.ships = ships;
    }

    public int getShipCount() {
        return shipCount;
    }

    public void setShipCount(int shipCount) {
        this.shipCount = shipCount;
    }

    public List<M_POINT> getPointsByStr(){
        if (pointStr == "")
            return null;
        List<M_POINT> points = new ArrayList<>();
        for (int i = 0; i < pointStr.split("#").length; i++){
            M_POINT point = new M_POINT();
            String sLon = pointStr.split("#")[i].split("@")[0];
            String sLat = pointStr.split("#")[i].split("@")[1];
            //System.out.println(String.format("%10s0", sLon));
            point.x = Integer.parseInt(String.format("%10s0", sLon).trim());
            point.y = Integer.parseInt(String.format("%9s0", sLat).trim());

            points.add(point);
        }
        this.points = points;
        return points;
    }
}
