<template>
  <div class="demo-container">
    <h1>电子围栏对话框演示</h1>
    <p>这是一个演示页面，展示电子围栏对话框组件的功能。</p>
    
    <div class="demo-buttons">
      <button @click="showDialog" class="demo-btn">打开电子围栏对话框</button>
    </div>
    
    <div class="demo-info" v-if="lastData">
      <h3>最后提交的数据：</h3>
      <pre>{{ JSON.stringify(lastData, null, 2) }}</pre>
    </div>
    
    <!-- 围栏对话框组件 -->
    <WeilanDialog v-if="showWeilanDialog" @close="closeDialog" @confirm="handleConfirm" />
  </div>
</template>

<script>
import WeilanDialog from './WeilanDialog.vue'

export default {
  name: 'WeilanDemo',
  components: {
    WeilanDialog
  },
  data() {
    return {
      showWeilanDialog: false,
      lastData: null
    }
  },
  methods: {
    showDialog() {
      this.showWeilanDialog = true
    },
    closeDialog() {
      this.showWeilanDialog = false
    },
    handleConfirm(data) {
      this.lastData = data
      this.showWeilanDialog = false
      console.log('接收到的围栏数据:', data)
    }
  }
}
</script>

<style scoped>
.demo-container {
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}

h1 {
  color: #1890ff;
  text-align: center;
  margin-bottom: 20px;
}

p {
  color: #666;
  text-align: center;
  margin-bottom: 30px;
  font-size: 16px;
}

.demo-buttons {
  text-align: center;
  margin-bottom: 30px;
}

.demo-btn {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.2s;
}

.demo-btn:hover {
  background: #40a9ff;
}

.demo-info {
  background: #f5f5f5;
  border-radius: 6px;
  padding: 20px;
  margin-top: 20px;
}

.demo-info h3 {
  margin-top: 0;
  color: #333;
}

pre {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 15px;
  overflow-x: auto;
  font-size: 14px;
  color: #333;
}
</style>
