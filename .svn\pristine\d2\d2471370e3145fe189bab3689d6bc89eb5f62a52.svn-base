package com.bd.controller;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.event.AbstractIgnoreExceptionReadListener;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bd.entity.*;
import com.bd.entity.BusinessManagement.AlarmRecord;
import com.bd.entity.dto.CrewCertificationQueryDto;
import com.bd.entity.dto.PeopleQueryDto;
import com.bd.entity.dto.ShipQueryDto;
import com.bd.entity.other.ExcelEntity;
import com.bd.entity.other.SpecialShip;
import com.bd.service.*;
import com.bd.util.AesUtil;
import com.bd.util.HttpTool;
//import com.bd.util.SignTest;
import com.bd.util.Utils;
import com.github.pagehelper.PageInfo;
import lombok.SneakyThrows;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.InetAddress;
import java.text.Normalizer;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/web")
@CrossOrigin
public class HttpController {

    @Autowired
    private ShipService shipService;

    @Autowired
    private ShipStaticInfoService shipStaticInfoService;

    @Autowired
    private PortService portService;

    @Autowired
    private UserService userService;

    @Autowired
    private TyphoonService typhoonService;

    @Autowired
    private AreaService areaService;

    @Autowired
    private PeopleService peopleService;

    @RequestMapping("/Login")
    public UserInfo Login(@RequestBody UserInfo user, HttpServletRequest request) {
        System.out.println(user);
/*
/*
        if(user.getLevel() == 0){
            String ip = request.getHeader("x-forwarded-for");
            if(ip == null || ip.length() == 0 || "unknow".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (ip == null || ip.length () == 0 || "unknown".equalsIgnoreCase (ip)) {
                ip = request.getHeader ("WL-Proxy-Client-IP");
            }
            if (ip == null || ip.length () == 0 || "unknown".equalsIgnoreCase (ip)) {
                ip = request.getRemoteAddr ();
                if (ip.equals ("127.0.0.1")) {
                    //根据网卡取本机配置的IP
                    InetAddress inet = null;
                    try {
                        inet = InetAddress.getLocalHost ();
                    } catch (Exception e) {
                        e.printStackTrace ();
                    }
                    ip = inet.getHostAddress ();
                }
            }
            // 多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
            if (ip != null && ip.length () > 15) {
                if (ip.indexOf (",") > 0) {
                    ip = ip.substring (0, ip.indexOf (","));
                }
            }

            if(!ip.equals("************") || !ip.equals("***********") || !ip.equals("**********")){
                user.setState("该用户限制此IP登录！");
                return user;
            }
        }
*/

        if (userService.GetUserName(user.getUsername()) < 1) {
            user.setState("用户不存在");
            return user;
        }
        UserInfo respUser = userService.GetUser(user);
        System.out.println(respUser);
        int errorCount = userService.GetErrorCount(user.getUsername());
        if (respUser == null) {
            if (errorCount < 5) {
                userService.SetErrorCount(user.getUsername(), errorCount + 1);
                user.setState("密码错误");
            } else {
                user.setState("输入错误密码超出限制");
            }
            return user;
        } else {
            if (errorCount == 5) {
                respUser.setState("输入错误密码超出限制");
            } else {
                userService.SetErrorCount(user.getUsername(), 0);
                respUser.setState("登陆成功");
            }
            return respUser;
        }

    }

    // 重置密码错误次数
    @RequestMapping("/resetErrorTimes")
    public void resetErrorTimes(@RequestParam String userName) {
        userService.SetErrorCount(userName, 0);
    }

    @RequestMapping("/UpdateUser")
    public String UpdateUser(@RequestParam("userId") int userId, @RequestBody UserInfo userInfo) {
        userService.UpdateUser(userId, userInfo);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    @RequestMapping("/DeleteUser")
    public String DeleteUser(@RequestBody List<Integer> userIds) {
        for (int i = 0; i < userIds.size(); i++) {
            userService.DeleteUser(userIds.get(i));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    @GetMapping("/GetBDShipPosition")
    public List<ShipDynamicInfo> GetBDShipPosition(@RequestParam("sec") int sec) {
        //return shipService.GetBDShipPosition((int)Utils.GetNowTimelong() - sec);
        //return shipService.GetBDShipPosition_ronghe((int)Utils.GetNowTimelong() - sec);
        return shipService.GetBDShipPosition_ronghe_strTime(Utils.GetNowTimeString(sec));
    }

    @GetMapping("/GetAllBDShipPosition")
    public String GetAllBDShipPosition(@RequestParam("pageNum") int pageNum) {
        pageNum = (pageNum - 1) * 400;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", shipService.GetAllBDShipPosition_ronghe(pageNum));
        jsonObject.put("total", shipService.GetBDShipPosition_rongheCount());
        return jsonObject.toJSONString();
    }

    // 港口操作
    @GetMapping("/GetAllPortInfo")
    public List<PortInfo> GetAllPortInfo() {
        return portService.GetAllPortInfo();
    }

    @GetMapping("/GetPortInfo")
    public String GetPortInfo(@RequestParam("pageNum") int pageNum) {
        pageNum = (pageNum - 1) * 10;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", portService.GetPortInfo(pageNum));
        jsonObject.put("total", portService.GetPortInfoCount());
        return jsonObject.toJSONString();
    }

    @GetMapping("/GetOnePortInfo")
    public List<PortInfo> GetOnePortInfo(@RequestParam("portId") int portId) {
        return portService.GetOnePortInfo(portId);
    }

    @RequestMapping("/AddPort")
    public int AddPort(@RequestParam("name") String name,
                       @RequestParam("content") String content,
                       @RequestParam("maxshapcount") int maxshapcount,
                       @RequestParam("windlevel") int windlevel,
                       @RequestParam("navmarkmmsi") String navmarkmmsi,
                       @RequestBody List<Coordinate> points) {
        String pointStr = "";
        for (int i = 0; i < points.size(); i++) {
            if (i == 0) {
                pointStr = pointStr + points.get(i).getLon() + "@" + points.get(i).getLat();
            } else {
                pointStr = pointStr + "#" + points.get(i).getLon() + "@" + points.get(i).getLat();
            }
        }
        portService.AddPort(name, points.size(), pointStr, content, maxshapcount, windlevel, navmarkmmsi);
        List<PortInfo> portInfo = portService.GetAllPortInfo();
        int id = portInfo.get(portInfo.size() - 1).getId();
        return id;
    }

    @RequestMapping("/EditPort")
    public String EditPort(@RequestParam("portId") int portId,
                           @RequestParam("newName") String newName,
                           @RequestParam("content") String content,
                           @RequestParam("maxshapcount") int maxshapcount,
                           @RequestParam("windlevel") int windlevel,
                           @RequestParam("navmarkmmsi") String navmarkmmsi,
                           @RequestBody List<Coordinate> newPoints) {
        String pointStr = "";
        for (int i = 0; i < newPoints.size(); i++) {
            if (i == 0) {
                pointStr = pointStr + newPoints.get(i).getLon() + "@" + newPoints.get(i).getLat();
            } else {
                pointStr = pointStr + "#" + newPoints.get(i).getLon() + "@" + newPoints.get(i).getLat();
            }
        }
        portService.EditPort(portId, newName, newPoints.size(), pointStr, content, maxshapcount, windlevel, navmarkmmsi);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    @GetMapping("/getShipInOutReport")
    public String getShipInOutReport(@RequestParam("shipName") String shipName,
                                     @RequestParam("status") String statue,
                                     @RequestParam("startTime") String startTime,
                                     @RequestParam("endTime") String endTime,
                                     @RequestParam("pageNum") int pageNum) {
        int iState = 3;
        if (statue.equals("进港"))
            iState = 0;
        else if (statue.equals("出港"))
            iState = 1;
        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ");
//        pageNum = (pageNum - 1)*10;
        JSONObject jsonObject = new JSONObject();
        PageInfo<FisheryBoatInOutReport> inoutReport = portService.getShipInoutReport(shipName, null, iState, startTime, endTime, pageNum);
        jsonObject.put("page", inoutReport);
        return jsonObject.toJSONString();

    }

    @GetMapping
    public String getShipInOutByShipName() {
        return "";
    }

    @RequestMapping("/DeletePort")
    public String DeletePort(@RequestBody List<Integer> portIds) {
        for (int i = 0; i < portIds.size(); i++) {
            portService.DeletePort(portIds.get(i));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    @GetMapping("/QueryShip")
    public List<ShipSerch> GetShipInfoByNameOrTerminalNumber(@RequestParam("keyword") String keyword) {
        List<String> appleNameList = peopleService.GetShipNameByPeople(keyword);
        List<String> shipNameList = new ArrayList<>();
        if (appleNameList != null && appleNameList.size() > 0) {
            for (String appleName : appleNameList) {
                if (appleName != null && appleName.length() > 5) {
                    String shipName = "";
                    for (int i = appleName.length() - 1; i >= 0; i--) {
                        if (!appleName.substring(i - 1, i).matches("[\u4e00-\u9fa5]")) {
                            //System.out.println(appleName.substring(i-1,i));
                            shipName = appleName.substring(0, i);
                            break;
                        }
                    }
                    shipNameList.add(shipName);
                }
            }
        }
        //System.out.println(shipNameList);
        if (shipNameList.size() > 0) {
            return shipStaticInfoService.GetShipInfoByShipNameList(shipNameList);
        }
        return shipStaticInfoService.GetShipInfoByNameOrTerminalNumber(keyword);
    }

    @RequestMapping("/GetOneShipInfoByShipName")
    public List<ShipStaticInfo_all> GetOneShipInfoByShipName(@RequestParam("name") String name) {
        return shipStaticInfoService.GetOneShipInfoByShipName(name);
    }

    //write for test by peter
    //@GetMapping("/TestGetShipInfoByShipName")
    //public List<ShipStaticInfo_all> TestGetShipInfoByShipName(@RequestParam("name")String name){
    //    return shipStaticInfoService.TestGetShipInfoByShipName(name);
    //}

    @GetMapping("/GetOneShipInfoById")
    public List<ShipStaticInfo_all> GetOneShipInfoById(@RequestParam("id") int id) {
        return shipStaticInfoService.GetOneShipInfoById(id);
    }


    @GetMapping("/GetOneShipCardById")
    public List<ShipStaticInfo_card> GetOneShipCardById(@RequestParam("id") int id) {
        return shipStaticInfoService.GetOneShipCardById(id);
    }

    //特许捕捞
    @GetMapping("/GetOneShipCardTexuById")
    public FisheryPermitInfo GetOneShipCardTexuById(@RequestParam("id") int id) {
        return shipStaticInfoService.GetOneShipCardTexuById(id);
    }

    @RequestMapping("/GetOneShipDynamicInfoById")
    public ShipDynamicInfo GetOneShipDynamicInfoById(@RequestParam("id") int id) {
        return shipService.GetOneShipDynamicInfoById(id);
    }

    @GetMapping("/GetOutInPortRecord")
    public String GetOutInPortRecord() {
        JSONObject jsonObject = new JSONObject();
        List<OutInPortRecord> outInPortRecords = portService.GetOutInPortRecord();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        for (OutInPortRecord record : outInPortRecords) {
            int shipId = record.getStaticShipId();
            // state 0 出港 | 1 进港
            if (record.getState() == 0)
                record.setReport(peopleService.GetPortNodeByShipId(shipId, 1));  /* 0 进港  1 出港 */
            else
                record.setReport(peopleService.GetPortNodeByShipId(shipId, 0));
            FisheryBoatInOutReport report = peopleService.GetPortNodeByShipId(shipId, null);
            map.put(String.valueOf(shipId), report);
        }
        jsonObject.put("list", outInPortRecords);
        jsonObject.put("map", map);
        // System.out.println(jsonObject);
        return jsonObject.toJSONString();
    }

    @GetMapping("/GetOutInPortRecordByShipId")
    public List<OutInPortRecord> GetOutInPortRecordByShipId(@RequestParam("shipId") String shipId) {
        return portService.GetOutInPortRecordByShipId(shipId, -1);
    }

    @GetMapping("/GetShipManyRegis")
    public String GetShipManyRegis(@RequestParam("shipId") int shipId) {
        JSONObject jsonObject = new JSONObject();
        List<FisheryBoatInOutReport> records = portService.get7DaysRecord();
        FisheryBoatInOutReport report = peopleService.GetPortNodeByShipId(shipId, null);
        ArrayList<String> names = new ArrayList<>();
        if (report != null && StringUtils.hasText(report.getCrewStr())) {
            String[] split = report.getCrewStr().split("@");
            for (String s : split) {
                names.add(s.split("#")[0]);
            }
        }

        // System.out.println(names);
        for (FisheryBoatInOutReport record : records) {
            if (report != null && record.getShipName().equals(report.getShipName()) || record.getCrewStr() == null)
                continue;
            for (String name : names) {
                if (record.getCrewStr().contains(name)) {
                    jsonObject.put(record.getShipName(), name);
                }
            }
        }

        return jsonObject.toJSONString();
    }

    @GetMapping("/GetPortShipCount")
    public List<PortShipCount> GetPortShipCount() {
        return portService.GetPortShipCount();
    }

    //在港渔船统计数量
    @RequestMapping("/GetInPortShipCount")
    public String GetShipCount() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("registeredFish", shipService.GetRegisteredFish());
        jsonObject.put("inPortFish", shipService.GetInPortFish());
        jsonObject.put("outPortFish", shipService.GetOutPortFish());
        jsonObject.put("wsInPortFish", shipService.GetWsInPortFish());
        return jsonObject.toJSONString();
    }

    //在港渔船统计数量D
    @RequestMapping("/GetShipDistribute")
    public Ship_DistributeCount GetShipDistribute(@RequestParam("portId") int portId) {
        Ship_DistributeCount sd = new Ship_DistributeCount();
        sd.setAllShipCount(shipService.getAllShipCount());//全部渔船
        sd.setOnlineCount(shipService.getOnlineShipCount());//在线渔船
        sd.setOtherAreaCount(shipService.GetOutPortFish());//沪籍港外
        sd.setInPortShipCount(shipService.GetInPortFish2(portId));//沪籍在省

        sd.setInShanghai_ws(GetWSInShangHaiShipInfo().size());//外省 在上海
        sd.setInJinbu_ws(GetWSInJinBuShipInfo().size());//外省在禁捕
        sd.setInPortShipCount_ws(GetWSInPortShipInfo(portId).size());//外省在港

        return sd;
    }

    // 获取单条船舶的历史轨迹
    @GetMapping("/GetShipHistoryTrackById")
    public List<ShipTrack> GetShipHistoryTrackById(@RequestParam("id") int id,
                                                   @RequestParam("startTime") long startTime,
                                                   @RequestParam("endTime") long endTime,
                                                   @RequestParam("addAis") int addAis,
                                                   @RequestParam("addBd") int addBd,
                                                   @RequestParam("bOutSide") int bOutSide) {
        //bOutSide 0 沪籍渔船  1 外省渔船
        return shipService.GetShipHistoryTrackById(id, startTime, endTime, addAis, addBd, bOutSide);
    }

    // 获取区域船舶的历史轨迹
    @GetMapping("/GetPlayAreaHistoryInfo")
    public List<ShipTrack> GetPlayAreaHistoryInfo(@RequestParam("startTime") String startTime,
                                                  @RequestParam("endTime") String endTime,
                                                  @RequestParam("minLon") String minLon,
                                                  @RequestParam("maxLon") String maxLon,
                                                  @RequestParam("minLat") String minLat,
                                                  @RequestParam("maxLat") String maxLat,
                                                  @RequestParam("addAis") int addAis) {
        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ");
        return shipService.GetPlayAreaHistoryInfo(startTime, endTime, minLon, maxLon, minLat, maxLat, addAis);
    }

    @GetMapping("/GetPlayShipInfoById")
    public PlayShipInfo GetPlayShipInfoById(@RequestParam("id") int id, @RequestParam("userId") int userId, @RequestParam("model") int model) {
        PlayShipInfo playShipInfo = shipStaticInfoService.GetPlayShipInfoById(id);
        JianCeShipInfo jianCeShipInfo = shipService.GetJianCeShipInfoByShipId(id);
        System.out.println(jianCeShipInfo);
        if (jianCeShipInfo != null) {
            playShipInfo.setBJianCe(1);
        } else {
            playShipInfo.setBJianCe(0);
        }

        AlarmRecord alarmRecord = portService.GetWarningShipInfoByShipId(id, userId, model);
        if (alarmRecord != null) {
            playShipInfo.setBWarning(1);
        } else {
            playShipInfo.setBWarning(0);
        }
        System.out.println(playShipInfo);
        return playShipInfo;
    }

    @PostMapping("/Postppp")
    public String post(@RequestParam("username") String username,
                       @RequestParam("password") String password) {
        return username;
    }
    @PostMapping("/UpdateOutLineReason")
    public String updateOutlineReason(@RequestBody ShipEncryption shipEncryption) {
            Integer shipId = shipEncryption.getShipId();
            String outLineReason = shipEncryption.getOutLineReason();
            List<PortShipInfo> portShipInfos = portService.GetAllShipInfo();
            for (PortShipInfo portShipInfo : portShipInfos) {
                if (portShipInfo.getShipId() == shipId) {
                    portService.UpdateOutlineReason(shipId, outLineReason);
                    return "after已保存:"+" "+outLineReason;
                }
            }
            return "after保存失败:"+" "+outLineReason;
    }
    //获取上海市渔业船舶信息排摸表的所有信息
    @GetMapping("/GetAllShanghaiShipsInfo")
    public List<PortShipInfo> GetAllShanghaiShipsInfo(){
        return portService.GetAllShanghaiShipsInfo();
    }

    @GetMapping("/GetShip_inOrOutPortInfoDetail")
    public String GetShip_inOrOutPortInfoDetail(@RequestParam("shipName") String shipName,
                                                @RequestParam("portName") String portName,
                                                @RequestParam("statue") String statue,
                                                @RequestParam("startTime") String startTime,
                                                @RequestParam("endTime") String endTime,
                                                @RequestParam("pageNum") int pageNum) {
        int iState = 3;
        if (statue.equals("进港"))
            iState = 1;
        else if (statue.equals("出港"))
            iState = 0;
        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ");
        pageNum = (pageNum - 1) * 10;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", portService.GetShip_inOrOutPortInfoDetail(shipName, portName, iState, startTime, endTime, pageNum));
        jsonObject.put("total", portService.GetShip_inOrOutPortInfoDetailCount(shipName, portName, iState, startTime, endTime).size());
        return jsonObject.toJSONString();
    }

    @GetMapping("/GetAllShip_inOrOutPortInfoDetail")
    public String GetAllShip_inOrOutPortInfoDetail(@RequestParam("shipName") String shipName,
                                                   @RequestParam("portName") String portName,
                                                   @RequestParam("statue") String statue,
                                                   @RequestParam("startTime") String startTime,
                                                   @RequestParam("endTime") String endTime) {
        int iState = 3;
        if (statue.equals("进港"))
            iState = 1;
        else if (statue.equals("出港"))
            iState = 0;
        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", portService.GetAllShip_inOrOutPortInfoDetail(shipName, portName, iState, startTime, endTime));
        return jsonObject.toJSONString();
    }

    @GetMapping("/test")
    public int pp() {
        int i = 0;
        try {
            i = shipService.selectshipidfromterm("10");
        } catch (Exception e) {
            i = -1;
        }
        return i;
    }

    @GetMapping("/token")
    public String token() throws IOException {
        return "";
    }

    ////////////////态势船位/////////////////
    @GetMapping("/GetBDShipPosition_ronghe")
    public List<ShipDynamicInfo> GetBDShipPosition_ronghe(@RequestParam("sec") int sec) {
        return shipService.GetBDShipPosition_ronghe((int) Utils.GetNowTimelong() - sec);
    }

    ////////////////重点跟踪/////////////////
    @GetMapping("/GetImportanceShip")
    public List<ShipDynamicInfo> GetImportanceShip(@RequestParam("userId") int userId) {
        return shipService.GetImportanceShip();
    }

    @GetMapping("/SetImportanceShip")
    public String SetImportanceShip(@RequestParam("id") int id,
                                    @RequestParam("userId") int userId) {
        shipService.SetImportanceShip(id);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    @GetMapping("/DeleteImportanceShip")
    public String DeleteImportanceShip(@RequestParam("id") int id,
                                       @RequestParam("userId") int userId) {
        shipService.DeleteImportanceShip(id);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    //////////伏休白名单////////////////////
    @GetMapping("/GetSpecialShip")
    public List<ShipDynamicInfo> GetSpecialShip() {
        return shipService.GetSpecialShip();
    }

    @GetMapping("/SetFuxiuWhiteShip")
    public String SetFuxiuWhiteShip(@RequestParam("id") int id) {
        shipService.SetFuxiuWhiteShip(id);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    @GetMapping("/DeleteFuxiuWhiteShip")
    public String DeleteFuxiuWhiteShip(@RequestParam("id") int id) {
        shipService.DeleteFuxiuWhiteShip(id);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    ////////////////区域///////////////////

    @GetMapping("/GetAreaInfo")
    public List<AreaInfo> GetAreaInfo() {
        return areaService.GetAreaInfo();
    }

    @GetMapping("/GetAreaInfoByUserId")
    public List<AreaInfo> GetAreaInfoByUserId(@RequestParam("userId") String userId) {
        List<AreaInfo> areaInfos = areaService.GetAreaInfoByUserId(userId);
        return areaInfos;
    }

    @GetMapping("/GetAreaInfoByUserIdPageNum")
    public String GetAreaInfoByUserIdPageNum(@RequestParam("userId") int userId, @RequestParam("pageNum") int pageNum) {
        pageNum = (pageNum - 1) * 10;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", areaService.GetAreaInfoByUserIdPageNum(userId, pageNum));
        jsonObject.put("total", areaService.GetAreaInfoByUserIdPageNumCount(userId));
        return jsonObject.toJSONString();
    }

    @GetMapping("/GetTodayWarningAllStatistics")
    public String GetTodayWarningAllStatistics(@RequestParam("userId") int userId,
                                               @RequestParam("updateTime") String updateTime,
                                               @RequestParam("model") int model) {
        //System.out.println(updateTime);
        updateTime = updateTime.replace("T", " ");
        int statistics = portService.GetTodayWarningAllStatistics(userId, updateTime, model);
        //System.out.println(statistics);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("statistics", statistics);

        if (statistics > 0) {
            List<WarningInfo> warningInfoList = portService.GetTodayWarningNewUpdateTime(userId, updateTime, model);
            //System.out.println(warningInfoList);
            //System.out.println(warningInfoList.size());
            jsonObject.put("updateTime", warningInfoList.get(0).getUpdateTime());
            jsonObject.put("warningType", warningInfoList.get(0).getWarningType());
        } else {
            jsonObject.put("updateTime", updateTime);
        }
        return jsonObject.toJSONString();
    }

    ///////////////预警///////////////////
    @GetMapping("/GetTodayWarningStatistics")
    public WarningStatistics GetTodayWarningStatistics(@RequestParam("userId") int userId, @RequestParam("model") int model) {
        //  渔港异常预警总数
        int countPortAll = portService.GetTodayWarningPortAllInfo(userId, model);
        //  渔港异常预警未处理数
        int countPortnoResolve = portService.GetTodayWarningPortUntreatedCountInfo(userId, model);
        //  渔船异常预警总数
        int countFishAll = portService.GetTodayWarningFishCountInfo(userId, model);
        //  渔船异常预警未处理数
        int countFishnoResolve = portService.GetTodayWarningFishUntreatedCountInfo(userId, model);
        //  渔船异常预警总数
        int countFishAll2 = portService.GetTodayWarningFishCountInfo2(userId, model);
        //  渔船异常预警未处理数
        int countFishnoResolve2 = portService.GetTodayWarningFishUntreatedCountInfo2(userId, model);
        //  船员异常预警总数
        int countCrewAll = portService.GetTodayWarningCrewCountInfo(userId, model);
        //  船员异常预警未处理数
        int countCrewnoResolve = portService.GetTodayWarningCrewUntreatedCountInfo(userId, model);

        WarningStatistics warningStatistics = new WarningStatistics();
        warningStatistics.setPortCount(countPortAll);
        warningStatistics.setPortUntreatedCount(countPortnoResolve);
        warningStatistics.setFishCount(countFishAll);
        warningStatistics.setFishUntreatedCount(countFishnoResolve);
        warningStatistics.setFishCount2(countFishAll2);
        warningStatistics.setFishUntreatedCount2(countFishnoResolve2);
        warningStatistics.setCrewCount(countCrewAll);
        warningStatistics.setCrewUntreatedCount(countCrewnoResolve);
        return warningStatistics;

    }

    @GetMapping("/GetTodayWarningInfo")
    public List<WarningInfo> GetTodayWarningInfo(@RequestParam("userId") int userId, @RequestParam("updateTime") String updateTime,
                                                 @RequestParam("model") int model) {
        List<AlarmRecord> alarmRecords = portService.GetTodayWarningInfo(userId, updateTime, model);
        List<WarningInfo> warningInfoList = new ArrayList<>();
        List<OutInPortRecord> outInPortRecords = new ArrayList<>();
        List<FisheryBoatInOutReport> recordsOf7 = new ArrayList<>();
        if (alarmRecords.size() > 0) {
            outInPortRecords = portService.GetOutInPortRecord(); // 近24小时船舶进出港记录
            recordsOf7 = portService.get7DaysRecord(); // 近7天进出港报告
        }
        for (AlarmRecord alarm : alarmRecords) {
            WarningInfo warningInfo = new WarningInfo();
            warningInfo.setTypeNumber(alarm.getTYPE());
            boolean skipFlag = false;
            switch (alarm.getTYPE()) {
                case 101://伏休 渔港报警
                    warningInfo.setID(alarm.getID());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setType("port");
                    warningInfo.setInfo("北斗紧急报警");
                    if (alarm.getSTATE() == 0)
                        warningInfo.setWarningType("未处理");
                    else
                        warningInfo.setWarningType("已处理");
                    warningInfo.setWarningEntityId(alarm.getSHIPNAME());
                    warningInfo.setTime(alarm.getLOADTIME());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setUpdateTime(alarm.getUPDATETIME());
                    break;
                case 102:
                    warningInfo.setID(alarm.getID());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setType("port");
                    warningInfo.setInfo("台风紧急预警");
                    if (alarm.getSTATE() == 0)
                        warningInfo.setWarningType("未处理");
                    else
                        warningInfo.setWarningType("已处理");
                    warningInfo.setWarningEntityId(alarm.getSHIPNAME());
                    warningInfo.setTime(alarm.getLOADTIME());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setUpdateTime(alarm.getUPDATETIME());
                    break;
                case 201:
                    warningInfo.setID(alarm.getID());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setType("crew");
                    warningInfo.setInfo("渔港容量预警");
                    if (alarm.getSTATE() == 0)
                        warningInfo.setWarningType("未处理");
                    else
                        warningInfo.setWarningType("已处理");
                    warningInfo.setWarningEntityId(alarm.getSHIPNAME());
                    warningInfo.setTime(alarm.getLOADTIME());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setUpdateTime(alarm.getUPDATETIME());
                    break;
                case 202:
                    warningInfo.setID(alarm.getID());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setType("fish");
                    warningInfo.setInfo("渔港抗风预警");
                    if (alarm.getSTATE() == 0)
                        warningInfo.setWarningType("未处理");
                    else
                        warningInfo.setWarningType("已处理");
                    warningInfo.setWarningEntityId(alarm.getSHIPNAME());
                    warningInfo.setTime(alarm.getLOADTIME());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setUpdateTime(alarm.getUPDATETIME());
                    break;
                case 203:
                    warningInfo.setID(alarm.getID());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setType("fish");
                    warningInfo.setInfo("明火作业预警");
                    if (alarm.getSTATE() == 0)
                        warningInfo.setWarningType("未处理");
                    else
                        warningInfo.setWarningType("已处理");
                    warningInfo.setWarningEntityId(alarm.getSHIPNAME());
                    warningInfo.setTime(alarm.getLOADTIME());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setUpdateTime(alarm.getUPDATETIME());
                    break;
                case 204:
                    warningInfo.setID(alarm.getID());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setType("fish");
                    warningInfo.setInfo("航标异常预警");
                    if (alarm.getSTATE() == 0)
                        warningInfo.setWarningType("未处理");
                    else
                        warningInfo.setWarningType("已处理");
                    warningInfo.setWarningEntityId(alarm.getSHIPNAME());
                    warningInfo.setTime(alarm.getLOADTIME());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setUpdateTime(alarm.getUPDATETIME());
                    break;
                case 301:
                    warningInfo.setID(alarm.getID());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setType("fish");
                    warningInfo.setInfo("渔船伏休预警");
                    if (alarm.getSTATE() == 0)
                        warningInfo.setWarningType("未处理");
                    else
                        warningInfo.setWarningType("已处理");
                    warningInfo.setWarningEntityId(alarm.getSHIPNAME());
                    warningInfo.setTime(alarm.getLOADTIME());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setUpdateTime(alarm.getUPDATETIME());
                    break;
                case 302:
                    warningInfo.setID(alarm.getID());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setType("fish");
                    warningInfo.setInfo("渔船区域预警");
                    if (alarm.getSTATE() == 0)
                        warningInfo.setWarningType("未处理");
                    else
                        warningInfo.setWarningType("已处理");
                    warningInfo.setWarningEntityId(alarm.getSHIPNAME());
                    warningInfo.setTime(alarm.getLOADTIME());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setUpdateTime(alarm.getUPDATETIME());
                    break;
                case 303:
                    warningInfo.setID(alarm.getID());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setType("fish");
                    warningInfo.setInfo("渔船涉外预警");
                    if (alarm.getSTATE() == 0)
                        warningInfo.setWarningType("未处理");
                    else
                        warningInfo.setWarningType("已处理");
                    warningInfo.setWarningEntityId(alarm.getSHIPNAME());
                    warningInfo.setTime(alarm.getLOADTIME());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setUpdateTime(alarm.getUPDATETIME());
                    break;
                case 304:
                    warningInfo.setID(alarm.getID());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setType("fish");
                    warningInfo.setInfo("监测船预警");
                    if (alarm.getSTATE() == 0)
                        warningInfo.setWarningType("未处理");
                    else
                        warningInfo.setWarningType("已处理");
                    warningInfo.setWarningEntityId(alarm.getSHIPNAME());
                    warningInfo.setTime(alarm.getLOADTIME());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setUpdateTime(alarm.getUPDATETIME());
                    break;
                case 305:
                    warningInfo.setID(alarm.getID());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setType("fish");
                    warningInfo.setInfo("北斗拆卸预警");
                    if (alarm.getSTATE() == 0)
                        warningInfo.setWarningType("未处理");
                    else
                        warningInfo.setWarningType("已处理");
                    warningInfo.setWarningEntityId(alarm.getSHIPNAME());
                    warningInfo.setTime(alarm.getLOADTIME());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setUpdateTime(alarm.getUPDATETIME());
                    break;
                case 306:
                    warningInfo.setID(alarm.getID());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setType("fish");
                    warningInfo.setInfo("进出港未报告预警");
                    if (alarm.getSTATE() == 0)
                        warningInfo.setWarningType("未处理");
                    else
                        warningInfo.setWarningType("已处理");
                    warningInfo.setWarningEntityId(alarm.getSHIPNAME());
                    warningInfo.setTime(alarm.getLOADTIME());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setUpdateTime(alarm.getUPDATETIME());
                    break;
                case 307:
                    warningInfo.setID(alarm.getID());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setType("fish");
                    warningInfo.setInfo("船舶证书异常预警");
                    if (alarm.getSTATE() == 0)
                        warningInfo.setWarningType("未处理");
                    else
                        warningInfo.setWarningType("已处理");
                    warningInfo.setWarningEntityId(alarm.getSHIPNAME());
                    warningInfo.setTime(alarm.getLOADTIME());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setUpdateTime(alarm.getUPDATETIME());
                    break;
                case 308:
                    warningInfo.setID(alarm.getID());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setType("fish");
                    warningInfo.setInfo("外省渔船预警");
                    if (alarm.getSTATE() == 0)
                        warningInfo.setWarningType("未处理");
                    else
                        warningInfo.setWarningType("已处理");
                    warningInfo.setWarningEntityId(alarm.getSHIPNAME());
                    warningInfo.setTime(alarm.getLOADTIME());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setUpdateTime(alarm.getUPDATETIME());
                    break;
                case 309:
                    warningInfo.setID(alarm.getID());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setType("fish");
                    warningInfo.setInfo("执法检查异常预警");
                    if (alarm.getSTATE() == 0)
                        warningInfo.setWarningType("未处理");
                    else
                        warningInfo.setWarningType("已处理");
                    warningInfo.setWarningEntityId(alarm.getSHIPNAME());
                    warningInfo.setTime(alarm.getLOADTIME());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setUpdateTime(alarm.getUPDATETIME());
                    break;
                case 401:
                    String[] infoArr = {"未进行进港报告", "未进行出港报告"};
                    int shipId = alarm.getSTATICSHIPID();
                    for (int i = 0; i < infoArr.length; i++) {
                        String info = infoArr[i];
                        if (alarm.getCONTENT().contains(info)) {
                            if (i == 0) { // 未进行进港报告
                                // 最新一条进港记录
                                List<OutInPortRecord> records = portService.GetOutInPortRecordByShipId(String.valueOf(shipId), 1);
                                FisheryBoatInOutReport inReport = peopleService.GetPortNodeByShipId(shipId, 0);
                                if (records.size() > 0 && inReport != null) {
                                    OutInPortRecord inRecord = records.get(0);
                                    if (DateUtil.between(
                                            DateUtil.parseDateTime(inRecord.getLoadTime()),
                                            DateUtil.parseDateTime(inReport.getTime()),
                                            DateUnit.SECOND) < 60 * 60 * 24) {
                                        // 修改成已处理
                                        alarm.setSTATE(1);
                                        portService.UpdateAlarmState(alarm.getID(), alarm.getUPDATETIME());
                                    }

                                }
                            } else if (i == 1) { // 未进行出港报告
                                // 最新一条出港记录
                                List<OutInPortRecord> records = portService.GetOutInPortRecordByShipId(String.valueOf(shipId), 0);
                                FisheryBoatInOutReport outReport = peopleService.GetPortNodeByShipId(shipId, 1);
                                if (records.size() > 0 && outReport != null) {
                                    OutInPortRecord outRecord = records.get(0);
                                    if (DateUtil.between(
                                            DateUtil.parseDateTime(outRecord.getLoadTime()),
                                            DateUtil.parseDateTime(outReport.getTime()),
                                            DateUnit.SECOND) < 60 * 60 * 24) {
                                        alarm.setSTATE(1);
                                        portService.UpdateAlarmState(alarm.getID(), alarm.getUPDATETIME());
                                    }
                                }
                            }
                        }
                    }
                    warningInfo.setID(alarm.getID());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setType("fish");
                    warningInfo.setInfo("船员信息异常预警");
                    if (alarm.getSTATE() == 0)
                        warningInfo.setWarningType("未处理");
                    else
                        warningInfo.setWarningType("已处理");
                    warningInfo.setWarningEntityId(alarm.getSHIPNAME());
                    warningInfo.setTime(alarm.getLOADTIME());
                    warningInfo.setSTATICSHIPID(alarm.getSTATICSHIPID());
                    warningInfo.setUpdateTime(alarm.getUPDATETIME());
                    break;
            }
            // if(!skipFlag)
            warningInfoList.add(warningInfo);
        }
        return warningInfoList;
    }

    @GetMapping("/GetWarningStatistics")
    public String GetWarningStatistics(@RequestParam("userId") int userId, @RequestParam("model") int model) {
        List<Integer> todayWarningStatistics = new ArrayList<>();
        todayWarningStatistics.add(portService.GetWarningStatistics(0, userId, 0, model));
        todayWarningStatistics.add(portService.GetWarningStatistics(0, userId, 1, model));
        todayWarningStatistics.add(portService.GetWarningStatistics(0, userId, 2, model));
        todayWarningStatistics.add(portService.GetWarningStatistics(0, userId, 3, model));
        todayWarningStatistics.add(portService.GetWarningStatistics(0, userId, 4, model));
        todayWarningStatistics.add(portService.GetWarningStatistics(0, userId, 5, model));
        todayWarningStatistics.add(portService.GetWarningStatistics(0, userId, 6, model));
        todayWarningStatistics.add(portService.GetWarningStatistics(0, userId, 7, model));
        todayWarningStatistics.add(portService.GetWarningStatistics(0, userId, 8, model));
        todayWarningStatistics.add(portService.GetWarningStatistics(0, userId, 9, model));
        todayWarningStatistics.add(portService.GetWarningStatistics(0, userId, 10, model));
        todayWarningStatistics.add(portService.GetWarningStatistics(0, userId, 102, model));
        todayWarningStatistics.add(portService.GetWarningStatistics(0, userId, 114, model));

        List<Integer> monthWarningStatistics = new ArrayList<>();
        monthWarningStatistics.add(portService.GetWarningStatistics(1, userId, 0, model));
        monthWarningStatistics.add(portService.GetWarningStatistics(1, userId, 1, model));
        monthWarningStatistics.add(portService.GetWarningStatistics(1, userId, 2, model));
        monthWarningStatistics.add(portService.GetWarningStatistics(1, userId, 3, model));
        monthWarningStatistics.add(portService.GetWarningStatistics(1, userId, 4, model));
        monthWarningStatistics.add(portService.GetWarningStatistics(1, userId, 5, model));
        monthWarningStatistics.add(portService.GetWarningStatistics(1, userId, 6, model));
        monthWarningStatistics.add(portService.GetWarningStatistics(1, userId, 7, model));
        monthWarningStatistics.add(portService.GetWarningStatistics(1, userId, 8, model));
        monthWarningStatistics.add(portService.GetWarningStatistics(1, userId, 9, model));
        monthWarningStatistics.add(portService.GetWarningStatistics(1, userId, 10, model));
        monthWarningStatistics.add(portService.GetWarningStatistics(1, userId, 102, model));
        monthWarningStatistics.add(portService.GetWarningStatistics(1, userId, 114, model));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("day", todayWarningStatistics);
        jsonObject.put("month", monthWarningStatistics);
        return jsonObject.toJSONString();
    }

    //执法船
    @GetMapping("/GetYuzhengShipInfo")
    public List<Ship_Yuzheng> GetYuzhengShipInfo() {
        return shipService.GetYuzhengShipInfo();
    }

    //执法人员
    @GetMapping("/GetZhifaPeopleInfo")
    public List<LawPeople> GetZhifaPeopleInfo() {
        return peopleService.GetLawPeopleInfo();
    }

    //
    @GetMapping("/GetAlarmShipIdByAlarmId")
    public int GetAlarmShipIdByAlarmId() {


        return 1;
    }

    //业务管理查询

    //船员
    @SneakyThrows
    @GetMapping("/GetAllPeople")
    public String GetAllPeople(@RequestParam("name") String name,
                               @RequestParam("shipName") String shipName,
                               @RequestParam("idcard") String idcard,
                               @RequestParam("fzrq") String fzrq,
                               @RequestParam("zslbmc") String zslbmc,
                               @RequestParam("zszlmc") String zszlmc,
                               @RequestParam("pageNum") int pageNum,
                               PeopleQueryDto queryDto) {
        List<People> data = peopleService.GetAllPeople(queryDto);

        // for(int i = 0; i < data.size(); i++)
        // {
        //     boolean verify = SignTest.verify(data.get(i).getIDCARD().getBytes(), data.get(i).getIDCARDSIGNCODE());
        //     if (verify == false){
        //         System.out.println("验证失败！身份证数据已被修改："+data.get(i).getIDCARD());
        //     }
        //
        // }

        pageNum = (pageNum - 1) * 10;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", peopleService.GetAllPeople(queryDto));
        jsonObject.put("total", peopleService.GetAllPeopleCount(queryDto));
        return jsonObject.toJSONString();
    }
    //导出所有船员信息并生成excel表
    @RequestMapping("/GetAllPeople_Export")
    public String GetAllPeople_Export(@RequestParam("name") String name,
                                      @RequestParam("shipName") String shipName,
                                      @RequestParam("idcard") String idcard,
                                      @RequestParam("fzrq") String fzrq,
                                      @RequestParam("zslbmc") String zslbmc,
                                      @RequestParam("zszlmc") String zszlmc,
                                      PeopleQueryDto queryDto) {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", peopleService.GetAllPeople_Export(queryDto));
        return jsonObject.toJSONString();

    }
    @GetMapping("/GetApplinameByShipId")
    public String GetApplinameByShipId(@RequestParam("shipId") int shipId) {

        JSONObject jsonObject = new JSONObject();
        String shipName = shipService.GetshipNameById(shipId);
        jsonObject.put("appliname", peopleService.GetApplinameByShipName(shipName));
        jsonObject.put("policyno", peopleService.GetPersonCount(shipName));
        return jsonObject.toJSONString();
    }

    @GetMapping("/GetPeopleByShipId")
    public String GetPeopleByShipId(@RequestParam("shipId") int shipId) {

        JSONObject jsonObject = new JSONObject();
        String shipName = shipService.GetshipNameById(shipId);
        jsonObject.put("list", peopleService.GetCrewBxByShipName(shipName));

        return jsonObject.toJSONString();
    }
    @GetMapping("/GetPeopleByShipIds")
    public String GetPeopleByShipIds(@RequestParam("shipIds") List<Integer> shipIds) {

        JSONObject jsonObject = new JSONObject();
        List<String> shipNames = shipService.GetshipNamesByIds(shipIds);
        jsonObject.put("list", peopleService.GetCrewBxByShipNames(shipNames));
        return jsonObject.toJSONString();
    }

    @RequestMapping(value = "/GetPeopleByIdcards", method = RequestMethod.POST)
    public String GetPeopleByIdcards(@RequestBody Map<String, List<String>> postData) throws Exception {
        //System.out.println(postData);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", peopleService.GetPeopleByIdcards(postData.get("idcards")));
        return jsonObject.toJSONString();
    }


    @RequestMapping("GetCrewPeopleByShipId")
    public String GetCrewPeopleByShipId(@RequestParam("shipId") int shipId) {
        JSONObject jsonObject = new JSONObject();
        //jsonObject.put("list", peopleService.GetCrewPeopleByShipId(shipId));

        String shipName = shipService.GetshipNameById(shipId);
        //System.out.println(shipName);
        List<String> idCardList = peopleService.GetPeopleByShipName(shipName, 1);
        // System.out.println(idCardList);
/*        List<String> idCardList_new = new ArrayList<>();
        for(String idCard:idCardList){
            //System.out.println("解析前:" + idCard);
            String newIdCard = AesUtil.aesDecrypt(idCard);
            //System.out.println("解析后:" + newIdCard);

            if(newIdCard == null){
                idCardList_new.add(idCard);
            }else {
                idCardList_new.add(newIdCard);
            }
        }
        if(idCardList_new.size() < 1) return null;
        System.out.println(idCardList_new);*/
        jsonObject.put("list", peopleService.GetPeopleByIdCard(idCardList, 1));

        return jsonObject.toJSONString();
    }

    @RequestMapping("GetPortNodeByShipId")
    public String GetPortNodeByShipId(@RequestParam("shipId") int shipId, @RequestParam("state") int state) {
        JSONObject jsonObject = new JSONObject();
        FisheryBoatInOutReport reports;
        if (state == -1)
            reports = peopleService.GetPortNodeByShipId(shipId, null);
        else
            reports = peopleService.GetPortNodeByShipId(shipId, state);
        if (reports != null && StringUtils.hasText(reports.getCrewStr())) {
            String[] strList = reports.getCrewStr().split("@");
            String newCrewStr = "";
            for (int i = 0; i < strList.length; i++) {
                String idCard = strList[i].split("#")[1].toUpperCase();
                String zshm = peopleService.GetZshmByIdCard(idCard);
                String endTime = peopleService.GetEndTimeByIdCard(idCard);

                String newCrewStr_tmp = strList[i] + "#" + (zshm == null ? "-" : zshm) + "#" + (endTime == null ? "-" : endTime.substring(0, 10));
                newCrewStr += newCrewStr_tmp + "@";
            }
            reports.setCrewStr(newCrewStr);
        }

        List<FisheryBoatInOutReport> ret = new ArrayList<>();
        ret.add(reports);
        jsonObject.put("list", ret);

        return jsonObject.toJSONString();
    }
    @RequestMapping("/GetPortNodeByShipIds")
    public String GetPortNodeByShipId(@RequestParam("shipIds") List<Integer> shipIds){
        JSONObject jsonObject = new JSONObject();
        List<FisheryBoatInOutReport> list = peopleService.GetPortNodeByShipIds(shipIds);
        jsonObject.put("list",list);
        return jsonObject.toJSONString();
    }
    //渔船数据统计
    @GetMapping("/GetShipStatistics")
    public String GetShipStatistics() {
        ShipCount shipCount = shipService.GetShipCount();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("allShipCount", shipCount.getAllShipCount());
        jsonObject.put("fisherShipCount", shipCount.getFisherShipCount());
        jsonObject.put("yuzhengShipCount", shipCount.getYuzhengShipCount());
        jsonObject.put("jiuyuanShipCount", shipCount.getJiuyuanShipCount());
        jsonObject.put("otherShipCount", shipCount.getOtherShipCount());
        return jsonObject.toJSONString();
    }

    @GetMapping("/GetShipOnlineStatistics")
    public String GetShipOnlineStatistics(@RequestParam("shipName") String shipName,
                                          @RequestParam("startTime") String startTime,
                                          @RequestParam("endTime") String endTime,
                                          @RequestParam("pageNum") int pageNum) {

        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ");
        pageNum = (pageNum - 1) * 10;

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", shipService.GetShipOnlineStatistics(shipName, startTime, endTime, pageNum));
        jsonObject.put("total", shipService.GetShipOnlineStatisticsCount(shipName, startTime, endTime));
        return jsonObject.toJSONString();
    }

    @GetMapping("/GetAllShipOnlineStatistics")
    public String GetAllShipOnlineStatistics(@RequestParam("shipName") String shipName,
                                             @RequestParam("startTime") String startTime,
                                             @RequestParam("endTime") String endTime) {

        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ");

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", shipService.GetAllShipOnlineStatistics(shipName, startTime, endTime));
        return jsonObject.toJSONString();
    }

    //渔港数据统计
    //港内外数量统计
    @GetMapping("/GetPortStatisticsInOutShipCount")
    public String GetPortStatisticsInOutShipCount() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("allShipCount", shipService.GetInPortFish() + shipService.GetOutPortFish());
        jsonObject.put("inPortShipCount", shipService.GetInPortFish());
        jsonObject.put("outPortShipCount", shipService.GetOutPortFish());
        return jsonObject.toJSONString();
    }

    //预警统计
    //预警数
    @GetMapping("/GetAlarmCountByTime")
    public TongjiModel GetAlarmCountByTime(@RequestParam("type") int type, @RequestParam("byear") int byear, @RequestParam("model") int model) {
        TongjiModel tongjiModel = new TongjiModel();

        // 1 预警总数 2 伏休 3 紧急救援 4 渔船违规作业 5 通讯记录 6 执法记录
        // 0 月  1 年
        if (byear == 0) {//月
            switch (type) {
                case 1:
                    tongjiModel = portService.GetAllAlarmCount_month(model);
                    break;
                case 2:
                    tongjiModel = portService.GetFXAlarmCount_month(model);
                    break;
                case 3:
                    tongjiModel = portService.GetJJAlarmCount_month(model);
                    break;
                case 4:
                    tongjiModel = portService.GetZYAlarmCount_month(model);
                    break;
                case 5:
                    tongjiModel = portService.GetTXAlarmCount_month();
                    break;
                case 6:
                    tongjiModel = portService.GetZFAlarmCount_month();
                    break;
            }
        }
        if (byear == 1) {//年
            switch (type) {
                case 1:
                    tongjiModel = portService.GetAllAlarmCount_year(model);
                    break;
                case 2:
                    tongjiModel = portService.GetFXAlarmCount_year(model);
                    break;
                case 3:
                    tongjiModel = portService.GetJJAlarmCount_year(model);
                    break;
                case 4:
                    tongjiModel = portService.GetZYAlarmCount_year(model);
                    break;
                case 5:
                    tongjiModel = portService.GetTXAlarmCount_year();
                    break;
                case 6:
                    tongjiModel = portService.GetZFAlarmCount_year();
                    break;
            }
        }
        return tongjiModel;
    }

    //运行监测
    @GetMapping("/GetServerState")
    public TongjiModel GetServerState(@RequestParam("type") int type) {
        //1 cpu 2 内存 3 硬盘 4 网络
        TongjiModel tongjiModel = new TongjiModel();
        switch (type) {
            case 1:
                tongjiModel = userService.GetCPUState();
                break;
            case 2:
                tongjiModel = userService.GetNeiCunState();
                break;
            case 3:
                tongjiModel = userService.GetYingPanState();
                break;
            case 4:
                tongjiModel = userService.GetNetState();
                break;
        }
        return tongjiModel;
    }


    // 区域信息
    @GetMapping("/GetAllAreaInfo")
    public String GetAllAreaInfo(@RequestParam("pageNum") int pageNum) {
        pageNum = (pageNum - 1) * 10;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", areaService.GetAllAreaInfo(pageNum));
        jsonObject.put("total", areaService.GetAllAreaInfoCount());
        return jsonObject.toJSONString();
    }

    // 执法记录管理
    @GetMapping("/GetAllLawRecordInfo")
    public String GetAllLawRecordInfo(@RequestParam("name") String name,
                                      @RequestParam("startTime") String startTime,
                                      @RequestParam("endTime") String endTime,
                                      @RequestParam("pageNum") int pageNum) {
        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ");
        pageNum = (pageNum - 1) * 10;

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", peopleService.GetAllLawRecordInfo(name, startTime, endTime, pageNum));
        jsonObject.put("total", peopleService.GetAllLawRecordInfoCount(name, startTime, endTime));
        return jsonObject.toJSONString();
    }

    // 执法记录管理
    @GetMapping("/GetLawRecordInfo")
    public String GetLawRecordInfo(@RequestParam("name") String name,
                                   @RequestParam("startTime") String startTime,
                                   @RequestParam("endTime") String endTime) {
        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ");

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", peopleService.GetLawRecordInfo(name, startTime, endTime));
        return jsonObject.toJSONString();
    }

    // 现场检查
    @GetMapping("/getCheckRecordByShipName")
    public String getCheckRecord(String shipName) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", shipService.getCheckRecordByShipName(shipName));
        return jsonObject.toJSONString();
    }

    /*
     * 删除执法记录
     */
    @RequestMapping("/DeletedLawRecordInfo")
    public String DeletedLawRecordInfo(@RequestBody List<Integer> lawRecordIds) {
        for (int i = 0; i < lawRecordIds.size(); i++) {
            peopleService.DeleteLawRecordInfo(lawRecordIds.get(i));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    // 报警记录
    @GetMapping("/GetAllAlarmRecordInfo")
    public String GetAllAlarmRecordInfo(@RequestParam("shipName") String shipName,
                                        @RequestParam("startTime") String startTime,
                                        @RequestParam("endTime") String endTime,
                                        @RequestParam("type") int type,
                                        @RequestParam("pageNum") int pageNum,
                                        @RequestParam("model") int model) {
        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ");
        pageNum = (pageNum - 1) * 10;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", portService.GetAllAlarmRecordInfo(shipName, startTime, endTime, type, pageNum, model));
        jsonObject.put("total", portService.GetAllAlarmRecordInfoCount(shipName, startTime, endTime, type, model));
        return jsonObject.toJSONString();
    }

    //报警记录
    @GetMapping("/GetAlarmRecordInfo")
    public String GetAlarmRecordInfo(@RequestParam("shipName") String shipName,
                                     @RequestParam("startTime") String startTime,
                                     @RequestParam("endTime") String endTime,
                                     @RequestParam("type") int type,
                                     @RequestParam("model") int model) {
        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", portService.GetAlarmRecordInfo(shipName, startTime, endTime, type, model));
        return jsonObject.toJSONString();
    }

    @RequestMapping("/DeleteAlarmRecordInfo")
    public String DeleteAlarmRecordInfo(@RequestBody List<Integer> alarmRecordIds) {
        for (int i = 0; i < alarmRecordIds.size(); i++) {
            portService.DeleteAlarmRecordInfo(alarmRecordIds.get(i));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    //全部渔船
    @RequestMapping("/GetAllFishShip")
    public String GetAllFishShip(@RequestParam("shipName") String shipName,
                                 @RequestParam("bdId") String bdId,
                                 @RequestParam("mmsi") String mmsi,
                                 @RequestParam("shipType") int shipType,
                                 @RequestParam(value = "managerid", required = false, defaultValue = "0") Integer managerid,
                                 @RequestParam(value = "pageNum") Integer pageNum,
                                 @RequestParam(value = "shipLength", required = false, defaultValue = "0") Integer shipLength,
                                 @RequestParam(value = "shipSmallOrBig", required = false, defaultValue = "0") Integer shipSmallOrBig,
                                 @RequestParam(value = "shipPerson", required = false, defaultValue = "0") Integer shipPerson,
                                 @RequestParam(value = "personSmallOrBig", required = false, defaultValue = "0") Integer personSmallOrBig,
                                 ShipQueryDto queryDto) {
        pageNum = (pageNum - 1) * 10;
        List<Long> managerIdList = Utils.GetMangerIdByPos(managerid);
        queryDto.setManagerIdList(managerIdList);
        JSONObject jsonObject = new JSONObject();
        PageInfo<ShipStaticInfo_all> pageInfo = shipStaticInfoService.GetAllFishShip(queryDto);
        jsonObject.put("list", pageInfo.getList());
        jsonObject.put("total", pageInfo.getTotal());
        // jsonObject.put("page",shipStaticInfoService.getAllFishShip(queryDto));
        return jsonObject.toJSONString();

    }

    //全部渔船 导出
    @RequestMapping("/GetAllFishShip_Export")
    public String GetAllFishShip_Export(@RequestParam("shipName") String shipName,
                                        @RequestParam("bdId") String bdId,
                                        @RequestParam("mmsi") String mmsi,
                                        @RequestParam("managerid") int managerid,
                                        @RequestParam("shipType") int shipType,
                                        @RequestParam("shipLength") int shipLength,
                                        @RequestParam("shipSmallOrBig") int shipSmallOrBig,
                                        @RequestParam("shipPerson") int shipPerson,
                                        @RequestParam("personSmallOrBig") int personSmallOrBig) {
        List<Long> managerIdList = Utils.GetMangerIdByPos(managerid);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", shipStaticInfoService.GetAllFishShip_Export(shipName, bdId, mmsi, shipType, managerIdList, shipLength, shipSmallOrBig, shipPerson, personSmallOrBig));
        return jsonObject.toJSONString();

    }


    //渔船在线数
    @RequestMapping("/GetOnlineShipCount")
    public String GetOnlineShipCount() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("onlineCount",shipStaticInfoService.GetOnlineShipCount());
        jsonObject.put("totalCount",shipStaticInfoService.GetTotalShipCount());
        return jsonObject.toJSONString();
    }

    //港区渔船
    @RequestMapping("/GetAllInPortShip")
    public String GetAllInPortShip(@RequestParam("portName") String portName, @RequestParam("pageNum") int pageNum) {
        pageNum = (pageNum - 1) * 10;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", shipStaticInfoService.GetAllInPortShip(portName, pageNum));
        jsonObject.put("total", shipStaticInfoService.GetAllInPortShipCount(portName).size());
        return jsonObject.toJSONString();
    }
    //每个港区当前停靠的渔船
    @RequestMapping("/GetCurrentInPortShips_Export")
    public String GetCurrentInPortShips() {
        JSONArray jsonArray = new JSONArray();
        List<Port> ports = shipStaticInfoService.GetCurrentAllPorts();
        for (Port port : ports) {
            //确保生成的Json数据的顺序
            LinkedHashMap<String,Object> map = new LinkedHashMap<>();
            List<String> shipsName = new ArrayList<>();
            List<Ship> ships = shipStaticInfoService.GetCurrentShipsByPortName(port.getPortName());
            for (Ship ship : ships) {
                shipsName.add(ship.getShipName());
            }
            map.put("port",port.getPortName());
            map.put("ships",shipsName);
            JSONObject jsonObject = new JSONObject(map);
            jsonArray.add(jsonObject);
        }
        return jsonArray.toJSONString();
    }
    //获取每个在港的外省停靠的渔船
    @RequestMapping("GetOutsideInPortShip_Export")
    public String GetOutsideInPortShip(){
        JSONArray jsonArray = new JSONArray();
        List<Port> ports = shipStaticInfoService.GetOutsideInPorts();
        for (Port port : ports) {
            LinkedHashMap<String,Object> map = new LinkedHashMap<>();
            List<String> ShipsName = new ArrayList<>();
            List<Ship> ships = shipStaticInfoService.GetOutsideShipsByPortName(port.getPortName());
            for (Ship ship : ships) {
                ShipsName.add(ship.getShipName());
            }
            map.put("port",port.getPortName());
            map.put("ships",ShipsName);
            JSONObject jsonObject = new JSONObject(map);
            jsonArray.add(jsonObject);
        }
        return jsonArray.toJSONString();
    }

    //港区渔船
    @RequestMapping("/GetAllInPortShip_Export")
    public List<ShipStaticInfo_all> GetAllInPortShip_Export(@RequestParam("portName") String portName) {
        return shipStaticInfoService.GetAllInPortShip_Export(portName);

    }

    //异常关机
    @RequestMapping("/GetAllShutDownInfo")
    public String GetAllShutDownInfo(@RequestParam("shipName") String shipName, @RequestParam("pageNum") int pageNum, @RequestParam("model") int model) {
        pageNum = (pageNum - 1) * 10;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", shipStaticInfoService.GetAllShutDownInfo(shipName, pageNum, model));
        jsonObject.put("total", shipStaticInfoService.GetAllShutDownInfoCount(shipName, model));
        return jsonObject.toJSONString();

    }

    //异常关机
    @RequestMapping("/GetAllShutDownInfo_Export")
    public List<AlarmRecord> GetAllShutDownInfo_Export(@RequestParam("shipName") String shipName, @RequestParam("model") int model) {
        return shipStaticInfoService.GetAllShutDownInfo_Export(shipName, model);

    }

    //航程
    @RequestMapping("/GetAllVoyageInfo")
    public String GetAllVoyageInfo(@RequestParam("shipName") String shipName, @RequestParam("pageNum") int pageNum) {
        pageNum = (pageNum - 1) * 10;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", shipStaticInfoService.GetAllVoyageInfo(shipName, pageNum));
        jsonObject.put("total", shipStaticInfoService.GetAllVoyageInfoCount(shipName));
        return jsonObject.toJSONString();

    }

    //航程
    @RequestMapping("/GetAllVoyageInfo_Export")
    public List<Ship_Voyage> GetAllVoyageInfo_Export(@RequestParam("shipName") String shipName) {
        return shipStaticInfoService.GetAllVoyageInfo_Export(shipName);

    }

    //作业天数
    @RequestMapping("/GetAllShipWorkInfo")
    public String GetAllShipWorkInfo(@RequestParam("shipName") String shipName,
                                     @RequestParam("startTime") String startTime,
                                     @RequestParam("endTime") String endTime,
                                     @RequestParam("bdOrAis") int bdOrAis,
                                     @RequestParam("pageNum") int pageNum) {
        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ") + " 23:59:59";
        pageNum = (pageNum - 1) * 10;
        JSONObject jsonObject = new JSONObject();

        jsonObject.put("list", shipStaticInfoService.GetAllShipWorkInfo(shipName, startTime, endTime, bdOrAis, pageNum));
        jsonObject.put("total", shipStaticInfoService.GetAllShipWorkInfoCount(shipName, startTime, endTime, bdOrAis).size());
        return jsonObject.toJSONString();

    }

    //作业天数
    @RequestMapping("/GetAllShipWorkInfo_Export")
    public List<Ship_WorkInfo> GetAllShipWorkInfo_Export(@RequestParam("shipName") String shipName,
                                                         @RequestParam("startTime") String startTime,
                                                         @RequestParam("endTime") String endTime,
                                                         @RequestParam("bdOrAis") int bdOrAis) {
        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ") + " 23:59:59";
        return shipStaticInfoService.GetAllShipWorkInfo_Export(shipName, startTime, endTime, bdOrAis);

    }


    //重点关注
    @RequestMapping("/GetAllFocusShip")
    public String GetAllFocusShip(@RequestParam("shipName") String shipName,
                                  @RequestParam("bdId") String bdId,
                                  @RequestParam("mmsi") String mmsi,
                                  @RequestParam("shipType") int shipType,
                                  @RequestParam("specialType") int specialType,
                                  @RequestParam("pageNum") int pageNum) {
        pageNum = (pageNum - 1) * 10;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", shipStaticInfoService.GetAllFishShip_special(shipName, bdId, mmsi, shipType, specialType, pageNum));
        jsonObject.put("total", shipStaticInfoService.GetAllFishShipCount_special(shipName, bdId, mmsi, shipType, specialType));
        return jsonObject.toJSONString();
    }

    //重点关注
    @RequestMapping("/GetFocusShip")
    public String GetFocusShip(@RequestParam("shipName") String shipName,
                               @RequestParam("bdId") String bdId,
                               @RequestParam("mmsi") String mmsi,
                               @RequestParam("shipType") int shipType,
                               @RequestParam("specialType") int specialType) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", shipStaticInfoService.GetFishShip_special(shipName, bdId, mmsi, shipType, specialType));
        return jsonObject.toJSONString();
    }

    // 添加特殊渔船
    @GetMapping("/SetSpecialShip")
    public String SetSpecialShip(@RequestParam("id") int id,
                                 @RequestParam("specialType") int specialType) {
        shipStaticInfoService.SetSpecialShip(id, specialType);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    //删除特殊渔船
    @RequestMapping("/DeleteSpecialShip")
    public String DeleteSpecialShip(@RequestBody List<SpecialShipType> specialShipTypes) {
        for (int i = 0; i < specialShipTypes.size(); i++) {
            shipStaticInfoService.DeleteSpecialShip(specialShipTypes.get(i).getId(), specialShipTypes.get(i).getSpecialType());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    //统一门户登录
    @RequestMapping("/DLogin")
    public void LoginFromIPMS(HttpServletResponse resp) throws IOException {
        String url = "http://***********:38001/auth/oauth/authorize?response_type=code&grant_type=authorization_code&client_id=ygyc&redirect_uri=http://***********:7001/web/LoginFromIPMS";
        resp.sendRedirect(url);
    }

    //统一门户登录
    @RequestMapping("/DLogin_XC")
    public void LoginFromIPMS_XC(HttpServletResponse resp) throws IOException {
        //String url = "http://***********:48002/auth/oauth/authorize?response_type=code&grant_type=authorization_code&client_id=ygyc&redirect_uri=http://***********:7001/web/LoginFromIPMS_XC";
        String url = "http://*********:8080/auth/oauth/authorize?response_type=code&grant_type=authorization_code&client_id=ygyc&redirect_uri=http://***********:7001/web/LoginFromIPMS_XC";
        resp.sendRedirect(url);
    }

    //统一门户登录
    @RequestMapping("/LoginFromIPMS")
    public void LoginFromIPMS(String code, HttpServletResponse resp) throws Exception {
        //System.out.println(code);
        String postFormDataUrl = "http://***********:38001/auth/oauth/token";
        Map<String, Object> mapFormData = new HashMap<>();
        mapFormData.put("grant_type", "authorization_code");
        mapFormData.put("code", code);
        mapFormData.put("client_id", "ygyc");
        mapFormData.put("client_secret", "af1f990d10e8499cbc4461dfce418c03");
        mapFormData.put("redirect_uri", "http://***********:7001/web/LoginFromIPMS");
        String formDataResponse = HttpTool.doPostFormData(postFormDataUrl, mapFormData);
        JSONObject jsonObject = JSONObject.parseObject(formDataResponse);
        JSONObject jsonObject2 = JSONObject.parseObject(jsonObject.get("data").toString());
        String access_token = jsonObject2.get("access_token").toString();
        String userResponse = HttpTool.doGet("http://***********:38001/admin/me/details", "Bearer " + access_token);
        JSONObject jsonObject3 = JSONObject.parseObject(userResponse);
        JSONObject jsonObject4 = JSONObject.parseObject(jsonObject3.get("data").toString());
        JSONObject jsonObject5 = JSONObject.parseObject(jsonObject4.get("dept").toString());
        String username = jsonObject4.get("username").toString();
        String nickname = jsonObject4.get("nickname").toString();
        String simpleName = jsonObject5.get("simpleName").toString();

        if (userService.GetUserName(username) < 1) {
            //todo 添加新用户
            UserInfo user = new UserInfo();
            user.setUsername(username);
            user.setPassword("Zfzd_123456");
            user.setName(nickname);
            user.setBm(simpleName);
            user.setLevel(1);
            userService.InsertUser(user);
        }

        UserInfo user = userService.GetUserInfoByUsername(username);

        String redirectUri = "http://***********:7001/index.html#/ComprehensiveSituation?userId=" + user.getId();
        resp.sendRedirect(redirectUri);
    }

    //统一门户登录
    @RequestMapping("/LoginFromIPMS_XC")
    public void LoginFromIPMS_XC(String code, HttpServletResponse resp) throws Exception {
        //System.out.println(code);
        //String postFormDataUrl = "http://***********:48002/auth/oauth/token";
        String postFormDataUrl = "http://*********:8080/auth/oauth/token";
        Map<String, Object> mapFormData = new HashMap<>();
        mapFormData.put("grant_type", "authorization_code");
        mapFormData.put("code", code);
        mapFormData.put("client_id", "ygyc");
        mapFormData.put("client_secret", "af1f990d10e8499cbc4461dfce418c03");
        mapFormData.put("redirect_uri", "http://***********:7001/web/LoginFromIPMS_XC");
        String formDataResponse = HttpTool.doPostFormData(postFormDataUrl, mapFormData);
        System.out.println(formDataResponse);
        JSONObject jsonObject = JSONObject.parseObject(formDataResponse);
        JSONObject jsonObject2 = JSONObject.parseObject(jsonObject.get("data").toString());
        String access_token = jsonObject2.get("access_token").toString();
        //String userResponse = HttpTool.doGet("http://***********:48002/admin/me/details", "Bearer " + access_token);
        String userResponse = HttpTool.doGet("http://*********:8080/admin/me/details", "Bearer " + access_token);
        JSONObject jsonObject3 = JSONObject.parseObject(userResponse);
        JSONObject jsonObject4 = JSONObject.parseObject(jsonObject3.get("data").toString());
        JSONObject jsonObject5 = JSONObject.parseObject(jsonObject4.get("dept").toString());
        String username = jsonObject4.get("username").toString();
        String nickname = jsonObject4.get("nickname").toString();
        String simpleName = jsonObject5.get("simpleName").toString();
        System.out.println(userResponse);
        if (userService.GetUserName(username) < 1) {
            //todo 添加新用户
            UserInfo user = new UserInfo();
            user.setUsername(username);
            user.setPassword("Zfzd_123456");
            user.setName(nickname);
            user.setBm(simpleName);
            user.setLevel(1);
            userService.InsertUser(user);
        }

        UserInfo user = userService.GetUserInfoByUsername(username);

        String redirectUri = "http://***********:7001/index.html#/ComprehensiveSituation?userId=" + user.getId();
        resp.sendRedirect(redirectUri);
    }

    @RequestMapping("/GetUserInfoById")
    public UserInfo GetUserInfoById(@RequestParam("userId") int userId) {
        return userService.GetUserInfoById(userId);
    }

    //执法记录
    @RequestMapping("/InsertLawRecordInfo")
    public int InsertLawRecordInfo(@RequestBody LawRecordInfo lawRecordInfo) {
        lawRecordInfo.setLOADTIME(lawRecordInfo.getLOADTIME().replace("T", " "));
        //处理预警
        portService.UpdateAlarmState(lawRecordInfo.getALARMID(), lawRecordInfo.getLOADTIME());
        return peopleService.InsertLawRecordInfo(lawRecordInfo);
    }

    // 发送北斗信息
    @RequestMapping("/InsertBdMsg")
    public int InsertBdMsg(@RequestBody BdMsg bdMsg) {
        bdMsg.setLoadTime(Utils.GetNowTimeString());
        bdMsg.setSendType(10);
        if (bdMsg.getBdId() == null || bdMsg.getBdId().equals("") ||
                bdMsg.getMsg() == null || bdMsg.getMsg().equals("")) {
            return 0;
        }
        System.out.println("InsertBdMsg:" + bdMsg.getBdId() + bdMsg.getMsg());
        if (bdMsg.getBdId().equals("上海") ||
                bdMsg.getBdId().equals("浦东") ||
                bdMsg.getBdId().equals("浦东·浦东南汇") ||
                bdMsg.getBdId().equals("崇明") ||
                bdMsg.getBdId().equals("崇明·长兴海星") ||
                bdMsg.getBdId().equals("崇明·横沙远洋") ||
                bdMsg.getBdId().equals("崇明·横沙海鸿") ||
                bdMsg.getBdId().equals("崇明·奚家港") ||
                bdMsg.getBdId().equals("崇明·六滧") ||
                bdMsg.getBdId().equals("崇明·开港") ||
                bdMsg.getBdId().equals("崇明·堡镇") ||
                bdMsg.getBdId().equals("崇明·华渔") ||
                bdMsg.getBdId().equals("崇明·渔政执法") ||
                bdMsg.getBdId().equals("奉贤") ||
                bdMsg.getBdId().equals("宝山") ||
                bdMsg.getBdId().equals("金山") ||
                bdMsg.getBdId().equals("海洋科研") ||
                bdMsg.getBdId().equals("松江") ||
                bdMsg.getBdId().equals("嘉定") ||
                bdMsg.getBdId().equals("青浦")) {
            bdMsg.setSendType(300);
        }


        return peopleService.InsertBdMsg(bdMsg);
    }

    // 获取北斗信息
    @RequestMapping("/GetBdMsg")
    public String GetBdMsg(@RequestParam("shipName") String shipName,
                           @RequestParam("bdId") String bdId,
                           @RequestParam("startTime") String startTime,
                           @RequestParam("endTime") String endTime,
                           @RequestParam("pageNum") int pageNum) {
        pageNum = (pageNum - 1) * 10;
        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", peopleService.GetBdMsg(shipName, bdId, startTime, endTime, pageNum));
        jsonObject.put("total", peopleService.GetBdMsgCount(shipName, bdId, startTime, endTime));
        return jsonObject.toJSONString();
    }

    // 获取所有的北斗通信记录
    @RequestMapping("/GetAllBdMsg")
    public List<BdMsg> GetAllBdMsg(@RequestParam("shipName") String shipName,
                                   @RequestParam("bdId") String bdId,
                                   @RequestParam("startTime") String startTime,
                                   @RequestParam("endTime") String endTime) {
        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ");
        return peopleService.GetAllBdMsg(shipName, bdId, startTime, endTime);
    }

    // 获取所有的北斗通信记录 by userid
    @RequestMapping("/GetAllBdMsgByUserId")
    public List<BdMsg> GetAllBdMsgByUserId(@RequestParam("userId") int userId, @RequestParam("shipId") int shipId) {
        return peopleService.GetAllBdMsgByUserId(userId, shipId);
    }

    // 删除北斗通信记录
    @RequestMapping("/DeleteBdMsg")
    public String DeleteBdMsg(@RequestBody List<Integer> msgIds) {
        for (int i = 0; i < msgIds.size(); i++) {
            peopleService.DeleteBdMsg(msgIds.get(i));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", "success");
        return jsonObject.toJSONString();
    }

    @RequestMapping("/GetAlarmDetialById")
    public AlarmRecord GetAlarmDetialById(@RequestParam("id") int id, int model) {
        return portService.GetAlarmDetialById(id, model);
    }

    @RequestMapping("/GetLawRecordTodayWarning")
    public List<LawRecordInfo> GetLawRecordTodayWarning(@RequestParam("model") int model) {
        return peopleService.GetLawRecordTodayWarning(model);
    }

    @GetMapping("/GetAreaInfoById")
    public AreaInfo GetAreaInfoById(@RequestParam("id") int id) {
        AreaInfo areaInfo = areaService.GetAreaInfoById(id);
        return areaInfo;
    }

    // 添加自定义监管区域
    @RequestMapping("/SetAreaInfo")
    public String SetAreaInfo(@RequestBody AreaInfo areaInfo) {
        areaInfo.setSTARTTIME(areaInfo.getSTARTTIME().replace("T", " "));
        areaInfo.setENDTIME(areaInfo.getENDTIME().replace("T", " "));
        areaInfo.setLOADTIME(areaInfo.getLOADTIME().replace("T", " "));
        areaService.InsertAreaInfo(areaInfo);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("areaId", areaInfo.getID());
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    /*
     * 修改自定义区域
     * */
    @RequestMapping("/EditAreaInfo")
    public String EditAreaInfo(@RequestBody AreaInfo areaInfo) {
        areaInfo.setSTARTTIME(areaInfo.getSTARTTIME().replace("T", " "));
        areaInfo.setENDTIME(areaInfo.getENDTIME().replace("T", " "));
        areaInfo.setLOADTIME(areaInfo.getLOADTIME().replace("T", " "));
        areaService.UpdateAreaInfo(areaInfo);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    /*
     * 删除自定义区域
     * */
    @RequestMapping("/DeletedAreaInfo")
    public String DeletedAreaInfo(@RequestBody List<Integer> areaIds) {
        for (int i = 0; i < areaIds.size(); i++) {
            areaService.DeleteAreaInfo(areaIds.get(i));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    @RequestMapping("/GetLawRecordInfoById")
    public LawRecordInfo GetLawRecordInfoById(@RequestParam("id") int id, @Param("model") int model) {
        return peopleService.GetLawRecordInfoById(id, model);
    }

    //系统管理 用户管理
    @RequestMapping("/GetAllUser")
    public String GetAllUser(@RequestParam("userName") String userName,
                             @RequestParam("userType") int userType,
                             @RequestParam("pageNum") int pageNum) {
        pageNum = (pageNum - 1) * 10;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", userService.GetAllUser(userName, userType, pageNum));
        jsonObject.put("total", userService.GetAllUserCount(userName, userType));
        return jsonObject.toJSONString();
    }

    @RequestMapping("/InsertUser")
    public String InsertUser(@RequestBody UserInfo user) {
        userService.InsertUser(user);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    //对接中台接口
    @RequestMapping("/api/specialShip")
    public List<SpecialShip> GetSpecialShipToZhongtai() {
        return shipService.GetSpecialShipToZhongtai();
    }

    //对接中台接口
    @RequestMapping("/api/shipCount")
    public String shipCount() {
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("name", "在线渔船");
        jsonObject1.put("count", shipService.getOnlineShipCount());
        jsonObject1.put("list", portService.GetOnLineShipInfo());

        JSONObject jsonObject2 = new JSONObject();
        jsonObject2.put("name", "渔船总数");
        jsonObject2.put("count", shipService.getAllShipCount());
        jsonObject2.put("list", portService.GetAllShipInfo());

        JSONObject jsonObject3 = new JSONObject();
        jsonObject3.put("name", "港外作业渔船");
        jsonObject3.put("count", shipService.GetOutPortFish());
        jsonObject3.put("list", portService.GetOutPortShipInfo());

        JSONObject jsonObject4 = new JSONObject();
        jsonObject4.put("name", "停港渔船");
        jsonObject4.put("count", shipService.GetInPortFish2(0));
        jsonObject4.put("list", portService.GetInPortShipInfo(0));

        JSONObject jsonObject5 = new JSONObject();
        jsonObject5.put("name", "上海水域内外省渔船");
        jsonObject5.put("count", shipService.GetWSInShanghai());
        jsonObject5.put("list", portService.GetWSInShangHaiShipInfo());

        JSONObject jsonObject6 = new JSONObject();
        jsonObject6.put("name", "禁捕区内外省渔船");
        jsonObject6.put("count", shipService.GetWSInJinbu());
        jsonObject6.put("list", portService.GetWSInJinBuShipInfo());

        JSONObject jsonObject7 = new JSONObject();
        jsonObject7.put("name", "外省停港渔船");
        jsonObject7.put("count", shipService.GetWSInPort(0));
        jsonObject7.put("list", portService.GetWSInPortShipInfo(0));

        JSONArray jsonArray1 = new JSONArray();
        jsonArray1.add(jsonObject1);
        jsonArray1.add(jsonObject2);
        jsonArray1.add(jsonObject3);
        jsonArray1.add(jsonObject4);
        jsonArray1.add(jsonObject5);
        jsonArray1.add(jsonObject6);
        jsonArray1.add(jsonObject7);

        JSONArray jsonArray2 = new JSONArray();
        List<PortInfo> portList = portService.GetAllPortInfo();
        for (PortInfo port : portList) {
            int count = portService.GetInPortShipCount(port.getId());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", port.getPortName());
            jsonObject.put("count", count);
            jsonObject.put("list", portService.GetShipInfoByPort(port.getId()));
            jsonArray2.add(jsonObject);
        }

        JSONArray jsonArray3 = new JSONArray();
        JSONObject jsonObject11 = new JSONObject();
        jsonObject11.put("in", portService.GetInPortCount());
        jsonObject11.put("out", portService.GetOutPortCount());
        jsonArray3.add(jsonObject11);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ship", jsonArray1);
        jsonObject.put("port", jsonArray2);
        jsonObject.put("inOutCount", jsonArray3);
        return jsonObject.toJSONString();
    }

    // 台风
    @GetMapping("/GetTyphoonInfo")
    public String GetTyphoonInfo() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", typhoonService.GetTyphoonInfo());
        jsonObject.put("total", typhoonService.GetTyphoonCountInfo());
        return jsonObject.toJSONString();
    }

    // 设置船舶和区域绑定
    @GetMapping("/SetShipWithArea")
    public String SetShipWithArea(@RequestParam("shipId") int shipId, @RequestParam("areaId") int areaId) {
        shipService.SetShipWithArea(shipId, areaId);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    // 获取hls路径
    @GetMapping("/GetHlsImg")
    public String GetHlsImg(@RequestParam("id") int id, @RequestParam("portId") int portId) {
        return portService.GetHlsImg(id, portId);
    }

    @GetMapping("/GetCameraInfo")
    public List<CameraInfo> GetCameraInfo() {
        return portService.GetCameraInfo();
    }


    //添加用户操作记录
    @RequestMapping("/SetUserOperationInfo")
    public String SetUserOperationInfo(@RequestBody UserOperation userOperation, HttpServletRequest request) {
        userOperation.setLoadTime(userOperation.getLoadTime().replace("T", " "));
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknow".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
            if (ip.equals("127.0.0.1")) {
                //根据网卡取本机配置的IP
                InetAddress inet = null;
                try {
                    inet = InetAddress.getLocalHost();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                ip = inet.getHostAddress();
            }
        }
        // 多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ip != null && ip.length() > 15) {
            if (ip.indexOf(",") > 0) {
                ip = ip.substring(0, ip.indexOf(","));
            }
        }
        userOperation.setIpAdress(ip);
        userService.addUserOperationInfo(userOperation);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    //  用户操作记录
    @GetMapping("/GetUserOperation")
    public String GetUserOperation(@RequestParam("userName") String userName,
                                   @RequestParam("startTime") String startTime,
                                   @RequestParam("endTime") String endTime,
                                   @RequestParam("type") int type,
                                   @RequestParam("pageNum") int pageNum) {
        pageNum = (pageNum - 1) * 10;
        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", userService.GetUserOperation(userName, startTime, endTime, type, pageNum));
        jsonObject.put("total", userService.GetUserOperationCount(userName, startTime, endTime, type));
        return jsonObject.toJSONString();
    }

    @RequestMapping("/GetHlsUrl")//弃用
    public String GetHlsUrl(@RequestParam("portId") int portId, @RequestParam("adress") String adress) {
        List<String> CameraIndexs = portService.GetCameraIndexByPortId(portId);
        String cameraIndex1 = "";
        String cameraIndex2 = "";
        if (CameraIndexs.size() > 1) {
            cameraIndex1 = CameraIndexs.get(0);
            cameraIndex2 = CameraIndexs.get(1);
        } else if (CameraIndexs.size() == 1) {
            cameraIndex1 = CameraIndexs.get(0);
            cameraIndex2 = "f901398962c446eaafb6ee4a97781fd9";
        } else {
            cameraIndex1 = "b658e74e4244479dabf78b56f5868bd7";
            cameraIndex2 = "f901398962c446eaafb6ee4a97781fd9";
        }

        /*JSONObject user = new JSONObject();
        user.put("username", "nwycyg");
        user.put("password", "Nw013579");
        String dataToken = HttpTool.doPostJson("http://***********:9092/api/token",user.toString(), "");
        String token = JSONObject.parseObject(JSONObject.parseObject(dataToken).getString("data")).getString("token");
        */
        String token = userService.GetOpenCenterToken();
        JSONObject hls1 = new JSONObject();
        hls1.put("token", token);
        hls1.put("streamType", "0");
        hls1.put("protocol", "hls");
        hls1.put("transmode", "0");
        hls1.put("cameraIndexCode", "['" + cameraIndex1 + "']");
        hls1.put("status", adress);
        JSONObject hls2 = new JSONObject();
        hls2.put("token", token);
        hls2.put("streamType", "0");
        hls2.put("protocol", "hls");
        hls2.put("transmode", "0");
        hls2.put("cameraIndexCode", "['" + cameraIndex2 + "']");
        hls2.put("status", adress);
        String dataHls1 = HttpTool.doPostJson("http://***********:9092/api/resource/v1/cameras/previewURLs", hls1.toString(), "");
        dataHls1 = dataHls1.replace("[", "");
        dataHls1 = dataHls1.replace("]", "");
        String hlsUrl1 = JSONObject.parseObject(dataHls1).getString("url");
        String dataHls2 = HttpTool.doPostJson("http://***********:9092/api/resource/v1/cameras/previewURLs", hls2.toString(), "");
        dataHls2 = dataHls2.replace("[", "");
        dataHls2 = dataHls2.replace("]", "");
        String hlsUrl2 = JSONObject.parseObject(dataHls2).getString("url");
        String url = hlsUrl1 + "," + hlsUrl2;
        System.out.println(url);
        return url;
    }

    @RequestMapping("/GetHlsUrlByIndex")
    public String GetHlsUrlByIndex(@RequestParam("index") String index, @RequestParam("adress") String adress) {
        /*JSONObject user = new JSONObject();
        user.put("username", "nwycyg");
        user.put("password", "Nw013579");
        String dataToken = HttpTool.doPostJson("http://10.90.7.5:9092/api/token",user.toString(), "");
        String token = JSONObject.parseObject(JSONObject.parseObject(dataToken).getString("data")).getString("token");
        */
        String token = userService.GetOpenCenterToken_HIK();
        JSONObject hls1 = new JSONObject();
        hls1.put("token", token);
        hls1.put("streamType", "0");
        hls1.put("protocol", "hls");
        hls1.put("transmode", "0");
        hls1.put("cameraIndexCode", "['" + index + "']");
        String dataHls1 = HttpTool.doPostJson("http://10.90.7.5:9092/api/resource/v1/cameras/previewURLs", hls1.toString(), "");
        //System.out.println("GetHlsUrlByIndex: dataHls1 :" + dataHls1);
        dataHls1 = dataHls1.replace("[", "");
        dataHls1 = dataHls1.replace("]", "");
        String hlsUrl1 = JSONObject.parseObject(dataHls1).getString("url");
        System.out.println("GetHlsUrlByIndex: hlsUrl1 :" + hlsUrl1);
/*        JSONObject hls1 = new JSONObject();
        hls1.put("streamType", "0");
        hls1.put("protocol", "hls");
        hls1.put("transmode", "0");
        hls1.put("cameraIndexCode", index);
        String dataHls1 = HttpTool.doPostJson("https://10.90.6.192:443/artemis/api/video/v1/cameras/previewURLs",hls1.toString(), "hls");
        System.out.println("GetHlsUrlByIndex: dataHls1 :" + dataHls1);*/
        return hlsUrl1;
    }

    @GetMapping("/GetUserCount")
    public String GetUserCount() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("curUserCount", userService.GetUserCount(0));
        jsonObject.put("todayUserCount", userService.GetUserCount(1));
        jsonObject.put("threeDayUserCount", userService.GetUserCount(3));
        jsonObject.put("oneWeekUserCount", userService.GetUserCount(7));
        jsonObject.put("monthUserCount", userService.GetUserCount(30));
        jsonObject.put("threeMonthUserCount", userService.GetUserCount(90));
        return jsonObject.toJSONString();
    }

    @GetMapping("/GetHlsById")
    public String GetHlsById(@RequestParam("id") int id) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", portService.GetHlsById(id));
        return jsonObject.toJSONString();
    }

    //天气预报
    @RequestMapping("/GetWeather")
    public String GetWeather() throws Exception {
        /*JSONObject user = new JSONObject();
        user.put("username", "nwycyg");
        user.put("rememberMe", "true");
        user.put("password", "Nw013579");
        String dataToken = HttpTool.doPostJson("http://10.90.7.5:18080/api/authenticate",user.toString(), "");
        String token = JSONObject.parseObject(dataToken).getString("id_token");
        */
        String token = userService.GetOpenCenterToken();
        String dataJson = HttpTool.doGet("http://10.90.7.5:18080/api/service/share/P288439990256406528?conditions=null&pageNum=1&pageSize=1000", "Bearer " + token);
        return dataJson;
    }

    // 标注内容
    @GetMapping("/GetAllMarkInfo")
    public String GetAllMarkInfo(@RequestParam("userId") int userId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", areaService.GetAllMarkInfo(userId));
        jsonObject.put("total", areaService.GetAllMarkInfoCount(userId));
        return jsonObject.toJSONString();
    }

    @RequestMapping("/InsertMarkInfo")
    public String InsertMarkInfo(@RequestBody MarkInfo markInfo) {
        markInfo.setLoadTime(markInfo.getLoadTime().replace("T", " "));
        areaService.InsertMarkInfo(markInfo);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("markId", markInfo.getId());
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    @RequestMapping("/deleteMarkInfoById")
    public String deleteMarkInfoById(@RequestParam("markId") int markId, @RequestParam("userId") int userId) {
        areaService.deleteMarkInfoById(markId, userId);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    @RequestMapping("/EditMarkInfo")
    public String EditMarkInfo(@RequestBody MarkInfo markInfo) {
        markInfo.setLoadTime(markInfo.getLoadTime().replace("T", " "));
        System.out.println(markInfo);
        areaService.UpdateMarkInfo(markInfo);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    @GetMapping("/GetMarkInfoById")
    public MarkInfo GetMarkInfoById(@RequestParam("id") int id) {
        MarkInfo markInfo = areaService.GetMarkInfoById(id);
        return markInfo;
    }

    // 获取监测船舶信息
    @GetMapping("/GetJianCeShipInfo")
    public String GetJianCeShipInfo(@RequestParam("shipName") String shipName,
                                    @RequestParam("pageNum") int pageNum) {
        pageNum = (pageNum - 1) * 10;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", shipService.GetJianCeShipInfo(shipName, pageNum));
        jsonObject.put("total", shipService.GetJianCeShipInfoCount(shipName));
        return jsonObject.toJSONString();
    }

    @GetMapping("/GetJianCeShipInfo_Export")
    public String GetJianCeShipInfo_Export(@RequestParam("shipName") String shipName) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", shipService.GetJianCeShipInfo_Export(shipName));
        jsonObject.put("total", shipService.GetJianCeShipInfoCount(shipName));
        return jsonObject.toJSONString();
    }

    // 删除监测船舶
    @RequestMapping("/DeleteJianCeShip")
    public String DeleteJianCeShip(@RequestBody List<Integer> jianCeIds) {
        for (int i = 0; i < jianCeIds.size(); i++) {
            shipService.DeleteJianCeShip(jianCeIds.get(i));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", "success");
        return jsonObject.toJSONString();
    }

    // 添加监测船舶
    @RequestMapping("/AddJianCeShip")
    public String AddJianCeShip(@RequestBody JianCeShipInfo jianCeShipInfo) {
        shipService.AddJianCeShip(jianCeShipInfo);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", "success");
        return jsonObject.toJSONString();
    }

    // 获取台风列表-year
    @GetMapping("/GetTyphoonInfoByYear")
    public String GetTyphoonInfoByYear(@RequestParam("year") int year) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", typhoonService.GetTyphoonInfoByYear(year));
        jsonObject.put("yearList", typhoonService.GetTyphoonInfoByYearCount());
        return jsonObject.toJSONString();
    }

    @GetMapping("/GetTyphoonInfoById")
    public String GetTyphoonInfoById(@RequestParam("id") int id) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("lslj", typhoonService.GetTyphoonInfoById(id));
        jsonObject.put("yblj", typhoonService.GetYBTyphoonInfoById(id));
        jsonObject.put("ybqd", typhoonService.GetYBQDById(id));
        jsonObject.put("total", id);
        return jsonObject.toJSONString();
    }

    // 今日台风
    @GetMapping("/GetTodayTyphoonInfo")
    public String GetTodayTyphoonInfo(@RequestParam("startTime") String startTime,
                                      @RequestParam("endTime") String endTime) {
        JSONObject jsonObject = new JSONObject();
        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ");
        jsonObject.put("list", typhoonService.GetTodayTyphoonInfo(startTime, endTime));
        jsonObject.put("total", typhoonService.GetTodayTyphoonInfoCount(startTime, endTime));
        return jsonObject.toJSONString();
    }

    // 模糊查询-视频
    @GetMapping("/GetCameraInfoBySearch")
    public String GetCameraInfoBySearch(@RequestParam("searchName") String searchName) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", portService.GetCameraInfoBySearch(searchName));
        return jsonObject.toJSONString();
    }

    // 伏休违规查询
    @GetMapping("/GetHistoryWarningInfo")
    public List<AlarmRecord> GetHistoryWarningInfo(@RequestParam("type") int type,
                                                   @RequestParam("startTime") String startTime,
                                                   @RequestParam("endTime") String endTime,
                                                   @RequestParam("model") int model) {
        startTime = startTime.replace("T", " ");
        endTime = endTime.replace("T", " ");
        List<AlarmRecord> alarmRecords = portService.GetHistoryWarningInfo(type, startTime, endTime, model);
        return alarmRecords;
    }

    @GetMapping("/UpdateCrewInfo")
    private void UpdateCrewInfo(@RequestParam("pageNum") int pageNum) throws Exception {
        //updateCrewInfoThread = new UpdateCrewInfoThread(peopleService);
        //updateCrewInfoThread.UpdateCrewInfo();
    }

    // 黑白名单--start--
    @GetMapping("GetWhiteAndBlackListInfoByType")
    public List<BlackAndWhiteList> GetWhiteAndBlackListInfoByType(@RequestParam("type") int type) {
        return shipService.GetWhiteAndBlackListInfoByType(type);
    }

    @GetMapping("GetWhiteAndBlackListInfoById")
    public BlackAndWhiteList GetWhiteAndBlackListInfoById(@RequestParam("typeId") int typeId) {
        return shipService.GetWhiteAndBlackListInfoById(typeId);
    }

    @RequestMapping("SetWhiteOrBlackList")
    public String SetWhiteOrBlackList(@RequestBody BlackAndWhiteList blackAndWhiteList) {
        System.out.println(blackAndWhiteList);
        shipService.SetWhiteOrBlackList(blackAndWhiteList);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    ;

    @RequestMapping("DeleteWhiteOrBlackList")
    public String DeleteWhiteOrBlackList(@RequestBody List<Integer> whiteOrBlackListIds) {
        for (int i = 0; i < whiteOrBlackListIds.size(); i++) {
            shipService.DeleteWhiteOrBlackList(whiteOrBlackListIds.get(i));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    ;

    @RequestMapping("UpdateWhiteOrBlackList")
    public String UpdateWhiteOrBlackList(@RequestBody BlackAndWhiteList blackAndWhiteList) {
        shipService.UpdateWhiteOrBlackList(blackAndWhiteList);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", "success");
        return jsonObject.toJSONString();
    }

    ;

    @GetMapping("GetAllWhiteList")
    public List<BlackAndWhiteList> GetAllWhiteList() {
        return shipService.GetAllWhiteList();
    }

    @GetMapping("GetAllBlackList")
    public List<BlackAndWhiteList> GetAllBlackList() {
        return shipService.GetAllBlackList();
    }

    @RequestMapping("/GetAllWhiteAndBlackShip")
    public String GetAllWhiteAndBlackShip(@RequestParam("shipName") String shipName,
                                          @RequestParam("bdId") String bdId,
                                          @RequestParam("mmsi") String mmsi,
                                          @RequestParam("shipType") int shipType,
                                          @RequestParam("specialType") int specialType) {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", shipService.GetAllWhiteAndBlackShip(shipName, bdId, mmsi, shipType, specialType));
        return jsonObject.toJSONString();
    }

    @RequestMapping("/GetWhiteAndBlackShip")
    public String GetWhiteAndBlackShip(@RequestParam("shipName") String shipName,
                                       @RequestParam("bdId") String bdId,
                                       @RequestParam("mmsi") String mmsi,
                                       @RequestParam("shipType") int shipType,
                                       @RequestParam("specialType") int specialType,
                                       @RequestParam("pageNum") int pageNum) {
        pageNum = (pageNum - 1) * 10;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", shipService.GetWhiteAndBlackShip(shipName, bdId, mmsi, shipType, specialType, pageNum));
        jsonObject.put("total", shipService.GetWhiteAndBlackShipCount(shipName, bdId, mmsi, shipType, specialType));
        return jsonObject.toJSONString();
    }
    // 黑白名单--end--


    //北斗消息
    @RequestMapping("/InsertBdMsgExample")
    public void InsertBdMsgExample(@RequestParam("content") String content,
                                   @RequestParam("userId") int userId) {
        peopleService.InsertBdMsgExample(content, userId);
    }

    @RequestMapping("/DeleteBdMsgExample")
    public void DeleteBdMsgExample(@RequestParam("id") int id) {
        peopleService.DeleteBdMsgExample(id);
    }

    @RequestMapping("/UpdateBdMsgExample")
    public void DeleteBdMsgExample(@RequestParam("id") int id, @RequestParam("userId") int userId, @RequestParam("content") String content) {
        peopleService.UpdateBdMsgExample(id, userId, content);
    }

    @RequestMapping("/GetBdMsgExample")
    public List<BDMsgExample> GetBdMsgExample(@RequestParam("userId") int userId) {
        return peopleService.GetBdMsgExample(userId);
    }

    //船队管理
    @RequestMapping("/AddShipTerm")
    public void AddShipTerm(@RequestParam("name") String name,
                            @RequestParam("userId") int userId) {
        shipService.AddShipTerm(name, userId);
    }

    @RequestMapping("/DeleteShipTerm")
    public void DeleteShipTerm(@RequestParam("id") int id) {
        shipService.DeleteShipTerm(id);
    }

    @RequestMapping("/GetShipTerm")
    public List<Ship_Term> GetShipTerm(@RequestParam("userId") int userId) {
        return shipService.GetShipTerm(userId);
    }

    @RequestMapping("/InsertShipForTerm")
    public void InsertShipForTerm(@RequestParam("termId") int termId,
                                  @RequestParam("staticShipId") int staticShipId) {
        shipService.InsertShipForTerm(termId, staticShipId);
    }

    @RequestMapping("/DeleteShipFromTerm")
    public void DeleteShipFromTerm(@RequestParam("id") int id) {
        shipService.DeleteShipFromTerm(id);
    }

    @RequestMapping("/GetShipFromTerm")
    public List<ShipStaticInfo_all> GetShipFromTerm(@RequestParam("termId") int termId) {
        return shipService.GetShipFromTerm(termId);
    }

    @RequestMapping("/CheckShipInTerm")
    public void CheckShipInTerm(@RequestParam("termId") int termId,
                                @RequestParam("staticShipId") int staticShipId) {
        shipService.CheckShipInTerm(termId, staticShipId);
    }

    @RequestMapping("/GetMapToken")
    public String GetMapToken() {
        return userService.GetMapToken();
    }

    // 判断船舶是否为外省船舶
    @RequestMapping("/CheckShipBout")
    public List<Integer> CheckShipBout(@RequestBody List<Integer> shipIds) {
        List<Integer> shipList = new ArrayList<>();
        for (int i = 0; i < shipIds.size(); i++) {
            int shipState = shipService.CheckShipBout(shipIds.get(i));
            if (shipState == 1) {
                shipList.add(shipIds.get(i));
            }
        }
        return shipList;
    }

    // 判断船舶类型
    @GetMapping("/GetAllShipType")
    public String GetAllShipType(@RequestParam("userId") int userId, @RequestParam("model") int model) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("bFocus", shipStaticInfoService.GetFocusShipType());
        jsonObject.put("warning", shipStaticInfoService.GetWarningShipType(userId, model));
        jsonObject.put("jiance", shipStaticInfoService.GetJianCeShipType());
        return jsonObject.toJSONString();
    }

    @GetMapping("/GetJianCeShipInfoByShipId")
    public JianCeShipInfo GetJianCeShipInfoByShipId(@RequestParam("shipId") int shipId) {
        return shipService.GetJianCeShipInfoByShipId(shipId);
    }

    @GetMapping("/GetJianCeShipInfoById")
    public JianCeShipInfo GetJianCeShipInfoById(@RequestParam("id") int id) {
        return shipService.GetJianCeShipInfoById(id);
    }

    // 离线渔船
    @GetMapping("/GetOutLineShipInfo")
    public String GetOutLineShipInfo(@RequestParam("type") String type) {
        JSONObject jsonObject = new JSONObject();
        switch (type) {
            case "0":
                jsonObject.put("2h", portService.GetOutLineShipInfo(2));
                jsonObject.put("12h", portService.GetOutLineShipInfo(12));
                jsonObject.put("24h", portService.GetOutLineShipInfo(24));
                jsonObject.put("48h", portService.GetOutLineShipInfo(48));
                jsonObject.put("99h", portService.GetOutLineShipInfo(99));
                break;
            case "2":
                jsonObject.put("2h", portService.GetOutLineShipInfo(2));
                break;
            case "12":
                jsonObject.put("2h", portService.GetOutLineShipInfo(2));
                jsonObject.put("12h", portService.GetOutLineShipInfo(12));
                break;
            case "24":
                jsonObject.put("2h", portService.GetOutLineShipInfo(2));
                jsonObject.put("12h", portService.GetOutLineShipInfo(12));
                jsonObject.put("24h", portService.GetOutLineShipInfo(24));
                break;
            case "48":
                jsonObject.put("2h", portService.GetOutLineShipInfo(2));
                jsonObject.put("12h", portService.GetOutLineShipInfo(12));
                jsonObject.put("24h", portService.GetOutLineShipInfo(24));
                jsonObject.put("48h", portService.GetOutLineShipInfo(48));
                break;
            case "99":
                jsonObject.put("99h", portService.GetOutLineShipInfo(99));
                break;
        }

        return jsonObject.toJSONString();
    }

    // 在线渔船
    @GetMapping("/GetOnLineShipInfo")
    public List<PortShipInfo> GetOnLineShipInfo() {
        return portService.GetOnLineShipInfo();
    }

    //总渔船
    @GetMapping("/GetAllShipInfo")
    public List<PortShipInfo> GetAllShipInfo() {
        return portService.GetAllShipInfo();
    }

    //异地作业渔船
    @GetMapping("/GetOutPortShipInfo")
    public List<PortShipInfo> GetOutPortShipInfo() {
        return portService.GetOutPortShipInfo();
    }

    //停港渔船
    @GetMapping("/GetInPortShipInfo")
    public List<PortShipInfo> GetInPortShipInfo(@RequestParam("portId") int portId) {
        return portService.GetInPortShipInfo(portId);
    }

    public static String convertToPinyin(String chinese) {
        StringBuilder pinyin = new StringBuilder();
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);  // 小写输出
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE); // 不带声调
        format.setVCharType(HanyuPinyinVCharType.WITH_V);  // ü 用 v 代替

        try {
            for (char c : chinese.toCharArray()) {
                if (Character.toString(c).matches("[\u4E00-\u9FA5]")) {  // 判断是否为汉字
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null) {
                        pinyin.append(pinyinArray[0]).append(" ");
                    }
                } else {
                    pinyin.append(c);  // 非汉字保持原样
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return pinyin.toString().trim();
    }
    // 上海水域内外省渔船
    @GetMapping("/GetWSInShangHaiShipInfo")
    public List<PortShipInfo> GetWSInShangHaiShipInfo() {
        //return portService.GetWSInShangHaiShipInfo();

        List<PortShipInfo> wsList = portService.GetWSInShangHaiShipInfo();
        Set<String> uniqueKeys = new HashSet<>();
        List<PortShipInfo> result = new ArrayList<>();

        for (PortShipInfo info : wsList) {
            // 将中文船名转换为拼音
            String pinyinKey;
            if(info.getShipName() != null)
                pinyinKey = convertToPinyin(info.getShipName());
            else
                pinyinKey = "";
            // 去除空格并转换为大写，生成唯一标识
            String key = pinyinKey.replaceAll("\\s+", "").toUpperCase();

            if (!uniqueKeys.contains(key)) {
                result.add(info);
                uniqueKeys.add(key);
            }
        }
        return result;
    }

    // 禁捕
    @GetMapping("/GetWSInJinBuShipInfo")
    public List<PortShipInfo> GetWSInJinBuShipInfo() {
        //return portService.GetWSInJinBuShipInfo();

        List<PortShipInfo> wsList = portService.GetWSInJinBuShipInfo();
        Set<String> uniqueKeys = new HashSet<>();
        List<PortShipInfo> result = new ArrayList<>();

        for (PortShipInfo info : wsList) {
            // 将中文船名转换为拼音
            String pinyinKey;
            if(info.getShipName() != null)
                pinyinKey = convertToPinyin(info.getShipName());
            else
                pinyinKey = "";
            // 去除空格并转换为大写，生成唯一标识
            String key = pinyinKey.replaceAll("\\s+", "").toUpperCase();

            if (!uniqueKeys.contains(key)) {
                result.add(info);
                uniqueKeys.add(key);
            }
        }
        return result;
    }

    // 168169
    @GetMapping("/GetWSIn168169ShipInfo")
    public List<PortShipInfo> GetWSIn168169ShipInfo() {
        //return portService.GetWSIn168169ShipInfo();

        List<PortShipInfo> wsList = portService.GetWSIn168169ShipInfo();
        Set<String> uniqueKeys = new HashSet<>();
        List<PortShipInfo> result = new ArrayList<>();

        for (PortShipInfo info : wsList) {
            // 将中文船名转换为拼音
            String pinyinKey;
            if(info.getShipName() != null)
                pinyinKey = convertToPinyin(info.getShipName());
            else
                pinyinKey = "";
            // 去除空格并转换为大写，生成唯一标识
            String key = pinyinKey.replaceAll("\\s+", "").toUpperCase();

            if (!uniqueKeys.contains(key)) {
                result.add(info);
                uniqueKeys.add(key);
            }
        }
        return result;
    }

    // 外省停港渔船
    @GetMapping("/GetWSInPortShipInfo")
    public List<PortShipInfo> GetWSInPortShipInfo(@RequestParam("portId") int portId) {
        //return portService.GetWSInPortShipInfo(portId);

        List<PortShipInfo> wsList = portService.GetWSInPortShipInfo(portId);
        Set<String> uniqueKeys = new HashSet<>();
        List<PortShipInfo> result = new ArrayList<>();

        for (PortShipInfo info : wsList) {
            // 将中文船名转换为拼音
            String pinyinKey;
            if(info.getShipName() != null)
                pinyinKey = convertToPinyin(info.getShipName());
            else
                pinyinKey = "";
            // 去除空格并转换为大写，生成唯一标识
            String key = pinyinKey.replaceAll("\\s+", "").toUpperCase();

            if (!uniqueKeys.contains(key)) {
                result.add(info);
                uniqueKeys.add(key);
            }
        }
        return result;
    }

    // 根据港口获取在港船舶信息
    @GetMapping("/GetShipInfoByPort")   
    public List<PortShipInfo> GetShipInfoByPort(@RequestParam("portId") int portId) {
        return portService.GetShipInfoByPort(portId);
    }

    @GetMapping("/GetShipIdByName")
    public int GetShipIdByName(@RequestParam("shipName") String shipName) {
        ShipStaticInfo_all shipStaticInfo_all = shipStaticInfoService.GetShipIdByName(shipName);
        if (shipStaticInfo_all == null) {
            return -1;
        } else {
            return shipStaticInfo_all.getId();
        }
    }

    ///统一权限管理///
    @GetMapping("/series/add")
    public String addseries() {
        String token = HttpTool.GetTokenFromCenter2();
        String url = "http://***********:9092/api/series/add";
        JSONObject series = new JSONObject();
        series.put("token", token);
        series.put("clientId", "ygyc");
        series.put("series", "common");
        series.put("seriesName", "普通用户");
        return HttpTool.doPostJson(url, series.toString(), "");
    }

    // 获取台风最近的摄像头
    @GetMapping("/GetNearestHls")
    public List<String> GetNearestHls(@RequestParam("adress") String adress) {
        HttpController con = new HttpController();

        String url1 = "00cae9c2e6ba4ffab04493acb85923f5";
        String url2 = "42dde00c243d41f4a63c019b13525312";
        String url3 = "ccba27e6066b49eda4854f9269fb80ab";
        String url4 = "1d30045a465441ea900e1ed54db523b7";

        String hlsUrl1 = con.GetHlsUrlByIndex(url1, adress);
        String hlsUrl2 = con.GetHlsUrlByIndex(url2, adress);
        String hlsUrl3 = con.GetHlsUrlByIndex(url3, adress);
        String hlsUrl4 = con.GetHlsUrlByIndex(url4, adress);

        List<String> hlss = new ArrayList<>();

        hlss.add(hlsUrl1);
        hlss.add(hlsUrl2);
        hlss.add(hlsUrl3);
        hlss.add(hlsUrl4);
        return hlss;
    }

    @RequestMapping("/GetIp")
    public String GetIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknow".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
            if (ip.equals("127.0.0.1")) {
                //根据网卡取本机配置的IP
                InetAddress inet = null;
                try {
                    inet = InetAddress.getLocalHost();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                ip = inet.getHostAddress();
            }
        }
        // 多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ip != null && ip.length() > 15) {
            if (ip.indexOf(",") > 0) {
                ip = ip.substring(0, ip.indexOf(","));
            }
        }
        return ip;

    }

    @RequestMapping("/SetUserSetting")
    public void SetUserSetting(@RequestBody List<UserSettingInfo> userSettingInfo) {
        for (int i = 0; i < userSettingInfo.size(); i++) {
            userService.SetUserSetting(userSettingInfo.get(i));
        }
    }

    @RequestMapping("/UpdateUserSetting")
    public void UpdateUserSetting(@RequestBody List<UserSettingInfo> userSettingInfo) {
        for (int i = 0; i < userSettingInfo.size(); i++) {
            userService.UpdateUserSetting(userSettingInfo.get(i));
        }
    }

    @RequestMapping("/GetUserSetting")
    public List<UserSettingInfo> GetUserSetting(@RequestParam("userId") int userId) {
        return userService.GetUserSetting(userId);
    }

    @GetMapping("/DeleteUserSetting")
    public void DeleteUserSetting(@RequestParam("userId") int userId, @RequestParam("setting") int setting) {
        userService.DeleteUserSetting(userId, setting);
    }

    @GetMapping("/GetShipImgUrl")
    public String GetShipImgUrl(@RequestParam("shipId") int shipId) {
        return shipService.GetShipImgUrl(shipId);
    }

    @GetMapping("/GetShipSetting")
    public String GetShipSetting(@RequestParam("userId") int userId) {
        return shipService.GetShipSetting(userId);
    }

    @GetMapping("/SetShipSetting")
    public void SetShipSetting(@RequestParam("userId") int userId, @RequestParam("content") String content) {
        System.out.println(shipService.GetShipSetting(userId));
        if (shipService.GetShipSetting(userId) != null) {
            shipService.UpdateShipSetting(userId, content);
        } else {
            shipService.SetShipSetting(userId, content);
        }
    }

    @RequestMapping("GetFishAreaShipInfo")
    public List<PortShipInfo> GetFishAreaShipInfo(@RequestParam("areaName") int areaName) {
        //0 全部 145-195 代表渔区号
        return portService.GetFishAreaShipInfo(areaName);
    }

    @GetMapping("/isCheckAccount")
    public UserInfo isCheckAccount(@RequestParam("sendName") String sendName) {
        return userService.isCheckAccount(sendName);
    }

    @RequestMapping("/SendWarningToOtherAccount")
    public void SendWarningToOtherAccount(@RequestParam("sendId") int sendId, @RequestParam("receiveId") int receiveId, @RequestParam("warningId") int warningId) {
        userService.SendWarningToOtherAccount(sendId, receiveId, warningId);
    }

    @RequestMapping("/GetWarningToOtherAccount")
    public List<PushInformation> GetWarningToOtherAccount(@RequestParam("userId") int userId, @RequestParam("model") int model) {
        List<PushInformation> pushInformation = userService.GetWarningToOtherAccount(userId, model);
        return pushInformation;
    }

    @RequestMapping("/SetWarningToOtherAccountIsRead")
    public void SetWarningToOtherAccountIsRead(@RequestParam("userId") int userId) {
        userService.SetWarningToOtherAccountIsRead(userId);
    }

    @RequestMapping("/GetlawCaseByShipId")
    public List<LawCase> GetlawCaseByShipId(@RequestParam("shipId") int shipId) {
        JSONObject jsonObject = new JSONObject();
        String shipName = shipService.GetshipNameById(shipId);
        //System.out.println(shipName);
        List<String> idCardList = peopleService.GetPeopleByShipName(shipName, 1);
        List<String> idCardList_new = new ArrayList<>();
        for (String idCard : idCardList) {
            //System.out.println("解析前:" + idCard);
            String newIdCard = AesUtil.aesDecrypt(idCard);
            //System.out.println("解析后:" + newIdCard);

            if (newIdCard == null) {
                idCardList_new.add(idCard);
            } else {
                idCardList_new.add(newIdCard);
            }
        }
        if (idCardList_new.size() < 1) return null;
        //System.out.println(idCardList_new);
        return peopleService.GetlawCaseByShipId(idCardList_new);
    }

    @RequestMapping("/GetManagerCount")
    public int GetManagerCount(@RequestParam("managerId") int managerId) {
        List<Long> managerIdList = Utils.GetMangerIdByPos(managerId);
        return shipStaticInfoService.GetManagerCount(managerIdList);
    }

    @RequestMapping("/excel/upload")
    public void excelUpload(@RequestBody List<ExcelEntity> entities) {
        System.out.println(entities);
        shipService.addExcelEntity(entities);
    }

    @GetMapping("/GetCrewCertificateStatistics")
    public String GetCrewCertificateStatistics(CrewCertificationQueryDto queryDto) {
        int rowLabel = NumberUtil.parseInt(queryDto.getRowLabel());
        int colLabel = NumberUtil.parseInt(queryDto.getColLabel());
        int analysisType = NumberUtil.parseInt(queryDto.getAnalysisType());
        List<People> people = peopleService.GetCrewCertificateStatistics(queryDto);
        // 定义所有可能的年龄段
        List<String> ageRanges = Arrays.asList("age_16_25", "age_26_35", "age_36_45", "age_46_55", "age_56_65", "age_65_above");

        // 1. 获取所有可能的证书种类
        Set<String> allCategories = new HashSet<>();
        if (rowLabel == 0) {
            allCategories = people.stream().map(People::getORGANIZATION).collect(Collectors.toSet());
        } else if (rowLabel == 1) {
            allCategories = people.stream().map(People::getCERTIFICATETYPE).collect(Collectors.toSet());
        } else if (rowLabel == 2) {
            allCategories = people.stream().map(People::getZSDJMC).collect(Collectors.toSet());
        } else if (rowLabel == 3) {
            allCategories = people.stream().map(People::getZHIWU).collect(Collectors.toSet());
        } else if (rowLabel == 4) {
            allCategories = people.stream().map(People::getSEX).collect(Collectors.toSet());
        }

        if (allCategories.size() <= 0) {
            return JSONObject.toJSONString(new LinkedHashMap<>());
        }

        // 2. 按证书分类统计每个年龄段中具有该证书的人数
        Map<String, Map<String, Long>> certificateAgeGroupCount = allCategories.stream()
                .collect(Collectors.toMap(
                        cert -> cert,
                        cert -> {
                            // 统计每个证书在不同年龄段的人数
                            Map<String, Long> ageGroupCount = people.stream()
                                    .filter(person -> {
                                        if (rowLabel == 0)
                                            return person.getORGANIZATION().equals(cert);
                                        else if (rowLabel == 1)
                                            return person.getCERTIFICATETYPE().equals(cert);
                                        else if (rowLabel == 2)
                                            return person.getZSDJMC().equals(cert);
                                        else if (rowLabel == 3)
                                            return person.getZHIWU().equals(cert);
                                        else if (rowLabel == 4)
                                            return person.getSEX().equals(cert);
                                        return person.getCERTIFICATETYPE().equals(cert);
                                    }) // 拥有该证书的人
                                    .collect(Collectors.groupingBy(
                                            this::getAgeRange, // 按年龄段分组
                                            Collectors.counting() // 统计人数
                                    ));

                            // 确保所有年龄段都有数据，即使人数为 0
                            Map<String, Long> fullAgeGroupCount = new HashMap<>();
                            for (String ageRange : ageRanges) {
                                fullAgeGroupCount.put(ageRange, ageGroupCount.getOrDefault(ageRange, 0L));
                            }

                            return fullAgeGroupCount;
                        }
                ));
        // 3. 计算合计并添加到结果中
        Map<String, Long> totalCounts = new HashMap<>();
        for (String ageRange : ageRanges) {
            long totalForAgeRange = certificateAgeGroupCount.values().stream()
                    .mapToLong(ageGroup -> ageGroup.getOrDefault(ageRange, 0L))
                    .sum();
            totalCounts.put(ageRange, totalForAgeRange);
        }

        if (rowLabel == 2) {
            certificateAgeGroupCount.put("无", certificateAgeGroupCount.remove("--"));
        } else if (rowLabel == 4) {
            certificateAgeGroupCount.put("男", certificateAgeGroupCount.remove("1"));
            certificateAgeGroupCount.put("女", certificateAgeGroupCount.remove("2"));
        }

        certificateAgeGroupCount = new LinkedHashMap<>(certificateAgeGroupCount);
        certificateAgeGroupCount.put("合计", totalCounts);
        return JSONObject.toJSONString(certificateAgeGroupCount);
    }

    // 定义一个方法，将年龄分配到对应的年龄段
    private String getAgeRange(People people) {
        String idCard = people.getIDCARD();
        if (!IdcardUtil.isValidCard(idCard)) return "-";
        int age = IdcardUtil.getAgeByIdCard(idCard);
        if (age >= 16 && age <= 25) {
            return "age_16_25";
        } else if (age >= 26 && age <= 35) {
            return "age_26_35";
        } else if (age >= 36 && age <= 45) {
            return "age_36_45";
        } else if (age >= 46 && age <= 55) {
            return "age_46_55";
        } else if (age >= 56 && age <= 65) {
            return "age_56_65";
        } else if (age > 65) {
            return "age_65_above";
        } else {
            return "-";
        }
    }

}
