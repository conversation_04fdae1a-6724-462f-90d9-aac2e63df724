import com.alibaba.fastjson.JSONObject;
import com.bd.Application;
import com.bd.entity.People;
import com.bd.mapper.PeopleMapper;
import com.bd.mapper.PortMapper;
import com.bd.mapper.ShipMapper;
import com.bd.util.HttpTool;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
// @ComponentScan("com.bd")
public class MyTest {

    @Autowired
    ShipMapper shipMapper;
    @Autowired
    PortMapper portMapper;
    @Autowired
    PeopleMapper peopleMapper;
    @Autowired
    ApplicationContext context;

    @Test
    public void test()  {
        String a = "23242424";
        String s = HttpTool.signData(a);
        JSONObject jsonObject = JSONObject.parseObject(s);
        String b64SignedData = jsonObject.getString("b64SignedData");
        String errorCode = jsonObject.getString("errorCode");
        System.out.println("签名错误码："+errorCode);
        System.out.println("数据签名：" + b64SignedData);
        try {
            String verifySignData = HttpTool.verifySignData("23242424", b64SignedData);
            JSONObject jsonObject1 = JSONObject.parseObject(verifySignData);
            String errorCode2 = jsonObject1.getString(errorCode);
            System.out.println("验签错误码："+errorCode2);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
