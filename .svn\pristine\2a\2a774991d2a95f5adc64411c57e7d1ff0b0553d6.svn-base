import Vue from 'vue'
import Router from 'vue-router'
// import Unicast from '@/components/Index'
import Unicast from '@/components/Unicast'
import Multicast from '@/components/Multicast'
import Index from '@/components/Index'
import XiangxiDialog from '@/components/XiangxiDialog'
import SimpleDialog from '@/components/SimpleDialog'
import shipInfo from '@/components/ShipInfo'
import Login from '@/components/Login'

Vue.use(Router)

const router = new Router({
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: Login,
      meta: { requiresAuth: false } // 登录页面不需要认证
    },
    {
      path: '/',
      name: 'Index',
      component: Index,
      meta: { requiresAuth: true } // 首页需要认证
      //component: Unicast,
      //component: Multicast,
      //component: XiangxiDialog,
      //component: SimpleDialog,
    },
    {
      path: '/shipInfo/:shipId/:shipName?',
      name: 'shipInfo',
      component: shipInfo,
      props: true,
      meta: { requiresAuth: true } // 船舶信息页面需要认证
    },
    {
      path: '/simple-dialog',
      name: 'SimpleDialog',
      component: () => import('@/components/SimpleDialog.vue'),
      meta: { requiresAuth: true } // 简单对话框需要认证
    },
    {
      path: '/xiangxi-dialog',
      name: 'XiangxiDialog',
      component: () => import('@/components/XiangxiDialog.vue'),
      meta: { requiresAuth: true } // 详细对话框需要认证
    },
    {
      path: '/unicast',
      name: 'Unicast',
      component: Unicast,
      meta: { requiresAuth: true } // 单播页面需要认证
    },
    {
      path: '/multicast',
      name: 'Multicast',
      component: Multicast,
      meta: { requiresAuth: true } // 多播页面需要认证
    },
  ]
})

// 路由守卫：检查用户是否已登录
router.beforeEach((to, from, next) => {
  // 检查路由是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);

  // 检查用户是否已登录
  const isLoggedIn = sessionStorage.getItem('isLogin') === 'true';

  if (requiresAuth && !isLoggedIn) {
    // 需要认证但用户未登录，跳转到登录页面
    next('/login');
  } else if (to.path === '/login' && isLoggedIn) {
    // 用户已登录但访问登录页面，跳转到首页
    next('/');
  } else {
    // 正常访问
    next();
  }
});

export default router
