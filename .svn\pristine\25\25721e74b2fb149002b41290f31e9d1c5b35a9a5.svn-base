package com.bd.thread;

import com.alibaba.fastjson.JSONObject;
import com.bd.entity.AdminLawExample;
import com.bd.entity.CrewBx;
import com.bd.entity.LawCase;
import com.bd.mapper.CrewExamMapper;
import com.bd.service.ShipStaticInfoService;
import com.bd.service.UserService;
import com.bd.util.AesUtil;
import com.bd.util.HttpTool;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
@Controller
@EnableScheduling
public class UpdateLawCaseInfo {

    @Resource
    private CrewExamMapper crewExamMapper;

    @Resource
    private UserService userService;

    public List<String> GetLawList(int pageNum) {
        String token = userService.GetOpenCenterToken();
        System.out.println("-.-" + token);
        String header = "Bearer " + token;
        String url = "http://*********:18080/api/service/share/P348235807930191872?conditions=null&pageNum=" + pageNum + "&pageSize=" + 500;
        String lawData = HttpTool.doGet(url, header);
        System.out.println("[data]" + lawData);
        lawData = lawData.replace("[", "");
        lawData = lawData.replace("]", "");
        lawData = lawData.replace("},{", "}#{");
        List<String> lawJsonList = Arrays.asList(lawData.split("#"));
        return lawJsonList;
    }

    public void updateLawCase() throws Exception {
        int pageNum = 1;
        List<String> lawList = new ArrayList<>();
        while (true) {
            try {
                lawList = GetLawList(pageNum);
            } catch (Exception e) {
                continue;
            }
            List<LawCase> lawCases = new ArrayList<>();
            if (lawList.size() < 2) break;
            for (String lawStr : lawList) {
                JSONObject lawCaseJson = JSONObject.parseObject(lawStr);
                //System.out.println(crewStr);
                LawCase lawCase = new LawCase();
                lawCase.setName(lawCaseJson.getString("suobjnname"));
                lawCase.setIdCard(lawCaseJson.getString("certno"));
                lawCase.setCaseName(lawCaseJson.getString("casename"));
                lawCase.setCauseContent(lawCaseJson.getString("causecontent"));
                lawCase.setOccurAddr(lawCaseJson.getString("occuraddr"));
                lawCase.setProstyle(lawCaseJson.getString("prostyle"));
                lawCase.setEnfleNames(lawCaseJson.getString("enflenames"));
                lawCase.setStage(lawCaseJson.getString("stage"));
                lawCase.setStatus(lawCaseJson.getString("status"));
                lawCase.setRegCaseDate(lawCaseJson.getString("regcasedate"));
                lawCase.setDecisionDate(lawCaseJson.getString("decisiondate"));
                lawCase.setCloseCaseDate(lawCaseJson.getString("closecasedate"));

                System.out.println(lawCase.getName());
                lawCases.add(lawCase);
            }
            System.out.println("updateLawCawe-pageNum:" + pageNum);
            //Thread.sleep(5000);
            pageNum++;

            crewExamMapper.insertLawCase(lawCases);

        }
    }

    public List<String> getAdminLawList(int pageNum) {
        String token = userService.GetOpenCenterToken();
        //System.out.println("-.-" + token);
        String header = "Bearer " + token;
        String url = "http://*********:18080/api/service/share/P415117495280603136?conditions=null&pageNum=" + pageNum + "&pageSize=" + 500;
        String adminLaw = HttpTool.doGet(url, header);
        //System.out.println("[data]" + adminLaw);
        adminLaw = adminLaw.replace("[", "");
        adminLaw = adminLaw.replace("]", "");
        adminLaw = adminLaw.replace("},{", "}#{");
        List<String> lawJsonList = Arrays.asList(adminLaw.split("#"));
        return lawJsonList;
    }

    public void updateAdminLaw() throws Exception {
        int pageNum = 1;
        List<String> lawList = new ArrayList<>();
        while (true) {
            try {
                lawList = getAdminLawList(pageNum);
            } catch (Exception e) {
                continue;
            }
            List<AdminLawExample> lawExamples = new ArrayList<>();
            if (lawList.size() < 2) break;
            for (String lawStr : lawList) {
                JSONObject adminLawJson = JSONObject.parseObject(lawStr);
                //System.out.println(crewStr);
                AdminLawExample lawExample = new AdminLawExample();
                lawExample.setAfdd(adminLawJson.getString("afdd"));
                lawExample.setAfsy(adminLawJson.getString("afsy"));
                lawExample.setAjly(adminLawJson.getString("ajly"));
                lawExample.setAjmc(adminLawJson.getString("ajmc"));
                lawExample.setCfjg(adminLawJson.getString("cfjg"));
                lawExample.setLarq(adminLawJson.getString("larq"));
                lawExample.setCf_result(adminLawJson.getString("cf_result"));
                lawExample.setCmh(adminLawJson.getString("cmh"));
                lawExample.setSfjyyj(adminLawJson.getString("sfjyyj"));
                lawExample.setJarq(adminLawJson.getString("jarq"));
                lawExample.setWj(adminLawJson.getString("wj"));
                lawExample.setYhw(adminLawJson.getString("yhw"));
                lawExample.setZasygl(adminLawJson.getString("zasygl"));
                System.out.println(lawExample);
                lawExamples.add(lawExample);
            }
            System.out.println("updateLawCawe-pageNum:" + pageNum);
            //Thread.sleep(5000);
            pageNum++;

            crewExamMapper.insertAdminLaw(lawExamples);

        }
    }

    //@Scheduled(fixedRate = Integer.MAX_VALUE)
    //@Scheduled(cron = "0 0 1 * * ?")
    public void run() throws Exception {
        crewExamMapper.deleteLawCase();
        updateLawCase();
        crewExamMapper.deleteAdminLaw();
        updateAdminLaw();
        System.out.println("==============end");
    }

}