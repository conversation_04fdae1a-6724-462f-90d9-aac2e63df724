<template>
  <div class="biaozhu-dialog" :class="{ 'mobile-keyboard-active': isMobile && isKeyboardActive }" :style="{
      left: dialogLeft + 'px',
      top: dialogTop + 'px',
      position: 'fixed',
      zIndex: 9999,
      transform: isMobile ? 'scale(0.6)' : 'scale(0.8)',
      transformOrigin: 'top left'
  }" @mousedown="onDragStart" @touchstart="onTouchStart">
    <div class="dialog-header">
      <div class="dialog-title">添加标注</div>
      <span class="close-btn" @click.stop="closeDialog">×</span>
    </div>

    <!-- 标注类型选择 -->
    <div class="type-selector" @mousedown.stop @touchstart.stop>
      <button
        class="type-btn"
        :class="{ active: annotationType === 'point' }"
        @click="switchType('point')"
      >
        点
      </button>
      <button
        class="type-btn"
        :class="{ active: annotationType === 'line' }"
        @click="switchType('line')"
      >
        线
      </button>
      <button
        class="type-btn"
        :class="{ active: annotationType === 'circle' }"
        @click="switchType('circle')"
      >
        圆形面
      </button>
      <button
        class="type-btn"
        :class="{ active: annotationType === 'polygon' }"
        @click="switchType('polygon')"
      >
        多边形面
      </button>
    </div>

    <!-- 提示文本 -->
    <div v-if="!annotationType" class="hint-section" @mousedown.stop @touchstart.stop>
      <p class="hint-text">请选择标注类型</p>
    </div>

    <!-- 表单区域 -->
    <div v-if="annotationType" class="form-section" @mousedown.stop @touchstart.stop>
      <!-- 标注名称 -->
      <div class="form-row">
        <label class="form-label">标注名称:</label>
        <input type="text" v-model="formData.name" class="form-input" placeholder="请输入标注名称">
      </div>

      <!-- 点标注表单 -->
      <template v-if="annotationType === 'point'">
        <div class="form-row">
          <label class="form-label">点经度:</label>
          <input type="text" :value="markData.pointCoords.x" class="form-input" readonly placeholder="请在地图上点击选择位置">
        </div>
        <div class="form-row">
          <label class="form-label">点纬度:</label>
          <input type="text" :value="markData.pointCoords.y" class="form-input" readonly placeholder="请在地图上点击选择位置">
        </div>
      </template>

      <!-- 圆标注表单 -->
      <template v-if="annotationType === 'circle'">
        <div class="form-row">
          <label class="form-label">圆心经度:</label>
          <input type="text" :value="markData.circleCoords.x" class="form-input" readonly placeholder="请在地图上绘制圆形">
        </div>
        <div class="form-row">
          <label class="form-label">圆心纬度:</label>
          <input type="text" :value="markData.circleCoords.y" class="form-input" readonly placeholder="请在地图上绘制圆形">
        </div>
        <div class="form-row">
          <label class="form-label">半径(海里):</label>
          <input type="text" :value="markData.circleCoords.radius" class="form-input" readonly placeholder="请在地图上绘制圆形">
        </div>
      </template>
    </div>

    <!-- 【优化】线标注的坐标表格 - 移除无用的操作列 -->
    <!-- 修改内容：移除了"操作"列，只保留序号、经度、纬度三列 -->
    <!-- 原因：操作列只显示"只读"文本，没有实际功能，占用空间且影响美观 -->
    <!-- 效果：表格更简洁，在移动设备上占用更少横向空间 -->
    <div v-if="annotationType === 'line'" class="table-section" @mousedown.stop @touchstart.stop>
      <table class="data-table">
        <thead>
          <tr>
            <th>序号</th>
            <th>经度</th>
            <th>纬度</th>
            <!-- 【移除】<th>操作</th> - 移除无用的操作列 -->
          </tr>
        </thead>
        <tbody>
          <tr v-for="(point, index) in markData.lineCoords" :key="index">
            <td>{{ index + 1 }}</td>
            <td>{{ point.x }}</td>
            <td>{{ point.y }}</td>
            <!-- 【移除】操作列的单元格和"只读"文本 -->
          </tr>
          <tr v-if="markData.lineCoords.length === 0">
            <!-- 【修改】colspan从4改为3，适应新的列数 -->
            <td colspan="3" class="empty-row">请在地图上点击添加线标注的各个点</td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 【优化】多边形标注的坐标表格 - 移除调试信息和无用操作列 -->
    <!-- 修改内容1：移除了顶部的调试信息显示（"调试：polygonCoords长度=0 数据：[]"） -->
    <!-- 修改内容2：移除了"操作"列，只保留序号、经度、纬度三列 -->
    <!-- 原因：调试信息对用户无用且影响界面美观；操作列只显示"只读"文本，无实际功能 -->
    <!-- 效果：界面更简洁，用户只看到有用的坐标数据表格 -->
    <div v-if="annotationType === 'polygon'" class="table-section" @mousedown.stop @touchstart.stop>
      <!-- 【移除】调试信息显示区域 -->
      <table class="data-table">
        <thead>
          <tr>
            <th>序号</th>
            <th>经度</th>
            <th>纬度</th>
            <!-- 【移除】<th>操作</th> - 移除无用的操作列 -->
          </tr>
        </thead>
        <tbody>
          <tr v-for="(point, index) in markData.polygonCoords" :key="index">
            <td>{{ index + 1 }}</td>
            <td>{{ point.x }}</td>
            <td>{{ point.y }}</td>
            <!-- 【移除】操作列的单元格和"只读"文本 -->
          </tr>
          <tr v-if="!markData.polygonCoords || markData.polygonCoords.length === 0">
            <!-- 【修改】colspan从4改为3，适应新的列数 -->
            <td colspan="3" class="empty-row">请在地图上点击添加多边形标注的各个点</td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 按钮区域 -->
    <div class="button-section" @mousedown.stop @touchstart.stop>
      <button class="action-btn cancel-btn" @click="closeDialog">取消</button>
      <button v-if="annotationType" class="action-btn confirm-btn" @click="confirmAction">添加</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BiaoZhuDialog',
  props: {
    markData: {
      type: Object,
      default: () => ({
        type: 0,
        pointCoords: { x: '', y: '' },
        lineCoords: [],
        circleCoords: { x: '', y: '', radius: 0 },
        polygonCoords: []
      })
    }
  },
  data() {
    const isMobile = /Android|webOS|iPhone|iPod|BlackBerry|iPad|Mobile/i.test(navigator.userAgent);
    return {
      isMobile: isMobile,
      dialogLeft: isMobile ? 10 : 300,
      dialogTop: isMobile ? 60 : 100,
      dragging: false,
      dragOffsetX: 0,
      dragOffsetY: 0,
      isKeyboardActive: false, // 键盘是否激活
      annotationType: '', // 默认不选中任何类型，三个按钮都是白色
      formData: {
        name: '',
        // 点标注
        longitude: '',
        latitude: '',
        // 圆标注
        centerLongitude: '',
        centerLatitude: '',
        radius: ''
      },
      tableData: [] // 线标注的坐标点数据
    };
  },
  watch: {
    // 【修复】监听markData.type的变化，自动设置annotationType - 修复坐标数据不显示问题
    // 问题：线标注和多边形标注的坐标数据不显示在表格中
    // 原因：annotationType需要手动点击按钮设置，但对话框打开时没有自动设置对应的标注类型
    // 解决：添加watch监听器，根据传入的markData.type自动设置对应的annotationType
    // 这样表格的v-if条件就能正确匹配，坐标数据就能正常显示
    'markData.type': {
      handler(newType) {
        console.log('markData.type changed:', newType);
        // 根据type值设置对应的annotationType
        // type值与annotationType的映射关系：
        // 1 -> 'point' (点标注)
        // 2 -> 'line' (线标注)
        // 3 -> 'polygon' (多边形面标注)
        // 5 -> 'circle' (圆标注)
        switch(newType) {
          case 1:
            this.annotationType = 'point';
            break;
          case 2:
            this.annotationType = 'line';
            break;
          case 3:
            this.annotationType = 'polygon';
            break;
          case 5:
            this.annotationType = 'circle';
            break;
          default:
            this.annotationType = '';
        }
        console.log('设置annotationType为:', this.annotationType);
      },
      immediate: true // 立即执行一次，确保组件初始化时就设置正确的类型
    }
  },
  mounted() {
    // 添加全局事件监听器
    document.addEventListener('mousemove', this.onDragMove);
    document.addEventListener('mouseup', this.onDragEnd);
    document.addEventListener('touchmove', this.onTouchMove, { passive: false });
    document.addEventListener('touchend', this.onTouchEnd);

    // 移动端键盘检测
    if (this.isMobile) {
      this.setupKeyboardDetection();
    }
  },
  beforeDestroy() {
    // 移除全局事件监听器
    document.removeEventListener('mousemove', this.onDragMove);
    document.removeEventListener('mouseup', this.onDragEnd);
    document.removeEventListener('touchmove', this.onTouchMove);
    document.removeEventListener('touchend', this.onTouchEnd);
  },
  methods: {
    closeDialog() {
      this.$emit('close');
    },
    switchType(type) {
      this.annotationType = type;
      // 切换类型时重置表单数据
      this.formData = {
        name: '',
        longitude: '',
        latitude: '',
        centerLongitude: '',
        centerLatitude: '',
        radius: ''
      };
      this.tableData = [];

      // 直接开始地图交互，不关闭对话框
      this.$emit('start-map-interaction', {
        type: this.annotationType
      });
    },
    confirmAction() {
      // 处理确定按钮点击 - 保存标注到数据库
      this.$emit('save-annotation', {
        type: this.annotationType,
        name: this.formData.name
      });
    },
    deletePoint(index) {
      this.tableData.splice(index, 1);
    },
    // PC端拖拽
    onDragStart(e) {
      if (e.button !== 0) return;
      this.dragging = true;
      this.dragOffsetX = e.clientX - this.dialogLeft;
      this.dragOffsetY = e.clientY - this.dialogTop;
    },
    onDragMove(e) {
      if (!this.dragging) return;
      this.dialogLeft = e.clientX - this.dragOffsetX;
      this.dialogTop = e.clientY - this.dragOffsetY;
    },
    onDragEnd() {
      this.dragging = false;
    },
    // 移动端触摸
    onTouchStart(e) {
      if (e.touches.length !== 1) return;
      this.dragging = true;
      const touch = e.touches[0];
      this.dragOffsetX = touch.clientX - this.dialogLeft;
      this.dragOffsetY = touch.clientY - this.dialogTop;
    },
    onTouchMove(e) {
      if (!this.dragging || e.touches.length !== 1) return;
      e.preventDefault();
      const touch = e.touches[0];
      this.dialogLeft = touch.clientX - this.dragOffsetX;
      this.dialogTop = touch.clientY - this.dragOffsetY;
    },
    onTouchEnd() {
      this.dragging = false;
    },
    // 设置键盘检测
    setupKeyboardDetection() {
      // 检测视口高度变化来判断键盘是否弹出
      const initialViewportHeight = window.innerHeight;

      const checkKeyboard = () => {
        const newHeight = window.innerHeight;
        const heightDiff = initialViewportHeight - newHeight;

        // 如果高度减少超过150px，认为键盘弹出
        if (heightDiff > 150 && !this.isKeyboardActive) {
          this.isKeyboardActive = true;
          this.adjustDialogForKeyboard(true);
        } else if (heightDiff <= 150 && this.isKeyboardActive) {
          this.isKeyboardActive = false;
          this.adjustDialogForKeyboard(false);
        }
      };

      // 监听窗口大小变化
      window.addEventListener('resize', checkKeyboard);

      // 监听输入框焦点事件
      this.$nextTick(() => {
        const inputs = this.$el.querySelectorAll('input[type="text"]');
        inputs.forEach(input => {
          input.addEventListener('focus', () => {
            setTimeout(() => {
              this.isKeyboardActive = true;
              this.adjustDialogForKeyboard(true);
            }, 300); // 延迟检测，等待键盘动画
          });

          input.addEventListener('blur', () => {
            setTimeout(() => {
              this.isKeyboardActive = false;
              this.adjustDialogForKeyboard(false);
            }, 300);
          });
        });
      });
    },
    // 调整弹窗位置以适应键盘
    adjustDialogForKeyboard(keyboardActive) {
      if (!this.isMobile) return;

      if (keyboardActive) {
        // 键盘弹出时，将弹窗移到更高的位置
        this.dialogTop = 20;
      } else {
        // 键盘收起时，恢复原始位置
        this.dialogTop = 60;
      }
    }
  }
}
</script>

<style scoped>
.biaozhu-dialog {
  background: white;
  border-radius: 8px;
  padding: 15px;
  width: 480px;
  height: auto;
  max-height: 320px;
  font-size: 16px;
  color: #333;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: fixed;
  z-index: 9999;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  cursor: move;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 5px 15px 8px 15px;
  margin-bottom: 15px;
  background: #1890ff;
  margin: -15px -15px 15px -15px;
  border-radius: 8px 8px 0 0;
}

.dialog-title {
  font-size: 24px;
  font-weight: bold;
  color: white;
  line-height: 1.2;
  margin: 0;
}

.close-btn {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 28px;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.close-btn:hover {
  color: rgba(255, 255, 255, 0.8);
}

.type-selector {
  display: flex;
  gap: 8px;
  margin-bottom: 15px;
  padding: 0 5px;
  flex-wrap: nowrap;
}

.type-btn {
  flex: 1;
  padding: 10px 8px;
  border: 2px solid #d9d9d9;
  background: white;
  color: #666;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  min-width: 0;
  white-space: nowrap;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.type-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.type-btn.active {
  border-color: #1890ff;
  background: #1890ff;
  color: white;
}

.hint-section {
  margin: 20px 0;
  text-align: center;
}

.hint-text {
  color: #999;
  font-size: 14px;
  margin: 0;
}

.form-section {
  margin-bottom: 15px;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 10px;
}

.form-label {
  width: 90px;
  font-size: 14px;
  color: #333;
  font-weight: 500;
  text-align: right;
  flex-shrink: 0;
}

.form-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.table-section {
  margin-bottom: 20px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.data-table th {
  background-color: #f5f5f5;
  padding: 10px 8px;
  text-align: center;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 1;
}

.data-table td {
  padding: 8px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  color: #666;
}

.data-table tr:hover {
  background-color: #f9f9f9;
}

.empty-row {
  color: #999;
  font-style: italic;
  padding: 20px;
}

.button-section {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding-top: 10px;
  border-top: 1px solid #e0e0e0;
  margin-top: auto;
}

.action-btn {
  padding: 10px 25px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 80px;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #d9d9d9;
}

.cancel-btn:hover {
  background: #e6e6e6;
  color: #333;
}

.confirm-btn {
  background: #1890ff;
  color: white;
}

.confirm-btn:hover {
  background: #40a9ff;
}

.delete-btn {
  background: #ff4d4f;
  color: white;
  padding: 4px 8px;
  font-size: 12px;
  min-width: auto;
}

.delete-btn:hover {
  background: #ff7875;
}

/* 移动端键盘激活时的特殊样式 */
.mobile-keyboard-active {
  position: fixed !important;
  top: 20px !important;
  max-height: 50vh !important;
  overflow-y: auto !important;
  z-index: 10000 !important;
}

/* 移动端适配 */
@media (max-width: 480px) {
  .biaozhu-dialog {
    width: 95vw;
    height: auto;
    max-height: 70vh;
    padding: 15px;
    font-size: 16px;
    top: 15vh;
    left: 2.5vw;
    transform: none;
    overflow-y: auto;
  }

  .dialog-header {
    padding: 10px 25px 15px 25px;
  }

  .dialog-title {
    font-size: 26px;
  }

  .close-btn {
    font-size: 32px;
    width: 40px;
    height: 40px;
  }

  .type-selector {
    flex-direction: row !important;
    gap: 5px !important;
    flex-wrap: nowrap !important;
  }

  .type-btn {
    padding: 8px 6px;
    font-size: 14px;
    min-width: 0;
    flex: 1;
    white-space: nowrap;
  }

  .form-label {
    width: 75px;
    font-size: 16px;
  }

  .form-input {
    font-size: 16px;
    padding: 10px 12px;
  }

  .action-btn {
    padding: 12px 20px;
    font-size: 16px;
  }

  /* 键盘激活时的移动端样式 */
  .biaozhu-dialog.mobile-keyboard-active {
    top: 10px !important;
    max-height: 45vh !important;
    width: 90vw !important;
    left: 5vw !important;
  }

  /* 键盘激活时的移动端样式 */
  .biaozhu-dialog.mobile-keyboard-active {
    top: 10px !important;
    max-height: 45vh !important;
    width: 90vw !important;
    left: 5vw !important;
  }
}
</style>