package com.bd.thread;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.bd.entity.BdMsg;
import com.bd.entity.BusinessManagement.AlarmRecord;
import com.bd.entity.PortInfo;
import com.bd.entity.ShipDynamicInfo;
import com.bd.entity.WarningInfo;
import com.bd.mapper.PeopleMapper;
import com.bd.mapper.PortMapper;
import com.bd.mapper.ShipMapper;
import com.bd.service.PortService;
import com.bd.service.ShipService;
import com.bd.util.Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class DailyTask {
    @Resource
    PortMapper portMapper;
    @Resource
    ShipMapper shipMapper;
    @Resource
    PeopleMapper peopleMapper;

    //@Scheduled(cron = "0 0 * * * ?")
    public void updateOutInRecordReport(){
        //更新进出港记录是否进行过进出港报告
        log.info("更新船舶报告");
        portMapper.updateOutInPortRecordReport();
    }

    // 给24小时内未进行进出港报告的船舶发送北斗消息
    //@Scheduled(cron = "0 0 * * * ?")
    public void sendBdMsgFor24UnReport(){
        String time1 = Utils.GetNowTimeString(25 * 3600);
        String time2 = Utils.GetNowTimeString(24 * 3600);
        System.out.println(time1 + "\t" + time2);
        List<AlarmRecord> unReportList = portMapper.getUnReportByTimePeriod(time1, time2);
        for (AlarmRecord alarmRecord : unReportList) {
            BdMsg bdMsg = new BdMsg();
            ShipDynamicInfo shipDynamicInfo = shipMapper.GetOneShipDynamicInfoById(alarmRecord.getSTATICSHIPID());
            if (shipDynamicInfo == null) continue;
            bdMsg.setStaticShipId(alarmRecord.getSTATICSHIPID());
            bdMsg.setBdId(shipDynamicInfo.getBDID());
            bdMsg.setUserId(0);
            bdMsg.setSendType(10);
            bdMsg.setLoadTime(Utils.GetNowTimeString());
            if (alarmRecord.getCONTENT().contains("未进行进港报告")){
                bdMsg.setMsg(String.format("%s 进入渔港已超24小时未报告，请及时整改，通过渔港通APP/小程序进行进港报告",alarmRecord.getSHIPNAME()));
            }else if(alarmRecord.getCONTENT().contains("未进行出港报告")){
                bdMsg.setMsg(String.format("%s 离开渔港已超24小时未报告，请及时整改，通过渔港通APP/小程序进行出港报告",alarmRecord.getSHIPNAME()));
            }
            peopleMapper.InsertBdMsg(bdMsg);
        }
        // System.out.println(unReportList);
    }

}
