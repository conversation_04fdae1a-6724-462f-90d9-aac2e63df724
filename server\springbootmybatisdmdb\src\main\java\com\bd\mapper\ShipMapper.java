package com.bd.mapper;

import com.bd.entity.*;
import com.bd.entity.dto.ShipQueryDto;
import com.bd.entity.other.ExcelEntity;
import com.bd.entity.other.SpecialShip;
import com.bd.util.M_POINT;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ShipMapper {
    List<Ship> GetBDShipPosition(@Param("sec") int sec);
    List<ShipDynamicInfo> GetBDShipPosition_ronghe(@Param("sec") int sec);
    List<ShipDynamicInfo> GetBDShipPosition_ronghe_strTime(@Param("time") String time);
    ShipDynamicInfo GetOneShipDynamicInfoById(@Param("id")int id);

    List<Ship> GetBDShipPositionOtherProvinces(@Param("sec") int sec);

    void UpdateShipInPortState(@Param("portId") int portId, @Param("state") int state, @Param("ID") int ID);

    void InsertOutInPortRecord(@Param("ship") ShipDynamicInfo ship);

    List<ShipTrack> GetShipHistoryTrackById(@Param("id")int id,@Param("startTime")long startTime,@Param("endTime") long endTime, @Param("addAis")int addAis, @Param("addBd")int addBd, @Param("bOutSide")int bOutSide);

    int selectshipidfromterm(@Param("BDID") String BDID);

    List<ShipDynamicInfo> GetImportanceShip();
    void SetImportanceShip(@Param("id") int id);
    void DeleteImportanceShip(@Param("id") int id);

    void SetFuxiuWhiteShip(@Param("id") int id);
    void DeleteFuxiuWhiteShip(@Param("id") int id);

    List<ShipDynamicInfo> GetSpecialShip();

    List<Ship_Yuzheng> GetYuzhengShipInfo();

    //4个数量统计
    int GetRegisteredFish();
    int GetInPortFish(@Param("time") String time);
    int GetOutPortFish(@Param("time") String time);
    int GetWsInPortFish(@Param("time") String time);

    List<ShipDynamicInfo> GetAllBDShipPosition_ronghe(@Param("pageNum")int pageNum);

    int GetBDShipPosition_rongheCount();

    List<SpecialShip> GetSpecialShipToZhongtai();

    List<ShipOnlineCount> GetShipOnlineStatistics(String shipName, String startTime, String endTime, int pageNum);

    List<ShipOnlineCount> GetAllShipOnlineStatistics(String shipName, String startTime, String endTime);

    int GetShipOnlineStatisticsCount(String shipName, String startTime, String endTime);

    void SetShipWithArea(@Param("shipId")int shipId, @Param("areaId")int areaId);

    List<ShipTrack> GetPlayAreaHistoryInfo(@Param("startTime")String startTime, @Param("endTime")String endTime, @Param("minLon")String minLon, @Param("maxLon")String maxLon,
                                           @Param("minLat")String minLat, @Param("maxLat")String maxLat, @Param("addAis") int addAis);

    List<JianCeShipInfo> GetJianCeShipInfo(@Param("shipName")String shipName, @Param("pageNum")int pageNum);

    int GetJianCeShipInfoCount(@Param("shipName")String shipName);

    List<JianCeShipInfo> GetJianCeShipInfo_Export(@Param("shipName")String shipName);

    void DeleteJianCeShip(@Param("id")int id);

    void AddJianCeShip(@Param("jianCeShipInfo")JianCeShipInfo jianCeShipInfo);

    List<BlackAndWhiteList> GetWhiteAndBlackListInfoByType(@Param("type")int type);

    BlackAndWhiteList GetWhiteAndBlackListInfoById(@Param("id")int id);

    void SetWhiteOrBlackList(@Param("blackAndWhiteList")BlackAndWhiteList blackAndWhiteList);

    void DeleteWhiteOrBlackList(@Param("typeId")int typeId);

    void UpdateWhiteOrBlackList(@Param("blackAndWhiteList")BlackAndWhiteList blackAndWhiteList);

    ShipCount GetShipCount();

    List<BlackAndWhiteList> GetAllWhiteList();

    List<BlackAndWhiteList> GetAllBlackList();

    List<BlackAndWhiteList> GetAllWhiteAndBlackShip(@Param("shipName")String shipName,
                                            @Param("bdId")String bdId,
                                            @Param("mmsi")String mmsi,
                                            @Param("shipType")int shipType,
                                            @Param("specialType")int specialType);

    List<BlackAndWhiteList> GetWhiteAndBlackShip(@Param("shipName")String shipName,
                                            @Param("bdId")String bdId,
                                            @Param("mmsi")String mmsi,
                                            @Param("shipType")int shipType,
                                            @Param("specialType")int specialType,
                                            @Param("pageNum")int pageNum);

    int GetWhiteAndBlackShipCount(@Param("shipName")String shipName,
                                            @Param("bdId")String bdId,
                                            @Param("mmsi")String mmsi,
                                            @Param("shipType")int shipType,
                                            @Param("specialType")int specialType);

    void AddShipTerm(@Param("name")String name, @Param("userId")int userId);

    void DeleteShipTerm(@Param("id")int id);

    List<Ship_Term> GetShipTerm(@Param("userId")int userId);

    void InsertShipForTerm(@Param("termId")int termId, @Param("staticShipId")int staticShipId);

    void DeleteShipFromTerm(@Param("id")int id);

    List<ShipStaticInfo_all> GetShipFromTerm(@Param("termId")int termId);

    int CheckShipInTerm(@Param("termId")int termId, @Param("staticShipId")int staticShipId);

    void InsertShipVoyage(@Param("voyage")Ship_Voyage voyage);

    List<ShipDynamicInfo> GetBDShipPosition_ronghe_strTime_thread(@Param("time")String getNowTimeString);

    int CheckShipBout(int shipId);

    Ship_DistributeCount GetShipdistribute(@Param("portId")int portId, @Param("time")String time);

    int getAllShipCount();

    int getOnlineShipCount(@Param("time") String time);

    int GetInPortFish2(@Param("portId")int portId, @Param("time") String time);

    int GetWSInShanghai(@Param("time") String time);

    int GetWSInJinbu(@Param("time") String time);

    int GetWSInPort(@Param("portId")int portId, @Param("time") String time);

    List<ShipDynamicInfo> GetBDShipPosition_ronghe_strTime_thread_ws(@Param("time")String getNowTimeString);

    void updateShipInShanghaiState(@Param("ship")ShipDynamicInfo ship, @Param("state")int state);

    JianCeShipInfo GetJianCeShipInfoByShipId(@Param("shipId")int shipId);

    JianCeShipInfo GetJianCeShipInfoById(@Param("id")int id);

    String GetshipNameById(@Param("shipId")int shipId);

    String GetShipImgUrl(@Param("shipId")int shipId);

    String GetShipSetting(@Param("userId")int userId);

    void SetShipSetting(@Param("userId")int userId, @Param("content")String content);

    void UpdateShipSetting(@Param("userId")int userId, @Param("content")String content);

    void updateFishAreaId(@Param("ship")ShipDynamicInfo ship, @Param("name")int name);

    int updateExcelEntity(@Param("entities") List<ExcelEntity> entities);

    List<String> GetTrackBdidList();

    int GetStaticshipidByBdid(@Param("bdid")String bdid);

    void InsertTrack(@Param("staticshipid")int staticshipid, @Param("bdid")String bdid);

    int GetstaticshipidCount(@Param("bdid")String bdid);

    CheckRecord getCheckRecordByShipName(@Param("shipName") String shipName);

    int getMaxBerth(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("portId") int portId);

    List<String> GetshipNamesByIds(@Param("shipIds") List<Integer> shipIds);

    List<M_POINT> GetAllShipsPositionByIds(@Param("shipIds") List<Integer> shipIds);

    String getAreaByShipName(String shipName);
}
