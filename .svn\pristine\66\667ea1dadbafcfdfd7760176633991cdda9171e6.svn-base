<!-- 船舶详情 -->
<template>
    <div>
      <div style="width: 800px; height: 520px; z-index: 4; position:absolute; background-color: #052757; color: white; border: 0;"></div>
        <!-- <div id="shipdetailsTwo" style="width: 1200px; height: 720px; z-index: 51; position:absolute; background-color: #052757; color: white; border: 0; "> -->
            <div id="shipdetailsTwo-head" style="position: absolute; top:0px; left: 0px; width: 800px; height: 70px;z-index: 6;">
              <div style="width: 800px; height: 30px; position: absolute; top: 0px; left: 0px; background-color: #052757;">
                <span style="position: absolute; width: 200px; height: 50px; text-align: left; font-size: 20px; left: 80px; top: 20px; z-index: 7;">{{topShipName}}</span><br>
                <button id="ShipCollection" style="width: 100px; height: 30px; background-color: #F46932; border: 0; border-radius:5px; position: absolute; z-index: 7; right: 150px; top: 20px;" @click="ShipCollection()">加入白名单</button>
                <button id="AddShipToFleet" style="width: 70px; height: 30px; background-color: #19690C; border: 0; border-radius:5px; position: absolute; z-index: 7; right: 70px; top: 20px;" @click="AddShipToFleet()">加入船队</button>
                <button style="width: 70px; height: 40px; background-color: #052757; border: 0; position: absolute; z-index: 7; right: 0px; top: 15px;" @click="closeShipDiv">×</button>
              </div>
              <!-- <span style="position: absolute; width: 500px; height: 50px; text-align: left; font-size: 14px; color: #ffffff; left: 260px; top: 80px;">北斗编号：322201</span><br> -->
            </div>
            <div style="width: 800px; height: 35px; position: absolute; top: 120px; left: 0px; background-color: #ffffff;"></div>
            <!-- <img src="../../../static/img/shipShow.jpg" style="position: absolute; width: 165px; height: 105px; top:10px; left:10px;z-index: 51;"> -->
            <div id="" style="width: 600px; height: 360px; position: absolute; top: 50px; left: 0; z-index: 7; background-color: #052757; color: white; border: 0px solid #FF0000;">
              <div id="shipdetails-menu" style="border:0;float:left; position: absolute; left: -10px; top: 20px; width: 210px; ">
                <button style="position: absolute; width: 140px; height: 15px; background-color: #052757; border: 0; left: 10px; top: 20px; text-align: center; font-size: 14px; " @click="replayByCollin(1)" id="shipMsg1" ref="shipMsg1" :class="{selected:(boxNum==1)?true:false, unselected:(boxNum==1)?true:false}">船舶基本信息</button><br/>
                <button style="position: absolute; width: 140px; height: 15px; background-color: #052757; border: 0; left: 10px; top: 65px; text-align: center;" @click="replayByCollin(3)" id="shipMsg3" ref="shipMsg3" :class="{selected:(boxNum==3)?true:false, unselected:(boxNum==3)?true:false}">捕捞许可证</button><br/>
                <button style="position: absolute; width: 140px; height: 15px; background-color: #052757; border: 0; left: 10px; top: 110px; text-align: center;" @click="replayByCollin(8)" id="shipMsg8" ref="shipMsg8" :class="{selected:(boxNum==8)?true:false, unselected:(boxNum==8)?true:false}">特许捕捞许可证</button><br/>
                <button style="position: absolute; width: 140px; height: 15px; background-color: #052757; border: 0; left: 10px; top: 155px; text-align: center;" @click="replayByCollin(4)" id="shipMsg4" ref="shipMsg4" :class="{selected:(boxNum==4)?true:false, unselected:(boxNum==4)?true:false}">船舶检验信息</button>
                <button style="position: absolute; width: 140px; height: 15px; background-color: #052757; border: 0; left: 10px; top: 200px; text-align: center;" @click="replayByCollin(5)" id="shipMsg5" ref="shipMsg5" :class="{selected:(boxNum==5)?true:false, unselected:(boxNum==5)?true:false}">船检安全设备信息</button>
                <button style="position: absolute; width: 140px; height: 15px; background-color: #052757; border: 0; left: 10px; top: 245px; text-align: center;" @click="replayByCollin(6)" id="shipMsg6" ref="shipMsg6" :class="{selected:(boxNum==6)?true:false, unselected:(boxNum==6)?true:false}">国籍证书</button>
                <button style="position: absolute; width: 140px; height: 15px; background-color: #052757; border: 0; left: 10px; top: 290px; text-align: center;" @click="replayByCollin(2)" id="shipMsg2" ref="shipMsg2" :class="{selected:(boxNum==2)?true:false, unselected:(boxNum==2)?true:false}">所有权登记证书</button>
                <button style="position: absolute; width: 140px; height: 15px; background-color: #052757; border: 0; left: 10px; top: 245px; text-align: center;  display: none;" @click="replayByCollin(7)" id="shipMsg7" ref="shipMsg7" :class="{selected:(boxNum==7)?true:false, unselected:(boxNum==7)?true:false}">船舶船名登记证书</button>
                <button style="position: absolute; width: 140px; height: 15px; background-color: #052757; border: 0; left: 10px; top: 335px; text-align: center;" @click="replayByCollin(9)" id="shipMsg9" ref="shipMsg9" :class="{selected:(boxNum==9)?true:false, unselected:(boxNum==9)?true:false}">执法信息</button>
              </div>
              <div id="shipdetails-msg" style="position: absolute;float:left; background-color: #F1F1F1; left: 145px;top: 20px; width: 625px; height: 400px;">

                <!-- 船舶基本信息 -->
                <div id="shipmsgBox1" v-show ="(boxNum==1)" style="width: 600px; height:375px; position: absolute; top: 15px; left: 15px; z-index: 7; background-color:  #ffffff; color: #ffffff; border: 0px solid #FF0000;">
                  <span style="width: 680px; height: 150px; text-align: left; font-size: 22px; font-weight:bold; color: #000; position: absolute; left: 35px; top:10px; ">船舶基本信息</span><br>
                  <div style="float:left; position: absolute; top: 0px; left: 35px;">
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 65px;">船名：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 100px;">船舶类型：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 135px;">组织机构：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 170px;">呼号：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 205px;">船长：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 240px;">型宽：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 275px;">作业场所：</span><br>
                    <!-- <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 310px;">船上人数：</span> -->
                  </div>
                  <div style="float:left; position: absolute; top: 0px; left: 255px;">
                    <span id="sp1" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 65px;"></span><br>
                    <span id="sp2" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 100px;"></span><br>
                    <span id="sp3" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 135px;"></span><br>
                    <span id="sp4" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 170px;"></span><br>
                    <span id="sp5" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 205px;"></span><br>
                    <span id="sp6" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 240px;"></span><br>
                    <span id="sp7" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 275px;"></span><br>
                    <!-- <span id="sp8" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 310px;"></span><br> -->
                  </div>
                  <div style="float:left; top: 0px; left: 460px;position: absolute;">
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 65px;">属地：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 100px;;">作业类型：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 135px;">船舶所有人：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 170px;">功率：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 205px;">吨位：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 240px;">建造完工日期：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 275px;">船体材料：</span><br>
                  </div>
                  <div style="float:left; position: absolute; top: 0px; left: 635px;">
                    <span id="sp8" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 65px;"></span><br>
                    <span id="sp9" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 100px;;"></span><br>
                    <span id="sp10" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 135px;"></span><br>
                    <span id="sp11" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 170px;"></span><br>
                    <span id="sp12" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 205px;"></span><br>
                    <span id="sp13" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 240px;"></span><br>
                    <span id="sp14" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 275px;"></span><br>
                  </div>
                </div>

                <!-- 所有权登记证书信息 -->
                <div id="shipmsgBox2" v-show ="(boxNum==2)" style="width: 600px; height:375px; position: absolute; top: 15px; left: 15px; z-index: 7; background-color:  #ffffff; color: #ffffff; border: 0px solid #FF0000;">
                  <span style="width: 680px; height: 150px; text-align: left; font-size: 22px; font-weight:bold; color: #000; position: absolute; left: 35px; top:10px; ">所有权登记证书信息</span><br>
                  <div style="float:left; position: absolute; top: 0px; left: 35px;">
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 65px;">所有权证书编号：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 100px; display: none;">船网指标批准书:</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 135px;">国籍证书编号：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 170px;">取得所有权日期：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 205px; ">证书有效期：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 240px;"></span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 275px;"></span><br>
                  </div>
                  <div style="float:left; position: absolute; top: 0px; left: 255px;">
                    <span  style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-130px; top: 65px;">{{ shipNationRegisInfo.yycbsyqdjzsbh || '-' }}</span><br>
                    <span  style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 100px; display: none;">{{ shipNationRegisInfo.yycbsyqdjzsbh || '-' }}</span><br>
                    <span  style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 135px;">{{ shipNationRegisInfo.yycbgjzsbh || '-' }}</span><br>
                    <span  style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 170px;">{{ shipNationRegisInfo.syqgjdjspsj || '-' }}</span><br>
                    <span  style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 205px;">{{ shipNationRegisInfo.yycbdjzsyxq || shipNationRegisInfo.yycbgjzsyxq || '-' }}</span><br>
                    <span  style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 240px;"></span><br>
                    <span  style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 275px;"></span><br>
                    <!-- <span id="sp8" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 310px;"></span><br> -->
                  </div>
                  <div style="float:left; top: 0px; left: 460px;position: absolute;">
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 65px;">申请人姓名：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 100px;;">联系电话：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 135px;">船籍港：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 170px;">渔船所属地：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 205px;">船舶种类：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 240px;"></span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 275px;"></span><br>
                  </div>
                  <div style="float:left; position: absolute; top: 0px; left: 635px;">
                    <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 65px;">{{ shipNationRegisInfo.czrmc || '-' }}</span><br>
                    <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 100px;;">{{ shipNationRegisInfo.czrdh || '-' }}</span><br>
                    <span  style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 135px;">{{ shipNationRegisInfo.cjg || '-' }}</span><br>
                    <span  style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 170px;">{{ shipNationRegisInfo.ycssdqmc || '-' }}</span><br>
                    <span  style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 205px;">{{ shipNationRegisInfo.cbzl || '-' }}</span><br>
                    <span  style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 240px;"></span><br>
                    <span  style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 275px;"></span><br>
                  </div>
                </div>

                <!-- 捕捞许可信息 -->
                <div id="shipmsgBox3" v-show ="(boxNum==3)" style="width: 600px; height:375px; position: absolute; top: 15px; left: 15px; z-index: 7; background-color:  #ffffff; color: #ffffff; border: 0px solid #FF0000;">
                  <span style="width: 680px; height: 150px; text-align: left; font-size: 22px; font-weight:bold; color: #000; position: absolute; left: 35px; top:10px; ">捕捞许可信息</span><br>
                  <div style="float:left; position: absolute; top: 0px; left: 35px;">
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 65px;">捕捞许可证号：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 100px;">持证人：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 135px;">持证人地址：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 170px;">证书签发日期：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 205px;">证书有效期：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 240px;">作业类型：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 275px;">作业场所：</span><br>
                    <!-- <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 310px;">船上人数：</span> -->
                  </div>
                  <div style="float:left; position: absolute; top: 0px; left: 255px;">
                    <span id="sp31" style="float: left; width: 250px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-130px; top: 65px;">{{ shipNationRegisInfo.yyblxkzbh  }}</span><br>
                    <span id="sp32" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 100px;">{{ shipNationRegisInfo.czr }}</span><br>
                    <span id="sp33" style="float: left; width: 200px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 135px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{ shipNationRegisInfo.czrdz }}</span><br>
                    <span id="sp34" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 170px;">{{ shipNationRegisInfo.qfsj }}</span><br>
                    <span id="sp35" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 205px;">{{ shipNationRegisInfo.zsyxq }}</span><br>
                    <span id="sp36" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 240px;">{{ shipNationRegisInfo.zzylx }}</span><br>
                    <span id="sp37" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 275px;">{{ shipNationRegisInfo.zzydyzyfs_zycs }}</span><br>
                    <!-- <span id="sp8" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 310px;"></span><br> -->
                  </div>
                  <div style="float:left; top: 0px; left: 460px;position: absolute;">
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 65px;">作业方式：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 100px;;">申请许可类型：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 135px;">渔具数量：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 170px;">渔具名称：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 205px;">渔具规格：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 240px;">主要捕捞品种：</span><br>
                    <!-- <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 275px;">捕捞限额：</span><br> -->
                  </div>
                  <div style="float:left; position: absolute; top: 0px; left: 635px;">
                    <span id="sp38" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 65px;">{{ shipNationRegisInfo.zzydyzyfs }}</span><br>
                    <span id="sp39" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 100px;;">{{ shipNationRegisInfo.sqxkzlx }}</span><br>
                    <span id="sp310" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 135px;">{{ shipNationRegisInfo.zzydyzyfs_yjsl }}</span><br>
                    <span id="sp311" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 170px;">{{ shipNationRegisInfo.zzydyzyfs_yjmc }}</span><br>
                    <span id="sp312" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 205px;">{{ shipNationRegisInfo.zzydyzyfs_yjgg }}</span><br>
                    <span id="sp313" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 240px;">{{ shipNationRegisInfo.zzydyzyfs_zyblpz }}</span><br>
                    <!-- <span id="sp314" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 275px;">{{ shipNationRegisInfo.zzydyzyfs_blxe || '-' }}</span><br> -->
                  </div>
                </div>

                <!-- 船舶检验信息 -->
                <div id="shipmsgBox4" v-show ="(boxNum==4)" style="width: 600px; height:375px; position: absolute; top: 15px; left: 15px; z-index: 7; background-color:  #ffffff; color: #ffffff; border: 0px solid #FF0000;">
                  <span style="width: 680px; height: 150px; text-align: left; font-size: 22px; font-weight:bold; color: #000; position: absolute; left: 35px; top:10px; ">船舶检验信息</span><br>
                  <div style="float:left; position: absolute; top: 0px; left: 35px;">
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 65px;">证书编号：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 100px;">证书有效期：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 135px;">发证日期：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 170px;">发证地点：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 205px;">检验地点：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 240px;"></span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 275px;"></span><br>
                    <!-- <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 310px;">船上人数：</span> -->
                  </div>
                  <div style="float:left; position: absolute; top: 0px; left: 255px;">
                    <span id="sp41" style="float: left; width: 250px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-130px; top: 65px;"></span><br>
                    <span id="sp42" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 100px;"></span><br>
                    <span id="sp43" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 135px;"></span><br>
                    <span id="sp44" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 170px;"></span><br>
                    <span id="sp45" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 205px;"></span><br>
                    <span id="sp46" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 240px;"></span><br>
                    <span id="sp47" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 275px;"></span><br>
                    <!-- <span id="sp8" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 310px;"></span><br> -->
                  </div>
                  <div style="float:left; top: 0px; left: 460px;position: absolute;">
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 65px;">检验类型：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 100px;;">检验部门：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 135px;">下次检验日期：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 170px;">下次检验类型：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 205px;">船舶制造厂：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 240px;"></span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 275px;"></span><br>
                  </div>
                  <div style="float:left; position: absolute; top: 0px; left: 635px;">
                    <span id="sp48" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 65px;"></span><br>
                    <span id="sp49" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 100px;;"></span><br>
                    <span id="sp410" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 135px;"></span><br>
                    <span id="sp411" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 170px;"></span><br>
                    <span id="sp412" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 205px;"></span><br>
                    <span id="sp413" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 240px;"></span><br>
                    <span id="sp414" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 275px;"></span><br>
                  </div>
                </div>

                <!-- 船检安全设备信息 -->
                <div id="shipmsgBox5" v-show ="(boxNum==5)" style="width: 600px; height:375px; position: absolute; top: 15px; left: 15px; z-index: 7; background-color:  #ffffff; color: #ffffff; border: 0px solid #FF0000;">
                  <span style="width: 680px; height: 150px; text-align: left; font-size: 22px; font-weight:bold; color: #000; position: absolute; left: 35px; top:10px; ">船检安全设备信息</span><br>
                  <div style="float:left; position: absolute; top: 0px; left: 35px;">
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 65px;">检验登记号：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 100px;">核载人数：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 135px;">船舶定员总人数：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 170px;">救生设备可用总人数：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 205px;">救生圈数量：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 240px;">甚高频设备型号：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 275px;">渔船无线电话型号：</span><br>
                  </div>
                  <div style="float:left; position: absolute; top: 0px; left: 255px;">
                    <span id="sp51" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;right:-60px; top: 65px;"></span><br>
                    <span id="sp52" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;right:-60px; top: 100px;"></span><br>
                    <span id="sp53" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;right:-60px; top: 135px;"></span><br>
                    <span id="sp54" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;right:-60px; top: 170px;"></span><br>
                    <span id="sp55" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;right:-60px; top: 205px;"></span><br>
                    <span id="sp56" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;right:-60px; top: 240px;"></span><br>
                    <span id="sp57" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;right:-60px; top: 275px;"></span><br>
                    <!-- <span id="sp8" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 310px;"></span><br> -->
                  </div>
                  <div style="float:left; top: 0px; left: 460px;position: absolute;">
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-135px; top: 65px;">左舷救生艇型式：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-135px; top: 100px;;">左舷救生艇数量：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-135px; top: 135px;">左舷救生艇共载人数：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-135px; top: 170px;">右舷救生艇型式：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-135px; top: 205px;">右舷救生艇数量：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-135px; top: 240px;">右舷救生艇共载人数：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-135px; top: 275px;">渔船无线电话识别码：</span><br>
                  </div>
                  <div style="float:left; position: absolute; top: 0px; left: 635px;">
                    <span id="sp58" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;right:35px; top: 65px;"></span><br>
                    <span id="sp59" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;right:35px; top: 100px;;"></span><br>
                    <span id="sp510" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;right:35px; top: 135px;"></span><br>
                    <span id="sp511" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;right:35px; top: 170px;"></span><br>
                    <span id="sp512" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;right:35px; top: 205px;"></span><br>
                    <span id="sp513" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;right:35px; top: 240px;"></span><br>
                    <span id="sp514" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;right:35px; top: 275px;"></span><br>
                  </div>
                </div>

                <!-- 国籍证书信息 -->
                <div id="shipmsgBox6" v-show ="(boxNum==6)" style="width: 600px; height:375px; position: absolute; top: 15px; left: 15px; z-index: 7; background-color:  #ffffff; color: #ffffff; border: 0px solid #FF0000;">
                  <span style="width: 680px; height: 150px; text-align: left; font-size: 22px; font-weight:bold; color: #000; position: absolute; left: 30px; top:10px; ">国籍证书信息</span><br>

                  <!-- 左侧 -->
                    <div style="float:left; position: absolute; top: 0px; left: 30px;">
                      <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 60px;">国籍证书编号:</span><br>
                      <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 95px;">船名:</span><br>
                      <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 130px;">船籍港:</span><br>
                      <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 165px;">船舶种类:</span><br>
                      <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 200px;">船体材质:</span><br>
                      <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 235px;">主尺度:</span><br>
                      <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 270px;">船舶所有人名称:</span><br>
                      <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 305px;">公民身份证号/统一社会信用代码:</span><br>
                    </div>

                    <div style="float:left; position: absolute; top: 0px; left: 140px;">
                      <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;   color: #000; position: absolute; left: 10px; top: 60px;">{{ shipNationRegisInfo.yycbgjzsbh || '-' }}</span><br>
                      <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;   color: #000; position: absolute; left: 10px; top: 95px;">{{ shipNationRegisInfo.cm || '-' }}</span><br>
                      <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;   color: #000; position: absolute; left: 10px; top: 130px;">{{ shipNationRegisInfo.cjg || '-' }}</span><br>
                      <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;   color: #000; position: absolute; left: 10px; top: 165px;">{{ shipNationRegisInfo.cbzl || '-' }}</span><br>
                      <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;   color: #000; position: absolute; left: 10px; top: 200px;">{{ shipNationRegisInfo.ctcz || '-' }}</span><br>
                      <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;   color: #000; position: absolute; left: 10px; top: 235px;">{{ shipNationRegisInfo.cc || '-' }}</span><br>
                      <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;   color: #000; position: absolute; left: 10px; top: 270px;">{{ shipNationRegisInfo.cbsyrmc || '-' }}</span><br>
                      <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;   color: #000; position: absolute; left: 10px; top: 305px;">{{ shipNationRegisInfo.czrjmsfzhmhgszch || '-' }}</span><br>
                    </div>

                    <!-- 右侧 -->
                    <div style="float:left; top: 0px; left: 330px; position: absolute;">
                      <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000; position: absolute; top: 60px;">渔船编号:</span><br>
                      <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000; position: absolute; top: 95px;">船舶呼号:</span><br>
                      <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000; position: absolute; top: 130px;">生产方式/养殖证号:</span><br>
                      <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000; position: absolute; top: 165px;">建造完工日期:</span><br>
                      <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000; position: absolute; top: 200px;">吨位:</span><br>
                      <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000; position: absolute; top: 235px;">船舶所有人地址:</span><br>
                      <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000; position: absolute; top: 270px;">船舶所有权登记证书编号:</span><br>

                    </div>

                    <div style="float:left; position: absolute; top: 0px; left: 440px;">
                      <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute;  left: 10px; top: 60px;">{{ shipNationRegisInfo.ycbm || '-' }}</span><br>
                      <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute;  left: 10px; top: 95px;">{{ shipNationRegisInfo.cbhhsbm || '-' }}</span><br>
                      <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute;  left: 10px; top: 130px;">{{ shipNationRegisInfo.cblx || '-' }}</span><br>
                      <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute;  left: 10px; top: 165px;">{{ shipNationRegisInfo.jzwgrq || '-' }}</span><br>
                      <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute;  left: 10px; top: 200px;">{{ shipNationRegisInfo.zdw || '-' }}</span><br>
                      <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute;  left: 10px; top: 235px;">{{ shipNationRegisInfo.cbsyrdz || '-' }}</span><br>
                      <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute;  left: 10px; top: 270px;">{{ shipNationRegisInfo.syqgjdjsqsbh || '-' }}</span><br>
                    </div>
                  </div>

              <!-- 船舶船名登记信息 -->
                <div id="shipmsgBox7" v-show ="(boxNum==7)" style="width: 600px; height:375px; position: absolute; top: 15px; left: 15px; z-index: 7; background-color:  #ffffff; color: #ffffff; border: 0px solid #FF0000;">
                  <span style="width: 680px; height: 150px; text-align: left; font-size: 22px; font-weight:bold; color: #000; position: absolute; left: 35px; top:10px; ">国籍证书信息</span><br>
                  <div style="float:left; position: absolute; top: 0px; left: 35px;">
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 65px;">渔船业务类型：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 100px;">船舶来源：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 135px;">船舶种类：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 170px;">渔船所属地：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 205px;">船籍港：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 240px;">作业类型：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 275px;">作业场所：</span><br>
                    <!-- <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 310px;">船上人数：</span> -->
                  </div>
                  <div style="float:left; position: absolute; top: 0px; left: 255px;">
                    <span id="sp71" style="float: left; width: 250px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-130px; top: 65px;"></span><br>
                    <span id="sp72" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 100px;"></span><br>
                    <span id="sp73" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 135px;"></span><br>
                    <span id="sp74" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 170px;"></span><br>
                    <span id="sp75" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 205px;"></span><br>
                    <span id="sp76" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 240px;"></span><br>
                    <span id="sp77" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 275px;"></span><br>
                    <!-- <span id="sp8" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 310px;"></span><br> -->
                  </div>
                  <div style="float:left; top: 0px; left: 460px;position: absolute;">
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 65px;">原船名：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 100px;;">船长：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 135px;">船体材质：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 170px;">作业方式：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 205px;">辅助作业方式：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 240px;">主机总功率：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 275px;">总吨位：</span><br>
                  </div>
                  <div style="float:left; position: absolute; top: 0px; left: 635px;">
                    <span id="sp78" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 65px;"></span><br>
                    <span id="sp79" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 100px;;"></span><br>
                    <span id="sp710" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 135px;"></span><br>
                    <span id="sp711" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 170px;"></span><br>
                    <span id="sp712" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 205px;"></span><br>
                    <span id="sp713" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 240px;"></span><br>
                    <span id="sp714" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-200px; top: 275px;"></span><br>
                  </div>
                </div>


                <div id="shipmsgBox8" v-show ="(boxNum==8)" style="width: 600px; height:375px; position: absolute; top: 15px; left: 15px; z-index: 7; background-color:  #ffffff; color: #ffffff; border: 0px solid #FF0000;">
                  <span style="width: 680px; height: 150px; text-align: left; font-size: 22px; font-weight:bold; color: #000; position: absolute; left: 35px; top:10px; ">特许捕捞许可证</span><br>
                  <div style="float:left; position: absolute; top: 0px; left: 35px;">
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 65px;">专项特许证编号：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 100px;">发证单位：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 135px;">发放对象：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 170px;">许可事项：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 205px;">作业时间：</span><br>
                    <!-- <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 240px;">备注：</span><br> -->
                  </div>
                  <div style="float:left; position: absolute; top: 0px; left: 255px;">
                    <span id="sp81" style="float: left; width: 250px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 65px;">-</span><br>
                    <span id="sp82" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 100px;">-</span><br>
                    <span id="sp83" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 135px;">-</span><br>
                    <span id="sp84" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 170px;">-</span><br>
                    <span id="sp85" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 205px;">-</span><br>
                    <!-- <span id="sp86" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-120px; top: 240px;">-</span><br> -->
                  </div>
                  <div style="float:left; top: 0px; left: 460px;position: absolute;">
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 65px;">作业水域：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 100px;;">作业方式：</span><br>
                    <span style="float: left; width: 120px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 135px;">捕捞品种：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 170px;">作业类型：</span><br>
                    <span style="float: left; width: 90px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute;left:-120px; top: 205px;">作业场所：</span><br>
                  </div>
                  <div style="float:left; position: absolute; top: 0px; left: 635px;">
                    <span id="sp87" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-220px; top: 65px;">-</span><br>
                    <span id="sp88" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-220px; top: 100px;">-</span><br>
                    <span id="sp89" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-220px; top: 135px;">-</span><br>
                    <span id="sp891" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-220px; top: 170px;">-</span><br>
                    <span id="sp892" style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; text-align: left; color: #000;position: absolute;left:-220px; top: 205px;">-</span><br>
                  </div>
                </div>

                <!-- 执法检查信息 -->
                <div id="shipmsgBox9" v-show ="(boxNum==9)" style=" width: 600px; height:375px; position: absolute; top: 15px; left: 15px; z-index: 7; background-color:  #ffffff; color: #ffffff; border: 0px solid #FF0000;">
                  <span style="width: 480px; height: 30px; text-align: left; font-size: 22px; font-weight:bold; color: #000; position: absolute; left: 35px; top:10px; ">执法检查信息</span><br>

                  <div style="float:left; position: absolute; top: 0px; left: 30px;">
                    <!-- <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 60px;">船籍:</span><br> -->
                    <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 60px;">船长:</span><br>
                    <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 95px;">执法部门:</span><br>
                    <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 130px;">检查地点:</span><br>
                    <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 165px;">检查时间:</span><br>
                    <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 200px;">处理意见:</span><br>
                    <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000;position: absolute; top: 270px;">当事人签字时间:</span><br>
                    <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000; position: absolute; top: 305px;">当事人意见:</span><br>
                  </div>
                  <div style="float:left; position: absolute; top: 0px; left: 140px;">
                    <!-- <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px; color: #000; position: absolute; left: 10px; top: 60px;">{{ checkData.cj || '-' }}</span><br> -->
                    <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute; left: 10px; top: 60px;">{{ shipNationRegisInfo.cbsyrmc || '-' }}</span><br>
                    <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute; left: 10px; top: 95px;">{{ checkRecord.dEPT_CNAMES || '-' }}</span><br>
                    <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute; left: 10px; top: 130px;">{{ checkRecord.cHECK_PLACE || '-' }}</span><br>
                    <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute; left: 10px; top: 165px;">{{ checkRecord.sIGN_DATE || '-' }}</span><br>
                    <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute; left: 10px; top: 200px;">{{ checkRecord.oTHER_CHECK_CONTENT}}</span><br>
                    <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute; left: 10px; top: 270px;">{{ checkRecord.sIGN_DATE || '-' }}</span><br>
                    <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute;  left: 10px; top: 305px;">{{ checkRecord.sUOBJ_OPINION || '-' }}</span><br>
                  </div>

                  <div style="float:left; top: 0px; left: 330px; position: absolute;">
                    <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000; position: absolute; top: 60px;">船东:</span><br>
                    <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000; position: absolute; top: 95px;;">电话:</span><br>
                    <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000; position: absolute; top: 130px;">执法人员:</span><br>
                    <span style="float: left; width: 110px; height: 15px; text-align: left; font-size: 12px; font-weight:bold;  color: #000; position: absolute; top: 165px;">检查内容:</span><br>
                  </div>
                  <div style="float:left; position: absolute; top: 0px; left: 440px;">
                    <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute;  left: 10px; top: 60px;">{{ checkData.cd || '-' }}</span><br>
                    <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute;  left: 10px; top: 95px;;">{{ checkData.dh || '-' }}</span><br>
                    <span style="float: left; width: 150px; height: 15px; text-align: left; font-size: 12px;  color: #000; position: absolute;  left: 10px; top: 130px;">{{ checkRecord.eNF_LE_NAMES || '-' }}</span><br>
                    <span style="float: left; width: 270px; height: 190px; text-align: left; font-size: 12px;  color: #000; position: absolute;  left: -110px; top: 185px; overflow: hidden">{{ checkRecord.cHECK_CONTENT || '' }}</span><br>
                  </div>
                  <!-- 列表形式展示执法检查信息 -->
                  <div style="height:80%; overflow-y: auto;margin-top:40px;padding: 0 35px;" id="tablseDiv">
                    <table style=" color:black;font-size: 12px;" id="caseTable">
                      <!-- <thead>
                        <tr>
                          <td>序号</td>
                          <td>船籍</td>
                          <td>船东</td>
                          <td>电话</td>
                          <td>违法行为</td>
                          <td>案发日期</td>
                          <td>立案日期</td>
                          <td>结案日期</td>
                          <td>案发地点</td>
                          <td>整改项</td>
                          <td>强制项</td>
                        </tr>
                      </thead>

                      <tbody>
                        <tr v-for="(item, index) in anJianList" :key="index" :class="{changeColor:(index%2 == 0)?true:false}" style="color:#000">
                          <td>{{(index+1)}}</td>
                          <td>{{item.caseName}}</td>
                          <td>{{item.personName}}</td>
                          <td :title="item.idNumber">{{item.shortIdNumber}}</td>
                          <td>{{item.content}}</td>
                          <td :title="item.happenTime">{{item.shortHappenTime}}</td>
                          <td :title="item.loadTime">{{item.shortLoadTime}}</td>
                          <td :title="item.endTime">{{item.shortEndTime}}</td>
                          <td :title="item.happenAdress">{{item.shortHappenAdress}}</td>
                          <td></td>
                          <td></td>
                        </tr>
                      </tbody> -->
                    </table>
                  </div>

                </div>
              </div>
            </div>
      </div>
</template>

<script>

import global from './Global.vue';
import $ from 'jquery';
import checkData from '../../static/js/checkData.js'
var _this = {};

export default {
  props: {
    whiteIsShow: {
          type: Boolean,
          required: true,
      }
  },
  name: 'HelloWorld',
  data () {
      return {
          boxNum: 1,
          // box1: true,
          // box2: false,
          // box3: false,
          // box4: false,
          // box5: false,
          // box6: false,
          // box7: false,
          // box8: false,
          // box9: false,
          ShipId: 0,
          ifadd: false,
          topShipName: "",
          anJianList:
          [
            // {caseName:"123", personName:"张三", idNumber:"414141414141414141", content:"违规作业", happenTime:"2022-01-01 14:00", loadTime:"2022-01-02 10:00", endTime:"2022-01-05 10:00", happenAdress:"横沙渔港东偏北三十度方向35°海里海域"},

          ],
          shipNationRegisInfo: {},
          checkData:{},
          checkRecord:{}
      }

  },
  beforeCreate() {
      _this = this;
      // var SHIPID = sessionStorage.getItem("SHIPID");
      // alert(SHIPID);
      // _this.$options.methods.shipInfos.bind(this)(SHIPID);

  },
  mounted () {
    // this.checkData = checkData
    // var mess = setInterval(function (){showmess()},1000);
    // function showmess () {
    //   var ids = sessionStorage.getItem("SHIPID");
    //   _this.$options.methods.shipInfos.bind(this)(ids);
    // }
  },
  methods: {
    shipInfos: function (id) {
      _this.ShipId = id;
      $.get(
        global.IP + "/web/GetOneShipInfoById?" +
        "id=" +
        id,
        function (data, status) {
          var shipnames = (data[0].shipname == undefined || data[0].shipname  == "" || data[0].shipname == null) ? "-" : data[0].shipname;
          _this.topShipName = shipnames;
          _this.checkData = checkData[_this.topShipName] || {};
          var shiptypes = (data[0].cblx == undefined || data[0].cblx == "" || data[0].cblx == null) ? "-" : data[0].cblx;
          var suoshus = (data[0].ycssdqmc == undefined || data[0].ycssdqmc == "" || data[0].ycssdqmc == null) ? "-" : data[0].ycssdqmc;
          var huhaos = (data[0].cbhhsbm == undefined || data[0].cbhhsbm  == "" || data[0].cbhhsbm == null) ? "-" : data[0].cbhhsbm;
          var lengths = (data[0].cz == undefined || data[0].cz  == "" || data[0].cz == null) ? "-" : data[0].cz;
          var widths = (data[0].xk == undefined || data[0].xk  == "" || data[0].xk == null) ? "-" : data[0].xk;
          var jobPlaces = (data[0].jobPlace == undefined || data[0].jobPlace  == "" || data[0].jobPlace == null) ? "-" : data[0].jobPlace;
          var shudis = (data[0].ycssdqmc == undefined || data[0].ycssdqmc  == "" || data[0].ycssdqmc == null) ? "-" : data[0].ycssdqmc;
          var dictJobTypes = (data[0].dictJobType == undefined || data[0].dictJobType  == "" || data[0].dictJobType == null) ? "-" : data[0].dictJobType;
          var owners = (data[0].owner == undefined || data[0].owner  == "" || data[0].owner == null) ? "-" : data[0].owner;
          var zjglqws = (data[0].zjzgl == undefined || data[0].zjzgl  == "" || data[0].zjzgl == null) ? "-" : data[0].zjzgl;
          var dws = (data[0].zdw == undefined || data[0].zdw  == "" || data[0].zdw == null) ? "-" : data[0].zdw;
          var overTimes = (data[0].jzwgrq == undefined || data[0].jzwgrq  == "" || data[0].jzwgrq == null) ? "-" : data[0].jzwgrq;
          var ctcz = (data[0].ctcz == undefined || data[0].ctcz  == "" || data[0].ctcz == null) ? "-" : data[0].ctcz;
          var zzylx = (data[0].zzylx == undefined || data[0].zzylx  == "" || data[0].zzylx == null) ? "-" : data[0].zzylx;
          var zycs = (data[0].zzydyzyfs_ZYCS == undefined || data[0].zzydyzyfs_ZYCS  == "" || data[0].zzydyzyfs_ZYCS == null) ? "-" : data[0].zzydyzyfs_ZYCS;

          owners = owners == '-' ? data[0].cbsyrmc || '-' : owners;

          document.getElementById('sp1').innerHTML = shipnames;
          document.getElementById('sp2').innerHTML = shiptypes;
          document.getElementById('sp3').innerHTML = suoshus;
          // document.getElementById('sp4').innerHTML = huhaos === null ? "-": huhaos;
          document.getElementById('sp4').innerHTML = huhaos;
          document.getElementById('sp5').innerHTML = lengths;
          document.getElementById('sp6').innerHTML = widths;
          document.getElementById('sp7').innerHTML = zycs;
          document.getElementById('sp8').innerHTML = shudis;
          document.getElementById('sp9').innerHTML = zzylx;
          document.getElementById('sp10').innerHTML = owners;
          document.getElementById('sp11').innerHTML = zjglqws;
          document.getElementById('sp12').innerHTML = dws;
          document.getElementById('sp13').innerHTML = overTimes;
          document.getElementById('sp14').innerHTML = ctcz;

          // document.getElementById('sp31').innerHTML = (data[0].fishing_PERMIT_NUMBER == undefined || data[0].fishing_PERMIT_NUMBER  == "" || data[0].fishing_PERMIT_NUMBER == null) ? "-" : data[0].fishing_PERMIT_NUMBER;
          // document.getElementById('sp32').innerHTML = (data[0].ship_OWNER == undefined || data[0].ship_OWNER  == "" || data[0].ship_OWNER == null) ? "-" : data[0].ship_OWNER;
          // document.getElementById('sp33').innerHTML = (data[0].address == undefined || data[0].address  == "" || data[0].address == null) ? "-" : data[0].address; data[0].address;
          // document.getElementById('sp34').innerHTML = (data[0].app_JOB_BEGDATE == undefined || data[0].app_JOB_BEGDATE  == "" || data[0].app_JOB_BEGDATE == null) ? "-" : data[0].app_JOB_BEGDATE;
          // document.getElementById('sp35').innerHTML = (data[0].app_JOB_ENDDATE == undefined || data[0].app_JOB_ENDDATE  == "" || data[0].app_JOB_ENDDATE == null) ? "-" : data[0].app_JOB_ENDDATE;
          // document.getElementById('sp36').innerHTML = (data[0].app_JOB_TYPE == undefined || data[0].app_JOB_TYPE  == "" || data[0].app_JOB_TYPE == null) ? "-" : data[0].app_JOB_TYPE;
          // document.getElementById('sp37').innerHTML = (data[0].app_JOB_PLACE == undefined || data[0].app_JOB_PLACE  == "" || data[0].app_JOB_PLACE == null) ? "-" : data[0].app_JOB_PLACE;
          // document.getElementById('sp38').innerHTML = (data[0].app_JOB_WAY == undefined || data[0].app_JOB_WAY  == "" || data[0].app_JOB_WAY == null) ? "-" : data[0].app_JOB_WAY;
          // document.getElementById('sp39').innerHTML = (data[0].dict_APP_TYPE == undefined || data[0].dict_APP_TYPE  == "" || data[0].dict_APP_TYPE == null) ? "-" : data[0].dict_APP_TYPE;
          // document.getElementById('sp310').innerHTML = (data[0].fishinggear_AMOUNT == undefined || data[0].fishinggear_AMOUNT  == "" || data[0].fishinggear_AMOUNT == null) ? "-" : data[0].fishinggear_AMOUNT;
          // document.getElementById('sp311').innerHTML = (data[0].fishinggear_NAME == undefined || data[0].fishinggear_NAME  == "" || data[0].fishinggear_NAME == null) ? "-" : data[0].fishinggear_NAME;
          // document.getElementById('sp312').innerHTML = (data[0].fishinggear_SPEC == undefined || data[0].fishinggear_SPEC  == "" || data[0].fishinggear_SPEC == null) ? "-" : data[0].fishinggear_SPEC;
          // document.getElementById('sp313').innerHTML = (data[0].main_BREED == undefined || data[0].main_BREED  == "" || data[0].main_BREED == null) ? "-" : data[0].main_BREED;
          // document.getElementById('sp314').innerHTML = (data[0].main_QUOTA == undefined || data[0].main_QUOTA  == "" || data[0].main_QUOTA == null) ? "-" : data[0].main_QUOTA;

          _this.shipCheckRecord(shipnames);
        }
      );
    },

    shipCheckRecord: function (shipName) {
      _this.checkRecord = {};
      $.get(global.IP + "/web/getCheckRecordByShipName?shipName=" + shipName, function (data) {
        // console.log(data);
        if (data.data){
          _this.checkRecord = data.data;
          var otherCheckContent = _this.checkRecord.oTHER_CHECK_CONTENT || '-';
          var handleOpinions = _this.checkRecord.hANDL_OPINIONS || '-';
          var result = otherCheckContent + ' ' + handleOpinions;
          _this.checkRecord.oTHER_CHECK_CONTENT = result ;
        }

      }, 'json')
    },

    shipCardInfo: function (id) {
      _this.ShipId = id;
      $.get(
        global.IP + "/web/GetOneShipCardById?" +
        "id=" +
        id,
        function (data, status) {
          _this.shipNationRegisInfo = {};
          document.getElementById('sp41').innerHTML = (data[0].main_CERTIFICATE_NUMBER == undefined || data[0].main_CERTIFICATE_NUMBER  == "" || data[0].main_CERTIFICATE_NUMBER == null) ? "-" : data[0].main_CERTIFICATE_NUMBER;
          document.getElementById('sp42').innerHTML = (data[0].main_CERTIFICATE_VALIDITY == undefined || data[0].main_CERTIFICATE_VALIDITY  == "" || data[0].main_CERTIFICATE_VALIDITY == null) ? "-" : data[0].main_CERTIFICATE_VALIDITY;
          document.getElementById('sp43').innerHTML = (data[0].main_CERITIFICATE_DATE == undefined || data[0].main_CERITIFICATE_DATE  == "" || data[0].main_CERITIFICATE_DATE == null) ? "-" : data[0].main_CERITIFICATE_DATE;
          document.getElementById('sp44').innerHTML = (data[0].main_ADDRESS_CH == undefined || data[0].main_ADDRESS_CH  == "" || data[0].main_ADDRESS_CH == null) ? "-" : data[0].main_ADDRESS_CH; data[0].main_ADDRESS_CH;
          document.getElementById('sp45').innerHTML = (data[0].main_VERIFYENR_ADDRESS_CH == undefined || data[0].main_VERIFYENR_ADDRESS_CH  == "" || data[0].main_VERIFYENR_ADDRESS_CH == null) ? "-" : data[0].main_VERIFYENR_ADDRESS_CH;
          // document.getElementById('sp46').innerHTML = "-";
          // document.getElementById('sp47').innerHTML = "-";
          document.getElementById('sp48').innerHTML = (data[0].dict_MAIN_VERIFYENR_TYPE == undefined || data[0].dict_MAIN_VERIFYENR_TYPE  == "" || data[0].dict_MAIN_VERIFYENR_TYPE == null) ? "-" : data[0].dict_MAIN_VERIFYENR_TYPE;
          document.getElementById('sp49').innerHTML = (data[0].main_VERIFY_DEPT_ID == undefined || data[0].main_VERIFY_DEPT_ID  == "" || data[0].main_VERIFY_DEPT_ID == null) ? "-" : data[0].main_VERIFY_DEPT_ID;
          document.getElementById('sp410').innerHTML = (data[0].main_NEXT_VERIFY_TIME == undefined || data[0].main_NEXT_VERIFY_TIME  == "" || data[0].main_NEXT_VERIFY_TIME == null) ? "-" : data[0].main_NEXT_VERIFY_TIME;
          document.getElementById('sp411').innerHTML = (data[0].dict_MAIN_NEXT_VERIFY_TYPE == undefined || data[0].dict_MAIN_NEXT_VERIFY_TYPE  == "" || data[0].dict_MAIN_NEXT_VERIFY_TYPE == null) ? "-" : data[0].dict_MAIN_NEXT_VERIFY_TYPE;
          document.getElementById('sp412').innerHTML = (data[0].link_FISHING_FACTORY_CH == undefined || data[0].link_FISHING_FACTORY_CH  == "" || data[0].link_FISHING_FACTORY_CH == null) ? "-" : data[0].link_FISHING_FACTORY_CH;
          // document.getElementById('sp413').innerHTML = "-";
          // document.getElementById('sp414').innerHTML = "-";

          document.getElementById('sp51').innerHTML = (data[0].verifyenr_NO == undefined || data[0].verifyenr_NO  == "" || data[0].verifyenr_NO == null) ? "-" : data[0].verifyenr_NO;
          // console.log(data[0]);
          document.getElementById('sp52').innerHTML = (data[0].link_RATED_LOAD_NUM == undefined || data[0].link_RATED_LOAD_NUM  == "" || data[0].link_RATED_LOAD_NUM == null) ? "-" : data[0].link_RATED_LOAD_NUM;
          document.getElementById('sp53').innerHTML = (data[0].passengers_NUM == undefined || data[0].passengers_NUM  == "" || data[0].passengers_NUM == null) ? "-" : data[0].passengers_NUM;
          document.getElementById('sp54').innerHTML = (data[0].save_EQUIPMENT_CAN_USED_NUM == undefined || data[0].save_EQUIPMENT_CAN_USED_NUM  == "" || data[0].save_EQUIPMENT_CAN_USED_NUM == null) ? "-" : data[0].save_EQUIPMENT_CAN_USED_NUM;
          document.getElementById('sp55').innerHTML = (data[0].life_BUOY_NUM == undefined || data[0].life_BUOY_NUM  == "" || data[0].life_BUOY_NUM == null) ? "-" : data[0].life_BUOY_NUM;
          document.getElementById('sp56').innerHTML = (data[0].vhf_TYPE == undefined || data[0].vhf_TYPE  == "" || data[0].vhf_TYPE == null) ? "-" : data[0].vhf_TYPE;
          document.getElementById('sp57').innerHTML = (data[0].cngrrtel_TYPE == undefined || data[0].cngrrtel_TYPE  == "" || data[0].cngrrtel_TYPE == null) ? "-" : data[0].cngrrtel_TYPE;
          document.getElementById('sp58').innerHTML = (data[0].ship_CHECK_LEFT_TYPE == undefined || data[0].ship_CHECK_LEFT_TYPE  == "" || data[0].ship_CHECK_LEFT_TYPE == null) ? "-" : data[0].ship_CHECK_LEFT_TYPE;
          document.getElementById('sp59').innerHTML = (data[0].save_EQT_LEFT_NUM == undefined || data[0].save_EQT_LEFT_NUM  == "" || data[0].save_EQT_LEFT_NUM == null) ? "-" : data[0].save_EQT_LEFT_NUM;
          document.getElementById('sp510').innerHTML = (data[0].save_EQT_LEFT_ACCOMMODATED == undefined || data[0].save_EQT_LEFT_ACCOMMODATED  == "" || data[0].save_EQT_LEFT_ACCOMMODATED == null) ? "-" : data[0].save_EQT_LEFT_ACCOMMODATED;
          document.getElementById('sp511').innerHTML = (data[0].ship_CHECK_RIGHT_TYPE == undefined || data[0].ship_CHECK_RIGHT_TYPE  == "" || data[0].ship_CHECK_RIGHT_TYPE == null) ? "-" : data[0].ship_CHECK_RIGHT_TYPE;
          document.getElementById('sp512').innerHTML = (data[0].save_EQT_RIGHT_NUM == undefined || data[0].save_EQT_RIGHT_NUM  == "" || data[0].save_EQT_RIGHT_NUM == null) ? "-" : data[0].save_EQT_RIGHT_NUM;
          document.getElementById('sp513').innerHTML = (data[0].save_EQT_RIGHT_ACCOMMODATED == undefined || data[0].save_EQT_RIGHT_ACCOMMODATED  == "" || data[0].save_EQT_RIGHT_ACCOMMODATED == null) ? "-" : data[0].save_EQT_RIGHT_ACCOMMODATED;
          document.getElementById('sp514').innerHTML = (data[0].cngrr_NUM == undefined || data下·[0].cngrr_NUM  == "" || data[0].cngrr_NUM == null) ? "-" : data[0].cngrr_NUM;

          // console.log('USER==>',data[0]);
          _this.shipNationRegisInfo = data[0]
          // document.getElementById('sp61').innerHTML = (data[0].yycbgjzsbh == undefined || data[0].yycbgjzsbh  == "" || data[0].yycbgjzsbh == null) ? "-" : data[0].yycbgjzsbh;
          // document.getElementById('sp62').innerHTML = (data[0].register_NUMBER == undefined || data[0].register_NUMBER  == "" || data[0].register_NUMBER == null) ? "-" : data[0].register_NUMBER;
          // document.getElementById('sp63').innerHTML = (data[0].cm == undefined || data[0].cm  == "" || data[0].cm == null) ? "-" : data[0].cm;
          // document.getElementById('sp64').innerHTML = (data[0].ownership_GET_DATE == undefined || data[0].ownership_GET_DATE  == "" || data[0].ownership_GET_DATE == null) ? "-" : data[0].ownership_GET_DATE;
          // document.getElementById('sp65').innerHTML = (data[0].ownership_CERT_PERIOD_DATE == undefined || data[0].ownership_CERT_PERIOD_DATE  == "" || data[0].ownership_CERT_PERIOD_DATE == null) ? "-" : data[0].ownership_CERT_PERIOD_DATE;
          // document.getElementById('sp66').innerHTML = "-";
          // document.getElementById('sp67').innerHTML = "-";
          // document.getElementById('sp68').innerHTML = (data[0].owner_NAME == undefined || data[0].owner_NAME  == "" || data[0].owner_NAME == null) ? "-" : data[0].owner_NAME;
          // document.getElementById('sp69').innerHTML = (data[0].owner_TEL == undefined || data[0].owner_TEL  == "" || data[0].owner_TEL == null) ? "-" : data[0].owner_TEL;
          // document.getElementById('sp610').innerHTML = (data[0].ship_PORT == undefined || data[0].ship_PORT  == "" || data[0].ship_PORT == null) ? "-" : data[0].ship_PORT;
          // document.getElementById('sp611').innerHTML = (data[0].dist_SHIP_DISTRICT == undefined || data[0].dist_SHIP_DISTRICT  == "" || data[0].dist_SHIP_DISTRICT == null) ? "-" : data[0].dist_SHIP_DISTRICT;
          // document.getElementById('sp612').innerHTML = (data[0].dict_SHIP_TYPE == undefined || data[0].dict_SHIP_TYPE  == "" || data[0].dict_SHIP_TYPE == null) ? "-" : data[0].dict_SHIP_TYPE;
          // document.getElementById('sp613').innerHTML = "-";
          // document.getElementById('sp614').innerHTML = "-";

          document.getElementById('sp71').innerHTML = (data[0].dict_SHIP_BUSINESS_TYPE == undefined || data[0].dict_SHIP_BUSINESS_TYPE  == "" || data[0].dict_SHIP_BUSINESS_TYPE == null) ? "-" : data[0].dict_SHIP_BUSINESS_TYPE;
          document.getElementById('sp72').innerHTML = (data[0].dict_SHIP_SOURCE == undefined || data[0].dict_SHIP_SOURCE  == "" || data[0].dict_SHIP_SOURCE == null) ? "-" : data[0].dict_SHIP_SOURCE;
          document.getElementById('sp73').innerHTML = (data[0].dict_SHIP_TYPE == undefined || data[0].dict_SHIP_TYPE  == "" || data[0].dict_SHIP_TYPE == null) ? "-" : data[0].dict_SHIP_TYPE;
          document.getElementById('sp74').innerHTML = (data[0].dist_SHIP_DISTRICT == undefined || data[0].dist_SHIP_DISTRICT  == "" || data[0].dist_SHIP_DISTRICT == null) ? "-" : data[0].dist_SHIP_DISTRICT;
          document.getElementById('sp75').innerHTML = (data[0].ship_PORT == undefined || data[0].ship_PORT  == "" || data[0].ship_PORT == null) ? "-" : data[0].ship_PORT;
          document.getElementById('sp76').innerHTML = (data[0].app_JOB_TYPE == undefined || data[0].app_JOB_TYPE  == "" || data[0].app_JOB_TYPE == null) ? "-" : data[0].app_JOB_TYPE;
          document.getElementById('sp77').innerHTML = (data[0].job_PLACE == undefined || data[0].job_PLACE  == "" || data[0].job_PLACE == null) ? "-" : data[0].job_PLACE;
          document.getElementById('sp78').innerHTML = (data[0].ship_NAME_OLD == undefined || data[0].ship_NAME_OLD  == "" || data[0].ship_NAME_OLD == null) ? "-" : data[0].ship_NAME_OLD;
          document.getElementById('sp79').innerHTML = (data[0].ship_LENGTH == undefined || data[0].ship_LENGTH  == "" || data[0].ship_LENGTH == null) ? "-" : data[0].ship_LENGTH;
          document.getElementById('sp710').innerHTML = (data[0].dict_SHIP_MATERIAL == undefined || data[0].dict_SHIP_MATERIAL  == "" || data[0].dict_SHIP_MATERIAL == null) ? "-" : data[0].dict_SHIP_MATERIAL;
          document.getElementById('sp711').innerHTML = (data[0].job_WAY == undefined || data[0].job_WAY  == "" || data[0].job_WAY == null) ? "-" : data[0].job_WAY; data[0].job_WAY;
          document.getElementById('sp712').innerHTML = (data[0].dict_JOB_WAY_FUZHU == undefined || data[0].dict_JOB_WAY_FUZHU  == "" || data[0].dict_JOB_WAY_FUZHU == null) ? "-" : data[0].dict_JOB_WAY_FUZHU;
          document.getElementById('sp713').innerHTML = (data[0].ship_TOT_POWER == undefined || data[0].ship_TOT_POWER  == "" || data[0].ship_TOT_POWER == null) ? "-" : data[0].ship_TOT_POWER;
          document.getElementById('sp714').innerHTML = (data[0].ship_TOT_TON == undefined || data[0].ship_TOT_TON  == "" || data[0].ship_TOT_TON == null) ? "-" : data[0].ship_TOT_TON;

          }
      );
    },

    shipMonitorInfo: function (id) {
      let url = global.IP + "/web/GetOneShipCardTexuById?id=" + id;
      $.get(url, function (data, status) {
        if(typeof(data.yyblxkzbh) == "undefined") {
          document.getElementById("sp81").innerHTML = "-";
        }
        else {
          document.getElementById("sp81").innerHTML = data.yyblxkzbh;
        }
        if(typeof(data.qfbm) == "undefined") {
          document.getElementById("sp82").innerHTML = "-";
        }
        else {
          document.getElementById("sp82").innerHTML = data.qfbm;
        }
        if(typeof(data.czr) == "undefined") {
          document.getElementById("sp83").innerHTML = "-";
        }
        else {
          document.getElementById("sp83").innerHTML = data.czr;
        }

        if(typeof(data.sqxkzlx) == "undefined") {
          document.getElementById("sp84").innerHTML = "-";
        }
        else {
          document.getElementById("sp84").innerHTML = data.sqxkzlx
        }

        if(typeof(data.zzydyzyfs_zykssj) == "undefined") {
          document.getElementById("sp85").innerHTML = "-";
        }
        else{
          document.getElementById("sp85").innerHTML = ((typeof(data.zzydyzyfs_zykssj) == "undefined") ? "" : data.zzydyzyfs_zykssj) + " - " + ((typeof(data.zzydyzyfs_zyjssj) == "undefined") ? "" : data.zzydyzyfs_zyjssj);
        }

        if(typeof(data.zzydyzyfs_jtcsmc) == "undefined") {
          document.getElementById("sp87").innerHTML = "-";
        }
        else {
          document.getElementById("sp87").innerHTML = data.zzydyzyfs_zycs;
          document.getElementById("sp87").title = data.zzydyzyfs_jtcsmc;
        }

        if(typeof(data.zzydyzyfs) == "undefined") {
          document.getElementById("sp88").innerHTML = "-";
        }
        else {
          document.getElementById("sp88").innerHTML = data.zzydyzyfs;
        }

        if(typeof(data.zzydyzyfs_zyblpz) == "undefined") {
          document.getElementById("sp89").innerHTML = "-";
        }
        else{
          document.getElementById("sp89").innerHTML = data.zzydyzyfs_zyblpz;
        }
        if(typeof(data.zzylx) == "undefined") {
          document.getElementById("sp891").innerHTML = "-";
        }
        else{
          document.getElementById("sp891").innerHTML = data.zzylx;
        }
        if(typeof(data.zzydyzyfs_jtcsmc) == "undefined") {
          document.getElementById("sp892").innerHTML = "-";
        }
        else{
          document.getElementById("sp892").innerHTML = data.zzydyzyfs_jtcsmc;
        }
      })
    },

    lawCaseInfo: function(id){
      // let url = global.IP + "/web/GetlawCaseByShipId?shipId=" + id;
      // $.get(url, function (data, status) {
      //   _this.anJianList = [];
      //   if(data.length > 0) {
      //     for(var i = 0 ; i < data.length; i++) {
      //       _this.anJianList.push();
      //     }
      //   }
      //console.log("id-->" + data);
      // anJianList = [];
      // var ddd = {caseName:"123", personName:"张三", idNumber:"414141414141414141", content:"违规作业", happenTime:"2022-01-01 14:00", loadTime:"2022-01-02 10:00", endTime:"2022-01-05 10:00", happenAdress:"横沙渔港东偏北三十度方向35°海里海域"};
      // anJianList.push(ddd);
      // for(var i = 0 ; i < data.length; i++){
      // }
      // });
    },

    // x的关闭函数
    closeShipDiv: function () {
      this.$emit('closeShipDiv',true);
      // var ids = sessionStorage.getItem("SHIPID");
      // alert(ids);
      // _this.$options.methods.shipInfos.bind(this)(ids);
    },

    ShipCollection: function(){
      // var sec;
      //   sec = $("#search1111").val();
      //   $.get(global.IP + "/GetEarlyWarningCount?time="+ sec +"", function (data, status) {
      //       var info = JSON.parse(data);
      //   }).fail(function(msg){

      //   });
      //   $.ajaxSettings.async = true;
      var shipID = _this.ShipId;
      _this.ifadd = true;
      this.$emit("receive",_this.ifadd);
      var data = {};
      data["staticShipId"] = shipID;
      data["type"] = 4;
      $.ajax({
          url: global.IP + "/web/SetWhiteOrBlackList",
          type:"POST",
          data: JSON.stringify(data),
          dataType: "json",
          contentType:"application/json",
          dateString: true,

          success: function(data){
            setAlertWindowShow("situationView", "添加成功","rgb(13, 38, 92)", 1);
          },
          error: function(data){
            setAlertWindowShow("situationView", "添加失败","rgb(13, 38, 92)", 1);
          },
      });
      // $.get(
      //   global.IP + "/web/SetFuxiuWhiteShip?" +
      //   "id=" +
      //   shipID,
      //   function (data, status) {
      //     alert("success");
      //   }
      // );
    },

    AddShipToFleet: function(){
      var sec;
        sec = $("#search1111").val();
        $.get(global.IP + "/GetEarlyWarningCount?time="+ sec +"", function (data, status) {
            var info = JSON.parse(data);
        }).fail(function(msg){
            console.log("error：" + JSON.stringify(msg))
        });
        $.ajaxSettings.async = true;
    },

    replayByCollin: function (shipMsg) {
      // console.log(shipMsg)
      this.boxNum = shipMsg;

      // 数据显示不下，所以显示一半数据，对数据的处理放在这里是比较合适的
      // for(let i=0; i <= this.anJianList.length; i++){
      //   this.anJianList[i].shortIdNumber = this.anJianList[i].idNumber.substring(0,4)+"...";
      //   this.anJianList[i].shortHappenTime = this.anJianList[i].happenTime.substring(0,3)+"...";
      //   this.anJianList[i].shortLoadTime = this.anJianList[i].loadTime.substring(0,7)+"...";
      //   this.anJianList[i].shortEndTime = this.anJianList[i].endTime.substring(0,7)+"...";
      //   this.anJianList[i].shortHappenAdress = this.anJianList[i].happenAdress.substring(0,5)+"...";
      // }
    },

  },
  beforeDestroy() {

  },
  watch: {
    whiteIsShow: function(newVal,oldVal){
      if(newVal == true && sessionStorage.getItem("jurisdiction") != 0) {
        document.getElementById("ShipCollection").style.display = "none";
      }
    }
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.changeColor{
    background-color: rgba(205,205,205,1);
  }

.selected{
  color: #FFFFFF;
}

.unselected{
  color:#778899;
}
#tableDiv::-webkit-scrollbar{
  width:0px;
}


</style>
