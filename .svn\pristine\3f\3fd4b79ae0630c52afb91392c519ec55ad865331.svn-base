package com.bd.entity;

import lombok.Data;

@Data
public class ShipOnlineCount {
    private String shipName;
    private String bdId;
    private int mmsi;
    private String owner;
    private String lxdh;
    private String lxdz;
    private int dw;
    private int shipType;
    private int length;
    private int width;
    private int onLineCount;//在线天数
    private int onLineCount2;//在线大于2小时天数
    private int closeCount;//异常关机次数
}
