package com.bd.service;

import com.bd.entity.BusinessManagement.AlarmRecord;
import com.bd.entity.*;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface TyphoonService {

    List<TyphoonInfo> GetTyphoonInfo();

    List<Integer> GetTyphoonCountInfo();

    List<TyphoonInfo> GetTyphoonInfoByYear(int year);

    List<Integer> GetTyphoonInfoByYearCount();

    List<TyphoonInfo> GetTyphoonInfoById(int id);

    List<TyphoonInfo> GetYBTyphoonInfoById(int id);

    List<TyphoonInfo> GetTodayTyphoonInfo(String startTime, String endTime);

    int GetTodayTyphoonInfoCount(String startTime, String endTime);

    List<TyphoonInfo> GetYBQDById(int id);
}
