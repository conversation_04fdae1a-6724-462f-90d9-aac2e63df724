package com.bd.entity;

import lombok.Data;

@Data
public class BlackAndWhiteList {
    private int id;
    private int staticShipId;
    private String shipName;
    private int type;
    private String owner;
    private String lxdh;
    private String loadTime;

    private String BDID;
    private int MMSI;
    private int IMO;
    private String CallSign;
    private String LXDZ;
    private String CTCL;
    private int DW;
    private int ZJGLQW;
    private String SSDW;
    private int SHIPTYPE = 2;
    private float length;
    private float width;
    private int bOutside = 1;
    private int nation = 1;
    private int bFocus = 0;
    private int bBlackFuXiu = 0;
    private int bWhiteFuXiu = 0;
    private String LastPosTermNo;
    private String LastPosBDId;
    private int people1;//核载人数
    private int people2;//最低配员
    private String DISTRICT;//属地
    private String overTime;//建造完工时间
    private String dictShipType;//船舶类型
    private String dictJobType;//作业类型
    private String inNetTime;//入网时间
    private String vLength;
    private String vWidth;
    private String vCall;
    private String jobPlace;//作业场所
    private String ZYLX;//船舶类型
    private String BLXKZ;//许可

    private String APP_JOB_BEGDATE;//申请作业开始时间
    private String APP_JOB_ENDDATE;//结束
    private String APP_JOB_PLACE;//场所
    private String APP_JOB_TYPE;//类型
    private String APP_JOB_WAY;//方式
    private String FISHING_PERMIT_NUMBER;//捕捞许可证号
    private String DICT_APP_TYPE;//申请许可类型
    private String FISHINGGEAR_AMOUNT;//渔具数量
    private String FISHINGGEAR_NAME;//渔具名称
    private String FISHINGGEAR_SPEC;//渔具规格
    private String MAIN_BREED;//主要捕捞品种
    private String MAIN_QUOTA;//捕捞限额
    private String SHIP_OWNER;//持证人
    private String ADDRESS;//持证人地址
}
