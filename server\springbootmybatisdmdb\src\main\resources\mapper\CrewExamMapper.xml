<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bd.mapper.CrewExamMapper">
    <insert id="insertCrewExam" parameterType="list">
        INSERT INTO SYSDBA.CREW_EXAM ("kszmh", "kscjxxwybs", "xm", "kskmdm",
        "kscjfs", "gxsj", "kd",
        "kskmmc", "czsj", "cyssdqdm", "zkzh", "sfzhm", "lsh", "sfjg", "etl_dt", "sfzhm", "lsh", "etl_dt") values
        <foreach collection="list" item="it" separator=",">
            (#{it.kszmh},#{it.kscjxxwybs},#{it.xm},#{it.kskmdm},
            #{it.kscjfs},#{it.gxsj},#{it.kd},
            #{it.kskmmc},#{it.czsj},#{it.cyssdqdm},#{it.zkzh},
            #{it.sfzhm},#{it.lsh},#{it.sfjg},
            #{it.etl_dt})
        </foreach>
    </insert>
    <insert id="insertCrewBX">
        insert into "SHIP"."CREW_BX"("POLICYNO", "APPLIADDRESS", "APPLIIDENTIFYNUMBER", "APPLINAME",
         "APPLIPHONENUMBER", "APPLIPOSTCODE", "ENDDATE", "HORSEPOWER", "INSUREDADDRESS",
         "INSUREDIDENTIFYNUMBER", "INSUREDNAME", "INSUREDPHONENUMBER", "INSUREDPOSTCODE",
         "MAKESTARTDATE", "PERSONCOUNT", "POLICYSTATUS", "SAILAREANAME", "SHIPCNAME",
         "SHIPSTRUCTNAME", "SHIPTYPENAME", "STARTDATE", "TONCOUNT")
        VALUES
        <foreach collection="crewBxes" item="it" separator=",">
        (
            #{it.policyno},
            #{it.appliaddress},
            #{it.appliidentifynumber},
            #{it.appliname},
            #{it.appliphonenumber},
            #{it.applipostcode},
            #{it.enddate},
            #{it.horsepower},
            #{it.insuredaddress},
            #{it.insuredidentifynumber},
            #{it.insuredname},
            #{it.insuredphonenumber},
            #{it.insuredpostcode},
            #{it.makestartdate},
            #{it.personcount},
            #{it.policystatus},
            #{it.sailareaname},
            #{it.shipcname},
            #{it.shipstructname},
            #{it.shiptypename},
            #{it.startdate},
            #{it.toncount}
            )
        </foreach>
    </insert>
    <insert id="insertLawCase">
        insert into "SHIP"."LAW_CASE"("NAME", "IDCARD", "CASENAME", "CAUSECONTENT", "OCCURADDR",
                                      "PROSTYLE", "ENFLENAMES", "STAGE", "STATUS", "REGCASEDATE",
                                      "DECISIONDATE", "CLOSECASEDATE")
        VALUES
        <foreach collection="lawCases" item="it" separator=",">
            (
            #{it.name},
            #{it.idCard},
            #{it.caseName},
            #{it.causeContent},
            #{it.occurAddr},
            #{it.prostyle},
            #{it.enfleNames},
            #{it.stage},
            #{it.status},
            #{it.regCaseDate},
            #{it.decisionDate},
            #{it.closeCaseDate}
            )
        </foreach>
    </insert>

    <insert id="insertFisheryPermitInfo">
    insert into "SHIP"."FISHERY_PERMIT_INFO"("YCBLXKXXWYBS", "YCXXWYBS", "GXSJ", "GXCZ", "YCSSDQDM",
    "YCSSDQMC", "YYBLXKZBH", "CZR", "SFZHHQYZCH", "CZRDZ",
    "SQBM", "SQRQ", "SQRYZBM", "SQRDH", "SQXKZLX", "SQLX",
    "SQLY", "QFR", "QFBM", "QFSJ", "CM", "YCLB", "YCBM",
    "YCJYZSBH", "GJDJZSBH", "CJG", "CBHHSBM", "JZWGRQ", "CZ",
    "XK", "XS", "ZDW", "JDW", "ZJZGL", "ZJXHY", "ZJXHE",
    "ZJXHS", "ZJGLY", "ZJGLE", "ZJGLS", "ZJJHY", "ZJJHE",
    "ZJJHS", "ZJSL", "YZJGL", "CTCZ", "YCSL", "YCRJ",
    "CWGJZBPZSBH", "SKGL", "ZCCM", "ZCSL", "ZCZGL", "ZCZJXH",
    "ZCZJSL", "ZZYLX", "ZZYDYZYFS", "ZZYDYZYFS_ZYCS", "ZZYDYZYFS_JTCSMC",
    "ZZYDYZYFS_ZYKSSJ", "ZZYDYZYFS_ZYJSSJ", "ZZYDYZYFS_ZYSXMS", "ZZYDYZYFS_YJMC",
    "ZZYDYZYFS_YJGG", "ZZYDYZYFS_YJSL", "ZZYDYZYFS_ZYBLPZ", "ZZYDYZYFS_BLXE",
    "ZZYDYZYFS_BLSCPSLDW", "ZZYDYZYFS_ZXWMHWNCC", "ZZYDEZYFS_ZYFS", "ZZYDEZYFS_SQZYCS",
    "ZZYDEZYFS_JTCSMC", "ZZYDEZYFS_ZYKSSJ", "ZZYDEZYFS_ZYJSSJ", "ZZYDEZYFS_SQZYSXMS", "ZZYDEZYFS_YJMC",
    "ZZYDEZYFS_YJGG", "ZZYDEZYFS_YJSL", "ZZYDEZYFS_ZYBLPZ", "ZZYDEZYFS_BLXE",
    "ZZYDEZYFS_BLDW", "ZZYDEZYFS_ZXWMHWNCC", "JZYLX", "JZYDYZYFS", "JZYDYZYFS_ZYCS",
    "JZYDYZYFS_JTCSMC", "JZYDYZYFS_ZYKSSJ", "JZYDYZYFS_ZYJSSJ", "JZYDYZYFS_ZYSXMS", "JZYDYZYFS_YJMC",
    "JZYDYZYFS_YJGG", "JZYDYZYFS_YJSL", "JZYDYZYFS_ZYBLPZ", "JZYDYZYFS_BLXE",
    "JZYDYZYFS_BLDW", "JZYDYZYFS_ZXWMHWNCC", "JZYDEZYFS_ZYFS", "JZYDEZYFS_ZYCS", "JZYDEZYFS_JTCSMC",
    "JZYDEZYFS_ZYKSSJ", "JZYDEZYFS_ZYJSSJ", "JZYDEZYFS_ZYSXMS", "JZYDEZYFS_YJMC", "JZYDEZYFS_YJGG",
    "JZYDEZYFS_YJSL", "JZYDEZYFS_ZYBLPZ", "JZYDEZYFS_BLXE", "JZYDEZYFS_BLDW", "JZYDEZYFS_ZXWMHWNCC",
    "FZZY_FZFS", "FZZY_ZYCS", "FZZY_JTZYCS", "FZZY_ZYKSSJ", "FZZY_ZYJSSJ", "FZZY_ZYSXMS",
    "FZZY_QTXZTJ", "ZZYDYZYFS_YJSSBZ", "ZZYDEZYFS_YJSSBZ", "JZYDYZYFS_YJSSBZ", "JZYDEZYFS_YJSSBZ",
    "SQSBH", "DYCS", "CZSM", "ETL_DT", "ZSYXQ")
    VALUES
    <foreach collection="InfoList" item="it" separator=",">
        (
        #{it.ycblxkxxwybs},
        #{it.ycxxwybs},
        #{it.gxsj},
        #{it.gxcz},
        #{it.ycssdqdm},
        #{it.ycssdqmc},
        #{it.yyblxkzbh},
        #{it.czr},
        #{it.sfzhhqyzch},
        #{it.czrdz},
        #{it.sqbm},
        #{it.sqrq},
        #{it.sqryzbm},
        #{it.sqrdh},
        #{it.sqxkzlx},
        #{it.sqlx},
        #{it.sqly},
        #{it.qfr},
        #{it.qfbm},
        #{it.qfsj},
        #{it.cm},
        #{it.yclb},
        #{it.ycbm},
        #{it.ycjyzsbh},
        #{it.gjdjzsbh},
        #{it.cjg},
        #{it.cbhhsbm},
        #{it.jzwgrq},
        #{it.cz},
        #{it.xk},
        #{it.xs},
        #{it.zdw},
        #{it.jdw},
        #{it.zjzgl},
        #{it.zjxhy},
        #{it.zjxhe},
        #{it.zjxhs},
        #{it.zjgly},
        #{it.zjgle},
        #{it.zjgls},
        #{it.zjjhy},
        #{it.zjjhe},
        #{it.zjjhs},
        #{it.zjsl},
        #{it.yzjgl},
        #{it.ctcz},
        #{it.ycsl},
        #{it.ycrj},
        #{it.cwgjzbpzsbh},
        #{it.skgl},
        #{it.zccm},
        #{it.zcsl},
        #{it.zczgl},
        #{it.zczjxh},
        #{it.zczjsl},
        #{it.zzylx},
        #{it.zzydyzyfs},
        #{it.zzydyzyfs_zycs},
        #{it.zzydyzyfs_jtcsmc},
        #{it.zzydyzyfs_zykssj},
        #{it.zzydyzyfs_zyjssj},
        #{it.zzydyzyfs_zysxms},
        #{it.zzydyzyfs_yjmc},
        #{it.zzydyzyfs_yjgg},
        #{it.zzydyzyfs_yjsl},
        #{it.zzydyzyfs_zyblpz},
        #{it.zzydyzyfs_blxe},
        #{it.zzydyzyfs_blscpsldw},
        #{it.zzydyzyfs_zxwmhwncc},
        #{it.zzydezyfs_zyfs},
        #{it.zzydezyfs_sqzycs},
        #{it.zzydezyfs_jtcsmc},
        #{it.zzydezyfs_zykssj},
        #{it.zzydezyfs_zyjssj},
        #{it.zzydezyfs_sqzysxms},
        #{it.zzydezyfs_yjmc},
        #{it.zzydezyfs_yjgg},
        #{it.zzydezyfs_yjsl},
        #{it.zzydezyfs_zyblpz},
        #{it.zzydezyfs_blxe},
        #{it.zzydezyfs_bldw},
        #{it.zzydezyfs_zxwmhwncc},
        #{it.jzylx},
        #{it.jzydyzyfs},
        #{it.jzydyzyfs_zycs},
        #{it.jzydyzyfs_jtcsmc},
        #{it.jzydyzyfs_zykssj},
        #{it.jzydyzyfs_zyjssj},
        #{it.jzydyzyfs_zysxms},
        #{it.jzydyzyfs_yjmc},
        #{it.jzydyzyfs_yjgg},
        #{it.jzydyzyfs_yjsl},
        #{it.jzydyzyfs_zyblpz},
        #{it.jzydyzyfs_blxe},
        #{it.jzydyzyfs_bldw},
        #{it.jzydyzyfs_zxwmhwncc},
        #{it.jzydezyfs_zyfs},
        #{it.jzydezyfs_zycs},
        #{it.jzydezyfs_jtcsmc},
        #{it.jzydezyfs_zykssj},
        #{it.jzydezyfs_zyjssj},
        #{it.jzydezyfs_zysxms},
        #{it.jzydezyfs_yjmc},
        #{it.jzydezyfs_yjgg},
        #{it.jzydezyfs_yjsl},
        #{it.jzydezyfs_zyblpz},
        #{it.jzydezyfs_blxe},
        #{it.jzydezyfs_bldw},
        #{it.jzydezyfs_zxwmhwncc},
        #{it.fzzy_fzfs},
        #{it.fzzy_zycs},
        #{it.fzzy_jtzycs},
        #{it.fzzy_zykssj},
        #{it.fzzy_zyjssj},
        #{it.fzzy_zysxms},
        #{it.fzzy_qtxztj},
        #{it.zzydyzyfs_yjssbz},
        #{it.zzydezyfs_yjssbz},
        #{it.jzydyzyfs_yjssbz},
        #{it.jzydezyfs_yjssbz},
        #{it.sqsbh},
        #{it.dycs},
        #{it.czsm},
        #{it.etl_dt},
        #{it.zsyxq}
        )
    </foreach>
    </insert>
    <insert id="insertFishingBoatInfo">
        insert into "SHIP"."FISHING_BOAT_INFO"("YCXXWYBS", "GXSJ", "GXCZ", "YCSSDQDM", "YCSSDQMC",
        "YCBM", "CM", "CMYW", "CBZL", "CZ",
        "XK", "XS", "ZDW", "JDW", "ZJZGL",
        "ZJXHY", "ZJXHE", "ZJXHS", "ZJGLY", "ZJGLE",
        "ZJGLS", "CTCZ", "CTCZYW", "JZWGRQ", "ZJSL",
        "CBHHSBM", "ZCZJSL", "ZCZJXH", "ZCCM", "ZCSL",
        "ZCZGL", "HYYYBLXKZLB", "CBSYRMC", "CBSYRMCYW", "CBSYRDZ",
        "CBSYRDZYW", "CBSYRDH", "CBLX", "CBLXYW", "ZJJHY",
        "ZJJHE", "ZJJHS", "CWGJZBPZSBH", "SKGL", "HZCZ", "HZZD", "ETL_TIME")
        VALUES
        <foreach collection="InfoList" item="it" separator=",">
        (
            #{it.ycxxwybs},
            #{it.gxsj},
            #{it.gxcz},
            #{it.ycssdqdm},
            #{it.ycssdqmc},
            #{it.ycbm},
            #{it.cm},
            #{it.cmyw},
            #{it.cbzl},
            #{it.cz},
            #{it.xk},
            #{it.xs},
            #{it.zdw},
            #{it.jdw},
            #{it.zjzgl},
            #{it.zjxhy},
            #{it.zjxhe},
            #{it.zjxhs},
            #{it.zjgly},
            #{it.zjgle},
            #{it.zjgls},
            #{it.ctcz},
            #{it.ctczyw},
            #{it.jzwgrq},
            #{it.zjsl},
            #{it.cbhhsbm},
            #{it.zczjsl},
            #{it.zczjxh},
            #{it.zccm},
            #{it.zcsl},
            #{it.zczgl},
            #{it.hyyyblxkzlb},
            #{it.cbsyrmc},
            #{it.cbsyrmcyw},
            #{it.cbsyrdz},
            #{it.cbsyrdzyw},
            #{it.cbsyrdh},
            #{it.cblx},
            #{it.cblxyw},
            #{it.zjjhy},
            #{it.zjjhe},
            #{it.zjjhs},
            #{it.cwgjzbpzsbh},
            #{it.skgl},
            #{it.hzcz},
            #{it.hzzd},
            #{it.etl_time}
            )
        </foreach>
    </insert>
    <insert id="insertShipNationalRegisInfo">
        INSERT INTO "SHIP"."SHIP_NATIONAL_REGIS_INFO"("GJSYQDJXXWYBS", "YCXXWYBS", "GXSJ", "GXCZ", "YCSSDQDM",
        "YCSSDQMC", "YYCBSYQDJZSBH", "YYCBGJZSBH", "YYCBDJZSYXQ", "YCSSDQ", "CJG", "CJGYW", "SYQGJDJSPJG",
        "SYQGJDJSPJGYJ", "SYQGJDJSPR", "SYQGJDJSPSJ", "SYQGJDJSQSBH", "SYQGJDJSQRQ", "SYRSZGF", "YYCBLSGJZSBH",
        "YYCBZJSYQZSBH", "CZRMC", "CZRMCYW", "CZRDZ", "CZRDZYW", "CZRJMSFZHMHGSZCH", "CZRDH", "YYCBGJZSYXQ", "GYRXX",
        "CBJYF", "CBJYFYW", "CQG", "CQGYW", "CBJYFDZ", "CBJYFDZYW", "YCBM", "CM", "CMYW", "CBZL", "CC", "XK", "XS",
        "ZDW", "JDW", "ZJZGL", "ZJXHY", "ZJXHE", "ZJXHS", "ZJGLY", "ZJGLE", "ZJGLS", "CTCZ", "CTCZYW", "JZWGRQ",
        "ZJSL", "CBHHSBM", "ZCCM", "ZCSL", "ZCZGL", "CBSYRMC", "CBSYRMCYW", "CBSYRDZ", "CBSYRDZYW", "CBSYRDH",
        "CBLX", "CBLXYW", "ZJJHY", "ZJJHE", "ZJJHS", "CWGJZBPZSBH", "SKGL", "HZCC", "HZZD", "BCFFZSLX", "CZSM", "ETL_DT")
        VALUES
        <foreach collection="InfoList" item="it" separator=",">
            (
            #{it.gjsyqdjxxwybs},
            #{it.ycxxwybs},
            #{it.gxsj},
            #{it.gxcz},
            #{it.ycssdqdm},
            #{it.ycssdqmc},
            #{it.yycbsyqdjzsbh},
            #{it.yycbgjzsbh},
            #{it.yycbdjzsyxq},
            #{it.ycssdq},
            #{it.cjg},
            #{it.cjgyw},
            #{it.syqgjdjspjg},
            #{it.syqgjdjspjgyj},
            #{it.syqgjdjspr},
            #{it.syqgjdjspsj},
            #{it.syqgjdjsqsbh},
            #{it.syqgjdjsqrq},
            #{it.syrszgf},
            #{it.yycblsgjzsbh},
            #{it.yycbzjsyqzsbh},
            #{it.czrmc},
            #{it.czrmcyw},
            #{it.czrdz},
            #{it.czrdzyw},
            #{it.czrjmsfzhmhgszch},
            #{it.czrdh},
            #{it.yycbgjzsyxq},
            #{it.gyrxx},
            #{it.cbjyf},
            #{it.cbjyfyw},
            #{it.cqg},
            #{it.cqgyw},
            #{it.cbjyfdz},
            #{it.cbjyfdzyw},
            #{it.ycbm},
            #{it.cm},
            #{it.cmyw},
            #{it.cbzl},
            #{it.cc},
            #{it.xk},
            #{it.xs},
            #{it.zdw},
            #{it.jdw},
            #{it.zjzgl},
            #{it.zjxhy},
            #{it.zjxhe},
            #{it.zjxhs},
            #{it.zjgly},
            #{it.zjgle},
            #{it.zjgls},
            #{it.ctcz},
            #{it.ctczyw},
            #{it.jzwgrq},
            #{it.zjsl},
            #{it.cbhhsbm},
            #{it.zccm},
            #{it.zcsl},
            #{it.zczgl},
            #{it.cbsyrmc},
            #{it.cbsyrmcyw},
            #{it.cbsyrdz},
            #{it.cbsyrdzyw},
            #{it.cbsyrdh},
            #{it.cblx},
            #{it.cblxyw},
            #{it.zjjhy},
            #{it.zjjhe},
            #{it.zjjhs},
            #{it.cwgjzbpzsbh},
            #{it.skgl},
            #{it.hzcc},
            #{it.hzzd},
            #{it.bcffzslx},
            #{it.czsm},
            #{it.etl_dt}
            )
        </foreach>
    </insert>
    <insert id="insertShipNameRegisInfo">
        insert into "SHIP"."SHIP_NAME_REGIS_INFO"("ZJZGL", "YYCBCMSQSBH", "YYYBLXKZBH", "YCM", "CBSYRDZ",
        "YYXMPZWH", "CBZL", "SQRDZ", "PZWH", "CBLXYW",
        "CBSYRMC", "YCSSDQMC", "ZXHZZDJZMSBH", "CBLX", "GXCZ",
        "CM", "SQRXM", "CBSYRDH", "CTCZYW", "YYCBCMDJBAHZSBH",
        "CZ", "CMDJXXWYBS", "SQRQ", "CBSYRMCYW", "CTCZ",
        "CWGJZBPZSBH", "CMYW", "CZSM", "SQRDH", "SQRJMSFZHMHGSZCH",
        "ZDW", "CBSYRDZYW", "GXSJ", "YCXXWYBS", "YCSSDQDM", "ETL_DT")
        VALUES
        <foreach collection="InfoList" item="it" separator=",">
            (
            #{it.zjzgl},
            #{it.yycbcmsqsbh},
            #{it.yyyblxkzbh},
            #{it.ycm},
            #{it.cbsyrdz},
            #{it.yyxmpzwh},
            #{it.cbzl},
            #{it.sqrdz},
            #{it.pzwh},
            #{it.cblxyw},
            #{it.cbsyrmc},
            #{it.ycssdqmc},
            #{it.zxhzzdjzmsbh},
            #{it.cblx},
            #{it.gxcz},
            #{it.cm},
            #{it.sqrxm},
            #{it.cbsyrdh},
            #{it.ctczyw},
            #{it.yycbcmdjbahzsbh},
            #{it.cz},
            #{it.cmdjxxwybs},
            #{it.sqrq},
            #{it.cbsyrmcyw},
            #{it.ctcz},
            #{it.cwgjzbpzsbh},
            #{it.cmyw},
            #{it.czsm},
            #{it.sqrdh},
            #{it.sqrjmsfzhmhgszch},
            #{it.zdw},
            #{it.cbsyrdzyw},
            #{it.gxsj},
            #{it.ycxxwybs},
            #{it.ycssdqdm},
            #{it.etl_dt}
            )
        </foreach>
    </insert>
    <insert id="insertAdminLaw">
        insert into "SHIP"."ADMIN_LAW"("ajmc", "afsy", "zasygl", "larq", "jarq",
        "cf_result", "cfjg", "cmh", "ajly", "afdd",
        "wj", "sfjyyj","yhw")
        VALUES
        <foreach collection="adminLaws" item="it" separator=",">
            (
            #{it.ajmc},
            #{it.afsy},
            #{it.zasygl},
            #{it.larq},
            #{it.jarq},
            #{it.cf_result},
            #{it.cfjg},
            #{it.cmh},
            #{it.ajly},
            #{it.afdd},
            #{it.wj},
            #{it.sfjyyj},
            #{it.yhw}
            )
        </foreach>
    </insert>
    <insert id="insertWorkDays">
        insert into ship.ship_workCount("STATICSHIPID", "LOADTIME")
        select STATICSHIPID, LOADTIME from (
        select t.staticshipid, t.loadtime, Row_Number() OVER (partition by trunc(t.loadtime) order by t.loadtime) rank
        from ship.ship_historytrackpoint_tmp t
        where t.staticshipid = #{shipId}
        and trunc(t.loadtime) &lt; trunc(sysdate)
        and trunc(t.loadtime) &gt; trunc(sysdate - 2)
        union all
        select t.staticshipid, t.loadtime, Row_Number() OVER (partition by trunc(t.loadtime) order by t.loadtime) rank
        from ship.ship_historytrackpoint_ais t
        where t.staticshipid = #{shipId}
        and trunc(t.loadtime) &lt; trunc(sysdate)
        and trunc(t.loadtime) &gt; trunc(sysdate - 2)
        limit 1
        ) where rank=1
    </insert>

    <insert id="insertWorkDays2">
        insert into ship.ship_workDayCount("STATICSHIPID", "LOADTIME")
        select STATICSHIPID, LOADTIME from (
        select t.staticshipid, t.loadtime, Row_Number() OVER (partition by trunc(t.loadtime) order by t.loadtime) rank
        from ship.ship_historytrackpoint_tmp t
        where t.staticshipid = #{shipId}
          and trunc(t.loadtime) &lt; trunc(sysdate)
          and trunc(t.loadtime) &gt; trunc(sysdate - 2)
          and t.lon > 1222500000
        union all
        select t.staticshipid, t.loadtime, Row_Number() OVER (partition by trunc(t.loadtime) order by t.loadtime) rank
        from ship.ship_historytrackpoint_ais t
        where t.staticshipid = #{shipId}
          and trunc(t.loadtime) &lt; trunc(sysdate)
          and trunc(t.loadtime) &gt; trunc(sysdate - 2)
          and t.lon > 1222500000
        limit 1
        ) where rank=1
    </insert>

    <insert id="insertWorkDays_BD">
        insert into ship.ship_workCount("STATICSHIPID", "LOADTIME", "BDORAIS")
        select STATICSHIPID, LOADTIME, 0 as BDORAIS from (
        select t.staticshipid, t.loadtime, Row_Number() OVER (partition by trunc(t.loadtime) order by t.loadtime) rank
        from ship.ship_historytrackpoint_tmp t
        where t.staticshipid = #{shipId}
        and trunc(t.loadtime) &gt; trunc(#{time1})
        and trunc(t.loadtime) &lt; trunc(#{time2})
        ) where rank=1
    </insert>

    <insert id="insertWorkDays_AIS">
        insert into ship.ship_workCount("STATICSHIPID", "LOADTIME", "BDORAIS")
        select STATICSHIPID, LOADTIME, 1 as BDORAIS from (
        select t.staticshipid, TO_CHAR(DATEADD(SECOND, t.TIMESTAMP, '1970-01-01'), 'YYYY-MM-DD HH24:MI:SS') AS LOADTIME,
        Row_Number() OVER (partition by trunc(TO_CHAR(DATEADD(SECOND, t.TIMESTAMP, '1970-01-01'), 'YYYY-MM-DD HH24:MI:SS')) order by TO_CHAR(DATEADD(SECOND, t.TIMESTAMP, '1970-01-01'), 'YYYY-MM-DD HH24:MI:SS')) rank
        from ship.ship_historytrackpoint_ais t
        where t.staticshipid = #{shipId}
          and trunc(TO_CHAR(DATEADD(SECOND, t.TIMESTAMP, '1970-01-01'), 'YYYY-MM-DD HH24:MI:SS')) &gt; trunc(#{time1})
          and trunc(TO_CHAR(DATEADD(SECOND, t.TIMESTAMP, '1970-01-01'), 'YYYY-MM-DD HH24:MI:SS')) &lt; trunc(#{time2})
        ) where rank=1
    </insert>

    <insert id="insertShipInOutReport">
        insert into SHIP.PORTNODE( PORTNAME, SHIPNAME, "TIME", CREWSTR, INOROUT) values
        <foreach collection="reports" item="report" index="index" separator=",">
            (
            #{report.portName,jdbcType=VARCHAR},
            #{report.shipName},
            #{report.time},
            #{report.crewStr},
            #{report.inOrOut}
            )
        </foreach>
    </insert>
    <insert id="insertWorkDaysTest">
        insert into ship.ship_workDayCount("STATICSHIPID", "LOADTIME")
        select STATICSHIPID, LOADTIME from (
            select t.staticshipid, t.loadtime, Row_Number() OVER (partition by trunc(t.loadtime) order by t.loadtime) rank
            from ship.ship_historytrackpoint_tmp t
            where t.staticshipid = #{shipId}
              and trunc(t.loadtime) &lt; trunc(#{time2})
              and trunc(t.loadtime) &gt; trunc(#{time1})
              and t.lon > 1222500000
            union all
            select t.staticshipid, t.loadtime, Row_Number() OVER (partition by trunc(t.loadtime) order by t.loadtime) rank
            from ship.ship_historytrackpoint_ais t
            where t.staticshipid = #{shipId}
              and trunc(t.loadtime) &lt; trunc(#{time2})
              and trunc(t.loadtime) &gt; trunc(#{time1})
              and t.lon > 1222500000
                limit 1
        ) where rank=1
    </insert>
    <insert id="insertWorkDays_BD_day">
        insert into ship.ship_workCount("STATICSHIPID", "LOADTIME", "BDORAIS")
            select staticshipid, bdtime, 0 as BDORAIS from ship.ship_dynamiction
             where staticshipid = #{shipId}
               and bdtime &gt; #{time1}
               and bdtime &lt; #{time2}

    </insert>
    <insert id="insertWorkDays_AIS_day">
        insert into ship.ship_workCount("STATICSHIPID", "LOADTIME", "BDORAIS")
            select staticshipid, aistime, 1 as BDORAIS from ship.ship_dynamiction
             where staticshipid = #{shipId}
               and aistime &gt; #{time1}
               and aistime &lt; #{time2}

    </insert>

    <update id="updateLxdh">
        update ship.people set lxdh = '-'
    </update>
    <delete id="delete">
        delete from ship.CREW_BX;
    </delete>
    <delete id="deleteShipNationRegisInfo">
        truncate table SHIP.SHIP_NATIONAL_REGIS_INFO
    </delete>
    <delete id="deleteShipNameRegisInfo">
        truncate table SHIP.SHIP_NAME_REGIS_INFO
    </delete>
    <delete id="deleteFisheryPermitInfo">
        truncate table SHIP.FISHERY_PERMIT_INFO
    </delete>
    <delete id="deleteLawCase">
        truncate table SHIP.LAW_CASE
    </delete>
    <delete id="deleteAdminLaw">
        truncate table SHIP.ADMIN_LAW
    </delete>
    <select id="getShipIdList" resultType="java.lang.Integer">
        select id from ship.ship_staticinfo where boutside = 0 and shiptype = 2;
    </select>
    <select id="select401MsgCount" resultType="java.lang.Long">
        select count(1)
        from SHIP.ALARMRECORDINFO
        where to_char(UPDATETIME, 'yyyy-mm-dd') = to_char(sysdate, 'yyyy-mm-dd')
          and TYPE = 401
          and CONTENT like concat('%', #{msg}, '%');
    </select>

</mapper>