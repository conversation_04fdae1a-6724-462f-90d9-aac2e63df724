<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bd.mapper.AreaMapper">
    <insert id="InsertAreaInfo" useGeneratedKeys="true" keyProperty="ID">
        insert into SHIP.AreaInfo(NAME, POSCOUNT, POINTSTR, TYPE, ROUTETYPE, STARTTIME, ENDTIME, BSPEED, SPEED, LOADTIME, USERID)
        VALUES(
        #{info.NAME},
        #{info.POSCOUNT},
        #{info.POINTSTR},
        #{info.TYPE},
        #{info.ROUTETYPE},
        #{info.STARTTIME},
        #{info.ENDTIME},
        #{info.BSPEED},
        #{info.SPEED},
        #{info.LOADTIME},
        #{info.USERID}
        );
    </insert>
    <update id="UpdateAreaInfo">
        update SHIP.AREAINFO
        set
        NAME = #{info.NAME},
        POSCOUNT = #{info.POSCOUNT},
        POINTSTR = #{info.POINTSTR},
        TYPE = #{info.TYPE},
        ROUTETYPE = #{info.ROUTETYPE},
        STARTTIME = #{info.STARTTIME},
        ENDTIME = #{info.ENDTIME},
        BSPEED = #{info.BSPEED},
        SPEED = #{info.SPEED},
        LOADTIME = #{info.LOADTIME},
        USERID = #{info.USERID}
        WHERE ID = #{info.ID};
    </update>
    <delete id="DeleteAreaInfo">
        DELETE FROM SHIP.AREAINFO WHERE ID = #{id};
    </delete>

    <select id="GetAreaInfo" resultType="com.bd.entity.AreaInfo">
        SELECT * FROM SHIP.AREAINFO where type != -1;
    </select>
    <select id="GetAreaInfoByUserId" resultType="com.bd.entity.AreaInfo">
        SELECT * FROM SHIP.AREAINFO where TYPE in (0,1,2,3,4,100) AND (USERID == ${userId} or USERID == 0)
    </select>
    <select id="GetAllAreaInfo" resultType="com.bd.entity.AreaInfo">
        SELECT * FROM SHIP.AREAINFO where TYpe != -1 limit #{pageNum}, 10;
    </select>
    <select id="GetAreaInfoById" resultType="com.bd.entity.AreaInfo">
        SELECT * FROM SHIP.AREAINFO where ID = #{id};
    </select>
    <select id="GetAreaInfoByUserIdPageNum" resultType="com.bd.entity.AreaInfo">
        SELECT * FROM SHIP.AREAINFO where TYPE != -1 AND (USERID == ${userId} or USERID == 0) limit #{pageNum}, 10;
    </select>
    <select id="GetAreaInfoByUserIdPageNumCount" resultType="java.lang.Integer">
        SELECT count(*) FROM SHIP.AREAINFO where TYPE != -1 AND (USERID == ${userId} or USERID == 0);
    </select>
    <select id="GetAllAreaInfoCount" resultType="java.lang.Integer">
        select count(*) from SHIP.AREAINFO where TYpe != -1
    </select>

    <select id="GetAllMarkInfo" resultType="com.bd.entity.MarkInfo">
        SELECT * FROM SHIP.MARKINFO WHERE USERID = #{userId} or OWNSHOW = 0 order by loadtime desc
    </select>

    <select id="GetAllMarkInfoCount" resultType="java.lang.Integer">
        SELECT COUNT (*) FROM SHIP.MARKINFO WHERE USERID = #{userId} or OWNSHOW = 0
    </select>

    <insert id="InsertMarkInfo" useGeneratedKeys="true" keyProperty="id">
        insert into SHIP.MARKINFO(TYPE, POSCOUNT, POINTSTR, CONTENT, LOADTIME, USERID, OWNSHOW, RADIUS)
        VALUES(
        #{markInfo.type},
        #{markInfo.posCount},
        #{markInfo.pointStr},
        #{markInfo.content},
        #{markInfo.loadTime},
        #{markInfo.userId},
        #{markInfo.ownShow},
        #{markInfo.radius}
        );
    </insert>

    <delete id="deleteMarkInfoById">
        DELETE FROM SHIP.MARKINFO WHERE ID = #{markId} AND USERID = #{userId}
    </delete>

    <update id="UpdateMarkInfo">
        update SHIP.MARKINFO
        set
            TYPE = #{markInfo.type},
            POSCOUNT = #{markInfo.posCount},
            POINTSTR = #{markInfo.pointStr},
            CONTENT = #{markInfo.content},
            LOADTIME = #{markInfo.loadTime},
            USERID = #{markInfo.userId},
            OWNSHOW = #{markInfo.ownShow},
            RADIUS = #{markInfo.radius}
        WHERE ID = #{markInfo.id};
    </update>

    <select id="GetMarkInfoById" resultType="com.bd.entity.MarkInfo">
        SELECT * FROM SHIP.MARKINFO where ID = #{id};
    </select>
    <select id="GetFishAreaInfo" resultType="com.bd.entity.FishArea">
        SELECT * FROM SHIP.FISHAREAINFO;
    </select>

    <select id="GetMarkInfoByName" resultType="com.bd.entity.ShipSerch">
        SELECT * FROM SHIP.MARKINFO
        WHERE USERID = #{userId}
           and content like concat('%',#{keyword},'%')
           or OWNSHOW = 0 order by loadtime desc
    </select>

</mapper>