package com.bd.mapper;

import com.bd.entity.*;
import com.bd.entity.dto.CrewCertificationQueryDto;
import com.bd.entity.dto.PeopleQueryDto;
import com.bd.entity.dto.ShipQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PeopleMapper {

    int GetAllPeopleCount(PeopleQueryDto queryDto);

    List<People> GetAllPeople(PeopleQueryDto queryDto);

    List<People> GetAllPeople_Export(PeopleQueryDto queryDto);

    int InsertLawRecordInfo(@Param("lawRecordInfo")LawRecordInfo lawRecordInfo);

    int InsertBdMsg(@Param("bdMsg") BdMsg bdMsg);

    List<BdMsg> GetBdMsg(@Param("shipName")String shipName,
                         @Param("bdId")String bdId,
                         @Param("startTime")String startTime,
                         @Param("endTime")String endTime,
                         @Param("pageNum")int pageNum);

    int GetBdMsgCount(@Param("shipName")String shipName,
                         @Param("bdId")String bdId,
                         @Param("startTime")String startTime,
                         @Param("endTime")String endTime);

    List<BdMsg> GetAllBdMsg(@Param("shipName")String shipName,
                         @Param("bdId")String bdId,
                         @Param("startTime")String startTime,
                         @Param("endTime")String endTime);

    List<BdMsg> GetAllBdMsgByUserId(@Param("userId")int userId, @Param("shipId")int shipId);

    void DeleteBdMsg(@Param("id")int id);

    List<LawRecordInfo> GetLawRecordTodayWarning(@Param("time")String time, @Param("model")int model);
    LawRecordInfo GetLawRecordInfoById(@Param("id")int id, @Param("model")int model);

    List<LawRecordInfo> GetAllLawRecordInfo(@Param("name")String name,
                                            @Param("startTime")String startTime,
                                            @Param("endTime")String endTime,
                                            @Param("pageNum")int pageNum);

    List<LawRecordInfo> GetLawRecordInfo(@Param("name")String name,
                                            @Param("startTime")String startTime,
                                            @Param("endTime")String endTime);

    void DeleteLawRecordInfo(@Param("id")int id);
    int GetAllLawRecordInfoCount(@Param("name")String name,
                                 @Param("startTime")String startTime,
                                 @Param("endTime")String endTime);

    List<People> GetPeopleByShipId(@Param("shipId")int shipId, @Param("pageNum")int pageNum);

    int GetPeopleByShipIdCount(@Param("shipId")int shipId);

    List<LawPeople> GetLawPeopleInfo();

    void InsertCrewInfo(@Param("list")List<People> crewList);

    void InsertBdMsgExample(@Param("content")String content, @Param("userId")int userId);

    void DeleteBdMsgExample(@Param("id")int id);

    void UpdateBdMsgExample(@Param("id")int id, @Param("userId")int userId, @Param("content")String content);

    List<BDMsgExample> GetBdMsgExample(@Param("userId")int userId);

    void ClearCrewInfo();

    List<String> GetPeopleByShipName(@Param("shipName")String shipName, @Param("pageNum")int pageNum);

    int GetPeopleByShipNameCount(@Param("idCardList")List<String> shipName);

    List<People> GetPeopleByIdCard(@Param("idCardList")List<String> idCardList, @Param("pageNum")int pageNum);

    List<People> GetCrewPeopleByShipId(@Param("shipId")int shipId);

    FisheryBoatInOutReport GetPortNodeByShipId(@Param("shipId") int shipId, @Param("state") Integer state);

    List<CrewBx> GetCrewBxByShipName(@Param("shipName")String shipName, @Param("time")String time);

    List<CrewBx> GetCrewBxByShipNames(@Param("shipNames") List<String> shipNames, @Param("time")String getNowTimeString);

    List<CrewBx> GetPeopleByIdcards(@Param("idcards") List<String> idcards);

    List<LawCase> GetlawCaseByShipId(@Param("idcards")List<String> idcards);

    List<String> GetShipNameByPeople(@Param("keyword")String keyword);

    Integer GetPersonCount(@Param("shipName")String shipName);

    String GetApplinameByShipName(String shipName);

    String GetZshmByIdCard(@Param("idcard")String idcard);

    String GetEndTimeByIdCard(@Param("idcard")String idcard);

    List<FisheryBoatInOutReport> GetPortNodeByShipIds(@Param("list") List<Integer> shipIds,@Param("beforeTime")String time);

    void UpdateCrewTimeState(@Param("time")String time);

    List<People> GetCrewCertificateStatistics(CrewCertificationQueryDto queryDto);

    String GetIdcardSignCodeByIdcard(String idcard);


}
