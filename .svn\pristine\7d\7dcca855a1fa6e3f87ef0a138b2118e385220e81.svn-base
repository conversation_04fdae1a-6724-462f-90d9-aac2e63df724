﻿body {
}

.ShowMouseInfoDiv
{
	font-size:11px;
	color:White;
	border: 2px solid #000000; 
	border-radius: 5px; 
	padding: 5px 5px; 
	line-height:14px;
	width: 65px; 
	height:23px;
	background-color:Black;
	-webkit-opacity: 0.6; /* Netscape and Older than Firefox 0.9 */
	-moz-opacity: 0.6; /* Safari 1.x (pre WebKit!) 老式khtml内核的Safari浏览器*/
	-khtml-opacity: 0.6; /* IE9 + etc...modern browsers */
	opacity: .6; /* IE 4-9 */
	filter: alpha(opacity=60); /*This works in IE 8 & 9 too*/
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=60)"; /*IE4-IE9*/
	filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=60);
}
	
.ShowSpanTextDiv
{
	font-size:11px;
	color:Black;
	-webkit-opacity: 0.4; /* Netscape and Older than Firefox 0.9 */
    -moz-opacity: 0.4; /* Safari 1.x (pre WebKit!) 老式khtml内核的Safari浏览器*/
    -khtml-opacity: 0.4; /* IE9 + etc...modern browsers */
    opacity: .4; /* IE 4-9 */
    filter: alpha(opacity=40); /*This works in IE 8 & 9 too*/
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=40)"; /*IE4-IE9*/
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=40);
	}

.ShowCutMapDiv
{
	font-size:15px;
	color:Black;
	border: 2px solid #379082; 
	border-radius: 5px; 
	padding: 10px 10px; 
	width: 90%; 
	height:90%;
	background-color:White;
	-webkit-opacity: 0.9; /* Netscape and Older than Firefox 0.9 */
    -moz-opacity: 0.9; /* Safari 1.x (pre WebKit!) 老式khtml内核的Safari浏览器*/
    -khtml-opacity: 0.9; /* IE9 + etc...modern browsers */
    opacity: .9; /* IE 4-9 */
    filter: alpha(opacity=90); /*This works in IE 8 & 9 too*/
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=90)"; /*IE4-IE9*/
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=90);

	}
	
.aLinkDlg
{
	font-size:13px; font-weight:bold;
	}

.aLinkDlg:link
{
	color:rgb(0, 100, 100); text-decoration:none;
	}
.aLinkDlg:visited
{
    color:rgb(0, 100, 100); text-decoration:none;
    }
.aLinkDlg:hover
{
	color:rgb(0, 0, 0); text-decoration:none;
	}

.zoomImgStyle
{
	-webkit-opacity: 0.7; /* Netscape and Older than Firefox 0.9 */
	-moz-opacity: 0.7; /* Safari 1.x (pre WebKit!) 老式khtml内核的Safari浏览器*/
	-khtml-opacity: 0.7; /* IE9 + etc...modern browsers */
	opacity: .7; /* IE 4-9 */
	filter: alpha(opacity=70); /*This works in IE 8 & 9 too*/
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=70)"; /*IE4-IE9*/
	filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=70);
	}
.zoomImgStyle:hover
{
	-webkit-opacity: 1; /* Netscape and Older than Firefox 0.9 */
	-moz-opacity: 1; /* Safari 1.x (pre WebKit!) 老式khtml内核的Safari浏览器*/
	-khtml-opacity: 1; /* IE9 + etc...modern browsers */
	opacity: .9; /* IE 4-9 */
	filter: alpha(opacity=100); /*This works in IE 8 & 9 too*/
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)"; /*IE4-IE9*/
	filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
	cursor:pointer;
	}

.TransparencyBgStyle
{
		background-color:White;
		-webkit-opacity: 0.1; /* Netscape and Older than Firefox 0.9 */
		-moz-opacity: 0.1; /* Safari 1.x (pre WebKit!) 老式khtml内核的Safari浏览器*/
		-khtml-opacity: 0.1; /* IE9 + etc...modern browsers */
		opacity: .1; /* IE 4-9 */
		filter: alpha(opacity=10); /*This works in IE 8 & 9 too*/
		-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=10)"; /*IE4-IE9*/
		filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=10);

	}

.zoomSlider 
{ 
    width:2px; 
    margin:0 auto; 
    height:150px; 
    background:#eee; 
    border:1px solid gray; 
    border-color:#858585 #858585 #858585 #858585; 
    border-radius: 3px; 
} 
.moveZoomSlider
{
    margin:0 auto; 
    background:#eee; 
    border:1px solid gray; 
    border-color:#858585 #858585 #858585 #858585; 
    border-radius: 3px; 
	}